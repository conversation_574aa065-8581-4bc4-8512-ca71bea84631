package com.digiwin.escloud.aioitms.bigdata.service;

import com.digiwin.escloud.aioitms.bigdata.model.Query;
import com.digiwin.escloud.aioitms.bigdata.model.SchemaPasswordExpiryDateParam;
import com.digiwin.escloud.common.model.ResponseBase;
import com.digiwin.escloud.common.model.UnitType;
import com.digiwin.escloud.common.response.BaseResponse;
import com.digiwin.escloud.common.response.PageInfo;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date: 2022-10-10 16:48
 * @Description
 */
public interface IOracleV3Service {
    List<Map<String, Object>> getBufferHitRate(String startTime, String endTime, String dbId);

    List<Map<String, Object>> getSharedPoolHitRate(String startTime, String endTime, String dbId);

    List<Map<String, Object>> getSortedPoolHitRate(String startTime, String endTime, String dbId);

    List<Map<String, Object>> getLogBufferRate(String startTime, String endTime, String dbId);

    String getLastCollectedTime(String dbId, String memoryName);

    List<Map<String, Object>> getOracleMemory(String startTime, String endTime, String dbId, String memoryName);

    Map<String, Object> getOracleInfo(String startTime, String endTime, String dbId);

    Map<String, Object> getOracleParameter(String startTime, String endTime, String dbId, String parameterName,
                                           int pageNum, int pageSize);

    List<Map<String, Object>> getResourceUsage(String startTime, String endTime, String dbId, String resourceName);

    Map<String, Object> getFileStatus(String startTime, String endTime, String dbId,
                                      int pageNum, int pageSize);

    Map<String, Object> getControlDocument(String startTime, String endTime, String dbId,
                                           int pageNum, int pageSize);

    Map<String, Object> getRedoLogFile(String startTime, String endTime, String dbId,
                                       int pageNum, int pageSize);

    Map<String, Object> getInvalidObject(String startTime, String endTime, String content, String objType, String dbId,
                                         int pageNum, int pageSize);

    Map<String, Object> getBigTable(String startTime, String endTime, String dbId, String tsName, String tbName, String tbType,
                                    int pageNum, int pageSize);

    Map<String, Object> getArchiveSpace(String startTime, String endTime, String dbId);

    Map<String, Object> getTableSpace(String startTime, String endTime, String dbId,
                                      int pageNum, int pageSize);

    Map<String, Object> getSchemaUsage(Query query);

    List<String> getAllSchema(String dbId);

    List<String> getAllTablespace(String dbId);

    List<String> getAllDefaultTablespace(String dbId);

    List<Map<String, Object>> getRedoCnt(String startTime, String endTime, String dbId,
                                         long interval, UnitType timeUnit);

    List<Map<String, Object>> getRedoDayCnt(String startTime, String endTime, String dbId);

    Map<String, Object> getRedoLineTotelLog(Integer page, Integer size, String dbId, String startTime, String endTime, Integer maxContention, String resourceName);

    Map<String, Object> getRedoSwitchLog(int page, int size, String dbId, String startTime, String endTime);

    List<Map<String, Object>> getLockWait(String startTime, String endTime, String dbId);

    Map<String, Object> getLockWaitList(String startTime, String endTime, String dbId, String content, int pageNum, int pageSize,
                                        List<Query.QueryOrder> orders);

    List<Map<String, Object>> getTop10ExecutionSql(String startTime, String endTime, String dbId);

    Map<String, Object> getSchemaPasswordExpiryDate(String type, SchemaPasswordExpiryDateParam param);

    Map<String, Object> getRMANDetail(Query query);

    Map<String, Object> getSQLTop10(Query query);

    List<Map<String, Object>> getExpDashBoard(Long eid, String dbId, LocalDateTime startTime, LocalDateTime endTime, Boolean isNewCollected);

    List<Map<String, Object>> getRMANDashboard(String dbId, String startTime, String endTime);
    PageInfo<Map<String, Object>> getRecycle(String dbId, String oracle_schema, LocalDateTime endTime, List<String> sortField, List<String> sortOrder, Integer pageNum, Integer pageSize);
}
