<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="escloud.issuekbmapperv3">
    <select id="getFailIssuesKbOverviews" resultType="com.digiwin.escloud.issueservice.t.model.issuekb.IssueKbOverview">
        SELECT ikb.*
        FROM issue_kb_overview ikb
        left JOIN issue_kb_execute_record iker ON iker.id = ikb.ikerid
        where iker.id = #{id} and ikb.statusAI != 1
    </select>

    <update id="updateIssueKbExecuteRecordStatus" >
        update issue_kb_execute_record a
        set a.status= #{status}
        where a.id = #{id}
    </update>
</mapper>