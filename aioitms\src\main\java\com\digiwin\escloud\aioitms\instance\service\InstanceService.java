package com.digiwin.escloud.aioitms.instance.service;

import com.alibaba.fastjson.JSONObject;
import com.digiwin.escloud.aiobasic.edr.model.edr.EdrDevice;
import com.digiwin.escloud.aioitms.authorize.service.IAiopsAuthorizeV2Service;
import com.digiwin.escloud.aioitms.bigdata.BigDataUtil;
import com.digiwin.escloud.aioitms.bigdata.HbasePut;
import com.digiwin.escloud.aioitms.bigdata.HbasePutList;
import com.digiwin.escloud.aioitms.collectconfig.model.CollectConfig;
import com.digiwin.escloud.aioitms.collectconfig.service.ICollectConfigService;
import com.digiwin.escloud.aioitms.common.Constants;
import com.digiwin.escloud.aioitms.common.DbHelp;
import com.digiwin.escloud.aioitms.device.model.AiopsKitDeviceDataSource;
import com.digiwin.escloud.aioitms.device.model.DeviceCollectDetail;
import com.digiwin.escloud.aioitms.device.model.DeviceCollectWarning;
import com.digiwin.escloud.aioitms.device.model.DeviceInfo;
import com.digiwin.escloud.aioitms.device.service.IDeviceV2Service;
import com.digiwin.escloud.aioitms.execParams.common.ExecParamsHelp;
import com.digiwin.escloud.aioitms.execParams.service.ExecParamsFactoryService;
import com.digiwin.escloud.aioitms.instance.dao.InstanceMapper;
import com.digiwin.escloud.aioitms.instance.model.*;
import com.digiwin.escloud.aioitms.model.authorize.AiopsAuthStatus;
import com.digiwin.escloud.aioitms.model.bigdata.UploadBigDataContext;
import com.digiwin.escloud.aioitms.model.device.AiopsKitDevice;
import com.digiwin.escloud.aioitms.model.instance.AiopsEDRInstance;
import com.digiwin.escloud.aioitms.model.instance.AiopsInstanceFixParam;
import com.digiwin.escloud.aioitms.model.instance.AiopsSmartMeterInstance;
import com.digiwin.escloud.aioitms.ruleengine.dao.RuleEngineDao;
import com.digiwin.escloud.aioitms.ruleengine.service.IRuleEngineService;
import com.digiwin.escloud.aioitms.upgrade.enums.DeviceUpgradeEnum;
import com.digiwin.escloud.aioitms.upgrade.enums.InstanceUpgradeEnum;
import com.digiwin.escloud.aioitms.upgrade.enums.UpgradeMode;
import com.digiwin.escloud.aioitms.upgrade.enums.UpgradeType;
import com.digiwin.escloud.aioitms.upgrade.model.AiopsUpgradeDcdpDetail;
import com.digiwin.escloud.aioitms.upgrade.model.AiopsUpgradeDcdpRequest;
import com.digiwin.escloud.aioitms.upgrade.service.UpgradeService;
import com.digiwin.escloud.aioitms.util.RestItemsUtil;
import com.digiwin.escloud.aioitms.util.RestUtil;
import com.digiwin.escloud.aiouser.model.tenant.AuthUsedRequest;
import com.digiwin.escloud.aiouser.model.tenant.TenantMapDTO;
import com.digiwin.escloud.aiouser.model.tenant.TenantModuleContractDetail;
import com.digiwin.escloud.aiouser.model.tenant.TenantNameListResponse;
import com.digiwin.escloud.common.controller.ParamCheckHelp;
import com.digiwin.escloud.common.feign.AioCmdbFeignClient;
import com.digiwin.escloud.common.feign.AioUserFeignClient;
import com.digiwin.escloud.common.model.ResponseBase;
import com.digiwin.escloud.common.model.ResponseCode;
import com.digiwin.escloud.common.response.BaseResponse;
import com.digiwin.escloud.common.response.PageInfo;
import com.digiwin.escloud.common.util.*;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.digiwin.escloud.aioitms.common.Constants.AiopsItemInstance.*;
import static com.digiwin.escloud.aioitms.common.Constants.BigData.INFO;
import static com.digiwin.escloud.aioitms.common.Constants.BigData.MODEL;
import static com.digiwin.escloud.aioitms.common.Constants.Device.*;
import static com.digiwin.escloud.aioitms.common.Constants.ExecParam.*;
import static com.digiwin.escloud.aioitms.common.Constants.SmartMeterConstants.ADCD_ID;
import static com.digiwin.escloud.aioitms.common.Constants.SmartMeterConstants.*;
import static com.digiwin.escloud.common.constant.AioConstant.EID;

@Service
@Slf4j
public class InstanceService implements IInstanceService, ParamCheckHelp, DbHelp, ExecParamsHelp {

    @Autowired
    ICollectConfigService collectConfigService;
    @Autowired
    @Lazy
    IDeviceV2Service deviceService;
    @Autowired
    IAiopsAuthorizeV2Service authorizeService;
    @Autowired
    IRuleEngineService ruleEngineService;
    @Autowired
    RuleEngineDao ruleEngineDao;

    @Autowired
    InstanceMapper instanceMapper;

//    @Autowired
//    AioCmdbFeignClient aioCmdbFeignClient;
    @Resource
    RestUtil restUtil;
    @Resource
    RestItemsUtil restItemsUtil;
    @Autowired
    AioUserFeignClient aioUserFeignClient;

    @Autowired
    ExecParamsFactoryService execParamsFactoryService;
    @Autowired
    UpgradeService upgradeService;
    @Autowired
    BigDataUtil bigDataUtil;
    @Autowired
    AioCmdbFeignClient aioCmdbFeignClient;

    @Override
    public List<AiopsInstance> getAiopsInstanceExistByAicList(AiopsAuthStatus aiopsAuthStatus,
                                                              Collection<AiopsItemContext> aicList) {
        return instanceMapper.selectAiopsInstanceExistByAicList(aiopsAuthStatus, aicList);
    }

    @Override
    public BaseResponse getAiopsItemList(Collection<String> aiopsItemCollection) {
        //region 参数检查

        Optional<BaseResponse> optResponse = checkParamIsEmpty(aiopsItemCollection, "aiopsItemCollection");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        //endregion

        return BaseResponse.ok(instanceMapper.selectAiopsItemByCodeList(aiopsItemCollection));
    }

    @Override
    public BaseResponse getAiopsItemList() {
        List<CollectConfig> collectConfigList = instanceMapper.selectAiopsItem(null);

        // 如果查询结果为空，直接返回一个空列表的成功响应，避免空指针
        if (collectConfigList == null || collectConfigList.isEmpty()) {
            return BaseResponse.ok(java.util.Collections.emptyList());
        }

        List<AiopsItem> resultList = collectConfigList.stream()
                .collect(Collectors.toMap(
                        CollectConfig::getAiopsItem,
                        Function.identity(),
                        (existingValue, newValue) -> existingValue
                ))
                .values()
                .stream()
                .map(i -> {
                    AiopsItem aiopsItem = new AiopsItem();
                    aiopsItem.setName(i.getAiopsItemName());
                    aiopsItem.setCode(i.getAiopsItem());
                    return aiopsItem;
                })
                .sorted(Comparator.comparing(AiopsItem::getCode))
                .collect(Collectors.toList());

        return BaseResponse.ok(resultList);
    }

    @Override
    public BaseResponse createAiopsInstanceByAicList(Long eid, List<AiopsItemContext> aicList) {
        if (CollectionUtils.isEmpty(aicList)) {
            return BaseResponse.ok(0);
        }
        //批量保存运维实例
        BaseResponse response = batchSaveAiopsInstanceByAicList(eid, aicList); //这边会处理执行参数
        if (!response.checkIsSuccess()) {
            return response;
        }
        //构建设备运维实例映射(如果有的话) 还处理了adcd
        //處理運維項所配的收集項
        response = deviceService.batchSaveDeviceInstanceMappingByAicList(aicList);
        if (!response.checkIsSuccess()) {
            return response;
        }
        //处理设备收集项明细只映射在实例AiId的内容(如果有的话)
        response = deviceService.processAdcdOnlyMappingAiId(aicList);
        if (!response.checkIsSuccess()) {
            return response;
        }
        //处理设备收集项明细执行参数(如果有的话)
        return deviceService.batchFixAdcdExecParamsContent(aicList);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public BaseResponse batchSaveAiopsInstanceByAicList(Long eid, Collection<AiopsItemContext> aicList) {
        //region 参数检查

        Optional<BaseResponse> optResponse = checkParamIsEmpty(eid, "eid");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        optResponse = checkParamIsEmpty(aicList, "aicList");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        //endregion

        List<AiopsUpgradeDcdpDetail> upgradeDcdpDetailList = new ArrayList<>();

        //先产生透过业务主键分组的字典加快查找
        Map<String, List<AiopsItemContext>> aicListMap = getSamcdIdAiopsItemIdGroupMap(aicList);

        //如果aicList内有指定aiId，表示要针对该aiId更新samcdId及aiopsItemId
        List<AiopsItemContext> needUpdateSamcdIdAndAiopsItemIdAicList = aicList.stream()
                .filter(x -> !LongUtil.isEmpty(x.getAiId()))
                .peek(x -> {
                    //设置需要更新设备收集项执行参数内容
                    x.setNeedUpdateDeviceCollectExecParamsContent(true);
                })
                .filter(x -> !BooleanUtils.toBoolean(x.getNeedSeparateAiopsInstance()))
                .collect(Collectors.toList());

        if (!CollectionUtils.isEmpty(needUpdateSamcdIdAndAiopsItemIdAicList)) {

            List<AiopsInstance> existAiList = getAiopsInstanceExistByAicList(null,
                    needUpdateSamcdIdAndAiopsItemIdAicList);

            if (!CollectionUtils.isEmpty(existAiList)) {
                //有查询到表示要调整的实例已经存在，需要进行迁移
                //创建出新旧aiId字典
                List<Long> oldAiIdList = new ArrayList<>(existAiList.size());
                List<Map<String, Object>> needUpdateAiIdMap = existAiList.stream().map(x -> {
                    String key = x.getBusinessKey();
                    List<AiopsItemContext> tempAicList = aicListMap.get(key);
                    if (CollectionUtils.isEmpty(tempAicList)) {
                        return null;
                    }
                    AiopsItemContext aic = tempAicList.get(0);
                    if (aic == null) {
                        return null;
                    }
                    //取第一个即可
                    Long oldAiId = aic.getAiId();
                    Optional<Map<String, Object>> optMap = createOldAiIdNewAiIdMap(oldAiId, x.getId(),
                            aic.getSourceDeviceId());
                    if (optMap.isPresent()) {
                        oldAiIdList.add(oldAiId);
                        return optMap.get();
                    }
                    return null;
                }).filter(Objects::nonNull).collect(Collectors.toList());

                fixOldAiIdToNewAiId(oldAiIdList, needUpdateAiIdMap, upgradeDcdpDetailList);
            }
            //考虑复杂性，这里不去做已经处理过的needUpdateSamcdIdAndAiopsItemIdAicList项目移除，顶多就是更新不到东西而已
            instanceMapper.batchUpdateAiopsInstanceSamcdIdAndAiopsItemIdByAicList(
                    needUpdateSamcdIdAndAiopsItemIdAicList);

            upgradeDcdpDetailList.addAll(needUpdateSamcdIdAndAiopsItemIdAicList.stream().map(x -> {
                Map<String, Object> map = new HashMap<>();
                map.put("id", x.getAiId());
                map.put("aiopsItemType", x.getAiopsItemType());
                map.put("aiopsItem", x.getAiopsItem());
                map.put("aiopsItemId", x.getAiopsItemId());
                AiopsUpgradeDcdpDetail detail = new AiopsUpgradeDcdpDetail(UpgradeType.UPDATE,
                        InstanceUpgradeEnum.INSTANCE.getTableName(), map);
                Set<String> targetColumnSet = detail.getTargetColumnSet();
                targetColumnSet.add("id");
                targetColumnSet.add("aiopsItemType");
                targetColumnSet.add("aiopsItem");
                targetColumnSet.add("aiopsItemId");
                detail.setNeedSaveOperationLog(true);
                Map<String, Object> operationInfoMap = OperationUtil.getHeaderOperationInfo();
                operationInfoMap.put("operationObjectClassification", "instance");
                operationInfoMap.put("operationObject", "instance");
                operationInfoMap.put("operationBehavior", "updateinstanceaiopsitem");
                detail.setOperationInfoMap(operationInfoMap);
                return detail;
            }).collect(Collectors.toList()));
        }

        List<AiopsInstance> existAiList = getAiopsInstanceExistByAicList(null, aicList);
        Map<String, List<AiopsInstance>> execParamsAiListMap = new HashMap<>(aicList.size());
        Map<Long, List<AiopsItemContext>> aiIdAicListMap = new HashMap<>(aicList.size());
        int effectRowCount = 0;
        List<Map<String, Object>> needUpdateAiIdMap = new ArrayList<>(aicListMap.size());
        //处理更新的部分
        if (!CollectionUtils.isEmpty(existAiList)) {
            //表示可能存在更新数据
            List<Long> needRecalculateAuthCountTmcdIdList = new ArrayList<>();
            List<String> needRecalculateAuthCountAiopsItemList = new ArrayList<>();
            //找出要更新的数据
            List<AiopsInstance> needUpdateList = existAiList.stream().filter(x -> {
                String key = String.join("_", LongUtil.safeToString(x.getSamcdId()), x.getAiopsItemId());
                List<AiopsItemContext> tempAicList = aicListMap.get(key);
                if (CollectionUtils.isEmpty(tempAicList)) {
                    //运维实例在本次运维项目上下文不存在的属不处理的数据，过滤掉
                    return false;
                }
                //有找到，就从字典里面移除
                aicListMap.remove(key);
                Long aiId = x.getId();

                //只取第一个做后续判断
                AiopsItemContext aic = tempAicList.get(0);
                String sourceDeviceId = aic.getSourceDeviceId();
                if (BooleanUtils.toBoolean(aic.getNeedSeparateAiopsInstance())) {
                    createOldAiIdNewAiIdMap(aic.getAiId(), aiId, sourceDeviceId)
                            .ifPresent(needUpdateAiIdMap::add);
                }
                x.setRelateDeviceId(sourceDeviceId);

                //所有的aic都要填充aiId
                tempAicList.forEach(y -> y.setAiId(aiId));
                boolean needUpdate = false;
                needUpdate |= checkAndSetAiopsInstanceExecParams(tempAicList, x, execParamsAiListMap, aiIdAicListMap);
                boolean changedUnauth = false;
                if (aic.getChangeToUnauth()) {
                    //若要异动为未授权，就判断一下当前运维实例是否为已授权，已授权就将其改为未授权
                    if (x.getAiopsAuthStatus() == AiopsAuthStatus.AUTHED) {
                        x.setAiopsAuthStatus(AiopsAuthStatus.UNAUTH);
                        needUpdate = true;
                        changedUnauth = true;
                    }
                }
                if (!LongUtil.isEmpty(eid) && !eid.equals(x.getEid())) {
                    x.setEid(eid);
                    needUpdate = true;
                }

                // 是否為獨立授權
                Boolean independentAuth = Optional.ofNullable(aic.getIndependentAuth())
                        .orElse(false);
                if (independentAuth) {
                    x.setIndependentAuth(independentAuth);
                    needUpdate = true;
                }

                //针对已存在的运维实例修正tmcdId
                TenantModuleContractDetail tmcd = aic.getSourceTmcd();
                if (tmcd == null) {
                    //表示没有找到合约，这里将当前实例的tmcdId设置为0，并且需要更新
                    x.setTmcdId(0L);
                    return true;
                }
                Long tmcdId = tmcd.getId();
                if (LongUtil.isEmpty(tmcdId)) {
                    //表示没有找到合约，这里将当前实例的tmcdId设置为0，并且需要更新
                    x.setTmcdId(0L);
                    return true;
                }
                if (changedUnauth) {
                    needRecalculateAuthCountTmcdIdList.add(tmcdId);
                    needRecalculateAuthCountAiopsItemList.add(x.getAiopsItem());
                }
                if (!tmcdId.equals(x.getTmcdId())) {
                    x.setTmcdId(tmcdId);
                    return true;
                }
                return needUpdate;
            }).collect(Collectors.toList());

            if (!CollectionUtils.isEmpty(needUpdateList)) {
                effectRowCount = instanceMapper.batchUpdateAiopsInstance(needUpdateList);
                upgradeDcdpDetailList.addAll(needUpdateList.stream().map(x -> {
                    AiopsUpgradeDcdpDetail detail = new AiopsUpgradeDcdpDetail(UpgradeType.UPDATE,
                            InstanceUpgradeEnum.INSTANCE.getTableName(), BeanUtil.object2Map(x));
                    Map<String, Object> paramsMap = detail.getParamsMap();
                    paramsMap.put(EID, x.getEid());
                    String deviceId = x.getRelateDeviceId();
                    paramsMap.put(DEVICE_ID, deviceId);
                    paramsMap.put("checkDeviceId", deviceId);
                    detail.setNeedSaveOperationLog(true);
                    Map<String, Object> operationInfoMap = OperationUtil.getHeaderOperationInfo();
                    operationInfoMap.put("operationObjectClassification", "instance");
                    operationInfoMap.put("operationObject", "instance");
                    operationInfoMap.put("operationBehavior", "updateinstanceinfo");
                    detail.setOperationInfoMap(operationInfoMap);
                    return detail;
                }).collect(Collectors.toList()));
                //实例发生改变，规则引擎也要调整(主要是eid还有授权状态)
                ruleEngineService.modifyRuleEngineByAiList(needUpdateList);
            }

            fixOldAiIdToNewAiId(null, needUpdateAiIdMap, upgradeDcdpDetailList);

            if (!CollectionUtils.isEmpty(needRecalculateAuthCountTmcdIdList)) {
                BaseResponse<Map<Long, Integer>> tmcdIdUsedCountMapResponse =
                        getAuthedCountMapByTmcdIdList(eid, true, needRecalculateAuthCountTmcdIdList);
                if (!tmcdIdUsedCountMapResponse.checkIsSuccess()) {
                    return tmcdIdUsedCountMapResponse;
                }
                BaseResponse response = aioUserFeignClient.recalculateTmcdUsedCountByAuthUsedRequest(RequestUtil.getHeaderSid(),
                        new AuthUsedRequest(eid, false, needRecalculateAuthCountAiopsItemList,
                                tmcdIdUsedCountMapResponse.getData()));
                if (!response.checkIsSuccess()) {
                    TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                    return response;
                }
            }
        }

        if (CollectionUtils.isEmpty(aicListMap)) {
            //处理一下执行参数
            processAiopsInstanceExecParams(execParamsAiListMap, aiIdAicListMap);
            BaseResponse response = executeUpgradeToDcdp(upgradeDcdpDetailList);
            if (!response.checkIsSuccess()) {
                TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                return response;
            }
            //没有需要处理的内容，返回成功
            return BaseResponse.ok(effectRowCount);
        }

        //处理新增的部分
        needUpdateAiIdMap.clear();

        List<AiopsInstance> aiList = aicListMap.entrySet().stream().map(x -> {
            List<AiopsItemContext> tempAicList = x.getValue();
            if (CollectionUtils.isEmpty(tempAicList)) {
                return null;
            }
            //只取第一个做后续判断
            AiopsItemContext aic = tempAicList.get(0);
            Long samcdId = aic.getSamcdId();
            String aiopsItemId = aic.getAiopsItemId();

            TenantModuleContractDetail tmcd = aic.getSourceTmcd();
            Long tmcdId;
            if (Objects.isNull(tmcd)) {
                tmcdId = 0L;
            } else {
                tmcdId = tmcd.getId();
            }
            AiopsInstance ai = new AiopsInstance(eid, tmcdId, samcdId,
                    aic.getAiopsItemType(), aic.getAiopsItem(), aiopsItemId);
            Long specifyCreateAiId = aic.getSpecifyCreateAiId();
            if (LongUtil.isNotEmpty(specifyCreateAiId)) {
                ai.setId(specifyCreateAiId);
            }
            Long aiId = ai.getId();
            String sourceDeviceId = aic.getSourceDeviceId();
            if (BooleanUtils.toBoolean(aic.getNeedSeparateAiopsInstance())) {
                createOldAiIdNewAiIdMap(aic.getAiId(), aiId, sourceDeviceId)
                        .ifPresent(needUpdateAiIdMap::add);
            }
            ai.setRelateDeviceId(sourceDeviceId);

            //所有的aic都要填充aiId
            tempAicList.forEach(y -> y.setAiId(aiId));
            ai.setExecParamsModelCode(aic.getExecParamsModelCode());
            //holdAuth必须是明确的，因此这里不做BooleanUtils.toBoolean
            if (aic.getHoldAuth()) {
                ai.setAiopsAuthStatus(AiopsAuthStatus.UNAUTH);
            } else {
                //不占用授权，自动填充无须授权
                ai.setAiopsAuthStatus(AiopsAuthStatus.NONE);
            }
            // 10766【後端API】【Athena稳态自动更新】稳态自动更新的授权起讫同步逻辑调整，支持稳态根据敏态的合约管控自动更新
            // 是否為獨立授權
            Boolean independentAuth = Optional.ofNullable(aic.getIndependentAuth())
                    .orElse(false);
            ai.setIndependentAuth(independentAuth);

            checkAndSetAiopsInstanceExecParams(tempAicList, ai, execParamsAiListMap, aiIdAicListMap);
            return ai;
        }).filter(Objects::nonNull).collect(Collectors.toList());

        if (!CollectionUtils.isEmpty(aiList)) {
            //批量保存运维实例
            effectRowCount += instanceMapper.batchInsertAiopsInstance(aiList);
            //TODO: 实例添加，不立刻过去数采，值到添加到设备的映射后，在新增到数采?
            upgradeDcdpDetailList.addAll(aiList.stream().map(x -> {
                AiopsUpgradeDcdpDetail detail = new AiopsUpgradeDcdpDetail(InstanceUpgradeEnum.INSTANCE.getTableName(),
                        BeanUtil.object2Map(x));
                Map<String, Object> paramsMap = detail.getParamsMap();
                paramsMap.put(EID, x.getEid());
                String deviceId = x.getRelateDeviceId();
                paramsMap.put(DEVICE_ID, deviceId);
                paramsMap.put("checkDeviceId", deviceId);
                detail.setNeedSaveOperationLog(true);
                Map<String, Object> operationInfoMap = OperationUtil.getHeaderOperationInfo();
                operationInfoMap.put("operationObjectClassification", "instance");
                operationInfoMap.put("operationObject", "instance");
                operationInfoMap.put("operationBehavior", "addinstance");
                detail.setOperationInfoMap(operationInfoMap);
                return detail;
            }).collect(Collectors.toList()));
        }

        fixOldAiIdToNewAiId(null, needUpdateAiIdMap, upgradeDcdpDetailList);

        processAiopsInstanceExecParams(execParamsAiListMap, aiIdAicListMap);
        BaseResponse response = executeUpgradeToDcdp(upgradeDcdpDetailList);
        if (!response.checkIsSuccess()) {
            return response;
        }

        return BaseResponse.ok(effectRowCount);
    }

    private Map<String, List<AiopsItemContext>> getSamcdIdAiopsItemIdGroupMap(Collection<AiopsItemContext> aicList) {
        //先产生透过业务主键分组的字典加快查找
        return aicList.stream().filter(Objects::nonNull).collect(Collectors.groupingBy(
                AiopsItemContext::getAiopsInstanceBusinessKey,
                Collectors.mapping(x -> x, Collectors.toList())));
    }

    private Optional<Map<String, Object>> createOldAiIdNewAiIdMap(Long oldAiId, Long newAiId, String deviceId) {
        if (LongUtil.isEmpty(oldAiId)) {
            return Optional.empty();
        }
        if (oldAiId.equals(newAiId)) {
            return Optional.empty();
        }
        Map<String, Object> map = new HashMap<>(3);
        map.put("oldAiId", oldAiId);
        map.put("newAiId", newAiId);
        map.put("deviceId", deviceId);
        return Optional.of(map);
    }

    private boolean checkAndSetAiopsInstanceExecParams(List<AiopsItemContext> aicList, AiopsInstance ai,
                                                       Map<String, List<AiopsInstance>> execParamsAiListMap,
                                                       Map<Long, List<AiopsItemContext>> aiIdAicListMap) {
        if (CollectionUtils.isEmpty(aicList) || ai == null || execParamsAiListMap == null || aiIdAicListMap == null) {
            return false;
        }
        //只取第一个做后续判断
        AiopsItemContext aic = aicList.get(0);
        String execParamsModelCode = aic.getExecParamsModelCode();
        if (StringUtils.isBlank(execParamsModelCode)) {
            return false;
        }
        //表示有执行参数
        String content = aic.getOriExecParamsContent();
        if (StringUtils.isBlank(content)) {
            //如果没有执行参数内容，就后面进行生成
            ai.setExecParamsModelCode(execParamsModelCode);
            if (BooleanUtils.toBoolean(aic.getNeedFixExecParamsContent())) {
                //需要修正执行参数内容才添加
                List<AiopsInstance> aiopsInstanceList = execParamsAiListMap
                        .computeIfAbsent(execParamsModelCode, k -> new ArrayList<>());
                aiopsInstanceList.add(ai);
                aiIdAicListMap.put(ai.getId(), aicList);
            }
        } else {
            //有的话直接填充进去
            ai.setExecParamsModelCode(execParamsModelCode);
            ai.setOriExecParamsContent(content);
            return true;
        }
        return false;
    }

    private void processAiopsInstanceExecParams(Map<String, List<AiopsInstance>> execParamsAiListMap,
                                                Map<Long, List<AiopsItemContext>> aiIdAicListMap) {
        if (CollectionUtils.isEmpty(execParamsAiListMap)) {
            return;
        }
        //尝试获取并产生执行参数
//        BaseResponse response = aioCmdbFeignClient.getModelJson(execParamsAiListMap.keySet());
        BaseResponse response = restUtil.getModelJson(execParamsAiListMap.keySet());
        Optional<Map<String, Object>> optResponse = response.checkAndGetCurrentData();
        if (!optResponse.isPresent()) {
            return;
        }
        Map<String, Object> modelsStruct = optResponse.get();
        Collection<AiopsInstance> needUpdateList = modelsStruct.entrySet().stream().flatMap(x -> {
            String key = x.getKey();
            Optional<Map<String, Object>> optModelStruct = MapUtil.getMap(modelsStruct, key);
            if (!optModelStruct.isPresent()) {
                return null;
            }
            List<AiopsInstance> aiList = execParamsAiListMap.get(key);
            if (CollectionUtils.isEmpty(aiList)) {
                return null;
            }
            return aiList.stream().map(ai -> {
                Map<String, Object> paramsMap = new HashMap<>(1);
                paramsMap.put("aiopsItemId", ai.getAiopsItemId());
                Optional<String> optContent = execParamsFactoryService.getExecParamContent(key,
                        optModelStruct.get(), paramsMap);
                if (!optContent.isPresent()) {
                    return null;
                }
                String content = optContent.get();
                ai.setExecParamsModelCode(key);
                ai.setOriExecParamsContent(content);
                List<AiopsItemContext> aicList = aiIdAicListMap.get(ai.getId());
                aicList.forEach(y -> y.setOriExecParamsContent(content));
                return ai;
            }).filter(Objects::nonNull);
        }).filter(Objects::nonNull).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(needUpdateList)) {
            return;
        }
        //如果有修复的内容，这里进行批量保存
        instanceMapper.batchUpdateAiopsInstanceOriExecParamsContent(needUpdateList);
    }

    private void fixOldAiIdToNewAiId(List<Long> oldAiIdList, List<Map<String, Object>> needUpdateAiIdMap,
                                     List<AiopsUpgradeDcdpDetail> upgradeDcdpDetailList) {
        if (CollectionUtils.isEmpty(needUpdateAiIdMap)) {
            return;
        }
        //1.将旧的aiId移除(如果有旧的aiId的话)
        //20241119:代码稽核 !CollectionUtils.isEmpty(oldAiIdList) 改为oldAiIdList != null && oldAiIdList.size() > 0
        if (oldAiIdList != null && oldAiIdList.size() > 0) {
            upgradeDcdpDetailList.addAll(getInstanceUpgradeToDcdpDetail(oldAiIdList.stream().map(x -> {
                AiopsUpgradeDcdpRequest.InstanceUpgrade instanceUpgrade =
                        new AiopsUpgradeDcdpRequest.InstanceUpgrade(x);
                instanceUpgrade.setUpgradeType(UpgradeType.DELETE);
                instanceUpgrade.setNeedSaveOperationLog(true);
                Map<String, Object> operationInfoMap = instanceUpgrade.getOperationInfoMap();
                operationInfoMap.put("operationObjectClassification", "instance");
                operationInfoMap.put("operationObject", "instance");
                operationInfoMap.put("operationBehavior", "deleteinstance");
                return instanceUpgrade;
            }).collect(Collectors.toList())));
            instanceMapper.deleteAiopsInstanceByAiIdList(oldAiIdList);
        }
        //2.调整设备实例映射将旧的aiId更成新的aiId
        deviceService.batchModifyDeviceInstanceMappingAiIdByMapList(needUpdateAiIdMap);
        //TODO: 目标是将adim的旧aiId更新为新的aiId，理论上走完上述更新逻辑，adim表旧aiId不会存在，因此直接查新的aiId进行更新
        upgradeDcdpDetailList.addAll(getDeviceUpgradeToDcdpDetail(needUpdateAiIdMap.stream().map(x -> {
            String deviceId = Objects.toString(x.get("deviceId"), "");
            AiopsUpgradeDcdpRequest.DeviceUpgrade deviceUpgrade =
                    new AiopsUpgradeDcdpRequest.DeviceUpgrade(deviceId, DeviceUpgradeEnum.DEVICE_INSTANCE_MAPPING,
                            UpgradeType.UPDATE);
            deviceUpgrade.setContainRelateInstance(false);
            deviceUpgrade.getOtherConditionMap().put("adim.aiId", x.get("newAiId"));
            deviceUpgrade.setNeedSaveOperationLog(true);
            Map<String, Object> operationInfoMap = deviceUpgrade.getOperationInfoMap();
            operationInfoMap.put("operationObjectClassification", "device");
            operationInfoMap.put("operationObject", "device");
            operationInfoMap.put("operationBehavior", "devicechangeinstancemapping");
            return deviceUpgrade;
        }).collect(Collectors.toList())));
        //3.调整设备收集项明细将旧的aiId更新成新的aiId
        deviceService.batchModifyDeviceCollectDetailAiIdByMapList(needUpdateAiIdMap);
        //TODO: 目标是将adcd的旧aiId更新为新的aiId，因此直接查新的aiId进行更新
        upgradeDcdpDetailList.addAll(getDeviceUpgradeToDcdpDetail(needUpdateAiIdMap.stream().map(x -> {
            String deviceId = Objects.toString(x.get("deviceId"), "");
            AiopsUpgradeDcdpRequest.DeviceUpgrade deviceUpgrade =
                    new AiopsUpgradeDcdpRequest.DeviceUpgrade(deviceId, DeviceUpgradeEnum.DEVICE_COLLECT_DETAIL,
                            UpgradeType.UPDATE);
            deviceUpgrade.getOtherConditionMap().put("adcd.aiId", x.get("newAiId"));
            deviceUpgrade.setNeedSaveOperationLog(true);
            Map<String, Object> operationInfoMap = deviceUpgrade.getOperationInfoMap();
            operationInfoMap.put("operationObjectClassification", "instance");
            operationInfoMap.put("operationObject", "instance");
            operationInfoMap.put("operationBehavior", "updatedevicecollectconfiginstance");
            return deviceUpgrade;
        }).collect(Collectors.toList())));
        //4.调整规则引擎实例
        ruleEngineService.modifyRuleEngineAiIdByMapList(needUpdateAiIdMap);
    }

    private List<AiopsUpgradeDcdpDetail> getDeviceUpgradeToDcdpDetail(
            List<AiopsUpgradeDcdpRequest.DeviceUpgrade> deviceUpgradeList) {
        return getUpgradeToDcdpDetail(deviceUpgradeList, null);
    }

    private List<AiopsUpgradeDcdpDetail> getInstanceUpgradeToDcdpDetail(
            List<AiopsUpgradeDcdpRequest.InstanceUpgrade> instanceUpgradeList) {
        return getUpgradeToDcdpDetail(null, instanceUpgradeList);
    }

    private List<AiopsUpgradeDcdpDetail> getUpgradeToDcdpDetail(
            List<AiopsUpgradeDcdpRequest.DeviceUpgrade> deviceUpgradeList,
            List<AiopsUpgradeDcdpRequest.InstanceUpgrade> instanceUpgradeList) {
        if (CollectionUtils.isEmpty(deviceUpgradeList) && CollectionUtils.isEmpty(instanceUpgradeList)) {
            return new ArrayList<>(0);
        }
        AiopsUpgradeDcdpRequest request = new AiopsUpgradeDcdpRequest();
        if (CollectionUtil.isNotEmpty(deviceUpgradeList)) {
            request.getDeviceUpgradeList().addAll(deviceUpgradeList);
        }
        if (CollectionUtil.isNotEmpty(instanceUpgradeList)) {
            request.getInstanceUpgradeList().addAll(instanceUpgradeList);
        }
        return upgradeService.getUpgradeToDcdpDetail(request);
    }

    private BaseResponse executeUpgradeToDcdp(List<AiopsUpgradeDcdpDetail> upgradeDcdpDetailList) {
        return upgradeService.upgradeToDcdpByDetail(UpgradeMode.SYNC, upgradeDcdpDetailList);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public BaseResponse batchSaveAiopsInstance(Collection<AiopsInstance> aiopsInstanceList) {
        //region 参数检查

        Optional<BaseResponse> optResponse = checkParamIsEmpty(aiopsInstanceList, "aiopsInstanceList");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        //endregion

        aiopsInstanceList.stream().filter(Objects::nonNull).forEach(x -> {
            //修正基础字段数据
            if (LongUtil.isEmpty(x.getId())) {
                x.setId(SnowFlake.getInstance().newId());
            }
            if (Objects.isNull(x.getAiopsAuthStatus())) {
                //没有设定授权，就认定为未授权
                x.setAiopsAuthStatus(AiopsAuthStatus.UNAUTH);
            }
        });
        if (CollectionUtils.isEmpty(aiopsInstanceList)) {
            BaseResponse.ok(0);
        }
        //创建运维实例并返回
        return BaseResponse.ok(instanceMapper.batchInsertOrUpdateAiopsInstance(aiopsInstanceList));
    }

    @Override
    public BaseResponse modifyAiopsInstanceAuthStatusByAicList(AiopsAuthStatus aiopsAuthStatus,
                                                               Collection<AiopsItemContext> aicList) {
        //region 参数检查

        Optional<BaseResponse> optResponse = checkParamIsEmpty(aicList, "aicList");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        optResponse = checkParamIsEmpty(aiopsAuthStatus, "aiopsAuthStatus");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        //endregion

        Map<String, AiopsItemContext> aicMap = aicList.stream().filter(x -> x != null)
                .collect(Collectors.toMap(x -> x.getAiopsItemId(), x -> x, (x, y) -> y));

        List<AiopsInstance> aiList = instanceMapper.selectAiopsInstanceByAiopsItemIdList(aicMap.keySet());
        if (CollectionUtils.isEmpty(aiList)) {
            return BaseResponse.ok(0);
        }
        aiList = aiList.stream().filter(x -> {
            String key = x.getAiopsItemId();
            AiopsItemContext aic = aicMap.get(key);
            if (aic == null) {
                return false;
            }
            //1.已授权->未授权 recoverableAuth变为0 2.未授权 ->已授权 recoverableAuth变为0
            x.setRecoverableAuth(false);

            AiopsAuthStatus innerAuthStatus;
            //holdAuth必须是明确的，因此这里不做BooleanUtils.toBoolean
            if (aic.getHoldAuth()) {
                //占用授权才会被授权状态影响
                innerAuthStatus = AiopsAuthStatus.NONE.isSame(aiopsAuthStatus) ?
                        AiopsAuthStatus.UNAUTH : aiopsAuthStatus;
            } else {
                //不占用授权，若是作废为作废，其他为无须授权
                innerAuthStatus = AiopsAuthStatus.INVALID.isSame(aiopsAuthStatus) ?
                        AiopsAuthStatus.INVALID : AiopsAuthStatus.NONE;
            }
            if (x.getAiopsAuthStatus() == innerAuthStatus) {
                return false;
            }
            x.setAiopsAuthStatus(innerAuthStatus);
            return true;
        }).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(aiList)) {
            return BaseResponse.ok(0);
        }

        return BaseResponse.ok(instanceMapper.batchUpdateAiopsInstance(aiList));
    }

    @Override
    public BaseResponse getAuthedCountByTmcdId(Long tmcdId, Boolean isContainHoldAuth) {
        //region 参数检查

        Optional<BaseResponse> optResponse = checkParamIsEmpty(tmcdId, "tmcdId");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        //endregion

        return BaseResponse.ok(instanceMapper.selectAuthedCountByTmcdId(tmcdId,
                BooleanUtils.toBoolean(isContainHoldAuth)));
    }

    @Override
    public BaseResponse getAuthedCountByAiopsItemList(Long eid, List<String> aiopsItemList) {
        //region 参数检查

        Optional<BaseResponse> optResponse = checkParamIsEmpty(eid, "eid");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        optResponse = checkParamIsEmpty(aiopsItemList, "aiopsItemList");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        //endregion

        return BaseResponse.ok(processTmcdIdAuthedCountToMap(
                instanceMapper.selectAuthedCountByAiopsItemList(eid, aiopsItemList)));
    }

    @Override
    public BaseResponse getStatisticsValidAiopsItemListByEid(Long eid) {
        //region 参数检查

        Optional<BaseResponse> optResponse = checkParamIsEmpty(eid, "eid");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        //endregion

        List<AiopsItemStatistics> aiopsItemStatisticsList = instanceMapper.selectStatisticsValidAiopsItemListByEid(eid);
        if (CollectionUtils.isEmpty(aiopsItemStatisticsList)) {
            return BaseResponse.ok(aiopsItemStatisticsList);
        }
        //串查是否有合併項
        Map<Long, List<AiopsItemStatistics>> samcdIdGroupMap = aiopsItemStatisticsList.stream()
                .filter(Objects::nonNull).filter(x -> Objects.nonNull(x.getSamcdId()))
                .collect(Collectors.groupingBy(AiopsItemStatistics::getSamcdId));
        BaseResponse<List<Map<String, Object>>> response =
                aioUserFeignClient.getMergeStatisticsDetailBySamcdIdList(samcdIdGroupMap.keySet());
        List<Map<String, Object>> mapList;
        if (!response.checkIsSuccess() || CollectionUtils.isEmpty(mapList = response.getData())) {
            //如果调用失败，或者没有合并项，直接返回原始结果
            return BaseResponse.ok(aiopsItemStatisticsList);
        }
        Map<Long, AiopsItemStatistics> samcIdAisMap = new HashMap<>(mapList.size());
        mapList.forEach(x -> {
            Long samcdId = LongUtil.objectToLong(x.get("samcdId"));
            if (LongUtil.isEmpty(samcdId)) {
                return;
            }
            Long samcId = LongUtil.objectToLong(x.get("samcId"));
            if (LongUtil.isEmpty(samcdId)) {
                return;
            }
            List<AiopsItemStatistics> aisList = samcdIdGroupMap.remove(samcdId);
            int total = aisList.stream().mapToInt(AiopsItemStatistics::getTotal).sum();
            boolean hasDevices = aisList.stream().anyMatch(AiopsItemStatistics::getHasDevices);
            AiopsItemStatistics ais = samcIdAisMap.get(samcId);
            if (Objects.isNull(ais)) {
                ais = new AiopsItemStatistics();
                ais.setAiopsItem(Objects.toString(x.get("classCode"), ""));
                ais.setTotal(total);
                ais.setHasDevices(hasDevices);
                samcIdAisMap.put(samcId, ais);
                return;
            }
            ais.setHasDevices(ais.getHasDevices() || hasDevices);
            ais.setTotal(ais.getTotal() + total);
        });
        return BaseResponse.ok(Stream.concat(samcdIdGroupMap.values().stream().flatMap(Collection::stream),
                samcIdAisMap.values().stream()).collect(Collectors.toList()));
    }

    @Override
    public BaseResponse getBatchAuthList(BatchAuthListRequest batchAuthListRequest) {
        //region 参数检查

        Optional<BaseResponse> optResponse = checkParamIsEmpty(batchAuthListRequest, "batchAuthListRequest");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        optResponse = checkParamIsEmpty(batchAuthListRequest.getEid(), "batchAuthListRequest.eid");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        //endregion

        Map<String, Object> map = BeanUtil.object2Map(batchAuthListRequest);

        int pageNum = batchAuthListRequest.getPageNum();
        int pageSize = batchAuthListRequest.getPageSize();
        pageNum = pageNum == 0 ? DEFAULT_PAGE_NO : pageNum;
        pageSize = pageSize == 0 ? DEFAULT_PAGE_SIZE : pageSize;
        Page page = PageHelper.startPage(pageNum, pageSize);
        instanceMapper.selectBatchAuthList(map);
        PageInfo<DeviceInfo> pageInfo = new PageInfo<>(page);

        return BaseResponse.ok(pageInfo);
    }

    @Override
    public BaseResponse getSNMPInstanceList(SNMPRequest snmpRequest) {
        //region 参数检查

        Optional<BaseResponse> optResponse = checkParamIsEmpty(snmpRequest, "snmpRequest");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        Long eid = snmpRequest.getEid();
        optResponse = checkParamIsEmpty(eid, "snmpRequest.eid");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        Boolean needPaging = BooleanUtils.toBoolean(snmpRequest.getNeedPaging());

        //endregion

        Map<String, Object> map = BeanUtil.object2Map(snmpRequest);
        processAiopsAuthStatusListCondition(snmpRequest.getAiopsAuthStatusList(), map);
        return getSNMPListCore(needPaging, snmpRequest.getPageNum(), snmpRequest.getPageSize(), map);
    }

    private void processAiopsAuthStatusListCondition(List<String> aiopsAuthStatusList, Map<String, Object> map) {
        if (!CollectionUtils.isEmpty(aiopsAuthStatusList)) {
            aiopsAuthStatusList = aiopsAuthStatusList.stream().filter(StringUtils::isNotBlank)
                    .map(AiopsAuthStatus::getStatusByString)
                    .filter(Optional::isPresent)
                    .map(x -> x.get().name())
                    .collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(aiopsAuthStatusList)) {
                map.put("aiopsAuthStatusList", aiopsAuthStatusList);
            } else {
                map.remove("aiopsAuthStatusList");
            }
        }
    }

    private BaseResponse getSNMPListCore(Boolean needPaging, Integer pageNum, Integer pageSize, Map<String, Object> map) {
        //查询
        if (BooleanUtils.toBoolean(needPaging)) {
            pageNum = IntegerUtil.isEmpty(pageNum) ? DEFAULT_PAGE_NO : pageNum;
            pageSize = IntegerUtil.isEmpty(pageSize) ? DEFAULT_PAGE_SIZE : pageSize;
            Page page = PageHelper.startPage(pageNum, pageSize);
            instanceMapper.selectAiopsSnmpInstanceAgentDevice(map);
            PageInfo<SNMPInfo> pageInfo = new PageInfo<>(page);
            if (pageInfo != null) {
                pageInfo.setList(addSNMPStatusToList(pageInfo.getList(), map.get("eid") != null ? map.get("eid").toString() : null));
            }
            return BaseResponse.ok(pageInfo);
        }
        List<SNMPInfo> list = instanceMapper.selectAiopsSnmpInstanceAgentDevice(map);
        return BaseResponse.ok(addSNMPStatusToList(list, map.get("eid") != null ? map.get("eid").toString() : null));
    }

    public List<SNMPInfo> addSNMPStatusToList(List<SNMPInfo> list, String eid) {
        try {
            if (!CollectionUtils.isEmpty(list)) {
                String aiIds = list.stream().filter(k -> k.getAiopsInstanceId() != null && k.getAiopsInstanceId() > 0L).map(o -> String.valueOf(o.getAiopsInstanceId())).collect(Collectors.joining(","));
                if (StringUtils.isEmpty(aiIds)) {
                    list.forEach(k -> {
                        k.setSnmpStatus("-");
                        k.setSecond("-");
                    });
                } else {
                    StringBuffer sf = new StringBuffer();
                    //3天内才算在线
                    sf.append("SELECT a.aiId,TIMESTAMPDIFF(SECOND, from_unixtime(flumeTimestamp/1000), NOW()) second FROM servicecloud.OfflineCheckCollected_sr_primary a ");
                    sf.append(" where a.aiId in (").append(aiIds).append(")");
                    if (eid != null) {
                        sf.append(" and a.eid = '").append(eid).append("'");
                    }
                    sf.append(" order by a.collectedTime desc");
                    List<Map<String, Object>> dataList = bigDataUtil.srQuery(sf.toString());
                    if (!CollectionUtils.isEmpty(dataList)) {
                        list.forEach(k -> {
                            k.setSnmpStatus(dealSnmpStatus(dataList, k.getAiopsInstanceId()));
                            k.setSecond(dealSnmpSecond(dataList, k.getAiopsInstanceId()));
                        });
                    } else {
                        list.forEach(k -> {
                            k.setSnmpStatus("-");
                            k.setSecond("-");
                        });

                    }
                }
            }
        } catch (Exception e) {
            System.out.println("addSNMPStatusToList:" + e);
        }

        return list;
    }

    public String dealSnmpStatus(List<Map<String, Object>> dataList, Long aiId) {
        try {
            if (CollectionUtils.isEmpty(dataList)) return "-";
            if (aiId == null) return "-";
            for (Map<String, Object> map : dataList) {
                if (map.get("aiId").toString().equals(String.valueOf(aiId))) {
                    return Long.valueOf(map.get("second").toString()) <= 259200L ? "true" : "false";
                }
            }
        } catch (Exception e) {
            System.out.println("dealSnmpStatus:" + e);
        }

        return "-";
    }

    public String dealSnmpSecond(List<Map<String, Object>> dataList, Long aiId) {
        try {
            if (CollectionUtils.isEmpty(dataList)) return "-";
            if (aiId == null) return "-";
            for (Map<String, Object> map : dataList) {
                if (map.get("aiId").toString().equals(String.valueOf(aiId))) {
                    return map.get("second").toString();
                }
            }
        } catch (Exception e) {
            System.out.println("dealSnmpSecond:" + e);
        }

        return "-";
    }
    //region 保存实例真实表相关

    @Override
    public BaseResponse getSNMPInstanceListByEid(Long eid, String snmpType) {
        //region 参数检查

        Optional<BaseResponse> optResponse = checkParamIsEmpty(eid, "eid");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        //endregion

        return BaseResponse.ok(instanceMapper.selectSNMPInstanceListByEid(eid, snmpType));
    }

    @Override
    public BaseResponse saveSNMPInstance(AiopsSNMPInstance aiopsSNMPInstance) {
        //region 参数检查

        Optional<BaseResponse> optResponse = checkParamIsEmpty(aiopsSNMPInstance, "aiopsSNMPInstance");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        Long eid = aiopsSNMPInstance.getEid();
        optResponse = checkParamIsEmpty(eid, "aiopsSNMPInstance.eid");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        String snmpType = aiopsSNMPInstance.getSnmpType();
        optResponse = checkParamIsEmpty(snmpType, "aiopsSNMPInstance.snmpType");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        String snmpIpAddress = aiopsSNMPInstance.getSnmpIpAddress();
        optResponse = checkParamIsEmpty(snmpIpAddress, "aiopsSNMPInstance.snmpIpAddress");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        String snmpPort = aiopsSNMPInstance.getSnmpPort();
        optResponse = checkParamIsEmpty(snmpPort, "aiopsSNMPInstance.snmpPort");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        //endregion

        //修正SNMPId
        aiopsSNMPInstance.fixSNMPId();

        //检查snmpId是否已经存在，若存在不允许添加
        String snmpId = aiopsSNMPInstance.getSnmpId();
        AiopsSNMPInstance existSNMPInstance = instanceMapper.selectAiopsSNMPInstanceBySNMPId(snmpId);
        if (existSNMPInstance != null && !existSNMPInstance.getId().equals(aiopsSNMPInstance.getId())) {
            return BaseResponse.error(ResponseCode.SNMP_INSTANCE_ALREADY_EXIST, existSNMPInstance);
        }

        Long resultAiId = 0L;
        Long asiId = aiopsSNMPInstance.getId();
        String oldSNMPId;
        if (LongUtil.isEmpty(asiId)) {
            aiopsSNMPInstance.setId(SnowFlake.getInstance().newId());
            //新增时，若不需修正执行参数内容，且原始执行参数内容不为空，要修正执行参数里面的snmpId
            checkAndFixExecParamsContent(aiopsSNMPInstance);
            oldSNMPId = "";
        } else {
            //查询已有的实例，并判断snmpId是否异动
            oldSNMPId = instanceMapper.selectSNMPIdByAsiId(asiId);
            if (StringUtils.isNotBlank(oldSNMPId) && !oldSNMPId.equals(aiopsSNMPInstance.getSnmpId())) {
                //若snmpId异动，查询是否已经存在该运维实例
                //查出已有的SNMP运维实例，进行迁移
                BaseResponse<Long> response = getAiIdByAiopsItemId(oldSNMPId);
                if (!response.checkIsSuccess()) {
                    return response;
                }
                //若有后面要针对已有的运维实例做大迁移(包括设备收集项明细的执行参数)
                resultAiId = response.getData();
                checkAndFixExecParamsContent(aiopsSNMPInstance);
            }
        }

        //创建SNMP实例
        instanceMapper.batchInsertOrUpdateAiopsSNMPInstance(Stream.of(aiopsSNMPInstance).collect(Collectors.toList()));
        BaseResponse response = syncTrustInstanceToDcdp(snmpId, oldSNMPId);
        if (!response.checkIsSuccess()) {
            return response;
        }
        return BaseResponse.ok(resultAiId);
    }

    private BaseResponse syncTrustInstanceToDcdp(String aiopsItemId, String oldAiopsItemId) {
        if (StringUtils.isBlank(aiopsItemId)) {
            return BaseResponse.ok(0);
        }
        AiopsUpgradeDcdpRequest request = new AiopsUpgradeDcdpRequest();
        AiopsUpgradeDcdpRequest.InstanceUpgrade instanceUpgrade =
                new AiopsUpgradeDcdpRequest.InstanceUpgrade(aiopsItemId);
        if (StringUtils.isNotBlank(oldAiopsItemId)) {
            instanceUpgrade.getParamsMap().put("oldAiopsItemId", oldAiopsItemId);
        }
        instanceUpgrade.setNeedSaveOperationLog(true);
        Map<String, Object> operationInfoMap = instanceUpgrade.getOperationInfoMap();
        operationInfoMap.put("operationObjectClassification", "instance");
        operationInfoMap.put("operationObject", "instance");
        if (StringUtils.isBlank(Objects.toString(operationInfoMap.get("operationBehavior"), ""))) {
            operationInfoMap.put("operationBehavior", "editinstance");
        }
        request.getInstanceUpgradeList().add(instanceUpgrade);
        return upgradeService.upgradeToDcdp(request);
    }

    private void checkAndFixExecParamsContent(AiopsSNMPInstance aiopsSNMPInstance) {
        if (BooleanUtils.toBoolean(aiopsSNMPInstance.getNeedFixExecParamsContent())) {
            return;
        }
        String oriExecParamsContent;
        if (StringUtils.isBlank(oriExecParamsContent = aiopsSNMPInstance.getOriExecParamsContent())) {
            return;
        }
        aiopsSNMPInstance.setOriExecParamsContent(fixExecParamsSnmpId(oriExecParamsContent,
                aiopsSNMPInstance.getSnmpId()));
    }

    private String fixExecParamsSnmpId(String oriExecParamsContent, String snmpId) {
        //因为新增场景前端不知道snmpId，因此这里针对执行参数内的snmpId进行修正
        //反序列化
        Optional<Map<String, Object>> optFullMap = tryGetFullExecParamsMap(oriExecParamsContent);
        if (!optFullMap.isPresent()) {
            return oriExecParamsContent;
        }
        Map<String, Object> fullMap = optFullMap.get();
        //取得ExecParam内容，取到就填充值
        Optional<Map<String, Object>> optMap = getExecParamMap(fullMap);
        if (optMap.isPresent()) {
            fixExecParamsSnmpIdCore(optMap.get(), snmpId);
            return SerializeUtil.JsonSerialize(fullMap);
        }
        return oriExecParamsContent;
    }

    private void fixExecParamsSnmpIdCore(Map<String, Object> execParamsContentMap, String snmpId) {
        //填充值
        setExecParamContentMapValue(execParamsContentMap, ATTACH, USED_TO_ATTACH_SNMP_ID, SNMP_ID,
                SNMP_ID_VALUE, snmpId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BaseResponse batchSaveSNMPInstance(Boolean autoOpenAuth, String sourceDeviceId,
                                              List<AiopsSNMPInstance> aiopsSNMPInstanceList) {
        //region 参数检查

        Optional<BaseResponse> optResponse = checkParamIsEmpty(aiopsSNMPInstanceList, "aiopsSNMPInstanceList");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        //endregion

        Long eid = RequestUtil.getHeaderEid();
        aiopsSNMPInstanceList.stream().filter(x -> LongUtil.isEmpty(x.getEid())).forEach(x -> x.setEid(eid));
        List<AiopsSNMPInstance> existsAiopsSNMPInstances =
                instanceMapper.selectExistSNMPInstances(aiopsSNMPInstanceList);
        if (!CollectionUtils.isEmpty(existsAiopsSNMPInstances)) {
            return BaseResponse.error(ResponseCode.SNMP_INSTANCE_ALREADY_EXIST, existsAiopsSNMPInstances);
        }
        //更新备份排程设备名称
        CompletableFuture.runAsync(() -> aiopsSNMPInstanceList.forEach(aiopsSNMPInstance ->
                aioCmdbFeignClient.updateDeviceName(aiopsSNMPInstance.getSnmpType(),
                        aiopsSNMPInstance.getSnmpId(),
                        aiopsSNMPInstance.getSnmpName()))
        );


        optResponse = aiopsSNMPInstanceList.stream().map(x -> {
            //保存SNMP
            BaseResponse response = saveSNMPInstance(x);
            if (!response.checkIsSuccess()) {
                return response;
            }
            //创建运维项目上下文
            AiopsItemContext aic = new AiopsItemContext(x.getSnmpType(), x.getSnmpId(), sourceDeviceId);
            aic.setAiId(LongUtil.objectToLong(response.getData()));
            aic.setNeedFixExecParamsContent(x.getNeedFixExecParamsContent());
            aic.setOriExecParamsContent(x.getOriExecParamsContent());
            Long currentEid = x.getEid();
            List<AiopsItemContext> aicList = Stream.of(aic).collect(Collectors.toList());
            AiopsAuthStatus aiopsAuthStatus = BooleanUtils.toBoolean(autoOpenAuth) ? AiopsAuthStatus.AUTHED : null;

            //创建运维实例(如果有的话)
            response = authorizeService.checkAndCreateAiopsInstanceByAiopsItemContext(currentEid, aiopsAuthStatus, aicList);
            if (!response.checkIsSuccess()) {
                return response;
            }
            if (aiopsAuthStatus != null) {
                authorizeService.modifyInstanceAuthStatus(currentEid, aiopsAuthStatus, aicList);
            }
            return null;
        }).filter(x -> x != null).findFirst();
        if (optResponse.isPresent()) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return optResponse.get();
        }
        return BaseResponse.ok(aiopsSNMPInstanceList.size());
    }

    @Override
    public BaseResponse saveBackupSoftwareInstance(AiopsBackupSoftwareInstance aiopsBackupSoftwareInstance) {
        //region 参数检查

        Optional<BaseResponse> optResponse = checkParamIsEmpty(aiopsBackupSoftwareInstance,
                "aiopsBackupSoftwareInstance");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        Long eid = aiopsBackupSoftwareInstance.getEid();
        optResponse = checkParamIsEmpty(eid, "aiopsBackupSoftwareInstance.eid");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        String backupSoftwareType = aiopsBackupSoftwareInstance.getBackupSoftwareType();
        optResponse = checkParamIsEmpty(backupSoftwareType, "aiopsBackupSoftwareInstance.backupSoftwareType");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        String deviceId = aiopsBackupSoftwareInstance.getDeviceId();
        optResponse = checkParamIsEmpty(deviceId, "aiopsBackupSoftwareInstance.deviceId");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        //endregion

        //修正备份软件Id
        aiopsBackupSoftwareInstance.fixBackupSoftwareId();

        String backupSoftwareId = aiopsBackupSoftwareInstance.getBackupSoftwareId();
        Long resultAiId = 0L;
        Long absiId = aiopsBackupSoftwareInstance.getId();
        String oldBackupSoftwareId;
        if (LongUtil.isEmpty(absiId)) {
            //检查备份软件Id如果是空的，先取看看是否有已经存在的(考虑到移除关联后，还会重新进行添加)
            AiopsBackupSoftwareInstance existBackupSoftwareInstance =
                    instanceMapper.selectAiopsBackupSoftwareInstanceByBackupSoftwareId(
                            aiopsBackupSoftwareInstance.getBackupSoftwareId());
            if (existBackupSoftwareInstance == null) {
                aiopsBackupSoftwareInstance.setId(SnowFlake.getInstance().newId());
            } else {
                aiopsBackupSoftwareInstance.setId(existBackupSoftwareInstance.getId());
            }
            oldBackupSoftwareId = "";
        } else {
            //查询已有的实例，并判断备份软件Id是否异动
            oldBackupSoftwareId = instanceMapper.selectAiopsBackupSoftwareInstanceByAbsiId(absiId);
            if (StringUtils.isNotBlank(oldBackupSoftwareId) && !oldBackupSoftwareId.equals(backupSoftwareId)) {
                //若备份软件Id异动，查询是否已经存在该运维实例
                //查出已有的备份软件运维实例，进行迁移
                BaseResponse<Long> response = getAiIdByAiopsItemId(oldBackupSoftwareId);
                if (!response.checkIsSuccess()) {
                    return response;
                }
                //若有后面要针对已有的运维实例做大迁移(包括设备收集项明细的执行参数)
                resultAiId = response.getData();
            }
        }

        //创建备份软件实例
        instanceMapper.batchInsertOrUpdateAiopsBackupSoftwareInstance(
                Stream.of(aiopsBackupSoftwareInstance).collect(Collectors.toList()));
        BaseResponse response = syncTrustInstanceToDcdp(backupSoftwareId, oldBackupSoftwareId);
        if (!response.checkIsSuccess()) {
            return response;
        }
        return BaseResponse.ok(resultAiId);
    }

    @Override
    public BaseResponse getHttpInstanceListByEid(Long eid, String apiCollectType) {
        //region 参数检查

        Optional<BaseResponse> optResponse = checkParamIsEmpty(eid, "eid");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        //endregion

        return BaseResponse.ok(instanceMapper.selectHttpInstanceListByEid(eid, apiCollectType));
    }

    @Override
    public BaseResponse getHttpInstanceList(HttpRequest httpRequest) {
        //region 参数检查
        Optional<BaseResponse> optResponse = checkParamIsEmpty(httpRequest, "httpRequest");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        Long eid = httpRequest.getEid();
        optResponse = checkParamIsEmpty(eid, "httpRequest.eid");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        Boolean needPaging = BooleanUtils.toBoolean(httpRequest.getNeedPaging());

        //endregion

        Map<String, Object> map = BeanUtil.object2Map(httpRequest);
        processAiopsAuthStatusListCondition(httpRequest.getAiopsAuthStatusList(), map);
        return getHttpListCore(needPaging, httpRequest.getPageNum(), httpRequest.getPageSize(), map);
    }

    private BaseResponse getHttpListCore(Boolean needPaging, Integer pageNum, Integer pageSize, Map<String, Object> map) {
        //查询
        if (BooleanUtils.toBoolean(needPaging)) {
            pageNum = IntegerUtil.isEmpty(pageNum) ? DEFAULT_PAGE_NO : pageNum;
            pageSize = IntegerUtil.isEmpty(pageSize) ? DEFAULT_PAGE_SIZE : pageSize;
            Page page = PageHelper.startPage(pageNum, pageSize);
            instanceMapper.selectHttpInstanceList(map);
            PageInfo<AiopsHttpInstance> pageInfo = new PageInfo<>(page);
            return BaseResponse.ok(pageInfo);
        }
        return BaseResponse.ok(instanceMapper.selectHttpInstanceList(map));
    }

    @Override
    public BaseResponse saveHttpInstance(AiopsHttpInstance aiopsHttpInstance) {
        //region 参数检查

        Optional<BaseResponse> optResponse = checkParamIsEmpty(aiopsHttpInstance, "aiopsHttpInstance");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        Long eid = aiopsHttpInstance.getEid();
        optResponse = checkParamIsEmpty(eid, "aiopsHttpInstance.eid");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

//        String deviceId = aiopsHttpInstance.getDeviceId();
//        optResponse = checkParamIsEmpty(deviceId, "aiopsHttpInstance.deviceId");
//        if (optResponse.isPresent()) {
//            return optResponse.get();
//        }

        String apiCollectType = aiopsHttpInstance.getApiCollectType();
        optResponse = checkParamIsEmpty(apiCollectType, "aiopsHttpInstance.apiCollectType");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        String apiAddress = aiopsHttpInstance.getApiAddress();
        optResponse = checkParamIsEmpty(apiAddress, "aiopsHttpInstance.apiAddress");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        //20221129 不用必填
//        String apiBasicLoginId = aiopsHttpInstance.getApiBasicLoginId();
//        optResponse = checkParamIsEmpty(apiBasicLoginId, "aiopsHttpInstance.apiBasicLoginId");
//        if (optResponse.isPresent()) {
//            return optResponse.get();
//        }
//
//        String apiBasicLoginPwd = aiopsHttpInstance.getApiBasicLoginPwd();
//        optResponse = checkParamIsEmpty(apiBasicLoginPwd, "aiopsHttpInstance.apiBasicLoginPwd");
//        if (optResponse.isPresent()) {
//            return optResponse.get();
//        }


        //endregion

        //修正ApiCollectId and ApiBasicAuthCode
        aiopsHttpInstance.fixApiCollectId();

        //检查http是否已经存在，若存在不允许添加
        String apiCollectId = aiopsHttpInstance.getApiCollectId();
        AiopsHttpInstance existHttpInstance = instanceMapper.selectAiopsHTTPInstanceByApiCollectId(apiCollectId);
        if (existHttpInstance != null && !existHttpInstance.getId().equals(aiopsHttpInstance.getId())) {
            return BaseResponse.error(ResponseCode.HTTP_INSTANCE_ALREADY_EXIST, existHttpInstance);
        } else {
            //通過檢查後, 要再重新計算, 因為可能為修改 或新增.
            aiopsHttpInstance.fixApiBasicAuthCode();
        }

        Long resultAiId = 0L;
        Long ahiId = aiopsHttpInstance.getId();
        String oldApiCollectId;
        if (LongUtil.isEmpty(ahiId)) {
            aiopsHttpInstance.setId(SnowFlake.getInstance().newId());
            //新增时，若不需修正执行参数内容，且原始执行参数内容不为空，要修正执行参数里面的 ApiCollectId // 目前不会用到
//            checkAndFixExecParamsContent(aiopsHttpInstance);
            oldApiCollectId = "";
        } else {
            //查询已有的实例，并判断 http CollectId 是否异动
            oldApiCollectId = instanceMapper.selectApiCollectIdByAhiId(ahiId);
            if (StringUtils.isNotBlank(oldApiCollectId) && !oldApiCollectId.equals(aiopsHttpInstance.getApiCollectId())) {
                //若 ApiCollectId 异动，查询是否已经存在该运维实例
                //查出已有的 ApiCollectId 运维实例，进行迁移
                BaseResponse<Long> response = getAiIdByAiopsItemId(oldApiCollectId);
                if (!response.checkIsSuccess()) {
                    return response;
                }
                //若有后面要针对已有的运维实例做大迁移(包括设备收集项明细的执行参数)
                resultAiId = response.getData();
//                checkAndFixExecParamsContent(aiopsHttpInstance); // 目前不会用到
            }
        }

        //创建HTTP实例
        instanceMapper.batchInsertOrUpdateAiopsHttpInstance(Stream.of(aiopsHttpInstance).collect(Collectors.toList()));
        BaseResponse response = syncTrustInstanceToDcdp(apiCollectId, oldApiCollectId);
        if (!response.checkIsSuccess()) {
            return response;
        }
        return BaseResponse.ok(resultAiId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BaseResponse batchSaveHttpInstance(Boolean autoOpenAuth, String sourceDeviceId,
                                              List<AiopsHttpInstance> aiopsHttpInstanceList) {
        //region 参数检查

        Optional<BaseResponse> optResponse = checkParamIsEmpty(aiopsHttpInstanceList, "aiopsHttpInstanceList");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }
        //endregion

        //检查实例是不是存在
        Long eid = RequestUtil.getHeaderEid();
        aiopsHttpInstanceList.stream().filter(x -> LongUtil.isEmpty(x.getEid())).forEach(x -> x.setEid(eid));
        List<AiopsHttpInstance> existsAiopsHttpInstances =
                instanceMapper.selectExistHttpInstances(aiopsHttpInstanceList);
        if (!CollectionUtils.isEmpty(existsAiopsHttpInstances)) {
            return BaseResponse.error(ResponseCode.HTTP_INSTANCE_ALREADY_EXIST, existsAiopsHttpInstances);
        }

        optResponse = aiopsHttpInstanceList.stream().map(x -> {
            //保存Http instance
            BaseResponse response = saveHttpInstance(x);
            if (!response.checkIsSuccess()) {
                return response;
            }

            //创建运维项目上下文
            AiopsItemContext aic = new AiopsItemContext(x.getApiCollectType(), x.getApiCollectId(), sourceDeviceId);
            aic.setAiId(LongUtil.objectToLong(response.getData()));
            aic.setNeedFixExecParamsContent(x.getNeedFixExecParamsContent());
            aic.setOriExecParamsContent(x.getOriExecParamsContent());
            Long currentEid = x.getEid();
            List<AiopsItemContext> aicList = Stream.of(aic).collect(Collectors.toList());
            AiopsAuthStatus aiopsAuthStatus = BooleanUtils.toBoolean(autoOpenAuth) ? AiopsAuthStatus.AUTHED : null;

            //创建运维实例(如果有的话)
            response = authorizeService.checkAndCreateAiopsInstanceByAiopsItemContext(currentEid, aiopsAuthStatus, aicList);
            if (!response.checkIsSuccess()) {
                return response;
            }
            if (aiopsAuthStatus != null) {
                authorizeService.modifyInstanceAuthStatus(currentEid, aiopsAuthStatus, aicList);
            }
            return null;
        }).filter(x -> x != null).findFirst();
        if (optResponse.isPresent()) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return optResponse.get();
        }
        return BaseResponse.ok(aiopsHttpInstanceList.size());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BaseResponse batchSaveEDRInstance(String sourceDeviceId, List<AiopsEDRInstance> aiopsEDRInstanceList) {
        //region 参数检查

        Optional<BaseResponse> optResponse = checkParamIsEmpty(aiopsEDRInstanceList, "aiopsEDRInstanceList");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        //endregion

        Map<String, AiopsEDRInstance> aeiMap = aiopsEDRInstanceList.stream().peek(AiopsEDRInstance::fixEDRId)
                .collect(Collectors.toMap(AiopsEDRInstance::getEdrId, x -> x, (x, y) -> y));

        List<AiopsEDRInstance> existsAiopsEDRInstances = instanceMapper.selectExistEDRInstances(aeiMap.keySet());
        if (!CollectionUtils.isEmpty(existsAiopsEDRInstances)) {
            //已经存在的不做处理，将其排除
            existsAiopsEDRInstances.forEach(x -> aeiMap.remove(x.getEdrId()));
        }
        if (CollectionUtils.isEmpty(aeiMap)) {
            return BaseResponse.ok();
        }
        //先批量保存
        instanceMapper.batchInsertAiopsEDRInstance(aeiMap.values());
        //创建运维项目上下文
        Map<Long, List<AiopsItemContext>> eidGroupAicListMap = aeiMap.values().stream().map(x -> {
            //创建运维项目上下文
            AiopsItemContext aic = new AiopsItemContext(x.getAiopsItem(), x.getEdrId(), sourceDeviceId);
            aic.setIsAddInstanceCollectToDevice(true);
            aic.setAdcdOnlyMappingByAiId(true);
            aic.setEid(x.getEid());
            aic.setNeedFixExecParamsContent(x.getNeedFixExecParamsContent());
            aic.setOriExecParamsContent(x.getOriExecParamsContent());
            return aic;
        }).collect(Collectors.groupingBy(AiopsItemContext::getEid));

        StringBuilder sbError = new StringBuilder();
        eidGroupAicListMap.forEach((key, value) -> {
            //创建运维实例(如果有的话)
            BaseResponse response = authorizeService.checkAndCreateAiopsInstanceByAiopsItemContext(key,
                    null, value);
            if (!response.checkIsSuccess()) {
                sbError.append("checkAndCreateAiopsInstanceByAiopsItemContext eid:");
                sbError.append(key);
                sbError.append(" errMsg:");
                sbError.append(response.getErrMsg());
                sbError.append("\n");
            }
        });
        if (sbError.length() > 0) {
            return BaseResponse.dynamicError(eidGroupAicListMap, ResponseCode.EDR_INSTANCE_BATCH_SAVE_ERROR,
                    sbError.toString());
        }
        return BaseResponse.ok(eidGroupAicListMap);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BaseResponse<Map<Long, List<AiopsItemContext>>> batchSaveSmartMeterInstance(String sourceDeviceId,
                                                                                       List<AiopsSmartMeterInstance> aiopsSmartMeterInstanceList) {
        //region 参数检查

        Optional<BaseResponse> optResponse = checkParamIsEmpty(aiopsSmartMeterInstanceList,
                "aiopsSmartMeterInstanceList");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        //endregion

        Map<String, AiopsSmartMeterInstance> asmiMap = aiopsSmartMeterInstanceList.stream()
                .peek(AiopsSmartMeterInstance::fixSmartMeterId)
                .peek(x -> {
                    //修正主键
                    if (LongUtil.isEmpty(x.getId())) {
                        x.setId(SnowFlake.getInstance().newId());
                    }
                    //修正运维项目
                    if (StringUtils.isBlank(x.getAiopsItem())) {
                        x.setAiopsItem(SMART_METER);
                    }
                }).collect(Collectors.toMap(AiopsSmartMeterInstance::getSmartMeterId, x -> x, (x, y) -> y));

        List<AiopsSmartMeterInstance> existsAiopsSmartMeterInstances =
                instanceMapper.selectExistSmartMeterInstances(asmiMap.keySet());
        if (!CollectionUtils.isEmpty(existsAiopsSmartMeterInstances)) {
            //已经存在的不做新增处理，将其排除，但要更新一下报到时间
            existsAiopsSmartMeterInstances.forEach(x -> {
                AiopsSmartMeterInstance asmi = asmiMap.remove(x.getSmartMeterId());
                if (asmi != null) {
                    BeanUtils.copyProperties(asmi, x, "id", "lastCheckInTime");
                }
            });
            instanceMapper.batchUpdateAiopsSmartMeterInstance(existsAiopsSmartMeterInstances);
        }
        if (CollectionUtils.isEmpty(asmiMap)) {
            return BaseResponse.ok();
        }
        //先批量保存
        instanceMapper.batchInsertAiopsSmartMeterInstance(asmiMap.values());
        Map<String, HbasePut> aiopsItemIdHbasePutMap = new HashMap<>(asmiMap.size());
        //创建运维项目上下文
        Map<Long, List<AiopsItemContext>> eidGroupAicListMap = asmiMap.values().stream().map(x -> {
            //创建运维项目上下文
            String aiopsItemId = x.getSmartMeterId();
            Long eid = x.getEid();
            AiopsItemContext aic = new AiopsItemContext(x.getAiopsItem(), aiopsItemId, sourceDeviceId);
            aic.setIsAddInstanceCollectToDevice(true);
            aic.setAdcdOnlyMappingByAiId(true);
            aic.setEid(eid);
            aic.setNeedFixExecParamsContent(x.getNeedFixExecParamsContent());
            aic.setOriExecParamsContent(x.getOriExecParamsContent());
            HbasePut hbasePut = new HbasePut();
            hbasePut.setTableName(SMART_METER_INSTANCE);
            hbasePut.setCf(INFO);
            hbasePut.setCn(MODEL);
            hbasePut.setRowKey(String.format("%d_%s", eid, x.getSmartMeterDeviceId()));
            hbasePut.setData(new JSONObject());
            aiopsItemIdHbasePutMap.put(aiopsItemId, hbasePut);
            aic.setAddedAdcdConsumer((y, z) ->
                    saveInstanceToBigData(aiopsItemIdHbasePutMap.get(y.getAiopsItemId()), y, z));
            return aic;
        }).collect(Collectors.groupingBy(AiopsItemContext::getEid));

        StringBuilder sbError = new StringBuilder();
        HbasePutList hbasePutListParam = new HbasePutList();
        hbasePutListParam.setTableName(SMART_METER_INSTANCE);
        eidGroupAicListMap.forEach((key, value) -> {
            //创建运维实例(如果有的话)
            BaseResponse response = authorizeService.checkAndCreateAiopsInstanceByAiopsItemContext(key,
                    null, value);
            if (!response.checkIsSuccess()) {
                sbError.append("checkAndCreateAiopsInstanceByAiopsItemContext eid:");
                sbError.append(key);
                sbError.append(" errMsg:");
                sbError.append(response.getErrMsg());
                sbError.append("\n");
            }
        });
        if (sbError.length() > 0) {
            return BaseResponse.dynamicError(eidGroupAicListMap, ResponseCode.SMART_METER_INSTANCE_BATCH_SAVE_ERROR,
                    sbError.toString());
        }
        return BaseResponse.ok(eidGroupAicListMap);
    }

    private void saveInstanceToBigData(HbasePut hbasePut, AiopsItemContext aic,
                                       List<DeviceCollectDetail> addedAdcdList) {
        if (hbasePut == null || aic == null || CollectionUtils.isEmpty(addedAdcdList)) {
            return;
        }
        if (addedAdcdList.size() == 1) {
            //只取第一个
            setSmartMeterHbasePutData(hbasePut, addedAdcdList.get(0), aic);
            //将关键数据保存到大数据平台
            bigDataUtil.save(hbasePut);
            return;
        }
        HbasePutList hbasePutList = new HbasePutList();
        hbasePutList.setPutList(addedAdcdList.stream().map(x -> {
            HbasePut curHbasePut = new HbasePut();
            BeanUtils.copyProperties(hbasePut, curHbasePut);
            setSmartMeterHbasePutData(curHbasePut, x, aic);
            return curHbasePut;
        }).collect(Collectors.toList()));
        bigDataUtil.updateList(hbasePutList);
    }

    private void setSmartMeterHbasePutData(HbasePut hbasePut, DeviceCollectDetail adcd,
                                           AiopsItemContext aic) {
        //范例:99990000_ddmanTest_SmartMeterCollected
        hbasePut.setRowKey(hbasePut.getRowKey() + "_" + adcd.getUploadDataModelCode());
        JSONObject data = hbasePut.getData();
        if (data == null) {
            data = new JSONObject();
            hbasePut.setData(data);
        }
        data.put(ADCD_ID, adcd.getId());
        data.put(ACC_ID, adcd.getAccId());
        data.put(AI_ID, aic.getAiId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BaseResponse batchSaveEaiCloudInstance(String sourceDeviceId,
                                                  List<AiopsEaiCloudInstance> aiopsEaiCloudInstanceList) {
        //region 参数检查

        Optional<BaseResponse> optResponse = checkParamIsEmpty(aiopsEaiCloudInstanceList,
                "aiopsEaiCloudInstanceList");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        //endregion

        Map<String, AiopsEaiCloudInstance> aeciMap = aiopsEaiCloudInstanceList.stream()
                .peek(AiopsEaiCloudInstance::fixEaiCloudId)
                .peek(x -> {
                    //修正主键
                    if (LongUtil.isEmpty(x.getId())) {
                        x.setId(SnowFlake.getInstance().newId());
                    }
                    //修正运维项目
                    if (StringUtils.isBlank(x.getAiopsItem())) {
                        x.setAiopsItem("CB_HOST");
                    }
                    //eaiType為S1EDR_HOST維運項目類型設為S1EDR，維運項目設為S1EDR_HOST
                    if ("S1EDR_HOST".equalsIgnoreCase(x.getEaiType())) {
                        x.setAiopsItemType("S1EDR");
                        x.setAiopsItem("S1EDR_HOST");
                    }
                }).collect(Collectors.toMap(AiopsEaiCloudInstance::getEaiCloudId, x -> x, (x, y) -> y));

        List<AiopsEaiCloudInstance> existAiopsEaiCloudInstances =
                instanceMapper.selectExistEaiCloudInstance(aeciMap.keySet());

        if (CollectionUtils.isEmpty(existAiopsEaiCloudInstances)) {
            //先批量保存
            instanceMapper.batchInsertAiopsEaiCloudInstance(aiopsEaiCloudInstanceList);
        } else {
            //已经存在的不做新增处理，将其排除，不存在进行新增
            Set<String> existKey = existAiopsEaiCloudInstances.stream().map(AiopsEaiCloudInstance::getEaiCloudId)
                    .collect(Collectors.toSet());
            List<AiopsEaiCloudInstance> needInsertAsciList = aiopsEaiCloudInstanceList.stream()
                    .filter(x -> !existKey.contains(x.getEaiCloudId())).collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(needInsertAsciList)) {
                //先批量保存
                instanceMapper.batchInsertAiopsEaiCloudInstance(needInsertAsciList);
            }
        }

        //创建运维项目上下文
        Map<Long, List<AiopsItemContext>> eidGroupAicListMap = aeciMap.values().stream().map(x -> {
            //创建运维项目上下文
            String aiopsItemId = x.getEaiCloudId();
            Long eid = x.getEid();
            AiopsItemContext aic = new AiopsItemContext(x.getAiopsItem(), aiopsItemId, sourceDeviceId);
            aic.setIsAddInstanceCollectToDevice(true);
            aic.setAdcdOnlyMappingByAiId(true);
            aic.setEid(eid);
            aic.setNeedFixExecParamsContent(x.getNeedFixExecParamsContent());
            aic.setOriExecParamsContent(x.getOriExecParamsContent());
            return aic;
        }).collect(Collectors.groupingBy(AiopsItemContext::getEid));

        StringBuilder sbError = new StringBuilder();
        eidGroupAicListMap.forEach((key, value) -> {
            //创建运维实例(如果有的话)
            BaseResponse response = authorizeService.checkAndCreateAiopsInstanceByAiopsItemContext(key,
                    null, value);
            if (!response.checkIsSuccess()) {
                sbError.append("checkAndCreateAiopsInstanceByAiopsItemContext eid:");
                sbError.append(key);
                sbError.append(" errMsg:");
                sbError.append(response.getErrMsg());
                sbError.append("\n");
            }
        });
        if (sbError.length() > 0) {
            return BaseResponse.dynamicError(eidGroupAicListMap, ResponseCode.EAI_CLOUD_INSTANCE_BATCH_SAVE_ERROR,
                    sbError.toString());
        }
        return BaseResponse.ok(eidGroupAicListMap);
    }

    @Override
    public BaseResponse getSelectAiopsEaiCloudWarningStatus(String serviceCode, String eaiType) {
        //region 参数检查
        Optional<BaseResponse> optResponse = checkParamIsEmpty(serviceCode, "serviceCode");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }
        optResponse = checkParamIsEmpty(eaiType, "eaiType");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }
        //endregion

        // 取得aiopsItemType
        String aiopsItemType = instanceMapper.selectAiopsItemTypeByAiopsItem(eaiType);

        return BaseResponse.ok(instanceMapper.selectAiopsEaiCloudIsWarningEnable(serviceCode, aiopsItemType));

    }

    @Override
    public BaseResponse getAiopsEaiCloudInstanceCollectDetail(String serviceCode, String uploadDataModelCode, String aiopsItemType, String eaiType) {
        //region 参数检查
        Optional<BaseResponse> optResponse = checkParamIsEmpty(eaiType, "eaiType");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }
        optResponse = checkParamIsEmpty(uploadDataModelCode, "uploadDataModelCode");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }
        //endregion

        // 取得aiopsItemType
        aiopsItemType = StringUtil.isNotEmpty(aiopsItemType) ? aiopsItemType : instanceMapper.selectAiopsItemTypeByAiopsItem(eaiType);

        return BaseResponse.ok(instanceMapper.selectAiopsEaiCloudInstanceDetail(serviceCode, uploadDataModelCode, eaiType, aiopsItemType));
    }

    @Override
    public BaseResponse removeAiopsEaiCloudInstanceCollectDetail(String serviceCode, String eaiType) {
        //region 参数检查
        Optional<BaseResponse> optResponse = checkParamIsEmpty(eaiType, "eaiType");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }
        optResponse = checkParamIsEmpty(serviceCode, "serviceCode");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }
        //endregion

        // 移除實例
        return BaseResponse.ok(instanceMapper.removeAiopsEaiCloudInstanceDetail(serviceCode, eaiType));
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public BaseResponse addNewInstanceCollectDetail(String serviceCode, String eaiType) {
        //region 参数检查
        Optional<BaseResponse> opt = checkParamIsEmpty(eaiType, "eaiType");
        if (opt.isPresent()) {
            return opt.get();
        }
        //endregion

        // 取得模組的收集項、預警項詳情 及 eai實例詳情
        List<AiopsModuleCollect> moduleCollectDetail = instanceMapper.getModuleCollectDetail(eaiType);
        List<AiopsInstanceCollectMapping> instanceCollectDetail = instanceMapper.getInstanceCollectDetail(serviceCode, eaiType);
        Map<String, List<AiopsInstanceCollectMapping>> collectDetailMap = instanceCollectDetail.stream()
                .collect(Collectors.groupingBy(AiopsInstanceCollectMapping::getServiceCode));

        // 創建收集項及預警項
        try {
            collectDetailMap.forEach((code, instanceDetail) -> {

                // 取得eid
                Long eid = instanceMapper.getEidByServiceCode(code);

                // 找出差異並聚合成批量新增的List
                List<Map<String, Object>> toAddCollectList = processCollectListToAdd(moduleCollectDetail, instanceDetail);

                // 批量新增收集項實例
                if (CollectionUtil.isNotEmpty(toAddCollectList)) {
                    instanceMapper.batchAddNewInstanceCollectDetail(toAddCollectList);
                }

                // 處理批量新增的收集項下，需要新增的預警清單
                List<Map<String, Object>> toAddWarningList = processWarningListToAdd(moduleCollectDetail, instanceDetail, toAddCollectList);

                if (CollectionUtil.isNotEmpty(toAddWarningList)) {
                    // 批量新增預警實例
                    instanceMapper.batchAddNewInstanceWarningDetail(toAddWarningList);

                    // 取得設備預警清單
                    List<DeviceCollectWarning> adcwList = getAdcwList(toAddWarningList);

                    // 設置規則引擎
                    ruleEngineService.batchSaveRuleEngineByAdcwList(adcwList);

                    // 帶入默認通知群組
                    deviceService.batchAddDefaultDeviceWarningNotifyMapping(adcwList, eid, "");
                }
            });
        } catch (Exception e) {
            log.error("AddNewInstanceCollectDetail Error: ", e);
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
        }

        return BaseResponse.ok();
    }

    @Override
    public BaseResponse modifyAiopsEaiCloudInstance(Boolean isWarningEnable, String serviceCode,
                                                    String eaiType) {
        //region 参数检查
        Optional<BaseResponse> optResponse = checkParamIsEmpty(serviceCode, "serviceCode");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        optResponse = checkParamIsEmpty(eaiType, "eaiType");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        isWarningEnable = BooleanUtils.toBoolean(isWarningEnable);
        //endregion

        // 取得aiopsItemType
        String aiopsItemType = instanceMapper.selectAiopsItemTypeByAiopsItem(eaiType);

        Integer effectRow = instanceMapper.updateAiopsEaiCloudInstance(isWarningEnable, serviceCode, eaiType, aiopsItemType);
        //查询adcwIdList
        List<Long> adcwIdList = instanceMapper.selectAiopsEaiCloudAdcwIdList(serviceCode, eaiType, aiopsItemType);
        if (CollectionUtils.isEmpty(adcwIdList)) {
            return BaseResponse.ok(effectRow);
        }
        //更新rule_engine_setting_mapping
        ruleEngineDao.updateResmEnableByAdcwId(isWarningEnable, adcwIdList);
        return BaseResponse.ok(effectRow);
    }

    @Override
    public BaseResponse getAiopsInstanceByAiopsItemId(String aiopsItemId) {
        //region 参数检查

        Optional<BaseResponse> optResponse = checkParamIsEmpty(aiopsItemId, "aiopsItemId");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        //endregion

        List<AiopsInstance> aiList = instanceMapper.selectAiopsInstanceByAiopsItemIdList(Stream.of(aiopsItemId)
                .collect(Collectors.toList()));
        if (CollectionUtils.isEmpty(aiList)) {
            return BaseResponse.ok();
        }
        return BaseResponse.ok(aiList.get(0));
    }

    @Override
    public BaseResponse getAiIdByAiopsItemId(String aiopsItemId) {
        //region 参数检查

        Optional<BaseResponse> optResponse = checkParamIsEmpty(aiopsItemId, "aiopsItemId");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        //endregion

        return BaseResponse.ok(instanceMapper.selectAiIdByAiopsItemId(aiopsItemId));
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public BaseResponse modifyAiopsInstanceDeviceMapping(String sourceDeviceId, String targetDeviceId,
                                                         Boolean copySourceDeviceAiopsItemCollect,
                                                         List<String> aiopsItemIdList) {
        //region 参数检查

        Optional<BaseResponse> optResponse = checkParamIsEmpty(targetDeviceId, "targetDeviceId");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        optResponse = checkParamIsEmpty(aiopsItemIdList, "aiopsItemIdList");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        //检查目标设备是否存在
        AiopsKitDevice targetDevice = deviceService.getDeviceByDeviceId(targetDeviceId);
        if (targetDevice == null) {
            return BaseResponse.dynamicError(targetDeviceId, ResponseCode.DEVICE_NOT_FOUND, targetDeviceId);
        }

        //过滤掉重复的
        Set<String> aiopsItemIdSet = new HashSet<>(aiopsItemIdList);

        copySourceDeviceAiopsItemCollect = BooleanUtils.toBoolean(copySourceDeviceAiopsItemCollect);

        List<AiopsInstance> aiList = instanceMapper.selectAiopsInstanceByAiopsItemIdList(aiopsItemIdSet);
        if (CollectionUtils.isEmpty(aiList)) {
            //运维实例不存在
            return BaseResponse.dynamicError(aiopsItemIdList, ResponseCode.AIOPS_INSTANCE_NOT_EXIST,
                    String.join(",", aiopsItemIdList));
        }
        if (aiList.size() != aiopsItemIdSet.size()) {
            //部分运维实例不存在
            aiList.stream().filter(Objects::nonNull).forEach(x -> aiopsItemIdSet.remove(x.getAiopsItemId()));
            return BaseResponse.dynamicError(aiopsItemIdSet, ResponseCode.AIOPS_INSTANCE_NOT_EXIST,
                    String.join(",", aiopsItemIdSet));
        }

        //endregion

        Map<String, Set<String>> aiopsItemTypeGroup = aiList.stream().filter(Objects::nonNull)
                .collect(Collectors.groupingBy(AiopsInstance::getAiopsItemType,
                        Collectors.mapping(AiopsInstance::getAiopsItemId, Collectors.toSet())));

        //因为数据库是透过地端做选择的，因此数据库迁移，要检查目标设备是否也包含该dbId，若不包含，不允许迁移
        Set<String> dbIdSet = aiopsItemTypeGroup.get(DATABASE);
        if (!CollectionUtils.isEmpty(dbIdSet)) {
            BaseResponse<List<String>> response = deviceService.getDeviceDatasourceMatchDbId(targetDeviceId, dbIdSet);
            if (!response.checkIsSuccess()) {
                return response;
            }
            List<String> matchedDbIdList = response.getData();
            if (!CollectionUtils.isEmpty(matchedDbIdList)) {
                matchedDbIdList.stream().filter(StringUtils::isNotBlank)
                        .forEach(dbIdSet::remove);
            }
            if (!CollectionUtils.isEmpty(dbIdSet)) {
                Long adId = deviceService.getAdIdByDeviceId(sourceDeviceId);
                //取得adId并检查来源设备是否存在
                if (LongUtil.isEmpty(adId)) {
                    return BaseResponse.dynamicError(sourceDeviceId, ResponseCode.DEVICE_NOT_FOUND, sourceDeviceId);
                }
                List<AiopsKitDeviceDataSource> notExistDatasource = deviceService.getDeviceDataSourceList(adId,
                        null, dbIdSet);
                return BaseResponse.error(ResponseCode.TRANSFER_ERROR_BY_TARGET_DB_NOT_EXIST, notExistDatasource);
            }
        }

        Set<String> backupSoftwareIdSet = aiopsItemTypeGroup.get(BACKUP);
        if (!CollectionUtils.isEmpty(backupSoftwareIdSet)) {
            //抛异常，不支持移转
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return BaseResponse.error(ResponseCode.TRANSFER_ERROR_BY_NOT_SUPPORT, BACKUP);
        }

        //TODO:处理产品应用/服务移转，要将设备应用/服务映射一同移转到目标设备
        //TODO:还要处理产品应用/服务下的执行参数问题
        // 产品应用/服务可能会包含某些必填收集项，其中会使用到adpmId，就需要整个重新调整执行参数
        // 因过于复杂，暂时先不支持
        Set<String> appIdSet = aiopsItemTypeGroup.get(PRODUCT_APP);
        if (!CollectionUtils.isEmpty(appIdSet)) {
            //抛异常，不支持移转
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return BaseResponse.error(ResponseCode.TRANSFER_ERROR_BY_NOT_SUPPORT, PRODUCT_APP);
//            Long sourceAiId = deviceService.getAdIdByDeviceId(sourceDeviceId);
//            Long targetAiId = deviceService.getAdIdByDeviceId(targetDeviceId);
//            List<Map<String, Object>> mapList = new ArrayList<>(appIdSet.size() * 2);
//            appIdSet.stream().forEach(x -> {
//                Map<String, Object> map = new HashMap<>(2);
//                map.put("adId", sourceAiId);
//                map.put("appId", x);
//                mapList.add(map);
//                map = new HashMap<>(2);
//                map.put("adId", targetAiId);
//                map.put("appId", x);
//                mapList.add(map);
//            });
//            List<DeviceProductMapping> adpmList = deviceService.getAdpmListByMapList(mapList);
//            if (!CollectionUtils.isEmpty(adpmList)) {
//                //产生字典
//                Map<String, DeviceProductMapping> adpmMap = adpmList.stream().collect(Collectors.toMap(x ->
//                                String.join("_", LongUtil.safeToString(x.getAdId()), x.getAppId()), x -> x,
//                        (x, y) -> y));
//                List<Long> needDeleteAdpmIdList = new ArrayList<>();
//                List<Long> needUpdateAdpmIdList = appIdSet.stream().map(x -> {
//                    String targetKey = String.join("_", LongUtil.safeToString(targetAiId), x);
//                    DeviceProductMapping targetAdpm = adpmMap.get(targetKey);
//                    //目标的设备应用/服务映射不存在，返回来源设备应用/服务映射(后面进行更新aiId)
//                    String sourceKey = String.join("_", LongUtil.safeToString(sourceAiId), x);
//                    DeviceProductMapping sourceAdpm = adpmMap.get(sourceKey);
//                    if (targetAdpm == null) {
//                        return sourceAdpm;
//                    }
//                    //目标的设备应用/服务映射如果存在，添加到要删除的列表并返回空(后面要删除)
//                    needDeleteAdpmIdList.add(sourceAdpm.getId());
//                    return null;
//                }).filter(x -> x != null).map(x -> x.getId()).collect(Collectors.toList());
//                //目标的设备应用/服务映射如果存在，就删除来源
//                if (!CollectionUtils.isEmpty(needDeleteAdpmIdList)) {
//                    deviceService.removeAdpmByAdpmIdList(needDeleteAdpmIdList);
//                }
//                //目标的设备应用/服务映射如果不存在，就更新来源aiId
//                if (!CollectionUtils.isEmpty(needUpdateAdpmIdList)) {
//                    deviceService.modifyAdpmAdIdByAdpmIdList(needUpdateAdpmIdList);
//                }
//            }
        }

        //处理移转设备实例映射
        BaseResponse response = deviceService.modifySourceAdimToTargetByAiopsItemIdList(sourceDeviceId,
                targetDevice.getId(), targetDevice.getDeviceId(), aiopsItemIdList);
        if (!response.checkIsSuccess()) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return response;
        }
        if (copySourceDeviceAiopsItemCollect) {
            //处理移转实例下设备收集项明细
            response = deviceService.modifySourceAdcdToTargetByAiopsItemIdList(sourceDeviceId, targetDevice.getId(),
                    targetDevice.getDeviceId(), aiopsItemIdList);
            if (!response.checkIsSuccess()) {
                TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                return response;
            }
            //迁移规则引擎
            response = ruleEngineService.modifySourceRuleEngineToTargetByAiopsItemIdList(sourceDeviceId, targetDeviceId,
                    aiopsItemIdList);
        } else {
            //如果没要迁移收集项，就将来源设备对应的实例收集项给删除
            response = deviceService.removeAdcdByAiopsItemIdList(sourceDeviceId, aiopsItemIdList);
            if (!response.checkIsSuccess()) {
                TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                return response;
            }
            //删除规则引擎
            Map<String, Object> map = new HashMap<>();
            map.put("deviceId", sourceDeviceId);
            map.put("aiopsItemIdList", aiopsItemIdList);
            response = ruleEngineService.removeReResmRsByMap(map);
        }
        if (!response.checkIsSuccess()) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return response;
        }

        //清缓存
        collectConfigService.clearDeviceIdCollectConfigCache(sourceDeviceId);
        collectConfigService.clearDeviceIdCollectConfigCache(targetDeviceId);
        restItemsUtil.clearDeviceIdCollectConfigCache(sourceDeviceId);

        return response;
    }

    @Override
    public BaseResponse getAiopsInstanceByAiopsItemIdList(List<String> aiopsItemIdList) {
        //region 参数检查

        Optional<BaseResponse> optResponse = checkParamIsEmpty(aiopsItemIdList, "aiopsItemIdList");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }
        //endregion

        return BaseResponse.ok(instanceMapper.selectAiopsInstanceByAiopsItemIdList(aiopsItemIdList));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BaseResponse modifyAiopsInstanceAuthStatusByContractExpire(List<Map<String, Object>> partTmcdList) {
        //region 参数检查

        Optional<BaseResponse> optResponse = checkParamIsEmpty(partTmcdList, "partTmcdList");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }
        //endregion

        List<Long> noneAuthTmcdIdList = new ArrayList<>();
        List<Long> holdAuthTmcdIdList = partTmcdList.stream().filter(Objects::nonNull)
                .filter(x -> {
                    if (BooleanUtil.objectToBoolean(x.get("isContainHoldAuth"))) {
                        return true;
                    }
                    //挑出无须授权的部分
                    Long tmcdId = LongUtil.objectToLong(x.get("tmcdId"));
                    if (LongUtil.isNotEmpty(tmcdId)) {
                        noneAuthTmcdIdList.add(tmcdId);
                    }
                    return false;
                })
                .map(x -> LongUtil.objectToLong(x.get("tmcdId")))
                .filter(LongUtil::isNotEmpty)
                .collect(Collectors.toList());
        //下面将tmcd分为占用授权与无须授权分别处理
        Map<Long, Integer> tmcdIdUsedCountMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(holdAuthTmcdIdList)) {
            // and 更新时将recoverableAuth更新为1
            instanceMapper.updateAiopsInstanceAuthStatusByContractExpire(holdAuthTmcdIdList);
            //异动规则引擎授权状态
            BaseResponse response = ruleEngineService.modifyRuleEngineAuthStatusByContractExpire(holdAuthTmcdIdList);
            if (!response.checkIsSuccess()) {
                TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            }

            //串查eid出来，进行缓存清理
            List<Long> eidList = instanceMapper.selectAiEidListByTmcdIdList(holdAuthTmcdIdList);
            if (!CollectionUtils.isEmpty(eidList)) {
                collectConfigService.clearEidListCollectConfigCache(eidList);
            }
            //获取新的已授权数量并返回
            BaseResponse<Map<Long, Integer>> tmcdIdUsedCountMapResponse =
                    getAuthedCountMapByTmcdIdList(null, true, holdAuthTmcdIdList);
            if (!tmcdIdUsedCountMapResponse.checkIsSuccess()) {
                TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                return tmcdIdUsedCountMapResponse;
            }
            tmcdIdUsedCountMap.putAll(tmcdIdUsedCountMapResponse.getData());
        }
        if (!CollectionUtils.isEmpty(noneAuthTmcdIdList)) {
            BaseResponse<Map<Long, Integer>> tmcdIdUsedCountMapResponse =
                    getAuthedCountMapByTmcdIdList(null, false, noneAuthTmcdIdList);
            if (!tmcdIdUsedCountMapResponse.checkIsSuccess()) {
                TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                return tmcdIdUsedCountMapResponse;
            }
            tmcdIdUsedCountMap.putAll(tmcdIdUsedCountMapResponse.getData());
        }
        return BaseResponse.ok(tmcdIdUsedCountMap);
    }

    @Override
    public BaseResponse getAuthStatusMapByAiopsItemIdList(List<String> aiopsItemIdList) {
        //region 参数检查

        Optional<BaseResponse> optResponse = checkParamIsEmpty(aiopsItemIdList, "aiopsItemIdList");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }
        //endregion

        Set<String> aiopsItemIdSet = aiopsItemIdList.stream()
                .filter(x -> StringUtils.isNotBlank(x))
                .collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(aiopsItemIdSet)) {
            return BaseResponse.ok(new ArrayList<>(0));
        }
        List<Map<String, String>> resultList = instanceMapper.selectAuthStatusMapByAiopsItemIdList(aiopsItemIdSet);
        if (CollectionUtils.isEmpty(resultList)) {
            return BaseResponse.ok(getUnauthMapByAiopsItemIdSet(aiopsItemIdSet));
        }
        Map<String, String> map = resultList.stream().filter(x -> x != null)
                .collect(Collectors.toMap(x -> {
                    String key = x.get("aiopsItemId");
                    aiopsItemIdSet.remove(key);
                    return key;
                }, x -> x.get("aiopsAuthStatus"), (x, y) -> y));
        if (!CollectionUtils.isEmpty(aiopsItemIdSet)) {
            map.putAll(getUnauthMapByAiopsItemIdSet(aiopsItemIdSet));
        }
        return BaseResponse.ok(map);
    }

    private Map<String, String> getUnauthMapByAiopsItemIdSet(Set<String> aiopsItemIdSet) {
        return aiopsItemIdSet.stream().collect(Collectors.toMap(x -> x, x -> "UNAUTH", (x, y) -> y));
    }

    @Override
    public BaseResponse getSNMPInstanceListByDeviceId(String deviceId, Boolean needPaging, Integer pageNum, Integer pageSize) {
        //region 参数检查

        Optional<BaseResponse> optResponse = checkParamIsEmpty(deviceId, "deviceId");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }
        //endregion

        if (BooleanUtils.toBoolean(needPaging)) {
            pageNum = IntegerUtil.isEmpty(pageNum) ? DEFAULT_PAGE_NO : pageNum;
            pageSize = IntegerUtil.isEmpty(pageSize) ? DEFAULT_PAGE_SIZE : pageSize;
            Page page = PageHelper.startPage(pageNum, pageSize);
            instanceMapper.selectSNMPInstanceListByDeviceId(deviceId);
            PageInfo<DeviceInfo> pageInfo = new PageInfo<>(page);
            return BaseResponse.ok(pageInfo);
        }
        return BaseResponse.ok(instanceMapper.selectSNMPInstanceListByDeviceId(deviceId));
    }

    @Override
    public BaseResponse getAuthedCountMapByTmcdIdList(Long eid, Boolean isContainHoldAuth, List<Long> tmcdIdList) {
        //region 参数检查

        Optional<BaseResponse> optResponse = checkParamIsEmpty(eid, "eid");
        if (optResponse.isPresent()) {
            //两个条件则一，如果两个都为空，就不允许
            optResponse = checkParamIsEmpty(tmcdIdList, "eid and tmcdIdList");
            if (optResponse.isPresent()) {
                return optResponse.get();
            }
        }
        //endregion

        List<Map<String, Object>> mapList = instanceMapper.selectAuthedCountMapByTmcdIdList(eid,
                BooleanUtils.toBoolean(isContainHoldAuth), tmcdIdList);
        if (CollectionUtils.isEmpty(mapList)) {
            return BaseResponse.ok(new HashMap<>(0));
        }

        return BaseResponse.ok(processTmcdIdAuthedCountToMap(mapList));
    }

    private Map<Long, Integer> processTmcdIdAuthedCountToMap(List<Map<String, Object>> mapList) {
        return mapList.stream().collect(Collectors.toMap(x -> LongUtil.objectToLong(x.get("tmcdId")),
                x -> IntegerUtil.objectToInteger(x.get("authedCount")), (x, y) -> y));
    }

    @Override
    public List<String> getDeviceIdListByAicList(List<AiopsItemContext> aicList) {
        return instanceMapper.selectDeviceIdListByAicList(aicList);
    }

    @Override
    public BaseResponse invalidDeviceAiopsInstanceAuthStatus(String deviceId) {
        //region 参数检查

        Optional<BaseResponse> optResponse = checkParamIsEmpty(deviceId, "deviceId");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        //endregion

        return BaseResponse.ok(instanceMapper.invalidDeviceAiopsInstanceAuthStatus(deviceId));
    }

    @Override
    public List<AiopsInstance> getAiopsInstanceByAiIdList(List<Long> aiIdList) {
        return instanceMapper.selectAiopsInstanceByAiIdList(aiIdList);
    }

    @Override
    public AiopsSNMPInstance getSNMPInstanceBySNMPId(String snmpId) {
        return instanceMapper.selectSNMPInstanceBySNMPId(snmpId);
    }

    @Override
    public AiopsBackupSoftwareInstance getBackupSoftwareInstanceByBackupSoftwareId(String backupSoftwareId) {
        return instanceMapper.selectAiopsBackupSoftwareInstanceByBackupSoftwareId(backupSoftwareId);
    }

    @Override
    public PageInfo<AiopsInstance> getInstances(String content, String aiopsItem, Long eid, Boolean showInApp, int pageNum, int pageSize, String aiopsAuthStatus) {
        Map<String, Object> map = new HashMap<>();
        map.put("content", content);
        map.put("eid", eid == null ? RequestUtil.getHeaderEid() : eid);
        map.put("aiopsItem", aiopsItem);
        map.put("showInApp", showInApp);

        //處理多筆aiopsAuthStatus
        List<String> aiopsAuthStatusList = Arrays.asList(aiopsAuthStatus.split(","));
        map.put("aiopsAuthStatusList", aiopsAuthStatusList);

        pageNum = pageNum == 0 ? Constants.General.DEFAULT_PAGE_NO : pageNum;
        pageSize = pageSize == 0 ? Constants.General.DEFAULT_PAGE_SIZE : pageSize;
        Page page = PageHelper.startPage(pageNum, pageSize);
        instanceMapper.getInstances(map);
        PageInfo<AiopsInstance> pageInfo = new PageInfo<>(page);
        return pageInfo;
    }

    @Override
    public String getAiopsItemIdByRelate(String truestInstanceTableName, Map<String, Object> businessPKValueMap,
                                         String aiopsItemIdColumnName) {
        return instanceMapper.selectAiopsItemIdByRelate(truestInstanceTableName, businessPKValueMap,
                aiopsItemIdColumnName);
    }

    @Override
    public List<String> getAiopsItemIdListByMap(Map<String, Object> map) {
        return instanceMapper.selectAiopsItemIdListByMap(map);
    }

    @Override
    public List<UploadBigDataContext> getUploadBigDataInfoByMap(Map<String, Object> map) {
        return instanceMapper.selectUploadBigDataInfoByMap(map);
    }

    @Override
    public BaseResponse getTrustDetailByAiopsItemId(String aiopsItemType, String aiopsItemId) {
        //region 参数检查

        Optional<BaseResponse> optResponse = checkParamIsEmpty(aiopsItemType, "aiopsItemType");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        optResponse = checkParamIsEmpty(aiopsItemId, "aiopsItemId");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        //endregion

        Map<String, Object> map = new HashMap<>(3);
        map.put("aiopsItemType", aiopsItemType);
        map.put("aiopsItemId", aiopsItemId);

        List<Map<String, Object>> mapList = instanceMapper.selectTrustDetailByMap(map);
        if (CollectionUtils.isEmpty(mapList)) {
            return BaseResponse.ok();
        }
        //取到只返回第一个
        return BaseResponse.ok(mapList.stream().filter(Objects::nonNull).findFirst().orElse(null));
    }

    @Override
    public BaseResponse getAiopsInstanceByClassCode(MergeStatisticsDetailRequest request) {
        //region 参数检查

        Optional<BaseResponse> optResponse = checkParamIsEmpty(request, "request");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        Long eid = request.getEid();
        optResponse = checkParamIsEmpty(eid, "request.eid");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        String classCode = request.getClassCode();
        optResponse = checkParamIsEmpty(classCode, "request.classCode");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        Boolean needPaging = BooleanUtils.toBoolean(request.getNeedPaging());

        //endregion

        //串查samcdId列表
        BaseResponse<List<Long>> response = aioUserFeignClient.getSamcdIdListByClassCode(classCode);
        if (!response.checkIsSuccess()) {
            return response;
        }
        List<Long> samcdIdList = response.getData();
        if (CollectionUtils.isEmpty(samcdIdList)) {
            return BaseResponse.ok(new ArrayList<>(0));
        }
        Map<String, Object> map = BeanUtil.object2Map(request);
        map.put("samcdIdList", samcdIdList);
        processAiopsAuthStatusListCondition(request.getAiopsAuthStatusList(), map);

        return getMergeStatisticsDetailCore(needPaging, request.getPageNum(), request.getPageSize(), map);
    }

    @Override
    public BaseResponse getAiopsInstanceAssetList(MergeStatisticsDetailRequest request) {
//region 参数检查

        Optional<BaseResponse> optResponse = checkParamIsEmpty(request, "request");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        Long eid = request.getEid();
        optResponse = checkParamIsEmpty(eid, "request.eid");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        List<String> aiopsItemList = request.getAiopsItemList();
        optResponse = checkParamIsEmpty(aiopsItemList, "request.aiopsItemList");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        Boolean needPaging = BooleanUtils.toBoolean(request.getNeedPaging());

        //endregion
        List<Map<String, Object>> checkInInstanceList = getCheckInInstanceList(eid, aiopsItemList);
        List<String> instanceIdList = checkInInstanceList.stream()
                .filter(o -> o.get("DEVICEID") != null && !("").equals(o.get("DEVICEID")))
                .map(o -> o.get("DEVICEID").toString())
                .collect(Collectors.toList());
        request.setInstanceIdList(instanceIdList);
        Map<String, Object> map = BeanUtil.object2Map(request);
        processAiopsAuthStatusListCondition(request.getAiopsAuthStatusList(), map);

        return getMergeStatisticsDetailCore(needPaging, request.getPageNum(), request.getPageSize(), map);
    }

    @Override
    public BaseResponse<List<com.digiwin.escloud.aioitms.model.instance.MergeStatisticsDetailInfo>> getCommonAiopsInstanceAssetListNoPage
            (com.digiwin.escloud.aioitms.model.instance.MergeStatisticsDetailRequest request) {

        Optional<BaseResponse> optResponse = checkParamIsEmpty(request, "request");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        List<String> aiopsItemList = request.getAiopsItemList();
        optResponse = checkParamIsEmpty(aiopsItemList, "request.aiopsItemList");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        //endregion
        Map<String, Object> map = BeanUtil.object2Map(request);
        processAiopsAuthStatusListCondition(request.getAiopsAuthStatusList(), map);
        BaseResponse<List<MergeStatisticsDetailInfo>> mergeStatisticsDetailNoPageCore = getMergeStatisticsDetailNoPageCore(map);
        List<com.digiwin.escloud.aioitms.model.instance.MergeStatisticsDetailInfo> collect = mergeStatisticsDetailNoPageCore.getData()
                .stream()
                .map(o -> {
                    com.digiwin.escloud.aioitms.model.instance.MergeStatisticsDetailInfo ret =
                            new com.digiwin.escloud.aioitms.model.instance.MergeStatisticsDetailInfo();
                    BeanUtils.copyProperties(o, ret);
                    return ret;

                }).collect(Collectors.toList());
        return BaseResponse.okT(collect);
    }

    private List<Map<String, Object>> getCheckInInstanceList(Long eid, List<String> aiopsItemList) {
        boolean hasEsxi = aiopsItemList.stream().anyMatch(o -> "ESXI".equals(o));
        if (hasEsxi) {
            aiopsItemList.add("Esxi_Host");
            aiopsItemList.add("Esxi_Storage");
        }
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < aiopsItemList.size(); i++) {
            sb.append(" select ROWKEY, EID , get_json_object(model,'$.BasicInfo.deviceId')  as \"DEVICEID\" ");
            sb.append(" from RS_" + aiopsItemList.get(i));
            sb.append(" where EID = '" + eid).append("'");
            if (i < aiopsItemList.size() - 1) {
                sb.append(" union all ");
            }
        }
        return bigDataUtil.phoenixQuery(sb.toString());
    }

    private BaseResponse getMergeStatisticsDetailCore(Boolean needPaging, Integer pageNum, Integer pageSize,
                                                      Map<String, Object> map) {
        //查询
        if (BooleanUtils.toBoolean(needPaging)) {
            pageNum = IntegerUtil.isEmpty(pageNum) ? DEFAULT_PAGE_NO : pageNum;
            pageSize = IntegerUtil.isEmpty(pageSize) ? DEFAULT_PAGE_SIZE : pageSize;
            Page page = PageHelper.startPage(pageNum, pageSize);
            instanceMapper.selectMergeStatisticsDetailByMap(map);
            PageInfo<MergeStatisticsDetailInfo> pageInfo = new PageInfo<>(page);
            if (pageInfo != null) {
                pageInfo.setList(addSNMPStatusToList2(pageInfo.getList(), map.get("eid") != null ? map.get("eid").toString() : null));
            }
            return BaseResponse.ok(pageInfo);
        }
        List<MergeStatisticsDetailInfo> list = instanceMapper.selectMergeStatisticsDetailByMap(map);
        return BaseResponse.ok(addSNMPStatusToList2(list, map.get("eid") != null ? map.get("eid").toString() : null));
    }

    private BaseResponse<List<MergeStatisticsDetailInfo>> getMergeStatisticsDetailNoPageCore(Map<String, Object> map) {
        //查询
        List<MergeStatisticsDetailInfo> list = instanceMapper.selectMergeStatisticsDetailByMap(map);
        return BaseResponse.okT(addSNMPStatusToList2(list, map.get("eid") != null ? map.get("eid").toString() : null));
    }

    public List<MergeStatisticsDetailInfo> addSNMPStatusToList2(List<MergeStatisticsDetailInfo> list, String eid) {
        try {
            if (!CollectionUtils.isEmpty(list)) {
                String aiIds = list.stream().filter(k -> k.getAiopsInstanceId() != null).map(o -> String.valueOf(o.getAiopsInstanceId())).collect(Collectors.joining(","));
                if (StringUtils.isEmpty(aiIds)) {
                    list.forEach(k -> {
                        k.setSnmpStatus("-");
                        k.setSecond("-");
                    });
                } else {
                    StringBuffer sf = new StringBuffer();
                    //3天内才算在线
                    sf.append("SELECT a.aiId,TIMESTAMPDIFF(SECOND, from_unixtime(flumeTimestamp/1000), NOW()) second FROM servicecloud.OfflineCheckCollected_sr_primary a ");
                    sf.append(" where a.aiId in (").append(aiIds).append(")");
                    if (eid != null) {
                        sf.append(" and a.eid = '").append(eid).append("'");
                    }
                    sf.append(" order by a.collectedTime desc");
                    List<Map<String, Object>> dataList = bigDataUtil.srQuery(sf.toString());
                    if (!CollectionUtils.isEmpty(dataList)) {
                        list.forEach(k -> {
                            k.setSnmpStatus(dealSnmpStatus(dataList, k.getAiopsInstanceId()));
                            k.setSecond(dealSnmpSecond(dataList, k.getAiopsInstanceId()));
                        });
                    } else {
                        list.forEach(k -> {
                            k.setSnmpStatus("-");
                            k.setSecond("-");
                        });
                    }
                }
            }
        } catch (Exception e) {
            System.out.println("addSNMPStatusToList2:" + e);
        }

        return list;
    }

    @Override
    public BaseResponse getAiTrustCommonInfoByAiId(Long aiId) {
        //region 参数检查

        Optional<BaseResponse> optResponse = checkParamIsEmpty(aiId, "aiId");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        //endregion

        Map<String, Object> map = new HashMap<>(1);
        map.put("aiId", aiId);

        List<Map<String, Object>> mapList = instanceMapper.selectAiTrustCommonInfoByMap(map);
        if (CollectionUtils.isEmpty(mapList)) {
            return BaseResponse.ok();
        }
        //取到只返回第一个
        return BaseResponse.ok(mapList.stream().filter(Objects::nonNull).findFirst().orElse(null));
    }

    @Override
    public BaseResponse fixAiopsInstanceTmcdIdByMapList(List<Map<String, Object>> mapList) {
        if (CollectionUtils.isEmpty(mapList)) {
            return BaseResponse.ok();
        }
        //检查必要参数(eid、tmcdId、samcdIdList)
        mapList = mapList.stream()
                .filter(x -> LongUtil.isNotEmpty(LongUtil.objectToLong(x.get("eid"))))
                .filter(x -> LongUtil.isNotEmpty(LongUtil.objectToLong(x.get("tmcdId"))))
                .filter(x -> CollectionUtil.isNotEmpty(CollectionUtil.objectToCollection(x.get("samcdIdList"))))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(mapList)) {
            return BaseResponse.ok();
        }
        Integer effectRow = instanceMapper.updateAiopsInstanceTmcdIdByMapList(mapList);
        if (effectRow == 0) {
            return BaseResponse.ok();
        }
        //有影响笔数，返回数量进行更新
        Map<Boolean, List<Long>> isContainHoldAuthGroupMap = mapList.stream()
                .collect(Collectors.groupingBy(x -> BooleanUtil.objectToBoolean(x.get("isContainHoldAuth")),
                        Collectors.mapping(x -> LongUtil.objectToLong(x.get("tmcdId")), Collectors.toList())));
        Map<Boolean, Map<Long, Integer>> resultMap = new HashMap<>();
        isContainHoldAuthGroupMap.forEach((k, v) -> {
            BaseResponse<Map<Long, Integer>> response = getAuthedCountMapByTmcdIdList(null, k, v);
            if (!response.checkIsSuccess()) {
                return;
            }
            resultMap.put(k, response.getData());
        });
        return BaseResponse.ok(resultMap);
    }

    @Override
    public BaseResponse getAiopsInstanceRelateTenantList(Map<String, Object> map) {
        //region 参数检查

        Optional<BaseResponse> optResponse = checkParamIsEmpty(map, "map");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        //endregion

        boolean needPaging = BooleanUtil.objectToBoolean(map.get("needPaging"));
        List<Map<String, Object>> mapList;
        Page<Map<String, Object>> page;
        if (needPaging) {
            int pageNum = PageUtil.getPageNum(IntegerUtil.objectToInteger(map.get("pageNum")));
            int pageSize = PageUtil.getPageSize(IntegerUtil.objectToInteger(map.get("pageSize")));
            page = PageHelper.startPage(pageNum, pageSize);
            //串查使用到的eid
            instanceMapper.selectAiRelateEidList(map);
            mapList = page.getResult();
        } else {
            page = null;
            //串查使用到的eid
            mapList = instanceMapper.selectAiRelateEidList(map);
        }
        if (CollectionUtils.isEmpty(mapList)) {
            if (needPaging) {
                return BaseResponse.ok(new PageInfo<>(page));
            }
            return BaseResponse.ok(new ArrayList<>(0));
        }

        //根据eid串查客户名称、客代信息
        TenantNameListResponse response = aioUserFeignClient.tenantServiceCodeGet(mapList.stream()
                .map(x -> LongUtil.objectToLong(x.get("eid"))).collect(Collectors.toList()));
        if (!response.checkIsSuccess()) {
            return BaseResponse.error(response.getCode(), response.getErrMsg());
        }
        List<TenantMapDTO> tenantMapDTOList = response.getTenantMapDTO();
        if (CollectionUtils.isEmpty(tenantMapDTOList)) {
            if (needPaging) {
                return BaseResponse.ok(new PageInfo<>(page));
            }
            return BaseResponse.ok(mapList);
        }
        //产生字典
        Map<Long, TenantMapDTO> eidGroupMap = tenantMapDTOList.stream()
                .collect(Collectors.toMap(TenantMapDTO::getEid, x -> x, (x, y) -> y));
        mapList.forEach(x -> {
            Long eid = LongUtil.objectToLong(x.get("eid"));
            TenantMapDTO current = eidGroupMap.get(eid);
            if (Objects.nonNull(current)) {
                x.putAll(BeanUtil.object2Map(current));
                x.put("nameServiceCode", (current.getName() + "(" +
                        current.getServiceCode() + ")"));
            }
        });
        if (needPaging) {
            return BaseResponse.ok(new PageInfo<>(page));
        }
        //未分页有机会做排序后返回
        return BaseResponse.ok(mapList.stream()
                .sorted(Comparator.comparing(x -> Objects.toString(x.get("name"), ""))));
    }

    @Override
    public BaseResponse<List<Map<String, Object>>> getAiAuthedCountByTmcdIdList(Collection<Long> tmcdIdList) {
        Optional<BaseResponse> optResponse = checkParamIsEmpty(tmcdIdList, "tmcdIdList");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        return BaseResponse.ok(instanceMapper.selectAiAuthedCountByTmcdIdList(tmcdIdList));
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public BaseResponse fixAiopsInstanceStatusByTmcdIdList(List<AiopsInstanceFixParam> aiParamList) {
        List<Map<String, Object>> tmcdUseCountList = new ArrayList<>();
        aiParamList.forEach(tmcd -> {

            List<Long> aiIdList = instanceMapper.selectAiIdByTmcdId(tmcd.getTmcdId(), tmcd.getAvailableCount());
            if (!CollectionUtils.isEmpty(aiIdList)) {
                instanceMapper.updateAiAuthRecoverAuth(aiIdList);
                //修复授权状态
                instanceMapper.updateRuleEngineAuthStatus(aiIdList);
                Map<String, Object> tmcdUseCountMap = new HashMap<>();
                tmcdUseCountMap.put("tmcdId", tmcd.getTmcdId());
                tmcdUseCountMap.put("useCount", aiIdList.size());
                tmcdUseCountList.add(tmcdUseCountMap);

                //清理设备缓存
                List<String> deviceIdList = instanceMapper.selectDeviceIdByAiIdList(aiIdList);
                log.info("[fixAiopsInstanceStatusByTmcdIdList] clear device cache : {}", deviceIdList);
                deviceIdList.forEach(deviceId -> collectConfigService.clearDeviceIdCollectConfigCache(deviceId));

            }
        });

        // 修正合约useCount
        if (!CollectionUtils.isEmpty(tmcdUseCountList)) {
            ResponseBase responseBase = aioUserFeignClient.fixTmcdUseCount(tmcdUseCountList);
            if (!responseBase.checkIsSuccess()) {
                throw new RuntimeException("[fixAiopsInstanceStatusByTmcdIdList] fixTmcdUseCount error !");
            }
        }
        return BaseResponse.ok();
    }

    @Override
    public BaseResponse tpInstanceSave(AiopsTpModuleInstance aiopsTpModuleInstance) {
        //region 参数检查

        Optional<BaseResponse> optResponse = checkParamIsEmpty(aiopsTpModuleInstance, "aiopsTpModuleInstance");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        Long eid = aiopsTpModuleInstance.getEid();
        optResponse = checkParamIsEmpty(eid, "aiopsTpModuleInstance.eid");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        String deviceId = aiopsTpModuleInstance.getDeviceId();
        optResponse = checkParamIsEmpty(deviceId, "aiopsTpModuleInstance.deviceId");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        String aiopsItem = aiopsTpModuleInstance.getAiopsItem();
        optResponse = checkParamIsEmpty(aiopsItem, "aiopsTpModuleInstance.aiopsItem");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        String tpModule = aiopsTpModuleInstance.getTpModule();
        optResponse = checkParamIsEmpty(tpModule, "aiopsTpModuleInstance.tpModule");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        //endregion
        aiopsTpModuleInstance.fixAiopsItemId();

        //检查是否已经存在，第三方设备实例 是唯一的 如果根据规则发现实例已经存在 就是更新操作
        String aiopsItemId = aiopsTpModuleInstance.getAiopsItemId();
        AiopsTpModuleInstance existTpModuleInstance = instanceMapper.selectAiopsTpInstanceByAiopsItemId(aiopsItemId);
        List<AiopsTpModuleInstance> existTpModuleInstanceList = instanceMapper.selectDeviceTpInstanceByDeviceIdAndEid(eid,
                deviceId, tpModule);
        if (existTpModuleInstance != null && !existTpModuleInstance.getId().equals(aiopsTpModuleInstance.getId())) {
            aiopsTpModuleInstance.setId(existTpModuleInstance.getId());
        }
        if (!CollectionUtils.isEmpty(existTpModuleInstanceList)) {
            AiopsTpModuleInstance aiopsTpModuleInstance2 = existTpModuleInstanceList.stream().findFirst().orElse(new AiopsTpModuleInstance()); //修复漏洞 空指针问题
            aiopsTpModuleInstance.setId(aiopsTpModuleInstance2.getId());
        }
        Long resultAiId = 0L;
        Long atmiId = aiopsTpModuleInstance.getId();
        String oldAiopsItemId;
        if (LongUtil.isEmpty(atmiId)) {
            aiopsTpModuleInstance.setId(SnowFlake.getInstance().newId());
            oldAiopsItemId = "";
        } else {
            //查询已有的实例，并判断 AiopsItemId 是否异动
            oldAiopsItemId = instanceMapper.selectTpAiopsItemIdByAtmiId(atmiId);
            if (StringUtils.isNotBlank(oldAiopsItemId) && !oldAiopsItemId.equals(aiopsTpModuleInstance.getAiopsItemId())) {
                //若 AiopsItemId 异动，查询是否已经存在该运维实例
                //查出已有的 AiopsItemId 运维实例，进行迁移
                BaseResponse<Long> response = getAiIdByAiopsItemId(oldAiopsItemId);
                if (!response.checkIsSuccess()) {
                    return response;
                }
                //若有后面要针对已有的运维实例做大迁移(包括设备收集项明细的执行参数)
                resultAiId = response.getData();
//                checkAndFixExecParamsContent(aiopsHttpInstance); // 目前不会用到
            }
        }

        //创建三方设备实例
        instanceMapper.batchInsertOrUpdateTpModuleInstance(Stream.of(aiopsTpModuleInstance).collect(Collectors.toList()));
        BaseResponse response = syncTrustInstanceToDcdp(aiopsItemId, oldAiopsItemId);
        if (!response.checkIsSuccess()) {
            return response;
        }
        return BaseResponse.ok(resultAiId);
    }

    @Override
    public String selectAiopsInstanceAiopsItemByDeviceId(String deviceId) {
        return instanceMapper.selectAiopsInstanceAiopsItemByDeviceId(deviceId);
    }

    @Override
    public BaseResponse<List<EdrDevice>> getEdrServerId(String deviceId) {
        List<EdrDevice> serverIdList = instanceMapper.selectEdrServerIdList(deviceId);
        return BaseResponse.okT(serverIdList);
    }

    @Override
    public BaseResponse getAccByAiopsItemList(List<String> aiopsItemList) {
        List<CollectConfigDTO> collectConfigList = instanceMapper.selectAccByAiopsItem(aiopsItemList);

        return BaseResponse.ok(collectConfigList);
    }

    private List<Map<String, Object>> processCollectListToAdd(List<AiopsModuleCollect> moduleCollectDetail,
                                                              List<AiopsInstanceCollectMapping> instanceDetail) {
        // 建立collectCode到moduleCollect的映射
        Map<String, List<AiopsModuleCollect>> moduleCollectMap = moduleCollectDetail.stream()
                .collect(Collectors.groupingBy(AiopsModuleCollect::getCollectCode));

        Set<String> existingCollectCodes = instanceDetail.stream()
                .map(AiopsInstanceCollectMapping::getCollectCode)
                .collect(Collectors.toSet());

        return moduleCollectMap.entrySet().stream()
                .filter(entry -> !existingCollectCodes.contains(entry.getKey()))
                .map(entry -> {
                    List<AiopsModuleCollect> moduleCollects = entry.getValue();
                    AiopsModuleCollect firstCollect = moduleCollects.get(0);

                    Map<String, Object> map = new HashMap<>();
                    map.put("id", SnowFlake.getInstance().newId());
                    map.put("accId", firstCollect.getAccId());
                    map.put("collectName", firstCollect.getCollectName());
                    map.put("aiId", instanceDetail.stream()
                            .map(AiopsInstanceCollectMapping::getAiId)
                            .findFirst()
                            .orElseThrow(() -> new RuntimeException("AddNewInstanceCollectDetail Error: AiId Not Found")));

                    // 收集所有相同collectCode的acwId
                    List<String> acwIds = moduleCollects.stream()
                            .map(AiopsModuleCollect::getAcwId)
                            .filter(Objects::nonNull)
                            .distinct()
                            .collect(Collectors.toList());
                    map.put("acwIds", acwIds);
                    return map;
                })
                .collect(Collectors.toList());
    }

    private List<Map<String, Object>> processWarningListToAdd(List<AiopsModuleCollect> moduleCollectDetail,
                                                              List<AiopsInstanceCollectMapping> instanceDetail,
                                                              List<Map<String, Object>> toAddCollectList) {
        List<Map<String, Object>> toAddWarningList = new ArrayList<>();

        // 1. 處理新增收集項的預警項
        toAddWarningList.addAll(processNewCollectWarnings(toAddCollectList));

        // 2. 處理既有收集項的新預警項
        toAddWarningList.addAll(processExistingCollectWarnings(moduleCollectDetail, instanceDetail));

        return toAddWarningList;
    }

    // 處理新增收集項的預警項
    private List<Map<String, Object>> processNewCollectWarnings(List<Map<String, Object>> toAddCollectList) {
        return toAddCollectList.stream()
                .filter(collectMap -> Objects.nonNull(collectMap.get("acwIds")))
                .flatMap(collectMap -> {
                    @SuppressWarnings("unchecked")
                    List<String> acwIds = (List<String>) collectMap.get("acwIds");
                    return acwIds.stream().map(acwId -> {
                        Map<String, Object> map = new HashMap<>();
                        map.put("id", SnowFlake.getInstance().newId());
                        map.put("adcdId", collectMap.get("id"));
                        map.put("acwId", acwId);
                        return map;
                    });
                })
                .collect(Collectors.toList());
    }

    // 處理既有收集項的新預警項
    private List<Map<String, Object>> processExistingCollectWarnings(
            List<AiopsModuleCollect> moduleCollectDetail,
            List<AiopsInstanceCollectMapping> instanceDetail) {

        // 以預警項Code為key建立Map
        Map<String, List<AiopsModuleCollect>> warningToModuleMap = moduleCollectDetail.stream()
                .filter(x -> Objects.nonNull(x.getAcwId()))
                .collect(Collectors.groupingBy(AiopsModuleCollect::getWarningCode));

        // 以收集項Code為key建立Map
        Map<String, List<AiopsInstanceCollectMapping>> collectCodeToInstanceMap = instanceDetail.stream()
                .filter(x -> Objects.nonNull(x.getCollectCode()))
                .collect(Collectors.groupingBy(AiopsInstanceCollectMapping::getCollectCode));

        // 取得已有的預警項code清單
        Set<String> existingWarningCodes = instanceDetail.stream()
                .map(AiopsInstanceCollectMapping::getWarningCode)
                .collect(Collectors.toSet());

        return warningToModuleMap.entrySet().stream()
                .filter(entry -> !existingWarningCodes.contains(entry.getKey()))
                .map(entry -> {
                    List<AiopsModuleCollect> moduleCollects = entry.getValue();

                    // 找到對應的collectCode
                    String collectCode = moduleCollects.get(0).getCollectCode();
                    List<AiopsInstanceCollectMapping> instanceMappings = collectCodeToInstanceMap.get(collectCode);
                    if (CollectionUtil.isEmpty(instanceMappings)) {
                        return null;
                    }

                    // 如果是既有的理應會是同一個adcdId，所以取第一筆就行
                    AiopsInstanceCollectMapping instanceMapping = instanceMappings.get(0);
                    if (instanceMapping == null) {
                        return null;
                    }

                    Map<String, Object> map = new HashMap<>();
                    map.put("id", SnowFlake.getInstance().newId());
                    map.put("adcdId", instanceMapping.getAdcdId());
                    map.put("acwId", moduleCollects.get(0).getAcwId());
                    return map;
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    private List<DeviceCollectWarning> getAdcwList(List<Map<String, Object>> toAddWarningList) {
        return toAddWarningList.stream().map(x -> {
            DeviceCollectWarning adcwInfo = new DeviceCollectWarning();
            adcwInfo.setId(LongUtil.objectToLong(x.get("id")));
            adcwInfo.setAdcdId(LongUtil.objectToLong(x.get("adcdId")));
            adcwInfo.setAcwId(LongUtil.objectToLong(x.get("acwId")));
            adcwInfo.setWarningEnable(Boolean.TRUE);    // 預設開啟
            adcwInfo.setReAuthStatus(Boolean.TRUE);     // 預設開啟

            return adcwInfo;
        }).collect(Collectors.toList());
    }
}
