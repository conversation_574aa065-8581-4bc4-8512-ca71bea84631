package com.digiwin.escloud.aioitms.report.model.serviceReport;

import com.digiwin.escloud.aiobasic.report.model.*;
import com.digiwin.escloud.aioitms.es.model.EsBase;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.data.elasticsearch.annotations.Document;

import java.util.Date;
import java.util.List;
import java.util.Map;

@EqualsAndHashCode(callSuper = true)
@ToString
@Data
@Document(indexName="vulnerability_es_report")
public class VulnerabilityEsReport extends EsBase {


        private String reportId;

        private String customerName;
        private String reportName;

        private String projectName;
        //专案执行日期
        private String projectExecuteDate;
        //制作日志

        private String reportGenerateDate;

        private String scanTool;

        private String year;

        private String scanBatch;
        private String scanRangeDesc;

        private String version;

        private String generateType;

        private String projectPurpose;
        //- 专案执行项目
        private String projectExecute;

        private String scanExecuteTool;

        private String scanType;

        private String repairSuggestion;

        private String vulnerabilityAbstractDesc;

        private String repairAbstractSuggestion;

        private String scanExecuteToolResult;

        private List<VulnerabilityProjectAsset> projectAssetList;

        private List<VulnerabilityStorehouse> vulnerabilityStorehouseList;
        private List<VulnerabilityProjectScanRecord> vulnerabilityProjectScanRecordList;
        private List<Map<String,Object>> vulnerabilityProjectAssetLevelList;
        private Map<String,Integer> vulnerabilityLevelList;
        private List<VulnerabilityProjectScanRecord2> vulnerabilityProjectScanRecordAppend;

        private String createDate;

        private String updateDate;


        private Integer vulnerabilityLanguage;
}
