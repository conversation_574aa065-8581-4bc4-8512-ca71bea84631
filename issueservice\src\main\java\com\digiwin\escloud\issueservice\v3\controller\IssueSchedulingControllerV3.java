package com.digiwin.escloud.issueservice.v3.controller;

import com.digiwin.escloud.issueservice.v3.service.IIssueSchedulingServiceV3;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Calendar;


@Api(value = "案件排程相关")
@RestController
@RequestMapping("/v3/issuescheduling")
@Slf4j
public class IssueSchedulingControllerV3 {

    @Autowired
    IIssueSchedulingServiceV3 iIssueSchedulingService;
    /**
     * TOPGP,T100产品的非个案结案
     */
    @ApiOperation(value = "TOPGP,T100产品的非个案自动结案")
    @RequestMapping(value = "/autoCloseIssue", method = RequestMethod.POST)
    public void autoCloseIssue() {
        iIssueSchedulingService.autoCloseIssue();
    }

    /**
     * 定时将抛转187异常的案件重新抛转到187
     * @return
     */
    @RequestMapping(value = "/issue/rePostCaseProcessRecords/auto", method = RequestMethod.POST)
    public void reSend187() {
        iIssueSchedulingService.rePostCaseProcessRecords();
    }

    @ApiOperation(value = "定时任务：每天凌晨生成昨天已结案的案件知识")
    @PostMapping(value = "/build/issue/kb", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public void buildIssueKb(@RequestParam(value="closeDate", required = false, defaultValue = "") String closeDate,
                             @RequestParam(value="ikcId", required = false, defaultValue = "") Long ikcId) {
        //当closeDate不传，取前一天的日期。
        if(StringUtils.isEmpty(closeDate)){
            DateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            Calendar calendar = Calendar.getInstance();
            calendar.add(Calendar.DATE, -1);
            closeDate = sdf.format(calendar.getTime());
        }
        iIssueSchedulingService.buildIssueKb(closeDate, ikcId);
    }

}
