<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.digiwin.escloud.aiocmdb.model.dao.ModelMapper">
    <resultMap id="ModelDetailMap" type="com.digiwin.escloud.aiocmdb.model.model.ModelDetail">
        <result property="sid" column="sid"/>
        <result property="eid" column="eid"/>
        <result property="modelCode" column="modelCode"/>
        <result property="modelName" column="modelName"/>
        <result property="modelGroupCode" column="modelGroupCode"/>
        <result property="modelGroupName" column="modelGroupName"/>
        <result property="fold" column="modelGroupFold"/>
        <result property="imgUrl" column="imgUrl"/>
        <result property="description" column="description"/>
        <result property="status" column="status"/>
        <collection property="modelFieldGroupList" resultMap="modelFieldGroupMap" />
    </resultMap>
    <resultMap id="modelFieldGroupMap" type="com.digiwin.escloud.aiocmdb.model.model.ModelFieldGroup">
        <result property="id" column="id"/>
        <result property="modelFieldGroupCode" column="modelFieldGroupCode"/>
        <result property="modelFieldGroupName" column="modelFieldGroupName"/>
        <result property="fold" column="modelFieldGroupFold"/>
        <collection property="modelFieldMappingList" resultMap="modelFieldMappingMap" />
    </resultMap>
    <resultMap id="modelFieldMappingMap" type="com.digiwin.escloud.aiocmdb.model.model.ModelFieldMapping">
        <result property="id" column="modelFieldMappingId"/>
        <result property="modelFieldGroupCode" column="modelFieldGroupCode"/>
        <result property="modelSettingType" column="modelSettingType"/>
        <result property="targetCode" column="targetCode"/>
        <result property="collection" column="collection"/>
        <result property="hide" column="hide"/>
        <result property="showTitle" column="showTitle"/>
        <result property="sort" column="sort"/>
        <result property="key" column="key"/>
        <result property="advance" column="advance"/>
        <association property="field" resultMap="fieldSetfieldMap" columnPrefix="f_"/>
        <association property="fieldSet" resultMap="fieldSetMap" />
    </resultMap>
    <resultMap id="fieldSetMap" type="com.digiwin.escloud.aiocmdb.fieldset.model.FieldSet">
        <result property="fieldSetCode" column="fieldSetCode"/>
        <result property="fieldSetName" column="fieldSetName"/>
        <result property="fieldSetGroupCode" column="fieldSetGroupCode"/>
        <association property="modelFieldFomula" resultMap="modelFieldFomula" columnPrefix="mfsf_"/>
        <collection property="fieldList" resultMap="fieldMap" columnPrefix="fs_"/>
    </resultMap>
    <resultMap id="fieldSetfieldMap" type="com.digiwin.escloud.aiocmdb.field.model.Field">
        <result property="id" column="fieldId"/>
        <result property="appCode" column="appCode"/>
        <result property="fieldCode" column="fieldCode"/>
        <result property="fieldName" column="fieldName"/>
        <result property="fieldType" column="fieldType"/>
        <result property="fieldTypeSettingJson" column="fieldTypeSettingJson"/>
        <result property="moduleId" column="moduleId"/>
        <result property="edit" column="isEdit"/>
        <result property="required" column="isRequired"/>
        <result property="autoCollection" column="isAutoCollection"/>
        <result property="summaryMethod" column="summaryMethod"/>
        <result property="regularCheck" column="regularCheck"/>
        <result property="tips" column="tips"/>
        <result property="regularTips" column="regularTips"/>
        <result property="defaultValue" column="defaultValue"/>
        <result property="description" column="description"/>
        <result property="format" column="format"/>
        <result property="system" column="system"/>
        <result property="width" column="width"/>
        <result property="sid" column="sid"/>
        <result property="eid" column="eid"/>
        <result property="offsetLeft" column="offsetLeft"/>
        <result property="offsetRight" column="offsetRight"/>
        <association property="modelFieldFomula" resultMap="modelFieldFomula" columnPrefix="mfff_"/>
        <collection property="fieldTypeEnumList" resultMap="FieldTypeEnumMap" columnPrefix="f_enum_"/>
    </resultMap>
    <resultMap id="fieldMap" type="com.digiwin.escloud.aiocmdb.field.model.Field">
        <result property="key" column="key"/>
        <result property="fieldCode" column="fieldCode"/>
        <result property="fieldName" column="fieldName"/>
        <result property="fieldType" column="fieldType"/>
        <result property="moduleId" column="moduleId"/>
        <result property="edit" column="isEdit"/>
        <result property="required" column="isRequired"/>
        <result property="autoCollection" column="isAutoCollection"/>
        <result property="summaryMethod" column="summaryMethod"/>
        <result property="regularCheck" column="regularCheck"/>
        <result property="tips" column="tips"/>
        <result property="regularTips" column="regularTips"/>
        <result property="defaultValue" column="defaultValue"/>
        <result property="description" column="description"/>
        <result property="format" column="format"/>
        <result property="system" column="system"/>
        <result property="width" column="width"/>
        <result property="offsetLeft" column="offsetLeft"/>
        <result property="offsetRight" column="offsetRight"/>
        <association property="modelFieldFomula" resultMap="modelFieldFomula" columnPrefix="mff_"/>
        <collection property="fieldTypeEnumList" resultMap="FieldTypeEnumMap" columnPrefix="fs_enum_"/>
    </resultMap>
    <resultMap id="modelFieldFomula" type="com.digiwin.escloud.aiocmdb.model.model.ModelFieldFomula">
        <result property="id" column="id"/>
        <result property="fieldCode" column="fieldCode"/>
        <result property="modelSettingType" column="modelSettingType"/>
        <result property="formula" column="formula"/>
        <result property="style" column="style"/>
        <result property="triggerScript" column="triggerScript"/>
        <result property="modelFieldMappingId" column="modelFieldMappingId"/>
    </resultMap>

    <resultMap id="FieldTypeEnumMap" type="com.digiwin.escloud.aiocmdb.field.model.FieldTypeEnum">
        <result property="fieldCode" column="fieldCode"/>
        <result property="enumCode" column="enumCode"/>
        <result property="enumName" column="enumName"/>
        <result property="sort" column="sort"/>
    </resultMap>

    <resultMap id="ModelGroup" type="com.digiwin.escloud.aiocmdb.model.model.ModelGroup">
        <result property="id" column="id"/>
        <result property="appCode" column="appCode"/>
        <result property="modelGroupCode" column="modelGroupCode"/>
        <result property="modelGroupName" column="modelGroupName"/>
        <result property="fold" column="fold"/>
        <result property="sid" column="sid"/>
        <result property="eid" column="eid"/>
        <result property="modelGroupFunctionCode" column="modelGroupFunctionCode"/>
        <collection property="modellist" resultMap="ModelMap" columnPrefix="m_" />
    </resultMap>
    <resultMap id="ModelMap" type="com.digiwin.escloud.aiocmdb.model.model.Model">
        <result property="id" column="id"/>
        <result property="appCode" column="appCode"/>
        <result property="modelGroupCode" column="modelGroupCode"/>
        <result property="modelCode" column="modelCode"/>
        <result property="modelName" column="modelName"/>
        <result property="status" column="status"/>
        <result property="imgUrl" column="imgUrl"/>
        <result property="description" column="description"/>
        <result property="sid" column="sid"/>
        <result property="eid" column="eid"/>
    </resultMap>
    <resultMap id="ModelGroupFieldMap" type="com.digiwin.escloud.aiocmdb.model.model.ModelGroupField">
        <result property="id" column="id"/>
        <result property="modelGroupCode" column="modelGroupCode"/>
        <result property="fieldCode" column="fieldCode"/>
        <result property="sort" column="sort"/>
        <result property="sid" column="sid"/>
        <result property="key" column="key"/>
        <association property="field" columnPrefix="f_" resultMap="fieldMap"/>
    </resultMap>

    <sql id="getAllCmDisplayColumn">
        <choose>
            <when test='${specialIdAlias} != ""'>
                ${alias}id AS ${asPrefix}${specialIdAlias},
            </when>
            <otherwise>
                ${alias}id AS ${asPrefix}id,
            </otherwise>
        </choose>
        ${alias}appCode AS ${asPrefix}appCode,
        ${alias}sid AS ${asPrefix}sid,
        ${alias}eid AS ${asPrefix}eid,
        ${alias}modelCode AS ${asPrefix}modelCode,
        ${alias}modelName AS ${asPrefix}modelName,
        ${alias}modelVersion AS ${asPrefix}modelVersion,
        ${alias}modelGroupCode AS ${asPrefix}modelGroupCode,
        ${alias}status AS ${asPrefix}status,
        ${alias}imgUrl AS ${asPrefix}imgUrl,
        ${alias}description AS ${asPrefix}description
    </sql>

    <select id="getModelGroupList" resultType="com.digiwin.escloud.aiocmdb.model.model.ModelGroup">
        SELECT *
        FROM cmdb_model_group mg
        <where>
            <if test="sid != null and sid > 0">
                AND mg.sid = #{sid}
            </if>
            <if test="appCode != null and appCode != ''">
                AND mg.appCode = #{appCode}
            </if>
            <if test="appCodeList != null">
                <foreach collection="appCodeList" item="item" open=" AND mg.appCode IN (" separator=", " close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        ORDER BY mg.modelGroupCode
    </select>

    <select id="getModelGroup" resultType="com.digiwin.escloud.aiocmdb.model.model.ModelGroup">
        SELECT *
        FROM cmdb_model_group mg
        where mg.id = #{id}
    </select>

    <select id="getFieldInModelGroup"
            resultType="com.digiwin.escloud.aiocmdb.model.dto.InnerFieldDTO">
        select distinct min(gf.id) as id,cf.fieldCode,cf.fieldName,max(CASE WHEN modelGroupCode = #{modelGroupCode} THEN true ELSE false END) AS selected
                      ,max(CASE WHEN modelGroupCode = #{modelGroupCode} and gf.`key` = true THEN true ELSE false END) AS `key`
        from cmdb_model_group_field gf ,cmdb_field cf
        where gf.fieldCode= cf.fieldCode
        group by cf.fieldCode,cf.fieldName
    </select>

    <select id="getModelGroupFunction"
            resultType="com.digiwin.escloud.aiocmdb.model.model.ModelGroupFunction">
        select distinct fun.name ,fun.code,fun.fieldCode,fun.id,fun.sort,
        case WHEN g.modelGroupCode is not null THEN true ELSE false END AS selected
        from cmdb_model_group_function fun
        left join cmdb_model_group g on fun.code = g.modelGroupFunctionCode
        <choose>
            <when test="modelGroupCode !=null and modelGroupCode != ''">
                and g.modelGroupCode = #{modelGroupCode}
            </when>
            <otherwise>
                and g.modelGroupCode = ''
            </otherwise>
        </choose>
        order by fun.id
    </select>

    <select id="getModelGroupFunctionFieldGroupByModelGroupCode"
            resultType="com.digiwin.escloud.aiocmdb.model.model.ModelGroupFunctionFieldGroup">
        select cmgffg.*
        from cmdb_model_group cmg,cmdb_model_group_function_field_group cmgffg
        where cmg.modelGroupCode = #{modelGroupCode}
          and cmg.modelGroupFunctionCode = cmgffg.modelGroupFunctionCode
    </select>

    <insert id="addModelGroup" parameterType="com.digiwin.escloud.aiocmdb.model.model.ModelGroup">
        insert into cmdb_model_group (id,
        <if test="appCode != null and appCode != ''">
            appCode,
        </if>
        modelGroupCode,modelGroupName,fold,sid
        <if test="eid>0">
            ,eid
        </if>
        ,modelGroupFunctionCode
        )
        values(#{id},
        <if test="appCode != null and appCode != ''">
            #{appCode},
        </if>
        #{modelGroupCode},#{modelGroupName},#{fold},#{sid}
        <if test="eid>0">
            ,#{eid}
        </if>
        ,#{modelGroupFunctionCode}
        )
    </insert>
    <update id="modifyModelGroup" parameterType="com.digiwin.escloud.aiocmdb.model.model.ModelGroup">
        update cmdb_model_group
        set modelGroupName =#{modelGroupName},fold =#{fold},sid =#{sid},modelGroupFunctionCode =#{modelGroupFunctionCode}
        where id =#{id}
    </update>
    <select id="getModelByCond" resultType="com.digiwin.escloud.aiocmdb.model.model.Model">
        SELECT *
        FROM cmdb_model m
        LEFT JOIN cmdb_model_group mg ON m.sid= mg.sid AND m.modelGroupCode = mg.modelGroupCode
        WHERE m.status='Y'
        <if test="sid != 0">
            AND m.sid = #{sid}
        </if>
        <if test="modelGroupId != 0">
            AND mg.id =#{modelGroupId}
        </if>
    </select>
    <delete id="deleteModelGroup">
        delete from cmdb_model_group where id= #{id}
    </delete>
    <select id="selectMaxSort" resultType="java.lang.Integer">
        select ifnull(max(sort),0)
        from cmdb_model_field_mapping m
        where 1=1
        <if test="sid != 0">
            AND m.sid = #{sid}
        </if>
        <if test="modelCode != null and modelCode != ''">
            AND m.modelCode = #{modelCode}
        </if>
    </select>
    <insert id="batchAddModelFieldMapping" parameterType="com.digiwin.escloud.aiocmdb.model.model.ModelFieldMapping">
        insert into cmdb_model_field_mapping(id, modelCode, modelFieldGroupCode, modelSettingType, targetCode,hide, collection,sort, showTitle, sid, eid, `key`)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.id}, #{item.modelCode}, #{item.modelFieldGroupCode}, #{item.modelSettingType}, #{item.targetCode}, #{item.hide}, #{item.collection}, #{item.sort}, #{item.showTitle}, #{item.sid}, #{item.eid}, #{item.key})
        </foreach>
    </insert>

    <delete id="batchDeleteModelFieldMapping">
        delete from cmdb_model_field_mapping
        WHERE modelFieldGroupCode=#{modelFieldGroupCode}
        <if test="modelCodeList != null">
            <foreach collection="modelCodeList" item="item" open=" and modelCode IN(" separator=", " close=")">
                #{item}
            </foreach>
        </if>
        <if test="fieldCodeList != null">
            <foreach collection="fieldCodeList" item="item" open=" and targetCode IN(" separator=", " close=")">
                #{item}
            </foreach>
        </if>
    </delete>

    <select id="getModelFieldMappingList" resultType="com.digiwin.escloud.aiocmdb.model.model.ModelFieldMapping">
        SELECT *
        from cmdb_model_field_mapping mfp
        WHERE mfp.modelCode = #{modelCode} AND mfp.modelFieldGroupCode= #{modelFieldGroupCode} AND mfp.sid= #{sid}
    </select>
    <select id="findModelCode" resultType="com.digiwin.escloud.aiocmdb.model.model.Model">
        select *
        from cmdb_model m
        where m.status='Y' and m.modelCode = #{modelCode}
    </select>
    <select id="findByModelCode" resultType="com.digiwin.escloud.aiocmdb.model.model.Model">
        select *
        from cmdb_model m
        where m.status='Y' and m.modelCode like CONCAT('%',#{modelCode},'%')
    </select>
    <insert id="addModel" parameterType="com.digiwin.escloud.aiocmdb.model.model.Model">
        insert into cmdb_model (id,
        <if test="appCode != null and appCode != ''">
            appCode,
        </if>
        modelCode, modelName, modelGroupCode, status, imgUrl, sid, description,modelVersion
        <if test="eid>0">
            ,eid
        </if>
        )
        values(#{id},
        <if test="appCode != null and appCode != ''">
            #{appCode},
        </if>
        #{modelCode}, #{modelName}, #{modelGroupCode}, #{status}, #{imgUrl}, #{sid}, #{description}, #{modelVersion}
        <if test="eid>0">
            ,#{eid}
        </if>
        )
    </insert>
    <update id="modifyModel" parameterType="com.digiwin.escloud.aiocmdb.model.model.Model">
        update cmdb_model
        set modelCode = #{modelCode},modelName = #{modelName},modelGroupCode = #{modelGroupCode},status = #{status},imgUrl = #{imgUrl},sid = #{sid}, description = #{description}
        where id = #{id}
    </update>
    <select id="getByCode" resultType="com.digiwin.escloud.aiocmdb.model.model.Model">
        select * from cmdb_model where modelCode = #{modelCode}
    </select>

    <update id="updateModelByCode" parameterType="com.digiwin.escloud.aiocmdb.model.model.Model">
        update cmdb_model
        <set>
            <if test="modelName != null and modelName != ''">
                modelName = #{modelName},
            </if>
            <if test="modelGroupCode != null and modelGroupCode != ''">
                modelGroupCode = #{modelGroupCode},
            </if>
            <if test="status != null and status != ''">
                status = #{status},
            </if>
            <if test="imgUrl != null and imgUrl != ''">
                imgUrl = #{imgUrl},
            </if>
            <if test="description != null and description != ''">
                description = #{description},
            </if>
            <if test="sid != 0">
                sid = #{sid},
            </if>
            <if test="modelVersion != null and modelVersion != ''">
                modelVersion = #{modelVersion},
            </if>
        </set>
        where modelCode = #{modelCode}
    </update>
    <delete id="deleteModel">
        delete cm,cmfm,cmfg
        from cmdb_model cm
        LEFT JOIN cmdb_model_field_group cmfg ON cm.modelCode=cmfg.modelCode
        LEFT JOIN cmdb_model_field_mapping cmfm ON cm.modelCode=cmfm.modelCode
        where cm.modelCode= #{modelCode}
    </delete>
    <delete id="deleteModelByModeCodeList">
        delete cm,cmfm,cmfg
        from cmdb_model cm
        LEFT JOIN cmdb_model_field_group cmfg ON cm.modelCode=cmfg.modelCode
        LEFT JOIN cmdb_model_field_mapping cmfm ON cm.modelCode=cmfm.modelCode
        where cm.modelCode in
        <foreach collection="modelCodeList" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>
    <select id="getAllModel" resultMap="ModelGroup">
        SELECT mg.*,m.id m_id,m.appCode m_appCode, m.modelGroupCode m_modelGroupCode, m.modelCode
        m_modelCode,m.modelName m_modelName,
        m.status m_status,m.imgUrl m_imgUrl,m.description m_description,m.sid m_sid,m.eid m_eid,mg.modelGroupFunctionCode
        FROM cmdb_model_group mg
        LEFT JOIN cmdb_model m ON mg.modelGroupCode = m.modelGroupCode AND m.status ='Y'
        and (
        (m.sid = #{sid} AND m.appCode = #{appCode}
        <foreach collection="eidList" item="item" open=" AND m.eid IN(" separator=", " close=")">
            #{item}
        </foreach>)
        or
        (m.appCode = #{globalAppCode})
        )
        WHERE
        (
        (mg.sid = #{sid} AND mg.appCode = #{appCode}
        <foreach collection="eidList" item="item" open=" AND mg.eid IN(" separator=", " close=")">
            #{item}
        </foreach>)
        or
        (mg.appCode = #{globalAppCode})
        )
        <if test="modelGroupCode != null and modelGroupCode != ''">
            AND mg.modelGroupCode = #{modelGroupCode}
        </if>
        <if test="modelGroupFunctionCode != null and modelGroupFunctionCode != ''">
            AND mg.modelGroupFunctionCode = #{modelGroupFunctionCode}
        </if>
        <if test="modelGroupCodeNotInList != null">
            <foreach collection="modelGroupCodeNotInList" item="item" open=" AND mg.modelGroupCode not IN(" separator=", " close=")">
                #{item}
            </foreach>
        </if>
        <if test="content != null and content != ''">
            and (m.modelCode like CONCAT('%',#{content},'%') or m.modelName like CONCAT('%',#{content},'%'))
        </if>
        ORDER BY mg.id,m.id
    </select>

    <select id="getAllRelateModel" resultMap="ModelGroup">
        SELECT mg.*,m.id m_id,m.appCode m_appCode, m.modelGroupCode m_modelGroupCode, m.modelCode m_modelCode,m.modelName m_modelName,m.status m_status,m.imgUrl m_imgUrl,m.description m_description,m.sid m_sid
        FROM cmdb_model_group mg
        LEFT JOIN cmdb_model m ON mg.modelGroupCode = m.modelGroupCode AND mg.sid = m.sid AND m.status ='Y'
        WHERE 1=1
        <if test="sid != 0">
            AND mg.sid = #{sid}
        </if>
        <if test="modelGroupCode != null and modelGroupCode != ''">
            AND mg.modelGroupCode = #{modelGroupCode}
        </if>
        <if test="appCode != null and appCode != ''">
            AND mg.appCode = #{appCode}
        </if>
        <if test="content != null and content != ''">
            and (m.modelCode like CONCAT('%',#{content},'%') or m.modelName like CONCAT('%',#{content},'%'))
        </if>
        <if test="targetModelCode != null and targetModelCode != ''">
            and EXISTS (SELECT 1 FROM cmdb_model_relate cmr WHERE cmr.targetModelCode=#{targetModelCode} AND cmr.sourceModelCode= m.modelCode AND cmr.relateType ='DEPEND')
        </if>
        ORDER BY mg.id,m.id
    </select>
    <delete id="deleteModelRelate">
        delete from cmdb_model_relate where sourceModelCode = #{modelCode}
    </delete>

    <delete id="deleteModelRelateInstance">
        delete from cmdb_model_relate_instance where sourceModelCode = #{modelCode}
    </delete>

    <delete id="deleteModelRelateByRelateCode">
        delete from cmdb_model_relate where modelRelateCode = #{modelRelateCode}
    </delete>

    <delete id="deleteModelRelateInstanceByRelateCode">
        delete from cmdb_model_relate_instance where modelRelateCode = #{modelRelateCode}
    </delete>

    <select id="getModelDetail" resultMap="ModelDetailMap">
        SELECT temp.id,temp.sid,temp.modelCode,temp.modelName,temp.description,temp.imgUrl,temp.status, temp.modelGroupFold,
               temp.modelFieldGroupCode,temp.modelFieldGroupName,temp.modelFieldGroupFold,temp.modelSettingType,temp.modelFieldMappingId,
               temp.targetCode,case when temp.modelSettingType='fieldSet' then fs.fieldSetName when temp.modelSettingType='field' then f.fieldName ELSE '' END targetName,

               temp.collection,temp.hide,temp.showTitle,temp.sort,fs.fieldSetCode,fs.fieldSetName,fs.fieldSetGroupCode,fs.description fieldSetDescription,
               temp.advance,temp.key,
               fsm.key fs_key,fss.fieldCode fs_fieldCode,fss.fieldName fs_fieldName,fss.fieldType fs_fieldType,fss.moduleId fs_moduleId,fss.isEdit fs_isEdit,fss.isRequired fs_isRequired,fss.isAutoCollection fs_isAutoCollection,fss.summaryMethod fs_summaryMethod,fss.regularCheck fs_regularCheck,fss.tips fs_tips,fss.regularTips fs_regularTips,fss.defaultValue fs_defaultValue,fss.description fs_description,fss.FORMAT fs_format,fss.system fs_system,fsm.width fs_width,fsm.offsetLeft fs_offsetLeft,fsm.offsetRight fs_offsetRight,

               f.id f_fieldId,f.appCode f_appCode,f.fieldCode f_fieldCode,f.fieldName f_fieldName,f.fieldType f_fieldType,f.moduleId f_moduleId,f.isEdit f_isEdit,f.isRequired f_isRequired,f.isAutoCollection f_isAutoCollection,f.summaryMethod f_summaryMethod,f.regularCheck f_regularCheck,f.tips f_tips,f.regularTips f_regularTips,f.defaultValue f_defaultValue,f.description f_description,f.FORMAT f_format,f.system f_system,f.sid f_sid,f.eid f_eid,temp.width f_width,temp.offsetLeft f_offsetLeft,temp.offsetRight f_offsetRight,
               f.fieldTypeSettingJson f_fieldTypeSettingJson,
               ffe.fieldCode fs_fs_enum_fieldCode,ffe.enumCode fs_fs_enum_enumCode,ffe.enumName fs_fs_enum_enumName,ffe.sort fs_fs_enum_sort,
               fe.fieldCode f_f_enum_fieldCode,fe.enumCode f_f_enum_enumCode,fe.enumName f_f_enum_enumName,fe.sort f_f_enum_sort,

               mff.id fs_mff_id,mff.modelFieldMappingId fs_mff_modelFieldMappingId,mff.fieldCode fs_mff_fieldCode,mff.formula fs_mff_formula,mff.style fs_mff_style,mff.triggerScript fs_mff_triggerScript,mff.modelSettingType fs_mff_modelSettingType,
               mfff.id f_mfff_id,mfff.modelFieldMappingId f_mfff_modelFieldMappingId,mfff.fieldCode f_mfff_fieldCode,mfff.formula f_mfff_formula,mfff.style f_mfff_style,mfff.triggerScript f_mfff_triggerScript,mfff.modelSettingType f_mfff_modelSettingType,
               mfsf.id mfsf_id,mfsf.modelFieldMappingId mfsf_modelFieldMappingId,mfsf.fieldCode mfsf_fieldCode,mfsf.formula mfsf_formula,mfsf.style mfsf_style,mfsf.triggerScript mfsf_triggerScript,mfsf.modelSettingType mfsf_modelSettingType


        FROM (SELECT m.sid,m.eid,m.modelCode,m.modelName,m.description,m.imgUrl,m.status, mg.fold modelGroupFold,
                     mfg.id,mfg.modelFieldGroupCode,mfg.modelFieldGroupName,mfg.fold modelFieldGroupFold,mfm.modelSettingType,
                     mfm.id modelFieldMappingId,mfm.targetCode,
                     mfm.collection,mfm.hide,mfm.showTitle,mfm.sort,mfm.width,mfm.offsetLeft,mfm.offsetRight,
                     mfm.advance,mfm.key

              FROM cmdb_model m
                       left join cmdb_model_group mg on mg.modelGroupCode = m.modelGroupCode
                       LEFT JOIN cmdb_model_field_group mfg ON mfg.modelCode = m.modelCode AND mfg.sid = m.sid and mfg.modelCode = #{modelCode}
                       LEFT JOIN cmdb_model_field_mapping mfm ON mfm.modelCode = m.modelCode AND mfm.modelFieldGroupCode = mfg.modelFieldGroupCode AND mfg.sid = mfm.sid and mfm.modelCode = #{modelCode}
              WHERE m.modelCode = #{modelCode}
             ) temp

                 LEFT JOIN cmdb_fieldset fs ON temp.modelSettingType='fieldSet' AND fs.fieldSetCode = temp.targetCode AND temp.sid = fs.sid
                 LEFT JOIN cmdb_fieldset_mapping fsm ON fsm.fieldSetCode = fs.fieldSetCode AND temp.sid = fsm.sid
                 LEFT JOIN cmdb_model_field_formula mfsf ON mfsf.modelFieldMappingId = temp.modelFieldMappingId AND mfsf.fieldCode = fs.fieldSetCode AND mfsf.modelSettingType = 'fieldSet'
                 LEFT JOIN cmdb_field fss ON fss.fieldCode= fsm.fieldCode AND temp.sid = fss.sid
                 left join cmdb_fieldtype_enum ffe on ffe.fieldCode = fss.fieldCode AND ffe.sid = temp.sid
                 LEFT JOIN cmdb_model_field_formula mff ON mff.modelFieldMappingId = temp.modelFieldMappingId AND mff.fieldCode = fss.fieldCode

                 LEFT JOIN cmdb_field f ON temp.modelSettingType='field' AND f.fieldCode = temp.targetCode AND f.sid = temp.sid
                 left join cmdb_fieldtype_enum fe on fe.fieldCode = f.fieldCode AND fe.sid = temp.sid
                 LEFT JOIN cmdb_model_field_formula mfff ON mfff.modelFieldMappingId = temp.modelFieldMappingId AND mfff.fieldCode = f.fieldCode AND mfff.modelSettingType = 'field'

        WHERE temp.modelCode = #{modelCode}
        order by temp.id ,temp.sort,fsm.sort,fe.sort,ffe.sort
    </select>

    <select id="getModelRelateList" resultType="com.digiwin.escloud.aiocmdb.model.ModelRelate">
        SELECT mr.id,mr.modelRelateCode,mr.relateType,mr.sourceModelCode,m.modelName sourceModelName, mr.targetModelCode,md.modelName targetModelName,mr.relation,mr.description,mr.sid,mr.sourceFieldCode,mr.targetFieldCode,mr.displayMethod
        FROM cmdb_model_relate mr
        LEFT JOIN cmdb_model m ON BINARY m.modelCode = mr.sourceModelCode AND mr.sid = m.sid
        LEFT JOIN cmdb_model md ON BINARY md.modelCode = mr.targetModelCode AND md.sid = m.sid
        WHERE m.modelCode = #{modelCode}
        <if test="sid != 0">
            AND m.sid = #{sid}
        </if>
        order by mr.modelRelateCode asc
    </select>

    <select id="getModelFieldGroupList" resultType="com.digiwin.escloud.aiocmdb.model.model.ModelFieldGroup">
        SELECT *
        FROM cmdb_model_field_group mfg
        WHERE 1=1
        <if test="sid != 0">
            AND mfg.sid = #{sid}
        </if>
        <if test="modelCode != null and modelCode != ''">
            AND mfg.modelCode = #{modelCode}
        </if>

        order by mfg.modelFieldGroupCode asc
    </select>

    <select id="getModelFieldGroupExist" resultType="java.lang.Integer">
        select 1
        from cmdb_model_field_group
        where modelCode=#{modelCode} and modelFieldGroupCode=#{modelFieldGroupCode}
        <if test="mode == 'update' ">
            and id!=#{id}
        </if>
        limit 1
    </select>

    <insert id="addModelFieldGroup" parameterType="com.digiwin.escloud.aiocmdb.model.model.ModelFieldGroup">
        insert into cmdb_model_field_group (id,modelCode,modelFieldGroupCode,modelFieldGroupName,fold,sid)
        values(#{id},#{modelCode},#{modelFieldGroupCode},#{modelFieldGroupName},#{fold},#{sid})
            ON DUPLICATE KEY UPDATE modelFieldGroupName = #{modelFieldGroupName}
    </insert>

    <insert id="batchAddModelFieldGroup" parameterType="com.digiwin.escloud.aiocmdb.model.model.ModelFieldGroup">
        insert into cmdb_model_field_group (id,modelCode,modelFieldGroupCode,modelFieldGroupName,fold,sid)
        values
        <foreach collection="list" item="item" separator=",">
            ( #{item.id}, #{item.modelCode}, #{item.modelFieldGroupCode}, #{item.modelFieldGroupName}, #{item.fold}, #{item.sid})
        </foreach>
    </insert>

    <update id="modifyModelFieldGroup" parameterType="com.digiwin.escloud.aiocmdb.model.model.ModelFieldGroup">
        update cmdb_model_field_group
        set modelFieldGroupCode=#{modelFieldGroupCode}, modelFieldGroupName =#{modelFieldGroupName},fold =#{fold},sid =#{sid}
        where id =#{id}
    </update>

    <delete id="deleteModelFieldGroup">
        delete from cmdb_model_field_group where modelCode=#{modelCode} and modelFieldGroupCode= #{modelFieldGroupCode}
    </delete>
    <select id="checkFieldInModel" resultType="com.digiwin.escloud.aiocmdb.model.model.Model">
        SELECT m.*
        FROM cmdb_model m
                 LEFT JOIN cmdb_model_field_mapping mfm ON mfm.modelCode = m.modelCode AND mfm.sid = m.sid AND mfm.modelSettingType='field'
                 LEFT JOIN cmdb_field f ON f.fieldCode = mfm.targetCode AND mfm.sid = f.sid
        WHERE m.STATUS = 'Y' AND f.fieldCode = #{fieldCode}
    </select>

    <select id="getModelRelateType" resultType="com.digiwin.escloud.aiocmdb.model.model.ModelRelateType">
        SELECT id,relateCode,relateName
        FROM cmdb_model_relatetype
    </select>

    <select id="getModelRelateExist" resultType="java.lang.Integer">
        select 1
        from cmdb_model_relate
        where modelRelateCode=#{modelRelateCode}
        <if test="mode == 'update' ">
            and id!=#{id}
        </if>
        limit 1
    </select>

    <insert id="saveModelRelate" keyProperty="id" keyColumn="id" parameterType="com.digiwin.escloud.aiocmdb.model.ModelRelate">
        insert into cmdb_model_relate (id,sid,modelRelateCode,relateType,sourceModelCode,sourceFieldCode,targetModelCode,targetFieldCode,
                                       relation,description,displayMethod)
        values(#{id},#{sid},#{modelRelateCode},#{relateType},#{sourceModelCode},#{sourceFieldCode},#{targetModelCode},#{targetFieldCode},
               #{relation},#{description},#{displayMethod})
            ON DUPLICATE KEY UPDATE relateType = #{relateType}, sourceModelCode = #{sourceModelCode},sourceFieldCode = #{sourceFieldCode},
                                 targetModelCode = #{targetModelCode},targetFieldCode = #{targetFieldCode},relation = #{relation},description = #{description},
                                 displayMethod = #{displayMethod}
    </insert>

    <insert id="batchInsertOrUpdateModelRelate">
        INSERT INTO cmdb_model_relate (id, sid, modelRelateCode, relateType, sourceModelCode, sourceFieldCode,
        targetModelCode, targetFieldCode, relation, description, displayMethod)
        <foreach collection="modelRelateList" item="item" open="VALUES(" separator="), (" close=")">
            #{item.id}, #{item.sid}, #{item.modelRelateCode}, #{item.relateType}, #{item.sourceModelCode},
            #{item.sourceFieldCode}, #{item.targetModelCode}, #{item.targetFieldCode}, #{item.relation},
            #{item.description}, #{item.displayMethod}
        </foreach>
        ON DUPLICATE KEY UPDATE relateType = VALUES(relateType), sourceModelCode = VALUES(sourceModelCode),
        sourceFieldCode = VALUES(sourceFieldCode), targetModelCode = VALUES(targetModelCode),
        targetFieldCode = VALUES(targetFieldCode), relation = VALUES(relation),
        description = VALUES(description), displayMethod = VALUES(displayMethod)
    </insert>

    <select id="getModelRelateInstance" resultType="com.digiwin.escloud.aiocmdb.model.ModelRelateInstance">
        select id,sid,modelRelateCode,relateType,sourceModelCode,sourceFieldCode,sourceFieldValue,targetModelCode,
               targetFieldCode,targetFieldValue
        from cmdb_model_relate_instance
        where modelRelateCode=#{modelRelateCode}
            limit 1
    </select>

    <update id="setFieldCollection" parameterType="com.digiwin.escloud.aiocmdb.model.model.ModelFieldMapping">
        update cmdb_model_field_mapping
        set collection=#{collection}
        where id =#{id}
    </update>

    <select id="getFieldsByFieldGroup" resultMap="modelFieldMappingMap">
        select a.id,a.modelCode,a.modelFieldGroupCode,a.modelSettingType,a.targetCode,a.collection,a.sid,a.sort,
        b.fieldCode as f_fieldCode, b.fieldName as f_fieldName, b.fieldType as f_fieldType, b.defaultValue as f_defaultValue,
        c.fieldSetCode,c.fieldSetName,e.fieldCode as fs_fieldCode,e.fieldName as fs_fieldName, e.fieldType as fs_fieldType,
        e.defaultValue as fs_defaultValue
        from cmdb_model_field_mapping a
        LEFT JOIN cmdb_field b ON a.modelSettingType='field' AND b.fieldCode = a.targetCode AND b.sid = a.sid
        LEFT JOIN cmdb_fieldset c ON a.modelSettingType='fieldSet' AND c.fieldSetCode = a.targetCode AND c.sid = a.sid
        LEFT JOIN cmdb_fieldset_mapping d ON c.fieldSetCode = d.fieldSetCode AND d.sid = a.sid
        LEFT JOIN cmdb_field e ON d.fieldCode= e.fieldCode AND e.sid = a.sid
        where a.modelCode=#{modelCode}
        <if test="modelFieldGroupCode != null and modelFieldGroupCode != ''">
            and a.modelFieldGroupCode=#{modelFieldGroupCode}
        </if>
        order by sort
    </select>

    <select id="getModelGroupField" resultMap="ModelGroupFieldMap">
        SELECT a.id,a.modelGroupCode,a.fieldCode,a.sort,a.sid,a.key,a.sort,b.id as f_id,b.fieldCode as f_fieldCode,b.fieldName as f_fieldName,
               b.fieldType as f_fieldType,b.isEdit as f_isEdit,b.isRequired as f_isRequired,b.isAutoCollection as f_isAutoCollection,b.summaryMethod as f_summaryMethod,
               b.regularCheck as f_regularCheck,b.tips as f_tips,b.defaultValue as f_defaultValue,b.description as f_description,b.format as f_format,
               b.system as f_system,b.sid as f_sid
        FROM cmdb_model_group_field a
                 INNER JOIN cmdb_field b ON b.fieldCode = a.fieldCode AND b.sid = a.sid
        WHERE 1=1 AND a.modelGroupCode = #{modelGroupCode}
        order by a.sort asc
    </select>

    <select id="getModelKeyField" resultMap="modelFieldMappingMap">
        SELECT a.id,a.modelCode,a.modelFieldGroupCode,a.modelSettingType,a.targetCode,a.key,a.sort
        FROM cmdb_model_field_mapping a
        WHERE 1=1 AND modelCode = #{modelGroupCode} and `key`=1 and modelSettingType='field'
    </select>

    <select id="getModelRelateInstanceByModel" resultType="com.digiwin.escloud.aiocmdb.model.ModelRelateInstance">
        select id,sid,modelRelateCode,relateType,sourceModelCode,sourceFieldCode,sourceFieldValue,targetModelCode,
               targetFieldCode,targetFieldValue
        from cmdb_model_relate_instance
        where sourceModelCode=#{sourceModelCode} and targetModelCode=#{targetModelCode} and sourceFieldCode=#{sourceFieldCode} and
            targetFieldCode=#{targetFieldCode} and sourceFieldValue=#{sourceFieldValue}
    </select>

    <select id="selectModelRelateInstanceBySourceInfo" resultType="com.digiwin.escloud.aiocmdb.model.ModelRelateInstance">
        select id, sid, modelRelateCode, relateType, sourceModelCode, sourceFieldCode, sourceFieldValue,
        targetModelCode, targetFieldCode, targetFieldValue
        from cmdb_model_relate_instance
        where 1=1
        <foreach collection="sourceConditions" item="item" open=" and ((" separator=") or (" close="))">
            sourceModelCode=#{item.sourceModelCode} and sourceFieldValue=#{item.sourceFieldValue}
            <if test="item.sourceFieldCode != null and item.sourceFieldCode != ''">
                and sourceFieldCode=#{item.sourceFieldCode}
            </if>
            <if test="item.relateType != null and item.relateType != ''">
                and relateType=#{item.relateType}
            </if>
        </foreach>
    </select>

    <select id="getModelRelateByModel" resultType="com.digiwin.escloud.aiocmdb.model.ModelRelate">
        select id,sid,modelRelateCode,relateType,sourceModelCode,sourceFieldCode,targetModelCode,targetFieldCode
        from cmdb_model_relate
        where sourceModelCode=#{sourceModelCode} and targetModelCode=#{targetModelCode}
            limit 1
    </select>

    <insert id="saveModelRelateInstance" keyProperty="id" keyColumn="id" parameterType="com.digiwin.escloud.aiocmdb.model.ModelRelateInstance">
        insert into cmdb_model_relate_instance (id,sid,modelRelateCode,relateType,sourceModelCode,sourceFieldCode,sourceFieldValue,
                                                targetModelCode,targetFieldCode,targetFieldValue)
        values(#{id},#{sid},#{modelRelateCode},#{relateType},#{sourceModelCode},#{sourceFieldCode},#{sourceFieldValue},
               #{targetModelCode},#{targetFieldCode},#{targetFieldValue})
            on duplicate key update modelRelateCode = values(modelRelateCode), relateType = values(relateType)
    </insert>

    <insert id="batchInsertOrUpdateModelRelateInstance">
        insert into cmdb_model_relate_instance (id, sid, modelRelateCode, relateType, sourceModelCode, sourceFieldCode,
        sourceFieldValue, targetModelCode, targetFieldCode, targetFieldValue)
        <foreach collection="modelRelateInstanceList" item="item" open="values(" separator="), (" close=")">
            #{item.id}, #{item.sid}, #{item.modelRelateCode}, #{item.relateType}, #{item.sourceModelCode},
            #{item.sourceFieldCode}, #{item.sourceFieldValue}, #{item.targetModelCode}, #{item.targetFieldCode},
            #{item.targetFieldValue}
        </foreach>
        on duplicate key update modelRelateCode = values(modelRelateCode), relateType = values(relateType)
    </insert>

    <insert id="batchInsertModelGroupField">
        insert into cmdb_model_group_field (id, sid, eid, appCode, modelGroupCode, fieldCode,sort,`key`)
        <foreach collection="list" item="item" open="values(" separator="), (" close=")">
            #{item.id}, #{item.sid}, #{item.eid}, #{item.appCode}, #{item.modelGroupCode}, #{item.fieldCode},#{item.sort},#{item.key}
        </foreach>
    </insert>

    <delete id="deleteModelRelateInstanceByIdList">
        delete from cmdb_model_relate_instance
        where id in
        <foreach collection="mriIdList" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>

    <delete id="batchDeleteModelRelateInstanceByCondition">
        DELETE FROM cmdb_model_relate_instance
        WHERE 1 != 1
        <if test="modelRelateInstanceList != null">
            <foreach collection="modelRelateInstanceList" item="item" open=" OR (" separator=") OR (" close=")">
                1 = 1
                <if test="item.sid != null and item.sid > 0">
                    AND sid = #{item.sid}
                </if>
                <if test="item.modelRelateCode != null and item.modelRelateCode != ''">
                    AND modelRelateCode = #{item.modelRelateCode}
                </if>
                <if test="item.sourceModelCode != null and item.sourceModelCode != ''">
                    AND sourceModelCode = #{item.sourceModelCode}
                </if>
                <if test="item.sourceFieldCode != null and item.sourceFieldCode != ''">
                    AND sourceFieldCode = #{item.sourceFieldCode}
                </if>
                <if test="item.sourceFieldValue != null and item.sourceFieldValue != ''">
                    AND sourceFieldValue = #{item.sourceFieldValue}
                </if>
                <if test="item.targetModelCode != null and item.targetModelCode != ''">
                    AND targetModelCode = #{item.targetModelCode}
                </if>
                <if test="item.targetFieldCode != null and item.targetFieldCode != ''">
                    AND targetFieldCode = #{item.targetFieldCode}
                </if>
                <if test="item.targetFieldValue != null and item.targetFieldValue != ''">
                    AND targetFieldValue = #{item.targetFieldValue}
                </if>
            </foreach>
        </if>
    </delete>

    <update id="addKeyToModel">
        update cmdb_model_field_mapping a
        set a.key = 1
        where a.id = #{id}
    </update>
    <update id="removeKeyFromModel">
        update cmdb_model_field_mapping a
        set a.key = 0
        where a.id = #{id}
    </update>

    <delete id="removeFieldFromModel">
        delete from cmdb_model_field_mapping where id = #{id}
    </delete>

    <delete id="removeFieldInGroupFromModel">
        delete from cmdb_model_field_mapping where modelCode= #{modelCode} and modelFieldGroupCode=#{modelFieldGroupCode}
    </delete>

    <select id="getModelFieldMappingById" resultType="com.digiwin.escloud.aiocmdb.model.model.ModelFieldMapping">
        select id,modelCode,modelSettingType,targetCode
        from cmdb_model_field_mapping
        where id=#{id}
            limit 1
    </select>

    <select id="getModelGroupNameExist" resultType="java.lang.Integer">
        select 1
        from cmdb_model_group
        where modelGroupName=#{modelGroupName}
        <if test="mode == 'update' ">
            and id!=#{id}
        </if>
        limit 1
    </select>

    <select id="getModelsByFieldSetCodes" resultType="com.digiwin.escloud.aiocmdb.model.model.Model">
        select distinct a.modelCode,a.modelName
        FROM cmdb_model a
        LEFT JOIN cmdb_model_field_mapping b on a.modelCode=b.modelCode
        WHERE 1=1 and a.status='Y'
        <if test="modelCode != null and modelCode != ''">
            and a.modelCode!=#{modelCode}
        </if>
        <if test="fieldSetCodes != null and fieldSetCodes.size()>0">
            AND b.targetCode in
            <foreach collection="fieldSetCodes" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            AND b.modelSettingType ='fieldSet'
        </if>
    </select>

    <delete id="deleteModelFieldMapping">
        delete mfm from cmdb_model_field_mapping as mfm
        inner join cmdb_field f on f.fieldCode = mfm.targetCode and mfm.modelSettingType='field'
        where f.id= #{id}
    </delete>

    <delete id="deleteModelGroupField">
        delete from cmdb_model_group_field where id= #{id}
    </delete>

    <delete id="deleteModelGroupFieldByGroupCode">
        delete from cmdb_model_group_field where modelGroupCode= #{modelGroupCode}
    </delete>

    <select id="selectModelNameByCodeList" resultType="java.util.Map">
        SELECT modelCode, modelName
        FROM cmdb_model
        WHERE sid = #{sid} AND (1 != 1
        <if test="codeList != null">
            <foreach collection="codeList" item="item" open=" OR modelCode IN (" separator=", " close=")">
                #{item}
            </foreach>
        </if>
        )
    </select>

    <select id="selectAppCodeByCodeList" resultType="java.util.Map">
        SELECT modelCode, appCode
        FROM cmdb_model
        WHERE (1 != 1
        <if test="codeList != null">
            <foreach collection="codeList" item="item" open=" OR modelCode IN (" separator=", " close=")">
                #{item}
            </foreach>
        </if>
        )
    </select>

    <select id="getModelDmpField" resultType="com.digiwin.escloud.aiocmdb.field.model.Field">
        select b.fieldCode,b.fieldName
        FROM cmdb_model_field_mapping a
                 LEFT JOIN cmdb_field b on a.targetCode=b.fieldCode
        WHERE a.modelCode=#{modelCode} and showInDmp=1
        order by a.sort
    </select>

    <select id="selectFieldRelateModelCode" resultType="java.lang.String">
        SELECT cmfm.modelCode
        FROM cmdb_model_field_mapping cmfm
        WHERE (EXISTS(SELECT 1 FROM cmdb_field cf
                      WHERE cf.fieldCode = cmfm.targetCode AND cmfm.modelSettingType = 'field' AND cf.id = #{cfId})
            OR EXISTS(SELECT 1 FROM cmdb_fieldset_mapping cfsm
                                        INNER JOIN cmdb_field cf ON cfsm.fieldCode = cf.fieldCode AND cf.id = #{cfId}
                      WHERE cfsm.fieldSetCode = cmfm.targetCode AND cmfm.modelSettingType = 'fieldSet'))
        GROUP BY cmfm.modelCode
    </select>

    <sql id="whereCondition">
        <foreach  collection="modelCodes" item="id" separator="," open="(" close=")">
            #{id}
        </foreach>
    </sql>

    <select id="selectAccModelByModelCodes" resultType="String">
        <!--触发器(读取器)-->
        select collectorModelCode as modelCode  FROM aiops_collect_config where collectorModelCode in
        <include refid="whereCondition"/>
        union all
        select uploadDataModelCode as modelCode FROM aiops_collect_config where uploadDataModelCode in
        <include refid="whereCondition"/>
        union all
        select execParamsModelCode as modelCode FROM aiops_collect_config where execParamsModelCode in
        <include refid="whereCondition"/>
        <!--执行器-->
        union all
        select modelCode as modelCode FROM dcdp_executor_model where modelCode in
        <include refid="whereCondition"/>
        union all
        select uploadDataModelCode as modelCode FROM dcdp_executor_model where uploadDataModelCode in
        <include refid="whereCondition"/>
        union all
        select execParamsModelCode as modelCode FROM dcdp_executor_model where execParamsModelCode in
        <include refid="whereCondition"/>
        <!--监护器-->
        union all
        select modelCode as modelCode FROM dcdp_guardian_model where modelCode in
        <include refid="whereCondition"/>
        union all
        select uploadDataModelCode as modelCode FROM dcdp_guardian_model where uploadDataModelCode in
        <include refid="whereCondition"/>
        union all
        select execParamsModelCode as modelCode FROM dcdp_guardian_model where execParamsModelCode in
        <include refid="whereCondition"/>
    </select>

    <select id="selectModelCountByMap" resultType="java.lang.Integer">
        SELECT COUNT(*) AS total
        FROM cmdb_model
        <where>
            <if test="sid != null and sid > 0">
                AND sid = #{sid}
            </if>
        </where>
    </select>

    <update id="updateGlobalModel">
        update cmdb_model_group cmg, cmdb_model cm
        set cmg.sid = #{globalSid},cm.sid = #{globalSid},cmg.eid = #{globalEid},cm.eid = #{globalEid},cmg.appCode =#{globalAppCode},cm.appCode=#{globalAppCode}
        where cmg.modelGroupCode = cm.modelGroupCode and cm.modelCode in
        <foreach collection="modelCodeList" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>

    <select id="getModelVersionByModelCode" resultType="java.util.Map">
        SELECT modelCode, modelVersion
        FROM cmdb_model
        WHERE modelCode IN
        <foreach collection="modelCodeList" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="getModelGroupCodeByModelCode" resultType="java.util.Map">
        SELECT modelCode, modelGroupCode
        FROM cmdb_model
        WHERE modelCode IN
        <foreach collection="modelCodeList" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="getModelGroupAutoSinkByGroupCode" resultType="com.digiwin.escloud.aiocmdb.model.model.ModelGroupAutoSink">
        SELECT id,appCode,modelGroupCode,canAutoSink,sinkType,sinkPk
        FROM cmdb_model_group_auto_sink
        WHERE modelGroupCode IN
        <foreach collection="modelGroupCodeList" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <update id="moveModelFieldSort" parameterType="java.util.List">
        <foreach collection="modelFieldMappingList" item="modelField" separator=";">
            UPDATE cmdb_model_field_mapping
            SET sort=#{modelField.sort}
            WHERE modelCode = #{modelField.modelCode}
            AND modelFieldGroupCode = #{modelField.modelFieldGroupCode}
            AND targetCode = #{modelField.targetCode}
        </foreach>
    </update>

    <select id="getNotBindProductApps" resultType="java.util.Map">
        SELECT #{spId} spId,cmr.targetModelCode modelCode,cmr.modelRelateCode modelRelateCode, cmr.sourceModelCode, ifnull(cm.modelName,'') modelName
        FROM cmdb_model_relate cmr
        LEFT JOIN cmdb_model cm ON cm.modelCode = cmr.targetModelCode
        WHERE cmr.relateType in ('CARRY','DEPLOY') and NOT EXISTS (
        SELECT *
        FROM (
        <foreach collection="modelRelateCodes" item="colValue" separator="union all">
            SELECT #{colValue} as modelRelateCode
        </foreach>
        ) a where a.modelRelateCode = cmr.modelRelateCode
        ) AND cmr.sourceModelCode = #{modelCode}
        <if test="filter != null and filter != ''">
            AND ( cm.modelName like "%"#{filter}"%" OR cmr.targetModelCode like "%"#{filter}"%")
        </if>
        order by cmr.modelRelateCode ASC
    </select>

    <select id="selectModelMapByMap" resultType="java.util.Map">
        SELECT
        <choose>
            <when test="specialColumns != null">
                <foreach collection="specialColumns" item="item" separator=", ">
                    ${item}
                </foreach>
            </when>
            <otherwise>
                <include refid="getAllCmDisplayColumn">
                    <property name="alias" value="cm."/>
                    <property name="asPrefix" value=""/>
                    <property name="specialIdAlias" value='""'/>
                </include>
            </otherwise>
        </choose>
        FROM cmdb_model cm
        <where>
            <if test="sid != null and sid > 0">
                AND cm.sid = #{sid}
            </if>
            <if test="appCode != null and appCode != ''">
                AND cm.appCode = #{appCode}
            </if>
            <if test="appCodeList != null">
                <foreach collection="appCodeList" item="item"
                         open=" AND cm.appCode IN (" separator=", " close=")">
                    #{item}
                </foreach>
            </if>
            <if test="modelCode != null and modelCode != ''">
                AND cm.modelCode = #{modelCode}
            </if>
            <if test="modelCodeList != null">
                <foreach collection="modelCodeList" item="item"
                         open=" AND cm.modelCode IN (" separator=", " close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        <if test="groupBy != null and groupBy != ''">
            GROUP BY ${groupBy}
        </if>
    </select>

    <select id="selectTargetCodesByModelCode" resultType="java.lang.String">
        SELECT targetCode
        FROM cmdb_model_field_mapping
        WHERE modelCode = #{modelCode} AND sid = #{sid}
        ORDER BY sort ASC
    </select>

    <select id="selectFieldMappingsByModelCode" resultType="com.digiwin.escloud.aiocmdb.model.model.ModelFieldMapping">
        SELECT id, modelCode, modelFieldGroupCode, modelSettingType, targetCode, collection, hide, showTitle, sid, eid, sort, `key`
        FROM cmdb_model_field_mapping
        WHERE modelCode = #{modelCode} AND sid = #{sid}
        ORDER BY sort ASC
    </select>
</mapper>