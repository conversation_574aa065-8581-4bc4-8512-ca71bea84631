package com.digiwin.escloud.aiocmdb.assetmaintenancev2.controller;

import cn.hutool.core.lang.Pair;
import com.digiwin.escloud.aiocmdb.assetmaintenancev2.model.AssetAttribute;
import com.digiwin.escloud.aiocmdb.assetmaintenancev2.model.AssetRelatedMap;
import com.digiwin.escloud.aiocmdb.assetmaintenancev2.service.AssetRelatedMapService;
import com.digiwin.escloud.common.response.BaseResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

@Api(value = "關聯資產", tags = {"AssetMaintenanceV2"} )
@Slf4j
@RestController
@RequestMapping("/asset/maintenance/v2")
public class AssetMaintenanceV2Controller {

    @Autowired
    private AssetRelatedMapService assetRelatedMapService;

    @ApiOperation(value = "查詢資產間的關聯")
    @GetMapping(value = "/map/assets")
    public BaseResponse queryRelatedAssets(
            @RequestParam String eid,
            @RequestParam String primaryAssetId) {
        try {
            return BaseResponse.ok(assetRelatedMapService.queryRelatedAssets(eid, primaryAssetId));
        } catch (Exception ex) {
            log.error(ex.getMessage());
            ex.printStackTrace();
            return BaseResponse.error(ex);
        }
    }

    @ApiOperation(value = "新增資產間的關聯")
    @PostMapping(value = "/map/assets")
    public BaseResponse addRelatedAsset(@RequestBody List<AssetRelatedMap> assetRelatedMaps) {
        try {
            Map<String, Object> data = assetRelatedMapService.addRelatedAsset(assetRelatedMaps);
            BaseResponse response = new BaseResponse<>();
            response.setCode(data.get("code").toString());
            response.setErrMsg(data.get("errMsg").toString());
            response.setData(data.get("data"));
            return response;
        } catch (Exception ex) {
            log.error(ex.getMessage());
            ex.printStackTrace();
            return BaseResponse.error(ex);
        }
    }

    @ApiOperation(value = "刪除資產間的關聯")
    @DeleteMapping(value = "/map/assets")
    public BaseResponse deleteRelatedAsset(
            @RequestParam String eid, @RequestParam long assetRelatedId
    ) {
        try {
            return BaseResponse.ok(assetRelatedMapService.deleteRelatedAsset(eid, assetRelatedId));
        } catch (Exception ex) {
            log.error(ex.getMessage());
            ex.printStackTrace();
            return BaseResponse.error(ex);
        }
    }

    @ApiOperation(value = "新增資產屬性")
    @PostMapping(value = "/save/attribute")
    public BaseResponse createAssetAttribute
            (
            @RequestParam String modelCode,
            @RequestBody AssetAttribute assetAttribute) {
        return this.updateAssetAttribute(modelCode, assetAttribute);
    }

    @ApiOperation(value = "更新資產屬性")
    @PutMapping(value = "/save/attribute")
    public BaseResponse updateAssetAttribute(
            @RequestParam String modelCode,
            @RequestBody AssetAttribute assetAttribute) {
        // 保存資產屬性
        try {
            Pair<Map<String, Object>, List<LinkedHashMap<String, Object>>> res = assetRelatedMapService.saveAssetAttribute(modelCode, assetAttribute);
            BaseResponse response = new BaseResponse<>();
            response.setCode(res.getKey().get("code").toString());
            response.setErrMsg(res.getKey().get("errMsg").toString());
            response.setData(res.getValue());
            return response;
        } catch (Exception ex) {
            log.error(ex.getMessage());
            ex.printStackTrace();
            return BaseResponse.error(ex);
        }
    }
}
