<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.digiwin.escloud.aiouser.dao.IAioAppDao">

    <sql id="getAllAppDisplayColumn">
        <choose>
            <when test='${specialIdAlias} != ""'>
                ${alias}id AS ${asPrefix}${specialIdAlias},
            </when>
            <otherwise>
                ${alias}id AS ${asPrefix}id,
            </otherwise>
        </choose>
        ${alias}appCode AS ${asPrefix}appCode,
        ${alias}appName AS ${asPrefix}appName,
        ${alias}tenantUniqueDb AS ${asPrefix}tenantUniqueDb,
        ${alias}createTime AS ${asPrefix}createTime
    </sql>

    <select id="getAioApps" resultType="com.digiwin.escloud.common.model.App">
        SELECT app.id, appCode, appName
        FROM app
        <if test="tenantSid !=null">
            inner join tenant_app_map tam on app.id=tam.appId
        </if>
        WHERE 1=1
        <if test="tenantSid !=null">
            AND eid=#{tenantSid}
        </if>
    </select>

    <select id="selectAioAppByMap" resultType="com.digiwin.escloud.common.model.App">
        SELECT
        <choose>
            <when test="specialColumns != null">
                <foreach collection="specialColumns" item="item" separator=", ">
                    ${item}
                </foreach>
            </when>
            <otherwise>
                <include refid="getAllAppDisplayColumn">
                    <property name="alias" value="app."/>
                    <property name="asPrefix" value=""/>
                    <property name="specialIdAlias" value='""'/>
                </include>
            </otherwise>
        </choose>
        FROM app
        <if test="tenantSid != null and tenantSid > 0">
            INNER JOIN tenant_app_map tam ON app.id = tam.appId
            AND tam.eid = #{tenantSid}
        </if>
        <if test="sid != null and sid > 0">
            INNER JOIN supplier_app_map sam ON app.id = sam.appId
            AND sam.sid = #{sid}
        </if>
        <where>
            <if test="appId != null and appId > 0">
                AND app.id = #{appId}
            </if>
            <if test="appCode != null and appCode != ''">
                AND app.appCode = #{appCode}
            </if>
            <if test="searchText != null and searchText != ''">
                AND (app.appCode LIKE CONCAT('%', #{searchText} ,'%')
                OR app.appName LIKE CONCAT('%', #{searchText} ,'%'))
            </if>
        </where>
        <if test="orderBy != null and orderBy != ''">
            ORDER BY ${orderBy}
        </if>
    </select>

    <select id="getTenantInfoByApp" resultType="com.digiwin.escloud.aiouser.model.tenant.TenantInfo">
        SELECT s.sid, s.eid, t.id, t.name, t.customer_id AS serviceCode, (t.sid IS NOT NULL) AS supplierDefault
        FROM supplier s
                 LEFT JOIN tenant t ON s.eid = t.sid
                 LEFT JOIN tenant_app_contract tac ON s.sid = tac.sid AND s.eid = tac.eid
        WHERE tac.appCode=#{appCode} AND tac.status=#{status}
    </select>
</mapper>