package com.digiwin.escloud.issueservice.v3.dao.impl;

import com.digiwin.escloud.issueservice.t.model.issuekb.IssueKbCondition;
import com.digiwin.escloud.issueservice.t.model.issuekb.IssueKbExecuteRecord;
import com.digiwin.escloud.issueservice.t.model.issuekb.IssueKbKeyWord;
import com.digiwin.escloud.issueservice.t.model.issuekb.IssueKbOverview;
import com.digiwin.escloud.issueservice.v3.dao.IIssueSchedulingDaoV3;
import org.apache.ibatis.session.SqlSession;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;


@Service
public class IssueSchedulingDaoV3 implements IIssueSchedulingDaoV3 {
    @Autowired
    private SqlSession sqlSession;

    @Override
    public List<Long> selectUnCloseCaseList(String dateTime) {
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("dateTime", dateTime);
        return sqlSession.selectList("escloud.issueschedulemapperv3.selectUnCloseCaseList",map);
    }

    @Override
    public List<IssueKbCondition> getIssueKbConditionList(Map<String,Object> map) {
        return sqlSession.selectList("escloud.issueschedulemapperv3.getIssueKbConditionList",map);
    }

    @Override
    public List<IssueKbKeyWord> getIssueKbKeyWordList() {
        Map<String, Object> map = new HashMap<String, Object>();
        return sqlSession.selectList("escloud.issueschedulemapperv3.getIssueKbKeyWordList",map);
    }
    @Override
    public List<IssueKbOverview> getCNIssueList(String closeDate, Long ikcId) {
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("closeDate", closeDate);
        map.put("ikcId", ikcId);
        return sqlSession.selectList("escloud.issueschedulemapperv3.getCNIssueList",map);
    }
    @Override
    public List<IssueKbOverview> getTWIssueList(String closeDate, Long ikcId) {
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("closeDate", closeDate);
        map.put("ikcId", ikcId);
        return sqlSession.selectList("escloud.issueschedulemapperv3.getTWIssueList",map);
    }
    @Override
    public int batchInsertIssueKbOverviews(List<IssueKbOverview> issueKbOverview) {
        return sqlSession.insert("escloud.issueschedulemapperv3.batchInsertIssueKbOverviews",issueKbOverview);
    }
    @Override
    public int saveIssueKbOverview(IssueKbOverview issueKbOverview) {
        return sqlSession.insert("escloud.issueschedulemapperv3.saveIssueKbOverview",issueKbOverview);
    }

    @Override
    public int updateIssueKbOverview(IssueKbOverview issueKbOverview) {
        return sqlSession.update("escloud.issueschedulemapperv3.updateIssueKbOverview",issueKbOverview);
    }

    @Override
    public int saveIssueKbExecuteRecord(IssueKbExecuteRecord issueKbExecuteRecord) {
        return sqlSession.insert("escloud.issueschedulemapperv3.saveIssueKbExecuteRecord",issueKbExecuteRecord);
    }

    @Override
    public int selectIssueKbAIStatus(Long ikerId) {
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("ikerId",ikerId);
        return sqlSession.selectOne("escloud.issueschedulemapperv3.selectIssueKbAIStatus",map);
    }
    @Override
    public int updateIssueKbExecuteRecord(Long ikerId, int status) {
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("id", ikerId);
        map.put("status", status);
        return sqlSession.update("escloud.issueschedulemapperv3.updateIssueKbExecuteRecord",map);
    }
}
