package com.digiwin.escloud.aiocmdb.asset.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

@Data // 自动生成 Getter, Setter, toString, equals, hashCode等方法
@NoArgsConstructor // 自动生成无参构造函数
@AllArgsConstructor // 自动生成全参构造函数
public class AssetExistenceStatus {

    /**
     * 资产ID
     */
    private Long assetId;
    private String assetCode;
    private String assetName;

    /**
     * 是否存在于关联表中 (true: 存在, false: 不存在)
     */

     public boolean isExists() {
         return StringUtils.isNotBlank(assetCode);
     }
}
