package com.digiwin.escloud.aiobasic.operatelog.handler.impl;

import com.digiwin.escloud.aiobasic.edr.model.base.ReportReceiver;
import com.digiwin.escloud.aiobasic.edr.model.base.ReportRecord;
import com.digiwin.escloud.aiobasic.operatelog.handler.OperateLogParamHandler;
import com.digiwin.escloud.aiobasic.operatelog.model.OperateLogSaveParam;
import com.digiwin.escloud.aiobasic.operatelog.model.OperateLogType;
import com.digiwin.escloud.common.util.DateUtil;
import com.digiwin.escloud.common.util.IntegerUtil;
import com.digiwin.escloud.common.util.StringUtil;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.digiwin.escloud.common.util.DateUtil.DATE_TIME_FORMATTER;

@Component
public class EDRGenerateSaveAndSendParamHandler implements OperateLogParamHandler {
    @Override
    public boolean isSupport(OperateLogType op) {
        return OperateLogType.SendMail.equals(op);
    }

    @Override
    public void handle(List<Object> param, OperateLogSaveParam logParam) {
        ReportRecord dto = ReportRecord.objectToReportRecord(param.get(0));

        // 檢查參數不為空及是否為版更評估(reportType = 4)
        if (Objects.isNull(dto) || IntegerUtil.isEmpty(dto.getReportType()) || dto.getReportType() != 4) {
            return;
        }

        String receiverMails = dto.getSendLog().getReceiverList().stream()
                .map(ReportReceiver::getReceiverMail)
                .collect(Collectors.joining(","));

        logParam.setEid(StringUtil.toString(dto.getEid()));
        logParam.setProcessUserId(dto.getUserId());
        logParam.setProcessUserName(dto.getUserName());
        logParam.setReportId(StringUtil.toString(dto.getReportId()));
        logParam.setOperateType(OperateLogType.SendMail.getCode());
        logParam.setStartTime(DateUtil.getNowFormatString(DATE_TIME_FORMATTER));
        logParam.setReceiverMails(receiverMails);
    }
}
