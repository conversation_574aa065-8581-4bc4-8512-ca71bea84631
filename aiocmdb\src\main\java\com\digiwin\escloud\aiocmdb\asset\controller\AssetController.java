package com.digiwin.escloud.aiocmdb.asset.controller;

import com.digiwin.escloud.aiocmdb.asset.model.AssetSaveBigDataParam;
import com.digiwin.escloud.aiocmdb.asset.service.impl.AssetAutomaticallyEstablishedService;
import com.digiwin.escloud.aiocmdb.asset.service.AssetService;
import com.digiwin.escloud.aioitms.model.instance.AiopsItemContextDTO;
import com.digiwin.escloud.common.response.BaseResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.LinkedHashMap;
import java.util.List;

@RestController
@RequestMapping("/asset")
public class AssetController {
    @Autowired
    private AssetService assetService;
    @Autowired
    private AssetAutomaticallyEstablishedService assetAutomaticallyEstablishedService;

    @DeleteMapping("/deleteAsset")
    public BaseResponse deleteAsset(@RequestParam String modelCode, @RequestParam Long id,@RequestParam Long eid) {
        return assetService.deleteAsset(modelCode, id, eid);
    }

    @PutMapping(value = "/asset/invalidCmdbAsset")
    public BaseResponse invalidCmdbAsset(List<AiopsItemContextDTO> aicList) {
        return assetService.invalidCmdbAsset(aicList);
    }

//    @PutMapping(value = "/batchUpdateAssetLevel")
//    public BaseResponse batchUpdateAssetLevel(@RequestBody AssetLevelBigDataParam param) {
//        return assetService.batchUpdateAssetLevel(param);
//    }

    @PostMapping(value = "/batchSaveAssetToStarRocksAndHBase")
    public BaseResponse batchSaveAssetToStarRocksAndHBase(@RequestParam String modelCode,@RequestParam String database,
                                                          @RequestParam Long eid,
                                                          @RequestBody List<LinkedHashMap<String, Object>> dataRows
                                             ) {
        return assetAutomaticallyEstablishedService.batchSaveToStarRocksAndHBaseWithUpdate(dataRows, modelCode, database, eid);
    }

    @PostMapping(value = "/modifyAssetStatus")
    public BaseResponse modifyAssetStatus(@RequestParam String modelCode,
                                          @RequestParam String database,
                                          @RequestParam(required = false) Boolean modifyInstanceStatus,
                                          @RequestBody List<LinkedHashMap<String, Object>> dataRows
    ) {
        return assetAutomaticallyEstablishedService.modifyAssetStatus(dataRows, modelCode, database,modifyInstanceStatus);
    }

    @PostMapping(value = "/batchSaveInstanceToStarRocksAndHBase")
    public BaseResponse batchSaveInstanceToStarRocksAndHBase(@RequestBody AssetSaveBigDataParam param) {
        return assetService.batchSaveInstanceToStarRocksAndHBase(param);
    }

    @PostMapping("/checkAssetExistsInAssetsRelated")
    public BaseResponse checkAssetExistsInAssetsRelated(@RequestBody List<Long> assetIdList) {
        return assetService.checkAssetsExistInAssetsRelated(assetIdList);
    }

}
