package com.digiwin.escloud.aiocmdb.model.model;

import com.digiwin.escloud.common.annotation.ModifyGroup;
import com.digiwin.escloud.common.annotation.POModify;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Objects;

@Data
@POModify(tableName = "cmdb_model", groups = ModifyGroup.class)
public class Model {
    @ApiModelProperty("主键")
    private long id;
    @ApiModelProperty("应用编号")
    private String appCode;
    @ApiModelProperty("模型编号，唯一")
    private String modelCode;
    @ApiModelProperty("模型名称")
    private String modelName;
    @ApiModelProperty("模型版本")
    private String modelVersion;
    @ApiModelProperty("模型分类编号")
    private String modelGroupCode;
    @ApiModelProperty("模型状态")
    private String status;
    @ApiModelProperty("图标地址")
    private String imgUrl;
    @ApiModelProperty("描述")
    private String description;
    @ApiModelProperty("运维商sid")
    private long sid;
    @ApiModelProperty("租户eid")
    private long eid;

    public static final String CONNECTOR = "_";
    public static final String MD5MODEL = "MD5MODEL";
    public static final String MD5MODEL_DEFAULT = "0";
    public static final String MD5ETL = "MD5ETL";
    public static final String MD5ETL_DEFAULT = "1";

    public boolean relatedAsset;

    public boolean modelChanged(String modelMd5){
        if (Objects.isNull(this.modelVersion)) {
            return true;
        }
        if (this.modelVersion.contains(CONNECTOR)) {
            String[] versions = modelVersion.split(CONNECTOR);
            return !versions[0].equals(modelMd5);
        }
        return !modelMd5.equals(this.modelVersion);
    }
    public boolean etlChanged(String etlMd5){
        if (Objects.isNull(this.modelVersion)) {
            return true;
        }
        if (this.modelVersion.contains(CONNECTOR)) {
            String[] versions = modelVersion.split(CONNECTOR);
            return !versions[1].equals(this.modelName);
        }
        return !etlMd5.equals(this.modelVersion);
    }
    public String buildNewVersion4Etl(String etlMd5){
        if (Objects.nonNull(this.modelVersion) && this.modelVersion.contains(CONNECTOR)) {
            String[] versions = modelVersion.split(CONNECTOR);
            return versions[0] + CONNECTOR + etlMd5;
        }
        return MD5MODEL_DEFAULT + CONNECTOR + etlMd5;
    }
    public String buildNewVersion4Model(String modelMd5){
        if (Objects.nonNull(this.modelVersion) && this.modelVersion.contains(CONNECTOR)) {
            String[] versions = modelVersion.split(CONNECTOR);
            return modelMd5+ CONNECTOR + versions[1];
        }
        return modelMd5+ CONNECTOR + MD5ETL_DEFAULT;
    }
}
