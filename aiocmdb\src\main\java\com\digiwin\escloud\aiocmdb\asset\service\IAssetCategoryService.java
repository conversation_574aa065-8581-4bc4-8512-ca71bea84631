package com.digiwin.escloud.aiocmdb.asset.service;

import com.digiwin.escloud.aiocmdb.asset.model.*;
import com.digiwin.escloud.common.model.ResponseBase;
import com.digiwin.escloud.common.response.BaseResponse;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 资产类别分类表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-01
 */
public interface IAssetCategoryService  {

    // AssetCategoryClassification 相关方法
    BaseResponse saveAssetCategoryClassification(AssetCategoryClassification classification);

    BaseResponse updateAssetCategoryClassification(AssetCategoryClassification classification);

    ResponseBase deleteAssetCategoryClassification(Long id);

    BaseResponse getAssetCategoryClassificationList(String categoryType);

    // AssetCategory 相关方法
    BaseResponse saveAssetCategory(AssetCategory category);

    BaseResponse updateAssetCategory(AssetCategory category);

    ResponseBase updateAssetCategoryStatus(Long id, String status);

    ResponseBase deleteAssetCategory(Long id);

    BaseResponse getAssetCategoryList(AssetCategoryQueryParam queryParam);

    // CmdbModelDataFieldRelationMapping 相关方法
    BaseResponse saveCmdbModelDataFieldRelationMapping(List<CmdbModelDataFieldRelationMapping> mappingList);

    ResponseBase<List<CmdbModelDataFieldRelationMapping>> getCmdbModelDataFieldRelationMappingList(String targetModelCode);

    // AssetCategoryCodingRule 相关方法
    ResponseBase getAllAssetCategoryCodingRule();

    ResponseBase batchUpsertAiopsCollectSink(List<AiopsCollectSink> sinkList);

    ResponseBase getAllModel(String modelGroupCode, String modelCode);

    /**
     * 根据modelCode填充模型显示字段配置
     *
     * @param modelCode 模型编码
     * @return 操作结果
     */
    ResponseBase populateModelShowFields(String modelCode,Long sid);

    /**
     * 根据用户ID删除用户字段配置
     *
     * @param userId    用户ID
     * @param modelCode
     * @return 操作结果
     */
    ResponseBase deleteCmdbModelShowFieldUser(String userId,String modelCode);

    ResponseBase dsProcess(AiopsProcessParam param);

    /**
     * 支持传入查询列和过滤条件，因为这是union all所以查询列和过滤条件列每个表都需要有
     *
     * @param request 查询请求参数
     * @return 查询结果
     */
    ResponseBase<List<Map<String, Object>>> queryStarRocksData(StarRocksQueryRequest request);
}
