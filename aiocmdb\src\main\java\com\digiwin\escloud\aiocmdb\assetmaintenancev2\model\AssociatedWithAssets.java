package com.digiwin.escloud.aiocmdb.assetmaintenancev2.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class AssociatedWithAssets {
    @Getter
    private String associatedAssetId; //	关联资产编碼	string

    @Getter
    private String associatedModelCode; //	关联模型 Code	string

    @Getter
    private String associatedSinkName;  //	关联模型 sinkName	string

    @Getter @Setter
    private String categoryName;        //資產類別名稱(代碼處理)

    @Getter @Setter
    private String categoryIconUrl;// 資產類別图标URL(代碼處理)

    @Getter
    private String assetRelatedId; //資產關聯id

    @Getter
    private String eid; //租戶id

    @Getter
    private List<Object> data;
    
    public AssociatedWithAssets() {
        this.data = new ArrayList<>();
    }
    public AssociatedWithAssets(
            String eid, String assetRelatedId, String associatedAssetId,
            String associatedModelCode, String associatedSinkName) {
        this.eid = eid;
        this.assetRelatedId = assetRelatedId;
        this.associatedAssetId = associatedAssetId;
        this.associatedModelCode = associatedModelCode;
        this.associatedSinkName = associatedSinkName;
        this.data = new ArrayList<>();
    }

    public AssociatedWithAssets(
            String associatedAssetId,
            String associatedModelCode, String associatedSinkName) {
        this.associatedAssetId = associatedAssetId;
        this.associatedModelCode = associatedModelCode;
        this.associatedSinkName = associatedSinkName;
        this.data = new ArrayList<>();
    }

    public void addData(List<?> newDatas) {
        this.data.addAll(newDatas);
    }
}
