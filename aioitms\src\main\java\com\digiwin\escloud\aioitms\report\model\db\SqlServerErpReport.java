package com.digiwin.escloud.aioitms.report.model.db;

import com.digiwin.escloud.aioitms.report.annotation.EsIndexCode;
import com.digiwin.escloud.aioitms.report.model.base.ProductDbReport;
import lombok.*;
import org.springframework.data.elasticsearch.annotations.Document;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

@Data
@Component
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@EsIndexCode("MSSQL_ERP") // 都要一樣才會配得到
@Document(indexName = "mssql_erp_" + "#{@dbEsAttribute.indexName}", shards = 3)
public class SqlServerErpReport extends ProductDbReport {
    private ProductInfo productInfo;
    private List<ProductCheck> productCheck;
    private List<DeviceCheck> deviceCheck;
    private Erp2Check erp2Check;
    private OtherCheck otherCheck;
    private String additionalNotes;
    private String customerWindow;

    @Data
    public static class ProductInfo {
        private Erp erp;
        private Erp2 erp2;
    }

    @Data
    public static class ProductCheck {
        private String deviceName;
        private String products;
        private String role;
        private String ipAddress;
    }

    @Data
    public static class DeviceCheck {
        private ProductPurchased productPurchased;
        private Environment environment;
        private List<Database> database;
    }

    @Data
    public static class Erp2Check {
        private DataCheck dataCheck;
        private List<Version> versionList;
    }

    @Data
    public static class OtherCheck {
        private List<BaseSpecification> baseSpecification;
        private CheckItems checkItems;
    }

    public static class Erp extends Product {
    }

    public static class Erp2 extends Product {
    }

    @Data
    public static class ProductPurchased  {
        private String erp;
        private List<String> erp2;
    }

    @Data
    public static class Environment {
        private String deviceId;
        private Map<String, Object> srvSpec;
        private List<Map<String, Object>> webSites;
        private List<Map<String, Object>> networks;
        private Map<String, Object> erpSetting;
    }

    @Data
    public static class Database {
        private String deviceId;
        private String sourceDbId;
        private Map<String, Object> serverInfo;
        private Map<String, Object> dbNameSize;
    }

    @Data
    public static class DataCheck {
        private List<ProductsInstallInfo> productsInstallInfo;
        private HrmInfo hrmInfo;
        private IsoModule isoModule;

        @Data
        public static class ProductsInstallInfo {
            private String productName;
            private String folderPath;
            private String folderSize;
            private String fileCount;
        }

        public static class HrmInfo extends Info {
        }

        public static class IsoModule extends Info {
        }
    }

    @Data
    public static class Version {
        private String productName;
        private String productVersion;
        private String licensesCount;
    }

    @Data
    public static class BaseSpecification {
        protected String osTitle;
        protected String cpu;
        protected String diskSpace;
        protected String memorySpace;
        protected String windowsUpdate;
        protected String networkBandwidth;
        protected String applicableVersions;
    }

    @Data
    public static class CheckItems {
        private OtherConnection otherConnection;
        private HasReplication hasReplication;
        private HasDecimal hasDecimal;
        private CrossConnection crossConnection;
        private String erpEtimatedHours;
        private String erp2EtimatedHours;

        public static class OtherConnection extends Info {
        }

        public static class HasReplication extends Info {
        }

        public static class HasDecimal extends Info {
        }

        public static class CrossConnection extends Info {
        }
    }

    @Data
    public static class Product {
        private List<String> allPurchased;
        private List<ProductModel> allProduct;

        @Data
        @Builder
        @NoArgsConstructor
        @AllArgsConstructor
        public static class ProductModel {
            private String code;
            private String value;
        }
    }

    @Data
    public static class Info {
        private String isWith;
        private String comment;
    }

    @Override
    public void setReportBasicData(DbReportRecord dbReportRecord) {
        super.setReportBasicData(dbReportRecord);
        this.setCustomerWindow("");
    }
}
