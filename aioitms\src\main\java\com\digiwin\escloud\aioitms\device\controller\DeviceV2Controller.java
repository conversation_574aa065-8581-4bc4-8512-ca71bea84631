package com.digiwin.escloud.aioitms.device.controller;

import com.digiwin.escloud.aioitms.authorize.service.IAiopsAuthorizeV2Service;
import com.digiwin.escloud.aioitms.collectapp.model.CollectAppMapping;
import com.digiwin.escloud.aioitms.collectwarning.model.CollectWarning;
import com.digiwin.escloud.aioitms.device.model.*;
import com.digiwin.escloud.aioitms.device.service.IDeviceV2Service;
import com.digiwin.escloud.aioitms.instance.model.AiopsItemContext;
import com.digiwin.escloud.aioitms.model.authorize.AiopsAuthStatus;
import com.digiwin.escloud.aioitms.model.device.AiopsKitDevice;
import com.digiwin.escloud.aioitms.model.device.DeviceChangeSource;
import com.digiwin.escloud.aioitms.model.device.DevicePlatform;
import com.digiwin.escloud.aioitms.model.device.TenantDeviceCntReq;
import com.digiwin.escloud.aioitms.model.instance.AdimRelateContext;
import com.digiwin.escloud.aioitms.modulecollect.model.ModuleCollectLayered;
import com.digiwin.escloud.aioitms.ruleengine.service.IRuleEngineService;
import com.digiwin.escloud.common.controller.ControllerBase;
import com.digiwin.escloud.common.model.ResponseCode;
import com.digiwin.escloud.common.response.BaseResponse;
import com.digiwin.escloud.common.response.PageInfo;
import com.digiwin.escloud.common.util.BooleanUtil;
import com.digiwin.escloud.common.util.CollectionUtil;
import com.digiwin.escloud.common.util.RequestUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.apache.commons.lang.BooleanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Api(value = "/v2/device", protocols = "HTTP", tags = {"设备相关v2接口"}, description = "设备相关v2接口")
@RestController
@RequestMapping("/v2/device")
public class DeviceV2Controller extends ControllerBase {
    @Autowired
    IDeviceV2Service deviceService;
    @Autowired
    IRuleEngineService ruleEngineService;

    @Autowired
    IAiopsAuthorizeV2Service authorizeService;

    //region 地端相关

    @ApiOperation("保存设备完整信息")
    @PostMapping(value = "/complete/info")
    public BaseResponse saveCompleteInfo(
            @ApiParam(value = "设备完整信息对象", required = true)
            @RequestBody AiopsKitDeviceCompleteInfo deviceCompleteInfo) {
        return getBaseResponse(() -> deviceService.saveDeviceByCompleteInfo(deviceCompleteInfo),
                false, false, null);
    }

    @ApiOperation("保存设备基础信息")
    @PostMapping(value = "/info")
    public BaseResponse saveDeviceInfo(
            @ApiParam(value = "设备基础信息对象", required = true)
            @RequestBody AiopsKitDevice device) {
        return getBaseResponse(() -> saveDeviceInfoCore(device),
                false, false, null);
    }

    private BaseResponse saveDeviceInfoCore(AiopsKitDevice device) {
        BaseResponse<Map<String, Object>> response = deviceService.saveDeviceInfo(device);
        if (!response.checkIsSuccess()) {
            return response;
        }
        Map<String, Object> resultMap = response.getData();
        if (CollectionUtil.isNotEmpty(resultMap) && BooleanUtil.objectToBoolean(resultMap.get("hasChanged"))) {
            //设备的任何异动，都要更新规则引擎
            Map<String, Object> map = new HashMap<>(1);
            map.put("deviceId", device.getDeviceId());
            ruleEngineService.asyncModifyReAdditionalContentByMap(map);
        }
        return response;
    }

    @ApiOperation("保存设备协议信息")
    @PostMapping(value = "/agreement")
    public BaseResponse saveDeviceAgreement(
            @ApiParam(value = "设备协议信息对象", required = true)
            @RequestBody AiopsKitDeviceAgreement deviceAgreement) {
        return getBaseResponse(() -> deviceService.saveDeviceAgreement(deviceAgreement),
                false, false, null);
    }

    @ApiOperation("保存设备客户信息")
    @PostMapping(value = "/{deviceId}/customer/info")
    public BaseResponse saveDeviceCustomerInfo(
            @ApiParam(value = "设备Id", required = true)
            @PathVariable("deviceId") String deviceId) {
        Long eid = RequestUtil.getHeaderEid();
        return getBaseResponse(() -> deviceService.saveDeviceCustomerInfo(deviceId, eid),
                false, false, null);
    }

    @ApiOperation("保存设备数据源信息")
    @PostMapping(value = "/{deviceId}/data/source")
    public BaseResponse saveDeviceDataSource(
            @ApiParam(value = "设备Id", required = true)
            @PathVariable("deviceId") String deviceId,
            @ApiParam(value = "设备数据源信息对象列表", required = true)
            @RequestBody List<AiopsKitDeviceDataSource> deviceDataSourceList) {
        return getBaseResponse(() -> deviceService.saveDeviceDataSource( deviceId, deviceDataSourceList));//huly: 修复漏洞/bug 去掉入参0l
    }

    @ApiOperation("保存设备特定数据源信息")
    @PostMapping(value = "/{deviceId}/part/data/source")
    public BaseResponse saveDeviceDataSource(
            @ApiParam(value = "设备Id", required = true)
            @PathVariable("deviceId") String deviceId,
            @ApiParam(value = "是否尝试开启授权")
            @RequestParam(value = "tryOpenAuth", required = false, defaultValue = "false") Boolean tryOpenAuth,
            @ApiParam(value = "设备数据源信息对象列表", required = true)
            @RequestBody AiopsKitDeviceDataSource deviceDataSource,
            @ApiParam(value = "采集项目标志位")
            @RequestParam(required = false) Boolean addCollItem) {
        return getBaseResponse(() -> deviceService.savePartDeviceDataSource(deviceId, tryOpenAuth, deviceDataSource,
                addCollItem, null), false, false, null);
    }

    @ApiOperation("删除设备特定数据源信息")
    @DeleteMapping(value = "/{deviceId}/part/data/source/{dbId}")
    public BaseResponse deleteDeviceDataSource(
            @ApiParam(value = "设备Id", required = true)
            @PathVariable("deviceId") String deviceId,
            @ApiParam(value = "设备Id", required = true)
            @PathVariable("dbId") String dbId) {
        return getBaseResponse(() -> deviceService.deletePartDeviceDataSource(deviceId, dbId),
                false, false, null);
    }

    @ApiOperation("获取设备基础信息")
    @GetMapping(value = "/{deviceId}/basic/info")
    public BaseResponse getDeviceBasicInfo(
            @ApiParam(value = "设备Id", required = true)
            @PathVariable("deviceId") String deviceId) {
        return getBaseResponse(() -> deviceService.getDeviceBasicInfo(deviceId),
                false, false, null);
    }

    @ApiOperation("获取设备授权状态")
    @GetMapping(value = "/{deviceId}/auth/status")
    public BaseResponse getDeviceAuthStatus(
            @ApiParam(value = "设备Id", required = true)
            @PathVariable("deviceId") String deviceId) {
        return getBaseResponse(() -> deviceService.getDeviceAuthStatus(deviceId),
                false, false, null);
    }

    @ApiOperation(value = "获取设备是否是未定义")
    @GetMapping(value = "/{deviceId}/is/undefined")
    public BaseResponse getDeviceIsUndefined(
            @ApiParam(value = "设备Id", required = true)
            @PathVariable(value = "deviceId") String deviceId) {
        return getBaseResponse(() -> deviceService.getDeviceIsUndefined(deviceId),
                false, false, null);
    }

    //endregion

    @ApiOperation(value = "实例真实信息保存(从数采异动同步)")
    @PostMapping(value = "/ai/trust/info/save")
    public BaseResponse aiTrustInfoSave(
            @ApiParam(value = "实例真实信息字典", required = true)
            @RequestBody() Map<String, Object> aiTrustInfoMap) {
        return getBaseResponse(() -> deviceService.aiTrustInfoSave(aiTrustInfoMap),
                false, false, null);
    }

    @ApiOperation(value = "实例真实信息移除(从数采异动同步)")
    @DeleteMapping(value = "/ai/trust/info/remove")
    public BaseResponse aiTrustInfoRemove(
            @ApiParam(value = "实例真实信息字典", required = true)
            @RequestBody() Map<String, Object> aiTrustInfoMap) {
        return getBaseResponse(() -> deviceService.aiTrustInfoRemove(aiTrustInfoMap),
                false, false, null);
    }

    @ApiOperation(value = "获取设备列表")
    @PostMapping(value = "/getDeviceList")
    public BaseResponse getDeviceList(
            @ApiParam(value = "设备请求条件", required = true)
            @RequestBody() DeviceRequest deviceRequest) {
        return getBaseResponse(() -> deviceService.getDeviceList(deviceRequest),
                false, false, null);
    }

    @ApiOperation(value = "设备基本信息")
    @GetMapping(value = "/getDeviceDetail")
    public BaseResponse<DeviceInfo> getDeviceDetail(
            @RequestParam(value = "id", required = false) Long id,
            @RequestParam(value = "deviceId", required = false) String deviceId) {
        Map<String, Object> map = new HashMap<>();
        map.put("id", id);
        map.put("deviceId", deviceId);
        return getBaseResponse(() -> BaseResponse.ok(deviceService.getDeviceDetail(map)),
                false, false, null);
    }

    @ApiOperation(value = "设备信息（产品或者收集项配置）")
    @GetMapping(value = "/getDeviceDetailByType")
    public BaseResponse<PageInfo> getDeviceDetailByType(
            @ApiParam(value = "设备主键Id", required = true)
            @RequestParam(value = "id") long id,
            @ApiParam(value = "设备Id", required = true)
            @RequestParam(value = "deviceId") String deviceId,
            @ApiParam(value = "类型", required = true)
            @RequestParam(value = "type", defaultValue = "product") String type,
            @ApiParam(value = "搜索条件")
            @RequestParam(value = "search", required = false) String search,
            @ApiParam(value = "页次")
            @RequestParam(value = "pageNum", required = false, defaultValue = "0") int pageNum,
            @ApiParam(value = "页笔数")
            @RequestParam(value = "pageSize", required = false, defaultValue = "0") int pageSize) {
        return getBaseResponse(() -> deviceService.getDeviceDetailByType(id, type, search, pageNum, pageSize),
                false, false, null, ResponseCode.QUERY_VERIFY);
    }

    @ApiOperation(value = "获取设备收集项预警项设定列表")
    @GetMapping(value = "/{adId}/collect/warning/list")
    public BaseResponse<PageInfo> getDeviceCollectWarningList(
            @ApiParam(value = "设备主键Id", required = true)
            @PathVariable(value = "adId") Long adId,
            @ApiParam(value = "页次")
            @RequestParam(value = "pageNum", required = false, defaultValue = "0") int pageNum,
            @ApiParam(value = "页笔数")
            @RequestParam(value = "pageSize", required = false, defaultValue = "0") int pageSize) {
        return getBaseResponse(() -> deviceService.getDeviceCollectWarningSetList(adId, pageNum, pageSize),
                false, false, null, ResponseCode.QUERY_VERIFY);
    }
    @ApiOperation(value = "依据运维实例Id获取预警设定列表")
    @GetMapping(value = "/warning/list/by/ai/id")
    public BaseResponse<PageInfo> getWarningListByAiId(
            @ApiParam(value = "运维实例Id", required = true)
            @RequestParam(value = "aiId") Long aiId,
            @ApiParam(value = "是否分页")
            @RequestParam(value = "needPaging", required = false, defaultValue = "true") Boolean needPaging,
            @ApiParam(value = "页次")
            @RequestParam(value = "pageNum", required = false, defaultValue = "0") Integer pageNum,
            @ApiParam(value = "页笔数")
            @RequestParam(value = "pageSize", required = false, defaultValue = "10") Integer pageSize) {
        return getBaseResponse(() -> deviceService.getWarningListByAiId(aiId, needPaging, pageNum, pageSize),
                false, false, null, ResponseCode.QUERY_VERIFY);
    }
    @ApiOperation(value = "设备添加产品应用")
    @PostMapping(value = "/addProductAppForDevice")
    public BaseResponse addProductAppForDevice(
            @ApiParam(value = "运维商产品Id", required = true)
            @RequestParam(value = "spId") long spId,
            @ApiParam(value = "设备主键Id", required = true)
            @RequestParam(value = "adId") long adId,
            @ApiParam(value = "设备Id", required = true)
            @RequestParam(value = "deviceId") String deviceId,
            @ApiParam(value = "是否自动添加产品收集项(如果是必选收集项会忽略此选项，因为收集项必选)")
            @RequestParam(value = "autoAddCollect", required = false) boolean autoAddCollect,
            @ApiParam(value = "设备产品服务/应用映射列表", required = true)
            @RequestBody List<DeviceProductMapping> deviceProductMappingList) {
        return getBaseResponse(() -> {
            BaseResponse baseResponse = deviceService.addProductAppForDevice(spId, adId, deviceId,
                    deviceProductMappingList);
            if (baseResponse.checkIsSuccess()) {
                deviceService.addAppCollectForDevice(adId, deviceId, autoAddCollect, deviceProductMappingList);
            }
            return baseResponse;
        }, false, false, null);
    }

    @ApiOperation(value = "查询设备已添加的产品应用")
    @GetMapping(value = "/getProductAppForDevice")
    public BaseResponse getProductAppForDevice(
            @ApiParam(value = "运维商产品Id", required = true)
            @RequestParam(value = "spId", required = false) long spId,
            @ApiParam(value = "设备主键Id", required = true)
            @RequestParam(value = "adId") long adId,
            @RequestParam(value = "modelCode", required = false, defaultValue = "") String modelCode
    ) {
        return getBaseResponse(() -> BaseResponse.ok(deviceService.getProductAppForDevice(spId, adId, modelCode)),
                false, false, null);
    }

    @ApiOperation(value = "设备修改产品应用")
    @PutMapping(value = "/updateProductAppForDevice")
    public BaseResponse updateProductAppForDevice(
            @ApiParam(value = "运维商产品Id", required = true)
            @RequestParam(value = "spId", required = false) long spId,
            @ApiParam(value = "设备主键Id", required = true)
            @RequestParam(value = "adId") long adId,
            @ApiParam(value = "设备Id", required = true)
            @RequestParam(value = "deviceId") String deviceId,
            @ApiParam(value = "是否自动添加产品收集项(如果是必选收集项会忽略此选项，因为收集项必选)")
            @RequestParam(value = "autoAddCollect", required = false) boolean autoAddCollect,
            @RequestParam(value = "aiopsItem", required = false) String aiopsItem,
            @ApiParam("是否自动开启授权")
            @RequestParam(value = "autoOpenAuth", required = false) Boolean autoOpenAuth,
            @ApiParam(value = "设备产品服务/应用映射列表", required = true)
            @RequestBody List<DeviceProductMapping> deviceProductMappingList) {
        return getBaseResponse(() -> {
            BaseResponse baseResponse = deviceService.updateProductAppForDevice(
                    spId, adId, deviceId, aiopsItem, deviceProductMappingList
            );
            if (baseResponse.checkIsSuccess()) {
                deviceService.addAppCollectForDevice(adId, deviceId, autoAddCollect, deviceProductMappingList);
            }

            // 檢查是不是要自動授權.
            AiopsAuthStatus aiopsAuthStatus = BooleanUtils.toBoolean(autoOpenAuth) ? AiopsAuthStatus.AUTHED : null;
            if (aiopsAuthStatus == null) {
                return baseResponse;
            }

            Long eid = RequestUtil.getHeaderEid();
            List<AiopsItemContext> aicList = deviceProductMappingList.stream()
                    .map(row -> row.getAiopsItemContext())
                    .collect(Collectors.toList());

            BaseResponse response = authorizeService.modifyInstanceAuthStatus(eid, aiopsAuthStatus, aicList);
            if (!response.checkIsSuccess()) {
                Map<String, Object> errMap = new HashMap<>();
                errMap.put("errData", response.getData());
                errMap.put("updateProductAppData", baseResponse.getData());
                response.setData(errMap);
                return response;
            }

            return baseResponse;
        }, false, false, null);
    }

    @ApiOperation(value = "设备删除产品应用")
    @DeleteMapping(value = "/deleteProductAppForDevice")
    public BaseResponse deleteProductAppForDevice(
            @ApiParam(value = "运维商产品Id", required = true)
            @RequestParam(value = "spId") long spId,
            @ApiParam(value = "设备主键Id", required = true)
            @RequestParam(value = "adId") long adId) {
        Long sid = RequestUtil.getHeaderSid();
        return getBaseResponse(() -> deviceService.deleteProductAppForDevice(sid, spId, adId),
                false, false, null, ResponseCode.DELETE_FAILD);
    }

    @ApiOperation(value = "查设备未添加的收集项列表")
    @GetMapping(value = "/getCollectConfigsNotInDevice")
    public BaseResponse<List<DeviceType>> getCollectConfigsNotInDevice(
            @ApiParam(value = "设备Id", required = true)
            @RequestParam(value = "deviceId") String deviceId,
            @ApiParam(value = "作用域Id")
            @RequestParam(value = "scopeId", required = false, defaultValue = "DefaultConfig") String scopeId,
            @ApiParam(value = "收集项名称(模糊查询用)")
            @RequestParam(value = "collectItem", required = false) String collectItem) {
        return getBaseResponse(() -> deviceService.getCollectConfigsNotInDevice(RequestUtil.getHeaderSid(), deviceId, scopeId, collectItem),
                false, false, null);
    }

    @ApiOperation(value = "设备新增收集项")
    @PostMapping(value = "/addCollectDetailForDevice")
    public BaseResponse addCollectDetailForDevice(
            @ApiParam(value = "设备收集项明细Id", required = true)
            @RequestParam(value = "id") long adcdId,
            @ApiParam(value = "设备主键Id")
            @RequestParam(value = "adId", required = false, defaultValue = "0") long adId,
            @ApiParam(value = "设备Id", required = true)
            @RequestParam(value = "deviceId") String deviceId,
            @ApiParam(value = "收集项名称", required = true)
            @RequestParam(value = "collectName") String collectName,
            @ApiParam(value = "收集项Id", required = true)
            @RequestParam(value = "accId") long accId,
            @ApiParam(value = "作用域Id")
            @RequestParam(value = "scopeId", required = false, defaultValue = "DefaultConfig") String scopeId) {
        return getBaseResponse(() -> deviceService.addCollectDetailForDevice(RequestUtil.getHeaderSid(), adcdId, adId,
                deviceId, collectName, accId, scopeId), false, false, null);
    }

    @ApiOperation(value = "设备修改收集项")
    @PutMapping(value = "/updateCollectDetailForDevice")
    public BaseResponse updateCollectDetailForDevice(
            @ApiParam(value = "设备收集项明细Id", required = true)
            @RequestParam(value = "id") long adcdId,
            @ApiParam(value = "收集项名称", required = true)
            @RequestParam(value = "collectName") String collectName,
            @ApiParam(value = "设备Id", required = true)
            @RequestParam(value = "deviceId") String deviceId) {
        return getBaseResponse(() -> deviceService.updateCollectDetailForDevice(adcdId, collectName, deviceId),
                false, false, null, ResponseCode.UPDATE_FAILD);
    }

    @ApiOperation(value = "设备删除收集项")
    @DeleteMapping(value = "/{adcdId}/deleteCollectDetailForDevice")
    public BaseResponse deleteCollectDetailForDevice(
            @ApiParam(value = "设备收集项明细Id", required = true)
            @PathVariable(value = "adcdId") long adcdId,
            @ApiParam(value = "收集项Id", required = true)
            @RequestParam(value = "accId") long accId,
            @ApiParam(value = "设备Id", required = true)
            @RequestParam(value = "deviceId") String deviceId,
            @ApiParam(value = "作用域Id")
            @RequestParam(value = "scopeId", required = false, defaultValue = "DefaultConfig") String scopeId,
            @ApiParam(required = true, value = "预警列表")
            @RequestBody List<CollectWarning> collectWarningList) {
        return getBaseResponse(() -> deviceService.deleteCollectDetailForDevice(adcdId, accId, deviceId, scopeId,
                collectWarningList), false, false, null);
    }

    @ApiOperation(value = "设备收集项-执行参数设定")
    @PutMapping(value = "/execParamsSetForDevice")
    public BaseResponse execParamsSetForDevice(
            @ApiParam(value = "设备主键Id", required = true)
            @RequestParam(value = "adId") long adId,
            @ApiParam(value = "设备Id", required = true)
            @RequestParam(value = "deviceId") String deviceId,
            @ApiParam(value = "租户Id")
            @RequestParam(value = "eid", required = false) Long eid,
            @ApiParam(value = "异动间隔时间")
            @RequestParam(value = "changeInterval", required = false) boolean changeInterval,
            @ApiParam(value = "设备收集项详情", required = true)
            @RequestBody DeviceCollectDetail deviceCollectDetail) {
        return getBaseResponse(() -> deviceService.execParamsSetForDevice(eid, changeInterval, adId, deviceId,
                deviceCollectDetail), false, false, null);
    }

    @ApiOperation(value = "收集项自定义(从数采异动同步)")
    @PostMapping(value = "/acc/customize")
    public BaseResponse customizeAcc(
            @ApiParam(value = "设备收集项Id", required = true)
            @RequestParam Long adcdId,
            @ApiParam(value = "设备Id", required = true)
            @RequestParam String deviceId,
            @ApiParam(value = "自定義收集项用字典", required = true)
            @RequestBody() Map<String, Object> map) {
        return getBaseResponse(() -> deviceService.customizeAcc(map,  adcdId, deviceId),
                false, false, null);
    }

    @ApiOperation(value = "更新收集项开关（是否执行）")
    @PutMapping(value = "/modifyCollectDetailEnableForDevice")
    public BaseResponse modifyCollectDetailEnableForDevice(
            @ApiParam(value = "设备收集项明细Id", required = true)
            @RequestParam(value = "adcdId") long adcdId,
            @ApiParam(value = "开关值", required = true)
            @RequestParam(value = "isEnable") boolean isEnable,
            @ApiParam(value = "设备Id", required = true)
            @RequestParam(value = "deviceId") String deviceId) {
        return getBaseResponse(() -> deviceService.modifyCollectDetailEnableForDevice(adcdId, isEnable,
                deviceId), false, false, null);
    }

    @ApiOperation(value = "更新收集项开关（是否预警）")
    @PutMapping(value = "/modifyCollectConfigWarningEnableForDevice")
    public BaseResponse modifyCollectConfigWarningEnableForDevice(
            @ApiParam(value = "设备收集项明细Id", required = true)
            @RequestParam(value = "adcdId") long adcdId,
            @ApiParam(value = "预警开关值", required = true)
            @RequestParam(value = "isWarningEnable") boolean isWarningEnable,
            @ApiParam(value = "收集项预警项列表", required = true)
            @RequestBody List<CollectWarning> collectWarningList) {
        return getBaseResponse(() -> deviceService.modifyCollectConfigWarningEnableForDevice(adcdId,
                isWarningEnable, collectWarningList), false, false, null);
    }

    @ApiOperation(value = "更新单个预警项开关（是否预警）")
    @PutMapping(value = "/modifyWarningEnableForDeviceWarning")
    public BaseResponse modifyWarningEnableForDeviceWarning(
            @ApiParam(value = "设备收集项明细Id", required = true)
            @RequestParam(value = "adcdId") long adcdId,
            @ApiParam(value = "收集项预警项id", required = true)
            @RequestParam(value = "acwId") long acwId,
            @ApiParam(value = "预警开关值", required = true)
            @RequestParam(value = "isWarningEnable") boolean isWarningEnable) {
        return getBaseResponse(() -> deviceService.modifyWarningEnableForDeviceWarning(adcdId, acwId,
                isWarningEnable), false, false, null);
    }

    @ApiOperation(value = "批量更新单个预警项开关（是否预警）")
    @PostMapping(value = "/modifyWarningEnableForDeviceWarning/batch")
    public BaseResponse modifyWarningEnableForDeviceWarningBatch(@RequestParam(value = "acwId") Long acwId,
                                                                 @RequestParam(value = "isWarningEnable") boolean isWarningEnable,
                                                                 @RequestParam(value = "eid") Long eid,
                                                                 @RequestBody List<Long> adcwIdList) {
        return getBaseResponse(() -> deviceService.modifyWarningEnableForDeviceWarningBatch(acwId, isWarningEnable , eid, adcwIdList),
                false, false, null);
    }

    @ApiOperation(value = "预警数据清洗")
    @PostMapping(value = "/data/clean")
    public BaseResponse warningDataClean(@RequestBody List<Long> eidList) {
        return ruleEngineService.warningDataClean(eidList);
    }



    @ApiOperation(value = "修改设备预警项设定")
    @PostMapping(value = "/modifyWarningSetForDevice")
    public BaseResponse modifyWarningSetForDevice(
            @ApiParam(value = "设备收集项明细Id", required = true)
            @RequestParam(value = "adcdId") long adcdId,
            @ApiParam(value = "收集项预警项id", required = true)
            @RequestParam(value = "acwId") long acwId,
            @ApiParam(value = "设备Id", required = true)
            @RequestParam(value = "deviceId") String deviceId,
            @ApiParam(value = "收集项预警项", required = true)
            @RequestBody CollectWarning collectWarning) {
        return getBaseResponse(() -> deviceService.modifyWarningSetForDevice(adcdId, acwId, deviceId, collectWarning),
                false, false, null);
    }

    @ApiOperation(value = "获取设备某个收集项的预警项设定列表（接驻派员收集数据后是否发预警通知邮件）")
    @GetMapping(value = "/getWarningListForDevice")
    public BaseResponse getWarningListForDevice(
            @ApiParam(value = "设备Id", required = true)
            @RequestParam(value = "deviceId") String deviceId,
            @ApiParam(value = "收集项Id", required = true)
            @RequestParam(value = "accId") long accId,
            @ApiParam(value = "设备收集项明细Id")
            @RequestParam(value = "adcdId", required = false) Long adcdId) {
        return getBaseResponse(() -> deviceService.getWarningListForDeviceCache(deviceId, accId, adcdId),
                false, false, null, ResponseCode.QUERY_VERIFY);
    }

    @ApiOperation(value = "清除某设备某收集项的预警缓存")
    @PostMapping(value = "/warningrule/remove")
    public BaseResponse removeWarningListForDevice(
            @ApiParam(value = "设备Id", required = true)
            @RequestParam(value = "deviceId") String deviceId,
            @ApiParam(value = "收集项Id", required = true)
            @RequestParam(value = "accId") long accId,
            @ApiParam(value = "设备收集项明细Id")
            @RequestParam(value = "adcdId", required = false) Long adcdId) {
        return getBaseResponse(() -> deviceService.removeWarningListForDeviceCache(deviceId, accId, adcdId),
                false, false, null, ResponseCode.QUERY_VERIFY);
    }


    @ApiOperation(value = "获取设备某个数据库列表")
    @GetMapping(value = "/{adId}/data/source")
    public BaseResponse getDeviceDataSource(
            @ApiParam(value = "设备主键Id", required = true)
            @PathVariable(value = "adId") Long adId,
            @ApiParam(value = "数据库类型")
            @RequestParam(value = "dbType", required = false) String dbType) {
        return getBaseResponse(() -> BaseResponse.ok(deviceService.getDeviceDataSourceList(adId, dbType, null)),
                false, false, null);
    }

    @ApiOperation(value = "获取设备应用/服务Id列表")
    @GetMapping(value = "/{adId}/app/id/list")
    public BaseResponse getDeviceAppIdList(
            @ApiParam(value = "设备主键Id", required = true)
            @PathVariable(value = "adId") Long adId) {
        return getBaseResponse(() -> BaseResponse.ok(deviceService.getDeviceAppIdList(adId)),
                false, false, null);
    }

    @ApiOperation(value = "依据产品服务/应用映射列表新增设备收集项")
    @PostMapping(value = "/collect/detail/by/app/mapping/list")
    public BaseResponse addDeviceCollectDetailByAppMappingList(
            @ApiParam(value = "设备主键Id", required = true)
            @RequestParam(value = "adId") Long adId,
            @ApiParam(value = "设备Id", required = true)
            @RequestParam(value = "deviceId") String deviceId,
            @ApiParam(value = "收集项产品服务/应用映射列表", required = true)
            @RequestBody() List<CollectAppMapping> collectAppMappingList) {
        return getBaseResponse(() -> BaseResponse.ok(deviceService.addDeviceCollectDetailByAppMappingList(adId,
                deviceId, collectAppMappingList)), false, false, null);
    }

    @ApiOperation(value = "获取设备执行参数设置列表")
    @PostMapping(value = "/collect/detail/exec/params")
    public BaseResponse getDeviceCollectDetailExecParams(
            @ApiParam(value = "设备收集项版本Id", required = true)
            @RequestParam(value = "adcId") Long adcId,
            @ApiParam(value = "收集项产品服务/应用映射列表", required = true)
            @RequestBody() List<CollectAppMapping> collectAppMappingList) {
        return getBaseResponse(() -> deviceService.getDeviceCollectDetailExecParams(adcId,
                collectAppMappingList), false, false, null);
    }

    @ApiOperation(value = "获取设备收集项预警列表(只有收集项预警项，没有预警设定相关内容)")
    @PostMapping(value = "/collect/warning/one/level/list")
    public BaseResponse getDeviceCollectWarningOneLevelList(
            @ApiParam(value = "设备收集项版本Id", required = true)
            @RequestParam(value = "adcId") Long adcId,
            @ApiParam(value = "收集项产品服务/应用映射列表", required = true)
            @RequestBody() List<CollectAppMapping> collectAppMappingList) {
        return getBaseResponse(() -> deviceService.getDeviceCollectWarningOneLevelList(adcId,
                collectAppMappingList), false, false, null);
    }

    @ApiOperation(value = "获取设备收集项预警")
    @PostMapping(value = "/collect/warning")
    public BaseResponse getDeviceCollectWarning(
            @ApiParam(value = "设备收集项预警Id", required = true)
            @RequestParam(value = "adcwId") Long adcwId,
            @ApiParam(value = "设备收集项列表Id", required = true)
            @RequestParam(value = "adcdId") Long adcdId,
            @ApiParam(value = "收集项产品服务/应用映射列表", required = true)
            @RequestBody() List<CollectAppMapping> collectAppMappingList) {
        return getBaseResponse(() -> BaseResponse.ok(deviceService.getDeviceCollectWarning(adcwId, adcdId,
                collectAppMappingList)), false, false, null);
    }

    @ApiOperation(value = "获取设备预警通知")
    @GetMapping(value = "/{adcwId}/warning/notify")
    public BaseResponse getDeviceWarningNotify(
            @ApiParam(value = "设备收集项预警Id", required = true)
            @PathVariable(value = "adcwId") Long adcwId,
            @ApiParam(value = "是否包含停发通知设定")
            @RequestParam(value = "containContinueStopSetting", required = false, defaultValue = "false") Boolean containContinueStopSetting) {
        return getBaseResponse(() -> deviceService.getWarningNotifyMapping(adcwId, containContinueStopSetting),
                false, false, null);
    }

    @ApiOperation(value = "保存设备预警通知")
    @PostMapping(value = "/{adcwId}/warning/notify")
    public BaseResponse saveDeviceWarningNotify(
            @ApiParam(value = "设备收集项预警Id", required = true)
            @PathVariable(value = "adcwId") Long adcwId,
            @ApiParam(value = "设备预警通知对象", required = true)
            @RequestBody() DeviceWarningNotify deviceWarningNotify) {
        return getBaseResponse(() -> deviceService.saveDeviceWarningNotify(adcwId, deviceWarningNotify),
                false, false, null);
    }

    @ApiOperation(value = "检查是否有设备预警通知存在")
    @GetMapping(value = "/warning/notify/exist")
    public BaseResponse checkDeviceWarningNotifyExist(
            @ApiParam(value = "来源Id(租户通知群组Id或租户群组通知人Id)", required = true)
            @RequestParam(value = "sourceId") Long sourceId,
            @ApiParam(value = "通知类别")
            @RequestParam(value = "notifyCategoryString", required = false, defaultValue = "") String notifyCategoryString) {
        return getBaseResponse(() -> deviceService.checkNotifyMappingExist(sourceId, notifyCategoryString),
                false, false, null);
    }

    @ApiOperation(value = "更新设备预警通知默认带入项")
    @PutMapping(value = "/warning/notify/default")
    public BaseResponse changeDeviceWarningNotifyDefault(
            @ApiParam(value = "原始的来源Id(租户通知群组Id或租户群组通知人Id)", required = true)
            @RequestParam(value = "oriSourceId") Long oriSourceId,
            @ApiParam(value = "来源Id(租户通知群组Id或租户群组通知人Id)", required = true)
            @RequestParam(value = "sourceId") Long sourceId,
            @ApiParam(value = "通知类别")
            @RequestParam(value = "notifyCategoryString", required = false, defaultValue = "") String notifyCategoryString) {
        return getBaseResponse(() -> deviceService.changeDeviceWarningNotifyDefault(oriSourceId, sourceId, notifyCategoryString),
                false, false, null);
    }

    @ApiOperation(value = "更新设备预警通知默认带入项")
    @PostMapping(value = "/fix/collect/warning")
    public BaseResponse fixDeviceCollectWarning(
            @ApiParam(value = "收集项Id")
            @RequestParam(value = "accId", required = false, defaultValue = "0") Long accId,
            @ApiParam(value = "收集项预警项Id")
            @RequestParam(value = "acwId", required = false, defaultValue = "0") Long acwId,
            @ApiParam(value = "是否启用预警")
            @RequestParam(value = "isWarningEnable", required = false, defaultValue = "0") Boolean isWarningEnable,
            @ApiParam(value = "是否异步运行")
            @RequestParam(value = "isAsync", required = false, defaultValue = "false") Boolean isAsync) {
        return getBaseResponse(() -> {
            if (isAsync) {
                deviceService.fixDeviceCollectWarningAsync(accId, acwId, isWarningEnable);
                return BaseResponse.ok();
            }
            return deviceService.fixDeviceCollectWarning(accId, acwId, isWarningEnable);
        }, false, false, null);
    }

    @ApiOperation(value = "地端获取设备类型")
    @GetMapping(value = "/type/list")
    public BaseResponse getDeviceTypeList() {
        return getBaseResponse(() -> deviceService.getDeviceTypeList(),
                false, false, null);
    }

    @ApiOperation(value = "获取设备预警通知映射数据")
    @GetMapping(value = "/warningNotifyMapping")
    public List<DeviceWarningNotifyMapping> getDeviceWarningNotifyMapping(@RequestParam Long adcwId, @RequestParam String notifyCategory) {
        return deviceService.getDeviceWarningNotifyMapping(adcwId, notifyCategory);
    }

    @ApiOperation(value = "获取客户设备统计数值")
    @GetMapping(value = "/type/online/statistic")
    public BaseResponse getDeviceTypeOnlineStatistic() {
        Long eid = RequestUtil.getHeaderEid();

        return getBaseResponse(() -> deviceService.getDeviceTypeOnlineStatistic(eid),
                false, false, null);
    }

    @ApiOperation(value = "获取客户离线设备统计数值")
    @GetMapping(value = "/offline/total")
    public BaseResponse getDeviceOfflineTotal() {
        Long eid = RequestUtil.getHeaderEid();

        return getBaseResponse(() -> deviceService.getDeviceOfflineTotal(eid),
                false, false, null);
    }

    @ApiOperation(value = "获取设备云管家用户映射列表")
    @GetMapping(value = "/scp/user/mapping/list")
    public BaseResponse getDeviceScpUserMappingList(
            @ApiParam(value = "客服代号", required = true)
            @RequestParam(value = "serviceCode") String serviceCode,
            @ApiParam("是否需要分页")
            @RequestParam(value = "needPaging", required = false, defaultValue = "true") Boolean needPaging,
            @ApiParam("分页页次")
            @RequestParam(value = "pageNum", required = false, defaultValue = "1") int pageNum,
            @ApiParam("每页总数")
            @RequestParam(value = "pageSize", required = false, defaultValue = "10") int pageSize,
            @ApiParam(value = "查找文字(设备名、IP、放置点、备注等)")
            @RequestParam(value = "searchText", required = false) String searchText) {
        Long eid = RequestUtil.getHeaderEid();

        return getBaseResponse(() -> deviceService.getDeviceScpUserMappingList(eid, serviceCode, needPaging, pageNum, pageSize, searchText),
                false, false, null);
    }

    @ApiOperation(value = "获取设备收集项串联用列表-用以下拉选择串联用收集项")
    @GetMapping(value = "/{adId}/adcd/series/list")
    public BaseResponse getAdcdSeriesList(
            @ApiParam(value = "设备主键Id", required = true)
            @PathVariable(value = "adId") Long adId) {
        return getBaseResponse(() -> deviceService.getAdcdSeriesList(adId),
                false, false, null);
    }

    @ApiOperation(value = "获取设备平台列表")
    @GetMapping(value = "/platform/list")
    public BaseResponse getDevicePlatformList() {
        return getBaseResponse(() -> BaseResponse.ok(Arrays.stream(DevicePlatform.values())
                        .filter(x -> x != DevicePlatform.UNKNOWN)
                        .collect(Collectors.toMap(x -> x.getCode(), x -> x.getName(), (x, y) -> y))),
                false, false, null);
    }

    @ApiOperation(value = "异动未定义设备作废状态")
    @PutMapping(value = "/undefine/is/deleted/status")
    public BaseResponse modifyUndefineDeviceIsDeletedStatus(
            @ApiParam(value = "设备Id", required = true)
            @RequestParam(value = "deviceId") String deviceId,
            @ApiParam(value = "作废状态", required = true)
            @RequestParam(value = "isDeleted") Boolean isDeleted) {
        return getBaseResponse(() -> deviceService.modifyUndefineDeviceIsDeletedStatus(deviceId, isDeleted),
                false, false, null);
    }

    @ApiOperation(value = "依据运维项目Id获取设备收集项列表")
    @GetMapping(value = "/collect/detail/by/aiops/item/id")
    public BaseResponse getDeviceCollectDetail(
            @ApiParam(value = "设备Id", required = true)
            @RequestParam(value = "deviceId") String deviceId,
            @ApiParam(value = "运维项目Id", required = true)
            @RequestParam(value = "aiopsItemId") String aiopsItemId) {
        return getBaseResponse(() -> deviceService.getDeviceCollectDetailByAiopsItemId(deviceId, aiopsItemId),
                false, false, null, ResponseCode.QUERY_VERIFY);
    }

    @ApiOperation(value = "依据运维项目类型获取设备运维实例列表")
    @GetMapping(value = "/aiops/instance/by/aiops/item/type")
    public BaseResponse getDeviceAiopsInstanceByAiopsItemType(
            @ApiParam(value = "设备Id", required = true)
            @RequestParam(value = "deviceId") String deviceId,
            @ApiParam(value = "运维项目群组")
            @RequestParam(value = "aiopsItemGroup", required = false) String aiopsItemGroup,
            @ApiParam(value = "运维项目类型")
            @RequestParam(value = "aiopsItemType", required = false) String aiopsItemType) {
        return getBaseResponse(() -> deviceService.getDeviceAiopsInstanceByAiopsItemType(deviceId, aiopsItemGroup,
                aiopsItemType), false, false, null, ResponseCode.QUERY_VERIFY);
    }

    @ApiOperation(value = "获取设备运维项目设定列表")
    @GetMapping(value = "/aiops/item/setting")
    public BaseResponse getDeviceAiopsItemSetting(
            @ApiParam(value = "设备Id", required = true)
            @RequestParam(value = "deviceId") String deviceId,
            @ApiParam(value = "运维项目群组")
            @RequestParam(value = "aiopsItemGroup", required = false) String aiopsItemGroup,
            @ApiParam(value = "运维项目类型")
            @RequestParam(value = "aiopsItemType", required = false) String aiopsItemType,
            @ApiParam(value = "是否分页")
            @RequestParam(value = "needPaging", required = false) Boolean needPaging,
            @ApiParam(value = "页次")
            @RequestParam(value = "pageNum", required = false) Integer pageNum,
            @ApiParam(value = "页笔数")
            @RequestParam(value = "pageSize", required = false) Integer pageSize) {
        return getBaseResponse(() -> deviceService.getDeviceAiopsItemSetting(deviceId, aiopsItemGroup, aiopsItemType,
                needPaging, pageNum, pageSize), false, false, null);
    }

    @ApiOperation(value = "依据运维项目Id获取运维实例收集项分层列表")
    @GetMapping(value = "/aiops/instance/collect/by/aiops/item/id")
    public BaseResponse getDeviceAiopsInstanceCollectByAiopsItemId(
            @ApiParam(value = "设备Id", required = true)
            @RequestParam(value = "deviceId") String deviceId,
            @ApiParam(value = "运维项目Id", required = true)
            @RequestParam(value = "aiopsItemId") String aiopsItemId) {
        return getBaseResponse(() -> deviceService.getDeviceAiopsInstanceCollectByAiopsItemId(deviceId, aiopsItemId),
                false, false, null, ResponseCode.QUERY_VERIFY);
    }

    @ApiOperation(value = "依据模组收集项分层列表添加设备收集项列表")
    @PostMapping(value = "/collect/detail/by/mcl/list")
    public BaseResponse addDeviceCollectDetailByModuleCollectLayered(
            @ApiParam(value = "设备Id", required = true)
            @RequestParam(value = "deviceId") String deviceId,
            @ApiParam(value = "运维项目Id", required = true)
            @RequestParam(value = "aiopsItemId") String aiopsItemId,
            @ApiParam(value = "模组收集项分层列表", required = true)
            @RequestBody() List<ModuleCollectLayered> mclList) {
        return getBaseResponse(() -> deviceService.addDeviceCollectDetailByModuleCollectLayered(deviceId, aiopsItemId,
                mclList), false, false, null);
    }

    @ApiOperation("云端保存设备基础信息")
    @PutMapping(value = "/basic/info/by/web")
    public BaseResponse saveDeviceInfoByWeb(
            @ApiParam(value = "设备基础信息对象", required = true)
            @RequestBody AiopsKitDevice device) {
        device.setDeviceChangeSource(DeviceChangeSource.BY_CLOUD);
        return getBaseResponse(() -> saveDeviceInfoCore(device),
                false, false, null);
    }

    @ApiOperation(value = "获取设备映射的运维实例项目类型")
    @GetMapping(value = "/mapping/aiops/instance/item/type")
    public BaseResponse getDeviceMappingAiopsInstanceItemType(
            @ApiParam(value = "设备Id", required = true)
            @RequestParam(value = "deviceId") String deviceId) {
        return getBaseResponse(() -> deviceService.getDeviceMappingAiopsInstanceItemType(deviceId),
                false, false, null, ResponseCode.QUERY_VERIFY);
    }

    @ApiOperation(value = "获取设备映射的运维实例项目群组")
    @GetMapping(value = "/mapping/aiops/instance/item/group")
    public BaseResponse getDeviceMappingAiopsInstanceItemGroup(
            @ApiParam(value = "设备Id", required = true)
            @RequestParam(value = "deviceId") String deviceId) {
        return getBaseResponse(() -> deviceService.getDeviceMappingAiopsInstanceItemGroup(deviceId),
                false, false, null, ResponseCode.QUERY_VERIFY);
    }

    @ApiOperation("添加设备运维实例映射")
    @PostMapping(value = "/aiops/instance/mapping")
    public BaseResponse addDeviceAiopsInstanceMapping(
            @ApiParam(value = "运维项目上下文", required = true)
            @RequestBody AiopsItemContext aiopsItemContext) {
        return getBaseResponse(() -> deviceService.addDeviceAiopsInstanceMapping(aiopsItemContext,
                true), false, false, null);
    }

    @ApiOperation("依据运维设备实例映射关系上下文添加设备运维实例映射")
    @PostMapping(value = "/aiops/instance/mapping/by/arc")
    public BaseResponse addDeviceAiopsInstanceMappingByArc(
            @ApiParam(value = "运维设备实例映射关系上下文", required = true)
            @RequestBody AdimRelateContext arc) {
        return getBaseResponse(() -> deviceService.addDeviceAiopsInstanceMappingByArc(arc),
                false, false, null);
    }

    @ApiOperation("移除设备运维实例映射")
    @DeleteMapping(value = "/aiops/instance/mapping")
    public BaseResponse removeDeviceAiopsInstanceMapping(
            @ApiParam(value = "设备Id", required = true)
            @RequestParam(value = "deviceId") String deviceId,
            @ApiParam(value = "运维项目Id列表", required = true)
            @RequestBody() List<String> aiopsItemIdList) {
        return getBaseResponse(() -> deviceService.removeDeviceAiopsInstanceMapping(deviceId, aiopsItemIdList),
                false, false, null);
    }

    @ApiOperation("获取云端运维实例是否存在")
    @GetMapping(value = "/aiops/item")
    public BaseResponse queryAiopsItem(@ApiParam(value = "设备Id", required = true) @RequestParam(value = "deviceId") String deviceId,
                                       @ApiParam(value = "运维项目Id", required = true) @RequestParam(value = "aiopsItemId") String aiopsItemId) {
        return getBaseResponse(() -> deviceService.queryAiopsItemInstance(deviceId, aiopsItemId));
    }

    @ApiOperation("依据运维设备实例映射关系上下文移除设备运维实例映射")
    @DeleteMapping(value = "/aiops/instance/mapping/by/arc")
    public BaseResponse removeDeviceAiopsInstanceMappingByArc(
            @ApiParam(value = "运维设备实例映射关系上下文", required = true)
            @RequestBody AdimRelateContext arc) {
        return getBaseResponse(() -> deviceService.removeDeviceAiopsInstanceMappingByArc(arc),
                false, false, null);
    }

    @ApiOperation(value = "获取特定设备类型设备列表")
    @GetMapping(value = "/type/{deviceType}/device/list")
    public BaseResponse getCurrentDeviceTypeDeviceList(
            @ApiParam(value = "租户Id", required = true)
            @RequestParam(value = "eid") Long eid,
            @ApiParam(value = "设备类型", required = true)
            @PathVariable(value = "deviceType") String deviceType,
            @ApiParam(value = "自己的设备Id")
            @RequestParam(value = "selfDeviceId", required = false) String selfDeviceId) {
        return getBaseResponse(() -> deviceService.getCurrentDeviceTypeDeviceList(eid, deviceType, selfDeviceId),
                false, false, null);
    }

    @ApiOperation(value = "依据租户Id获取设备数据库列表")
    @GetMapping(value = "/data/source/by/eid")
    public BaseResponse getDeviceDataSourceByEid(
            @ApiParam(value = "租户Id", required = true)
            @RequestParam(value = "eid") Long eid) {
        return getBaseResponse(() -> deviceService.getDeviceDataSourceByEid(eid),
                false, false, null);
    }

    @ApiOperation(value = "获取分组设备收集项明细执行参数")
    @PostMapping(value = "/group/adcd/exec/param")
    public BaseResponse getGroupAdcdExecParam(
            @ApiParam(value = "条件字典", required = true)
            @RequestParam("deviceId") String deviceId,
            @ApiParam(value = "运维项目Id列表", required = true)
            @RequestBody() List<String> aiopsItemIdList) {
        return getBaseResponse(() -> deviceService.getGroupAdcdExecParam(deviceId, aiopsItemIdList),
                false, false, null);
    }

    @ApiOperation(value = "保存分组设备收集项明细执行参数")
    @PostMapping(value = "/group/adcd/exec/param/save")
    public BaseResponse saveGroupAdcdExecParam(
            @ApiParam(value = "設備Id", required = true)
            @RequestParam("deviceId") String deviceId,
            @ApiParam(value = "设备主键Id")
            @RequestParam(value = "adId", required = false) Long adId,
            @ApiParam(value = "租戶Sid")
            @RequestParam(value = "eid", required = false) Long eid,
            @ApiParam(value = "分组设备收集项明细执行参数", required = true)
            @RequestBody() List<GroupAdcdExecParam> groupAdcdExecParamList) {
        return getBaseResponse(() -> deviceService.saveGroupAdcdExecParam(deviceId, adId, eid, groupAdcdExecParamList),
                false, false, null);
    }

    @ApiOperation(value = "获取设备看板")
    @GetMapping(value = "/dashboard")
    public BaseResponse getDeviceDashboard(@RequestParam(value = "eid") Long eid) {
        return deviceService.getDeviceDashboard(eid);
    }

    @ApiOperation(value = "获取设备看板")
    @PostMapping(value = "/getDashboardUpperData")
    public BaseResponse getDashboardUpperData(@RequestParam(value = "eid") Long eid,
    @RequestBody List<String> aiopsItemList) {
        return deviceService.getDashboardUpperData(eid, aiopsItemList);
    }

    @ApiOperation(value = "获取运维项目下的预警项")
    @GetMapping(value = "/getAwcByEidAndAiopsItem")
    public BaseResponse getAwcByEidAndAiopsItem(@RequestParam Long eid,
                                                @RequestParam String aiopsItem,
                                                @RequestParam(required = false, defaultValue = "1") Integer pageNum,
                                                @RequestParam(required = false, defaultValue = "5") Integer pageSize,
                                                @RequestParam(required = false, defaultValue = "") String warningCode,
                                                @RequestParam(required = false, defaultValue = "") String warningName,
                                                @RequestParam(required = false, defaultValue = "") String warningLevelString,
                                                @RequestParam(required = false) Boolean customize
                                                ) {
        return deviceService.getAwcByEidAndAiopsItem(eid, aiopsItem, pageNum, pageSize,warningCode,warningName,warningLevelString,customize);
    }

    @ApiOperation(value = "获取运维项目下的预警项下的设备")
    @GetMapping(value = "/getAwcByEidAndAiopsItemAndWarningCode")
    public BaseResponse getAwcByEidAndAiopsItemAndWarningCode(@RequestParam Long eid,
                                                              @RequestParam String aiopsItem,
                                                              @RequestParam Long acwId) {
        return deviceService.getAwcByEidAndAiopsItemAndAcwId(eid, aiopsItem, acwId);
    }


    @ApiOperation(value = "批量修改设备预警项设定")
    @PostMapping(value = "/batchModifyWarningSetForDevice")
    public BaseResponse batchModifyWarningSetForDevice(
            @RequestBody BatchModifyWarningSetForDeviceRequest request) {
        return getBaseResponse(() -> deviceService.batchModifyWarningSetForDevice(request),
                false, false, null);
    }

    @ApiOperation(value = "获取租戶设备数量")
    @PostMapping(value = "/tenant/cnt")
    public BaseResponse getTenantDeviceCnt(@RequestBody() TenantDeviceCntReq tenantDeviceCntReq) {
        return getBaseResponse(() -> deviceService.getTenantDeviceCnt(tenantDeviceCntReq),
                false, false, null);
    }

    @ApiOperation(value = "批量修改自定义id的预警项设定")
    @PostMapping(value = "/batchModifyWarningSet")
    public BaseResponse batchModifyWarningSet(
            @RequestBody BatchModifyWarningSetRequest request) {
        return getBaseResponse(() -> deviceService.batchModifyWarningSet(request),
                false, false, null);
    }

    @ApiOperation(value = "更新租户预警")
    @PostMapping(value = "/update/tenant/warning")
    public BaseResponse updateTenantWarningEnable(@RequestBody AiopsTenantWarning tenantWarning) {
        return deviceService.updateTenantWarningEnable(tenantWarning);
    }

    @ApiOperation(value = "修复数据")
    @GetMapping(value = "/fixSourceAcwIdNull")
    public BaseResponse fixSourceAcwIdNull(@RequestParam Long eid) {
        return deviceService.fixSourceAcwIdNull(eid);
    }

    @ApiOperation(value = "获取acw预警是否开启")
    @GetMapping(value = "/getAcwIsEnable")
    public BaseResponse getAcwIsEnable(@RequestParam Long acwId,@RequestParam Long eid) {
        return deviceService.getAcwIsEnable(acwId, eid);
    }

    @ApiOperation(value = "获取运维项目下的设备且添加已报到资产")
    @GetMapping(value = "/getAwcByEidAndAiopsItemAndAsset")
    public BaseResponse getAwcByEidAndAiopsItemAndAsset(@RequestParam Long eid, @RequestParam String aiopsItem,
                                                        @RequestParam(value = "related", required = false) Boolean related,
                                                        @RequestParam(value = "deviceNameOrId", required = false) String deviceNameOrId,
                                                        @RequestParam(required = false) int pageNum,
                                                        @RequestParam(required = false) int pageSize,
                                                        @RequestParam(value = "aiId" , required = false)  Long aiopsInstance) {
        return deviceService.getAwcByEidAndAiopsItemAndAsset(eid, aiopsItem, related, deviceNameOrId, pageNum, pageSize, aiopsInstance);
    }
}
