package com.digiwin.escloud.aiobasic.clouddrive.service;

import com.digiwin.escloud.aiobasic.clouddrive.controller.dto.DocServerConfigResponse;

/**
 * 文档服务器服务器接口
 */
public interface DocServerService {
    /**
     * 获取文档服务器配置
     * @param sid 运维商Id
     * @param userSid 用户Sid
     * @param token Token
     * @param isIntranet 是否是企业内部网路
     * @param serviceRegion 服务中心
     * @return 文档服务器配置回覆对象
     */
    DocServerConfigResponse getDocServerConfig(Long sid, Long userSid, String token, Boolean isIntranet, String serviceRegion);
}
