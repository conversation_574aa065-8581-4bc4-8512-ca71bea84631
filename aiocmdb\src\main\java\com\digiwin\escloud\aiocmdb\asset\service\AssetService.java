package com.digiwin.escloud.aiocmdb.asset.service;

import com.digiwin.escloud.aiocmdb.asset.model.AssetSaveBigDataParam;
import com.digiwin.escloud.aioitms.model.instance.AiopsItemContextDTO;
import com.digiwin.escloud.common.response.BaseResponse;

import java.util.List;

public interface AssetService {
    BaseResponse deleteAsset(String modelCode, Long id,Long eid);

    BaseResponse invalidCmdbAsset(List<AiopsItemContextDTO> aicList);
//    @Deprecated
//    BaseResponse batchUpdateAssetLevel(AssetLevelBigDataParam param);

    BaseResponse batchSaveInstanceToStarRocksAndHBase(AssetSaveBigDataParam param);

    /**
     * 批量检查资产ID是否存在于AssetsRelated表中。
     * @param assetIdList 要检查的资产ID列表
     * @return BaseResponse 包含一个列表，列表中的每个对象都说明了对应assetId的存在状态。
     */
    BaseResponse checkAssetsExistInAssetsRelated(List<Long> assetIdList);
}
