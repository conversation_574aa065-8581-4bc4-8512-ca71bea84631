package com.digiwin.escloud.issueservice.v3.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.digiwin.escloud.common.util.LongUtil;
import com.digiwin.escloud.issueservice.cache.KmoCache;
import com.digiwin.escloud.issueservice.common.IndepthAICommon;
import com.digiwin.escloud.issueservice.t.model.issuekb.*;
import com.digiwin.escloud.issueservice.v3.dao.IIssueSchedulingDaoV3;
import com.digiwin.escloud.issueservice.v3.service.IIndepthAIService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.codec.ServerSentEvent;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.reactive.function.client.WebClientResponseException;
import reactor.core.publisher.Flux;

import javax.annotation.Resource;
import java.util.Map;


@Service
public class IndepthAIServiceImpl extends IndepthAICommon implements IIndepthAIService {

    @Value("${digiwin.issue.connectarea}")
    private String connectArea;
    @Value("${issue.kb.indepthai.code}")
    private String issuekbIndepthAICode;
    @Resource(name = "indepthAIWebClient")
    private WebClient webClient;
    @Autowired
    KmoCache kmoCache;
    @Autowired
    IIssueSchedulingDaoV3 iIssueSchedulingDaoV3;

    public Flux<ServerSentEvent<Object>> generateTextStream(String askMessage, String askSessionId, IssueKbOverview issueKbOverview) {
        ChatMessage chatMessage = new ChatMessage();
        chatMessage.setQuestion(askMessage);
        chatMessage.setSessionId(askSessionId);
        chatMessage.setIssueKbOverview(issueKbOverview);

        Flux<ServerSentEvent<Object>> flux = webClient.post()
                .uri("/" + getIndepthAICode())
                .header("X-Accel-Buffering", "no")
                .header("token", kmoCache.getChatFileAuth())
                .header("Content-Type", "application/json")
                .header("Accept-Language", "CN".equals(connectArea) ? "zh-CN" : "zh-TW")
                .bodyValue(chatMessage)
                .retrieve()
                .bodyToFlux(new ParameterizedTypeReference<ServerSentEvent<Object>>() {});
        return this.packageFlowProcess(chatMessage, flux);
    }

    // 智能體編號
    private String getIndepthAICode(){
        return issuekbIndepthAICode;
    }

    @Override
    protected void callApiError(Throwable error, String askMessage) {
        //TODO call api error
        if (error instanceof WebClientResponseException) {
            WebClientResponseException wcrErr = (WebClientResponseException) error;
            System.out.println(wcrErr.getResponseBodyAsString());
        } else {
            System.out.println(error.getMessage());
        }
    }

    @Override
    protected void saveMsg(Object procDomainObject, String fullFlowMsg, String msgSourceList, Throwable error, ChatMessage chatMessage) {
        //TODO process msg has error
        this.saveMsg(procDomainObject,null , fullFlowMsg, msgSourceList,chatMessage );
    }

    @Override
    protected void saveMsg(Object procDomainObject, Object procDomainDoneObject, String fullFlowMsg, String msgSourceList, ChatMessage chatMessage) {
        IssueKbOverview issueKbOverview = chatMessage.getIssueKbOverview();
        System.out.println(fullFlowMsg);
        try{
            Map<String,String> map = JSONObject.parseObject(fullFlowMsg,Map.class);
            issueKbOverview.setTitleFromAI(map.getOrDefault("title",""));
            issueKbOverview.setAnswerFromAI(map.getOrDefault("description",""));
            issueKbOverview.setKeywordFromAI(map.getOrDefault("keyword",""));
            issueKbOverview.setStatusAI(IssueKbAIStatus.SUCCESS.getIndex());
            issueKbOverview.setResult(fullFlowMsg);
        }catch (Exception e){
            issueKbOverview.setTitleFromAI("");
            issueKbOverview.setAnswerFromAI("");
            issueKbOverview.setKeywordFromAI("");
            issueKbOverview.setStatusAI(IssueKbAIStatus.FAIL.getIndex());
            issueKbOverview.setResult(fullFlowMsg);
        }finally {
            if(LongUtil.isEmpty(issueKbOverview.getId())) {
                iIssueSchedulingDaoV3.saveIssueKbOverview(issueKbOverview);
            }else {
                iIssueSchedulingDaoV3.updateIssueKbOverview(issueKbOverview);
            }

            if(issueKbOverview.getIndex() == issueKbOverview.getSize() -1){
                //看知识一览里 这一批次是不是有失败或者生产中的，如果有 就是失败；
                int count = iIssueSchedulingDaoV3.selectIssueKbAIStatus(issueKbOverview.getIkerId());
                iIssueSchedulingDaoV3.updateIssueKbExecuteRecord(issueKbOverview.getIkerId(), count > 0? IssueKbExecuteRecordStatus.FAIL.getIndex():IssueKbExecuteRecordStatus.SUCCESS.getIndex());
            }
        }

    }

    @Override
    protected Object getProcessDomainObject(ChatMessage chatMessage, Object recMsg, String eventId) {
        Map<String, Object> flowRow = (Map<String, Object>) recMsg;

//        String returnSampleJson = "{\n" +
//                "    \"action\": \"answer\",\n" +
//                "    \"blockId\": \"4e8fd224-1c77-402a-8501-33fd6603e57f\",\n" +
//                "    \"message\": \"昨天上午 10:30 提的案件:111199 的问题处理好了吗？什么时候给我打电话?\\n{<br>  \\\"eid\\\": 99990000,<br>  \\\"deviceId\\\": 8759487583,<br>  \\\"userId\\\": \\\"asam_user_id\\\",<br>  \\\"case_no\\\": [\\\"111199\\\"],<br>  \\\"query_start_date\\\": \\\"2025-04-10 10:30:00\\\",<br>  \\\"query_end_date\\\": \\\"2025-04-10 10:30:00\\\"<br>}\",\n" +
//                "    \"messageSource\": \"text_reply\",\n" +
//                "    \"nodeId\": \"7b26b6c3-6bde-4b93-b6ba-a4d72c8b5cc5\",\n" +
//                "    \"sessionId\": \"91539a51-e6cb-4886-b2be-070fe3eaffc1\",\n" +
//                "    \"status\": \"done\"\n" +
//                "}";

        String action = this.getReturnFlowValue("action", flowRow);
        String blockId = this.getReturnFlowValue("blockId", flowRow);
        String message = this.getReturnFlowValue("message", flowRow);
        String messageSource = this.getReturnFlowValue("messageSource", flowRow);
        String nodeId = this.getReturnFlowValue("nodeId", flowRow);
        String sessionId = this.getReturnFlowValue("sessionId", flowRow);
        String status = this.getReturnFlowValue("status", flowRow);

        ChatMessageV2 chatMsgRow = new ChatMessageV2();

        // basic info
//        chatMsgRow.setId(); // 目前不確定用哪一個值
//        chatMsgRow.setSid();
//        chatMsgRow.setUserSid();
//        chatMsgRow.setUserId();

        // user ask
        chatMsgRow.setQuestion(chatMessage.getQuestion());

        // from ai reply
        chatMsgRow.setSessionId(sessionId);
        chatMsgRow.setBlockId(blockId);
        chatMsgRow.setNodeId(nodeId);
        chatMsgRow.setStatus(status);

        // system info
//        chatMsgRow.setSender();
//        chatMsgRow.setReceiver();
//        chatMsgRow.setCreateTime();
//        chatMsgRow.setUpdateTime();
        return chatMsgRow;
    }
}