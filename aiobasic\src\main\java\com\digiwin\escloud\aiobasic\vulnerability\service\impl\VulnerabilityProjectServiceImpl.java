package com.digiwin.escloud.aiobasic.vulnerability.service.impl;

import com.digiwin.escloud.aiobasic.assetrisk.model.enums.VulnerabilityLevelEnum;
import com.digiwin.escloud.aiobasic.assetrisk.model.enums.VulnerabilityReportStatus;
import com.digiwin.escloud.aiobasic.report.model.VulnerabilityReportContent;
import com.digiwin.escloud.aiobasic.vulnerability.dao.*;
import com.digiwin.escloud.aiobasic.vulnerability.model.*;
import com.digiwin.escloud.aiobasic.vulnerability.model.enums.VulnerabilityProjectStatus;
import com.digiwin.escloud.aiobasic.vulnerability.model.excel.VulnerabilityScanData;
import com.digiwin.escloud.aiobasic.vulnerability.model.param.VulnerabilityProjectScanParam;
import com.digiwin.escloud.aiobasic.vulnerability.service.*;
import com.digiwin.escloud.common.feign.AioUserFeignClient;
import com.digiwin.escloud.common.model.ResponseBase;
import com.digiwin.escloud.common.model.ResponseCode;
import com.digiwin.escloud.common.model.VulnerabilityProjectDTO;
import com.digiwin.escloud.common.response.BaseResponse;
import com.digiwin.escloud.common.util.CollectionUtil;
import com.digiwin.escloud.common.util.IntegerUtil;
import com.digiwin.escloud.common.util.LongUtil;
import com.digiwin.escloud.common.util.SnowFlake;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.TransactionStatus;
import org.springframework.util.ObjectUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.*;
import java.util.*;
import java.util.concurrent.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Slf4j
@Service
public class VulnerabilityProjectServiceImpl implements VulnerabilityProjectService {

    @Resource
    VulnerabilityProjectMapper vulnerabilityProjectMapper;
    @Resource
    VulnerabilityProjectScanMapper vulnerabilityProjectScanMapper;
    @Resource
    VulnerabilityProjectAssetMapper vulnerabilityProjectAssetMapper;
    @Resource
    VulnerabilityReportRecordMapper vulnerabilityReportRecordMapper;
    @Resource
    VulnerabilityProjectScanAssetMapper vulnerabilityProjectScanAssetMapper;
    @Resource
    VulnerabilityAssetService assetService;
    @Resource
    VulnerabilityMaintenanceAssetService maintenanceAssetService;
    @Resource
    VulnerabilityMaintenanceAssetService vulnerabilityMaintenanceAssetService;
    @Resource
    VulnerabilityReportRecordService vulnerabilityReportRecordService;
    @Resource
    VulnerabilityStoreService vulnerabilityStoreService;
    @Resource
    VulnerabilityProjectService vulnerabilityProjectService;
    @Resource
    VulnerabilityProjectScanService vulnerabilityProjectScanService;
    @Resource
    private AioUserFeignClient aioUserFeignClient;
    @Resource
    private PlatformTransactionManager platformTransactionManager;
    @Autowired
    VulnerabilityAssetMapper vulnerabilityAssetMapper;

    @Resource
    private ServiceMaintenanceService serviceMaintenanceService;
//    private final ExecutorService executorService = Executors.newFixedThreadPool(2);
    private final ThreadPoolExecutor executorService = new ThreadPoolExecutor(
            5,
            10,
            0,
            TimeUnit.SECONDS,
            new LinkedBlockingQueue<>()
    );


    @Override
    public BaseResponse insertVulnerabilityProject(VulnerabilityProjectParam vulnerabilityProject, List<MultipartFile> files) {
        String projectNumber = vulnerabilityProjectMapper.selectVulnerabilityProjectByNumberAndEid(vulnerabilityProject.getEid()
                , vulnerabilityProject.getProjectNumber());
        if (StringUtils.isNotBlank(projectNumber)) {
            return BaseResponse.error(ResponseCode.VULNERABILITY_PROJECT_ID_IS_EXIST);
        }
        vulnerabilityProject.setId(SnowFlake.getInstance().newIdStr());

        String projectId = vulnerabilityProject.getId();

        vulnerabilityProjectMapper.insertVulnerabilityProject(vulnerabilityProject);
        vulnerabilityProjectService.updateVulnerabilityProjectStatus(vulnerabilityProject.getEid());
        if (!files.isEmpty()) {
            //異步前先處理資料
            List<Set<VulnerabilityScanData>> dataLists = vulnerabilityProjectScanService.processScanDataFiles(files);
            // 提交一个新任務到線程池執行
            executorService.submit(() -> vulnerabilityProjectFileProcess(vulnerabilityProject, dataLists, false));

            Constants.getThreadTaskList().add(projectId);
//        } else {
//            //若未上傳檔案則總結內容預設0
//            VulnerabilityReportContent content = vulnerabilityReportRecordMapper.selectReportContentByLanguage(vulnerabilityProject.getLanguage());
//            vulnerabilityProject.setProjectSummary(String.format(content.getScanExecuteToolResult(),0,0,0,0));
        }

        // 新增成功回传id
        return BaseResponse.ok(projectId);
    }

    @Override
    public List<VulnerabilityProject> selectVulnerabilityProject(Long eid, String project, String scanBatch, String startDate, String endDate, int page, int size, String projectId, Integer projectStatus) {
        HashMap<String, Object> condition = buildselectVulnerabilityProject(eid, project, scanBatch, startDate, endDate, page, size, projectId,projectStatus);
        List<VulnerabilityProject> vulnerabilityProjects = vulnerabilityProjectMapper.selectVulnerabilityProject(condition);

        fillServiceCodeAndName(vulnerabilityProjects);

        return vulnerabilityProjects;
    }

    public void fillServiceCodeAndName(List<? extends VulnerabilityBase> vb){
        if (!vb.isEmpty()) {
            List<Long> eidList = vb.stream().map(VulnerabilityBase::getEid).collect(Collectors.toList());
            ResponseBase rb = aioUserFeignClient.getSupplierTenantMapListByEidList(eidList);

            if (rb.checkIsSuccess()) {
                List<Map<String, Object>> supplierTenantMaps = (List<Map<String, Object>>) rb.getData();
                Map<Long, Map<String, Object>> eidToSupplierMap = supplierTenantMaps.stream()
                        .filter(map -> map.get("eid") != null)
                        .collect(Collectors.toMap(map -> LongUtil.objectToLong(map.get("eid")), Function.identity(), (o, n) -> n));

                vb.forEach(vp -> {
                    Map<String, Object> supplierInfo = eidToSupplierMap.get(vp.getEid());
                    if (supplierInfo != null) {
                        vp.setName(Objects.toString(supplierInfo.get("name"), ""));
                        vp.setServiceCode(Objects.toString(supplierInfo.get("serviceCode"), ""));
                    }
                });
            }
        }
    }

    @Override
    public int selectVulnerabilityProjectCount(Long eid, String project, String scanBatch, String startDate, String endDate, String projectId, Integer projectStatus) {
        HashMap<String, Object> map = new HashMap<>();
        map.put("eid", eid);
        map.put("project", project);
        map.put("scanBatch", scanBatch);
        map.put("startDate",startDate);
        map.put("endDate",endDate);
        map.put("projectStatus",projectStatus);
        map.put("projectId",projectId);
        map.put("eidList",serviceMaintenanceService.getServiceEidList(vulnerabilityProjectMapper.selectVulnerabilityProjectEidList()));
        int selectVulnerabilityProjectCount = vulnerabilityProjectMapper.selectVulnerabilityProjectCount(map);
        return selectVulnerabilityProjectCount;
    }

    private HashMap<String, Object> buildselectVulnerabilityProject(Long eid, String project,String scanBatch, String startDate, String endDate, int page, int size,String projectId,Integer projectStatus) {
        int start = (page - 1) * size;
        HashMap<String, Object> map = new HashMap<>();
        map.put("eid", eid);
        map.put("project", project);
        map.put("scanBatch",scanBatch);
        map.put("projectId",projectId);
        map.put("startDate",startDate);
        map.put("endDate",endDate);
        map.put("projectStatus",projectStatus);
        map.put("eidList",serviceMaintenanceService.getServiceEidList(vulnerabilityProjectMapper.selectVulnerabilityProjectEidList()));
        if (page > 0)
            start = (page - 1) * size;
        map.put("start", start);
        map.put("size", size);
        return map;
    }

    @Override
    public BaseResponse updateVulnerabilityProject(VulnerabilityProjectParam vulnerabilityProject, List<MultipartFile> files) throws IOException{
        Integer vulnerabilityReportRecord = vulnerabilityReportRecordMapper.selectVulnerabilityReportRecord(vulnerabilityProject.getId());
        VulnerabilityProject vp = vulnerabilityProjectMapper.selectVulnerabilityProjectById(vulnerabilityProject.getId());
        vulnerabilityProject.setEid(vp.getEid());
        if (IntegerUtil.isNotEmpty(vulnerabilityReportRecord)) {
            // 已经生成报告不可以编辑
            return BaseResponse.error(ResponseCode.VULNERABILITY_PROJECT_NOT_EDIT);
        }
        if (Objects.nonNull(files)) {
            // 檢查是否正在上傳
            if(Constants.getThreadTaskList().contains(vulnerabilityProject.getId())){
                return BaseResponse.error(ResponseCode.VULNERABILITY_SCAN_DATA_IS_BEING_UPLOADED);
            }

            // 新增線程
            Constants.getThreadTaskList().add(vulnerabilityProject.getId());
            //異步前先處理資料
            List<Set<VulnerabilityScanData>> dataLists = vulnerabilityProjectScanService.processScanDataFiles(files);
            // 提交一个新任務到線程池執行
            executorService.submit(() -> vulnerabilityProjectFileProcess(vulnerabilityProject, dataLists, true));

        }
        vulnerabilityProjectMapper.updateVulnerabilityProject(vulnerabilityProject);
        return BaseResponse.ok();
    }

    @Override
    public BaseResponse deleteVulnerabilityProject(VulnerabilityProject vulnerabilityProject) {
        Integer vulnerabilityReportRecord = vulnerabilityReportRecordMapper.selectVulnerabilityReportRecord(vulnerabilityProject.getId());
        if (IntegerUtil.isNotEmpty(vulnerabilityReportRecord)) {
            // 已经生成报告不可以删除
            return BaseResponse.error(ResponseCode.VULNERABILITY_REPORT_EXIST);
        }
        Integer vulnerabilityProjectAsset = vulnerabilityProjectAssetMapper.selectVulnerabilityProjectAsset(vulnerabilityProject.getId());
        if (IntegerUtil.isNotEmpty(vulnerabilityProjectAsset)) {
            // 有添加过设备，要删除
            vulnerabilityProjectAssetMapper.deleteVulnerabilityProjectAsset(vulnerabilityProject.getId());
        }
        Integer vulnerabilityProjectScanRecord = vulnerabilityProjectScanMapper.selectVulnerabilityProjectScanRecord(vulnerabilityProject.getId());
        if (IntegerUtil.isNotEmpty(vulnerabilityProjectScanRecord)) {
            // 添加过弱点清单，要删除
            vulnerabilityProjectScanMapper.deleteVulnerabilityProjectScanRecord(vulnerabilityProject.getId());
        }
        // 删除弱点扫描专案记录
        return BaseResponse.ok(vulnerabilityReportRecordMapper.deleteVulnerabilityProject(vulnerabilityProject.getId()));
    }

    @Override
    public BaseResponse getProjectSummary(String projectId, Integer language) {

        if (StringUtils.isEmpty(projectId)) {
            return BaseResponse.error(ResponseCode.VULNERABILITY_PROJECT_ID_IS_NEED);
        }

        VulnerabilityProject vulnerabilityProject = vulnerabilityProjectMapper.selectVulnerabilityProjectById(projectId);

        if(ObjectUtils.isEmpty(vulnerabilityProject)){
            return BaseResponse.error(ResponseCode.VULNERABILITY_PROJECT_IS_EMPTY);
        }

        String summary = vulnerabilityProject.getProjectSummary();

        if(StringUtils.isEmpty(summary)){
            VulnerabilityRiskCount param = new VulnerabilityRiskCount();
            param.setEid(vulnerabilityProject.getEid());
            param.setProjectId(projectId);
            param.setLanguage(language);
            List<VulnerabilityLevelMap> levelSizeMaps = (List<VulnerabilityLevelMap>) getVulnerabilityRisksCount(param).getData();
            VulnerabilityReportContent content = vulnerabilityReportRecordMapper.selectReportContentByLanguage(language);
            summary = String.format(content.getScanExecuteToolResult(),
                    levelSizeMaps.get(0).getCRITICAL(),
                    levelSizeMaps.get(0).getHIGH(),
                    levelSizeMaps.get(0).getMEDIUM(),
                    levelSizeMaps.get(0).getLOW());

            return BaseResponse.ok(summary);
        }

        return BaseResponse.ok(summary);
    }

    @Override
    public BaseResponse updateProjectSummary(VulnerabilityProjectSummary projectSummary) {

        if(ObjectUtils.isEmpty(projectSummary)){
            return BaseResponse.error(ResponseCode.VULNERABILITY_PROJECT_SUMMARY_CAN_NOT_EMPTY);
        }

        Integer isSuccess = vulnerabilityProjectMapper.updateVulnerabilityProjectSummary(projectSummary);
        return BaseResponse.ok(isSuccess);
    }

    @Override
    public BaseResponse getVulnerabilityRisksCount(VulnerabilityRiskCount param){
//        if (LongUtil.isEmpty(param.getEid())) {
//            return BaseResponse.error(ResponseCode.RISK_EID_EMPTY);
//        }

        if (StringUtils.isBlank(param.getProjectId())) {
            return BaseResponse.error(ResponseCode.VULNERABILITY_PROJECT_ID_IS_NEED);
        }

        List<VulnerabilityProjectScanRecord> scanRecords = vulnerabilityProjectScanMapper.getVulnerabilityProjectScanRecord(param);

        List<VulnerabilityLevelMap> levelSizeMaps = new ArrayList<>();
        Map<String, List<VulnerabilityProjectScanRecord>> levelMap =
                scanRecords.stream().collect(
                        Collectors.groupingBy(VulnerabilityProjectScanRecord::getVulnerabilityLevel));

        VulnerabilityLevelMap levelSizeMap = new VulnerabilityLevelMap();
        levelSizeMap.setCRITICAL(levelMap.getOrDefault(VulnerabilityLevelEnum.CRITICAL.getName(), new ArrayList<>()).size());
        levelSizeMap.setHIGH(levelMap.getOrDefault(VulnerabilityLevelEnum.HIGH.getName(), new ArrayList<>()).size());
        levelSizeMap.setMEDIUM(levelMap.getOrDefault(VulnerabilityLevelEnum.MEDIUM.getName(), new ArrayList<>()).size());
        levelSizeMap.setLOW(levelMap.getOrDefault(VulnerabilityLevelEnum.LOW.getName(), new ArrayList<>()).size());

        String projectExecuteTime = vulnerabilityProjectMapper.selectVulnerabilityProjectById(param.getProjectId()).getProjectExecuteTime();

        levelSizeMap.setProjectExecuteTime(projectExecuteTime);
        levelSizeMaps.add(levelSizeMap);

        return BaseResponse.ok(levelSizeMaps);
    }

    @Override
    public void updateVulnerabilityProjectStatus(Long eid) {
        fixProjectStatus(Stream.of(eid).collect(Collectors.toList()));
    }

    @Override
    public int fixProjectStatus(List<Long> eidList) {
        List<VulnerabilityProject> vpList = vulnerabilityProjectMapper.selectVulnerabilityProjectByEid(eidList);

        for (VulnerabilityProject vp : vpList) {
            Integer hasReport = vulnerabilityReportRecordMapper.selectVulnerabilityReportRecordByProjectId(vp.getId());
            if (Objects.nonNull(hasReport) && hasReport > 0) {
                vulnerabilityProjectMapper.updateVulnerabilityProjectStatus(vp.getId(), VulnerabilityProjectStatus.GENERATE_REPORT.getCode());
                continue;
            }
            Integer i = vulnerabilityProjectScanMapper.
                    selectVulnerabilityProjectScanRecord(vp.getId());
            if (Objects.nonNull(i)&&i > 0) {
                vulnerabilityProjectMapper.updateVulnerabilityProjectStatus(vp.getId(), VulnerabilityProjectStatus.ALREADY_SCAN.getCode());
                continue;
            }

            vulnerabilityProjectMapper.updateVulnerabilityProjectStatus(vp.getId(), VulnerabilityProjectStatus.NO_SCAN.getCode());
        }
        return vpList.size();
    }

    @Override
    public ResponseBase<VulnerabilityProjectDTO> getProjectInfo(List<Long> eid) {

        List<VulnerabilityProject> vpList = vulnerabilityProjectMapper.selectVulnerabilityProjectByEid(eid);
        List<VulnerabilityReportRecord> vrrList = vulnerabilityReportRecordMapper.selectVulnerabilityReportRecordByEid(eid)
                .stream().filter(vrr -> !VulnerabilityReportStatus.DELETE.equals(vrr.getReportStatus())).collect(Collectors.toList());
        Map<Integer, List<VulnerabilityProject>> statusMap = vpList
                .stream()
                .filter(i -> Objects.nonNull(i.getProjectStatus()))
                .collect(Collectors.groupingBy(VulnerabilityProject::getProjectStatus));

        return ResponseBase.okT(new VulnerabilityProjectDTO(
                statusMap.getOrDefault(VulnerabilityProjectStatus.NO_SCAN.getCode(),new ArrayList()).size(),
                statusMap.getOrDefault(VulnerabilityProjectStatus.ALREADY_SCAN.getCode(),new ArrayList()).size(),
                vrrList.size()));
    }

    private List<String> getAssetIds(VulnerabilityProjectParam vulnerabilityProject, Set<String> ips){
        List<String> assetIds = new ArrayList<String>();
        ips.stream().forEach(ip -> {

            Set<String> existAssetIp = new HashSet<>();
            if (StringUtils.isNotBlank(vulnerabilityProject.getId())) {
                existAssetIp = vulnerabilityProjectAssetMapper.projectAssetGetByProjectIdAndAssetIp(vulnerabilityProject.getId(), null)
                        .stream()
                        .map(VulnerabilityProjectAsset::getAssetIp)
                        .collect(Collectors.toSet());
            }

            List<VulnerabilityAsset> vulnerabilityAssets = vulnerabilityProjectAssetMapper.assetGetByEidOrIds(vulnerabilityProject.getEid(), null, null);

            Set<String> finalExistAssetIp = existAssetIp;
            List<VulnerabilityAsset> filteredAssets = vulnerabilityAssets.stream()
                    .filter(asset -> !finalExistAssetIp.contains(asset.getAssetIp()))
                    .collect(Collectors.toList());

            filteredAssets.forEach( assets -> {
                Optional<VulnerabilityAsset> assetOpt = Optional.ofNullable(assets);
                assetOpt.filter(asset -> ip.equals(asset.getAssetIp()))
                        .ifPresent(asset -> {
                            assetIds.add(asset.getId());
                        });
            });
        });
        return assetIds;
    }

    public void vulnerabilityProjectFileProcess(
            VulnerabilityProjectParam vulnerabilityProject,
            List<Set<VulnerabilityScanData>> datalists,
            Boolean modify
    ) {
        String projectId = vulnerabilityProject.getId();

        // 開啟事務
        TransactionStatus transactionStatus = platformTransactionManager.getTransaction(TransactionDefinition.withDefaults());

        try {
            //重新上传漏扫清单时 覆盖旧的漏扫清单
            vulnerabilityProjectScanAssetMapper.deleteVulnerabilityProjectScanRecord(projectId);
            //重新上傳弱掃清單時 覆蓋舊的掃描結果設備清單
            vulnerabilityProjectScanAssetMapper.deleteVulnerabilityProjectScanAsset(projectId);

            // 處理數據
            datalists.forEach(dataList -> {
                // 新增編輯專案之"設備掃描清單"記錄
                Set<String> ips = dataList.stream().map(VulnerabilityScanData::getHost).collect(Collectors.toSet());

                List<String> assetIds = getAssetIds(vulnerabilityProject, ips);
                if (CollectionUtil.isEmpty(assetIds) || assetIds.size() != ips.size()) {
                    List<VulnerabilityAsset> vulnerabilityAssets = vulnerabilityProjectAssetMapper.assetGetByEidOrIds(vulnerabilityProject.getEid(), null, null);
                    // 取得租戶不存在設備清單
                    ips.stream()
                            .filter(ip -> vulnerabilityAssets.stream().noneMatch(asset -> ip.equals(asset.getAssetIp())))
                            .forEach(ip -> {
                                VulnerabilityAsset assetParam = new VulnerabilityAsset();
                                assetParam.setAssetIp(ip);
                                assetParam.setAssetName("device-" + ip);
                                assetParam.setAssetNumber("device-" + ip);
                                assetParam.setAssetType(1); //預設伺服器
                                assetParam.setEid(vulnerabilityProject.getEid());
                                assetParam.setMaintain(true);
                                assetParam.setProjectId(projectId);
                                assetService.assetSave(assetParam);
                                vulnerabilityAssets.add(assetParam);
                            });
                    //已存在設備新增
                    List<String> assetIpList = ips.stream()
                            .filter(ip -> vulnerabilityAssets.stream().anyMatch(asset -> ip.equals(asset.getAssetIp())))
                            .collect(Collectors.toList());
                    List<String> assetIdList = vulnerabilityAssetMapper.getAssetIdListByIpList(assetIpList, vulnerabilityProject.getEid());
                    List<VulnerabilityAsset> matchAssets = vulnerabilityProjectAssetMapper.assetGetByEidOrIds(vulnerabilityProject.getEid(), assetIdList, null);
                    matchAssets.forEach(asset -> {
                        asset.setProjectId(projectId);
                        maintenanceAssetService.projectAssetSave(asset, true);
                    });
                } else {
                    List<VulnerabilityAsset> vulnerabilityAssets = vulnerabilityProjectAssetMapper.assetGetByEidOrIds(vulnerabilityProject.getEid(), assetIds, null);
                    vulnerabilityAssets.forEach(vulnerabilityAsset -> {
                        vulnerabilityAsset.setProjectId(projectId);
                        maintenanceAssetService.projectAssetSave(vulnerabilityAsset, true);
                    });
                }
            });

            // 新增編輯專案之"弱點掃描清單"記錄
            VulnerabilityProjectScanParam scanParam = new VulnerabilityProjectScanParam();
            scanParam.setProjectId(projectId);
            scanParam.setLanguage(vulnerabilityProject.getLanguage());
            scanParam.setEid(vulnerabilityProject.getEid());
            scanParam.setDuplicateCheck(vulnerabilityProject.getDuplicateCheck());

            //上傳弱點清單
            vulnerabilityProjectScanService.processVulnerabilityScanUpload(scanParam, datalists, modify);
            platformTransactionManager.commit(transactionStatus);
        } catch (Exception e) {
            log.error("[vulnerabilityProjectFileProcess] error:{}", e.getMessage(), e);
            Constants.getThreadTaskList().remove(projectId);
            platformTransactionManager.rollback(transactionStatus);
        }
    }

    @Override
    public ResponseBase<List<Long>> getProjectEidList() {
        List<Long> eidList = vulnerabilityProjectMapper.selectVulnerabilityProjectEidList();
        return ResponseBase.okT(eidList);
    }
}
