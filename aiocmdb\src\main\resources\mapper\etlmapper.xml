<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.digiwin.escloud.aiocmdb.etl.dao.EtlMapper">
    <insert id="saveEtlEngine" keyProperty="id" keyColumn="id" parameterType="com.digiwin.escloud.etl.model.EtlEngine">
        insert into etl_engine (appCode,collectCode,schemaName,sinkMode,etlJson,sinkType,sinkName,sinkPk,sinkFieldsJson,
        basePriority,sinkTableTtl)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.appCode},#{item.collectCode},#{item.schemaName},#{item.sinkMode},#{item.etlJson},#{item.sinkType},
            #{item.sinkName},#{item.sinkPk},#{item.sinkFieldsJson},#{item.basePriority},#{item.sinkTableTtl})
        </foreach>
        ON DUPLICATE KEY UPDATE etlJson = values(etlJson)
    </insert>

    <update id="updateEtlJson">
        update etl_engine
        <set>
            etlJson = #{etlJson},
            <if test="sinkFieldsJson != null and sinkFieldsJson != ''">
                sinkFieldsJson = #{sinkFieldsJson},
            </if>
        </set>
        WHERE collectCode = #{modelCode}
    </update>

    <delete id="deleteEtlEngine">
        delete from etl_engine
        where appCode=#{appCode} and collectCode=#{collectCode}
    </delete>

    <select id="getSinkFieldsType" resultType="java.lang.String">
        SELECT GROUP_CONCAT(CONCAT(fieldCode,':',fieldType,':',defaultValue) ORDER BY modelFieldGroupCode,sort SEPARATOR ';') as fieldName
        from
            (
                SELECT a.modelCode,b.fieldCode, b.fieldType,b.defaultValue, a.modelFieldGroupCode,a.sort
                FROM cmdb_model_field_mapping a
                         left join cmdb_field b on a.modelSettingType='field' AND a.targetCode=b.fieldCode AND b.sid = a.sid
                WHERE IFNULL(modelCode,'')!='' and fieldName!='' and a.modelCode=#{modelCode} and sort!=-1
                union all
                SELECT a.modelCode,c.fieldSetCode as fieldCode, 'VARCHAR','' defaultValue, a.modelFieldGroupCode,a.sort
                FROM cmdb_model_field_mapping a
                    LEFT JOIN cmdb_fieldset c ON a.modelSettingType='fieldSet' AND c.fieldSetCode = a.targetCode AND c.sid = a.sid
                WHERE IFNULL(modelCode,'')!='' and fieldSetName!='' and a.modelCode=#{modelCode} and sort!=-1
            ) a
        GROUP BY modelCode
    </select>

    <select id="selectModelCodesByCodeList" resultType="java.lang.String">
        SELECT modelCode
        FROM cmdb_model
        WHERE 1=1
        <if test="modelCodes != null">
            <foreach collection="modelCodes" item="item" open=" and modelCode IN (" separator=", " close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="getLatestEtlEngine" resultType="com.digiwin.escloud.etl.model.EtlEngine">
        SELECT appCode,collectCode,schemaName,sinkMode,etlJson,sinkName,latestTable,latestTableName,latestTablePk,
        latestFieldsType,latestType,latestTableTtl
        FROM etl_engine
        WHERE latestTable = 1 and ifnull(latestTableName,'')!='' and ifnull(latestFieldsType,'')!=''
        <if test="modelCodes != null">
            <foreach collection="modelCodes" item="item" open=" and sinkName IN (" separator=", " close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="getModelFieldGroupCode" resultType="com.digiwin.escloud.aiocmdb.model.model.ModelFieldMapping">
        SELECT modelFieldGroupCode, targetCode
        FROM cmdb_model_field_mapping
        WHERE modelCode=#{modelCode} and targetCode =#{targetCode}
    </select>

    <select id="getMainEtlFieldsType" resultType="com.digiwin.escloud.etl.model.EtlEngine">
        SELECT id,sinkType,sinkName,sinkFieldsType
        FROM etl_engine
        WHERE sinkEnable = 1 and ifnull(sinkName,'')!='' and ifnull(sinkFieldsType,'')!='' and sinkType!='eshbase'
        <if test="modelCodes != null">
            <foreach collection="modelCodes" item="item" open=" and sinkName IN (" separator=", " close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <update id="updateEtlFieldJson">
        update etl_engine
        set sinkFieldsJson=#{sinkFieldsJson}
        where id=#{id}
    </update>

    <update id="batchUpdateEtlFieldJson">
        update etl_engine
        set sinkFieldsJson=#{sinkFieldsJson}
        <where>
            <if test="idList != null">
                <foreach collection="idList" item="item" open=" id IN (" separator=", " close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </update>

    <select id="getMainEtlEngine" resultType="com.digiwin.escloud.etl.model.EtlEngine">
        select *
        from etl_engine
        <where>
            <if test="appCode != null and appCode!=''">
                and appCode=#{appCode}
            </if>
            <if test="modelCode != null and modelCode!=''">
                and collectCode=#{modelCode}
            </if>
            <if test="sinkName != null and sinkName!=''">
                and sinkName=#{sinkName}
            </if>
            <if test="schemaName != null and schemaName!=''">
                and schemaName=#{schemaName}
            </if>
            <if test="sinkType != null and sinkType!=''">
                and sinkType=#{sinkType}
            </if>
            <if test="storageSettingId != null">
                and storageSettingId=#{storageSettingId}
            </if>
        </where>
        limit 1;
    </select>

    <select id="getEtlList" resultType="com.digiwin.escloud.aiocmdb.etl.dto.EtlEngineRespDTO">
        select ee.*,ifnull(count,0) sinkCount
        from etl_engine ee
        LEFT JOIN (
        SELECT etlId,COUNT(*) as count
        FROM etl_engine_table
        GROUP BY etlId
        ) eet ON ee.id = eet.etlId
        where 1=1
        <if test="modelCode != null and modelCode!=''">
            and collectCode=#{modelCode}
        </if>
        <if test="sinkLandEnable != null">
            and sinkLandEnable=#{sinkLandEnable} and sinkEnable=1
        </if>
        order by id
    </select>


    <select id="getEtlById" resultType="com.digiwin.escloud.aiocmdb.etl.dto.EtlEngineRespDTO">
        select ee.*,ifnull(count,0) sinkCount
        from etl_engine ee
                 LEFT JOIN (
            SELECT etlId,COUNT(*) as count
            FROM etl_engine_table
            GROUP BY etlId
        ) eet ON ee.id = eet.etlId
        where ee.id=#{id}
    </select>

    <update id="enableEtlStatus">
        update etl_engine
        set sinkEnable=#{status}
        where id=#{id}
    </update>

    <update id="updateEtlStatusBySink">
        update etl_engine
        set sinkEnable=#{sinkEnable}
        where appCode=#{appCode} and collectCode=#{collectCode} and schemaName=#{schemaName} and
            sinkType=#{sinkType} and sinkName=#{sinkName}
    </update>

    <select id="getEtlExist" resultType="java.lang.Integer">
        select 1
        from etl_engine
        where ifnull(appCode,'')=#{appCode} and collectCode=#{collectCode} and schemaName=#{schemaName} and
        sinkType=#{sinkType} and sinkName=#{sinkName}
        <if test="mode == 'update' ">
            AND id!=#{id}
        </if>
        LIMIT 1;
    </select>

    <insert id="saveEtl" keyProperty="id" keyColumn="id" parameterType="com.digiwin.escloud.etl.model.EtlEngine">
        insert into etl_engine (appCode,collectCode,schemaName,sinkMode,etlJson,sinkType,sinkName,sinkPk,sinkFieldsJson,
                                sinkTableTtl)
        values
            (#{appCode},#{collectCode},#{schemaName},#{sinkMode},#{etlJson},#{sinkType},#{sinkName},#{sinkPk},#{sinkFieldsJson},
             #{sinkTableTtl})
            ON DUPLICATE KEY UPDATE appCode = values(appCode),schemaName = values(schemaName),etlJson = values(etlJson),
            sinkType = values(sinkType),sinkName = values(sinkName),sinkPk = values(sinkPk),sinkFieldsJson = values(sinkFieldsJson),
                                 sinkTableTtl = values(sinkTableTtl)
    </insert>

    <insert id="insertEtl" keyProperty="id" useGeneratedKeys="true" keyColumn="id" parameterType="com.digiwin.escloud.etl.model.EtlEngine">
        insert into etl_engine (appCode,collectCode,schemaName,schemaNameDynamic,sinkMode,etlJson,sinkType,sinkName,sinkNameDynamic,sinkPk,sinkFieldsJson,
                                sinkTableTtl,sinkTableTtlField,sinkTableTtlUnit,sinkEnable,sinkSchemaCreatedSelf,sinkNameCreatedSelf,sinkDdl,showInDataLog,storageSettingId)
        values
            (#{appCode},#{collectCode},#{schemaName},#{schemaNameDynamic},#{sinkMode},#{etlJson},#{sinkType},#{sinkName},#{sinkNameDynamic},#{sinkPk},#{sinkFieldsJson},
             #{sinkTableTtl},#{sinkTableTtlField},#{sinkTableTtlUnit},#{sinkEnable},#{sinkSchemaCreatedSelf},#{sinkNameCreatedSelf},#{sinkDdl},#{showInDataLog},#{storageSettingId})
    </insert>

    <update id="updateEtl">
        update etl_engine
        <set>
            sinkTableTtl = #{sinkTableTtl},
            sinkTableTtlField = #{sinkTableTtlField},
            sinkTableTtlUnit = #{sinkTableTtlUnit},
            <if test="appCode != null and appCode!=''">
                appCode = #{appCode},
            </if>
            <if test="schemaName != null and schemaName!=''">
                schemaName = #{schemaName},
            </if>
            <if test="schemaNameDynamic != null and schemaNameDynamic!=''">
                schemaNameDynamic = #{schemaNameDynamic},
            </if>
            <if test="etlJson != null and etlJson!=''">
                etlJson = #{etlJson},
            </if>
            <if test="sinkType != null and sinkType!=''">
                sinkType = #{sinkType},
            </if>
            <if test="sinkName != null and sinkName!=''">
                sinkName = #{sinkName},
            </if>
            <if test="sinkNameDynamic != null and sinkNameDynamic!=''">
                sinkNameDynamic = #{sinkNameDynamic},
            </if>
            <if test="sinkPk != null and sinkPk!=''">
                sinkPk = #{sinkPk},
            </if>
            <if test="sinkFieldsJson != null and sinkFieldsJson!=''">
                sinkFieldsJson = #{sinkFieldsJson},
            </if>
            <if test="sinkSchemaCreatedSelf != null and sinkSchemaCreatedSelf!=''">
                sinkSchemaCreatedSelf = #{sinkSchemaCreatedSelf},
            </if>
            <if test="sinkNameCreatedSelf != null and sinkNameCreatedSelf!=''">
                sinkNameCreatedSelf = #{sinkNameCreatedSelf},
            </if>
            <if test="sinkDdl != null and sinkDdl!=''">
                sinkDdl = #{sinkDdl},
            </if>
            <if test="changeOp != null and changeOp!=''">
                changeOp = #{changeOp},
            </if>
            <if test="showInDataLog != null and showInDataLog!=''">
                showInDataLog = #{showInDataLog},
            </if>
            <if test="storageSettingId != null">
                storageSettingId = #{storageSettingId},
            </if>
        </set>
        where id=#{id}
    </update>

    <delete id="deleteEtl">
        delete from etl_engine
        where id=#{id}
    </delete>

    <select id="getSinkFieldsJson" resultType="java.lang.String">
        select sinkFieldsJson
        from etl_engine
        where schemaName=#{schemaName} and sinkType=#{sinkType} and sinkName=#{sinkName}
            LIMIT 1;
    </select>

    <select id="getEtlEngineBySinkInfo" resultType="com.digiwin.escloud.etl.model.EtlEngine">
        select *
        from etl_engine
        where schemaName=#{schemaName} and sinkType=#{sinkType} and sinkName=#{sinkName}
            LIMIT 1;
    </select>

    <select id="getSinkFieldDesc" resultType="com.digiwin.escloud.aiocmdb.etl.model.EtlModelFieldDesc">
        select id, #{sinkName} sinkName, sinkField, fieldDescCN,fieldDescTW
        from etl_model_field_desc
        where 1=1
        <if test="fieldCodes != null">
            <foreach collection="fieldCodes" item="item" open=" and sinkField IN (" separator=", " close=")">
                #{item}
            </foreach>
        </if>
        <!--        select emfd.id, #{sinkName} sinkName, ifnull(sinkField,cf.fieldCode) sinkField, ifnull(fieldDescCN,cf.fieldName) fieldDescCN,-->
        <!--               ifnull(fieldDescTW,cf.fieldName) fieldDescTW-->
        <!--        from cmdb_field cf-->
        <!--        left join etl_model_field_desc emfd on emfd.sinkField=cf.fieldCode-->
        <!--        where 1=1-->
        <!--        <if test="fieldCodes != null">-->
        <!--            <foreach collection="fieldCodes" item="item" open=" and fieldCode IN (" separator=", " close=")">-->
        <!--                #{item}-->
        <!--            </foreach>-->
        <!--        </if>-->
        <!--        union all-->
        <!--        select emfd.id, #{sinkName} sinkName, ifnull(sinkField,cfs.fieldSetCode) sinkField, ifnull(fieldDescCN,cfs.fieldSetName) fieldDescCN,-->
        <!--        ifnull(fieldDescTW,cfs.fieldSetName) fieldDescTW-->
        <!--        from cmdb_fieldset cfs-->
        <!--        left join etl_model_field_desc emfd on emfd.sinkField=cfs.fieldSetCode-->
        <!--        where 1=1-->
        <!--        <if test="fieldCodes != null">-->
        <!--            <foreach collection="fieldCodes" item="item" open=" and fieldSetCode IN (" separator=", " close=")">-->
        <!--                #{item}-->
        <!--            </foreach>-->
        <!--        </if>-->
        <!--        order by id,sinkField-->
    </select>

    <select id="getModelFieldBaseInfo" resultType="java.util.HashMap">
        SELECT a.modelCode,b.fieldCode, b.fieldType,b.fieldName,a.sort
        FROM cmdb_model_field_mapping a
                 left join cmdb_field b on a.modelSettingType='field' AND a.targetCode=b.fieldCode AND b.sid = a.sid
        WHERE IFNULL(modelCode,'')!='' and fieldName!='' and a.modelCode=#{modelCode}
        union all
        SELECT a.modelCode,c.fieldSetCode as fieldCode, 'VARCHAR',fieldSetName as fieldName,a.sort
        FROM cmdb_model_field_mapping a
                 LEFT JOIN cmdb_fieldset c ON a.modelSettingType='fieldSet' AND c.fieldSetCode = a.targetCode AND c.sid = a.sid
        WHERE IFNULL(modelCode,'')!='' and fieldSetName!='' and a.modelCode=#{modelCode}
        union all
        SELECT a.modelCode,c.fieldCode, c.fieldType,c.fieldName,a.sort*1000+b.sort
        FROM cmdb_model_field_mapping a
                 LEFT JOIN cmdb_fieldset_mapping b ON a.modelSettingType='fieldSet' AND b.fieldSetCode = a.targetCode AND b.sid = a.sid
                 left join cmdb_field c on b.fieldCode=c.fieldCode AND b.sid = c.sid
        WHERE IFNULL(modelCode,'')!='' and fieldName!='' and a.modelCode=#{modelCode}
    </select>

    <select id="getSinkTypeEtl" resultType="com.digiwin.escloud.etl.model.EtlEngine">
        select schemaName, sinkName, sinkType
        from etl_engine
        where sinkType=#{sinkType} and sinkEnable=1
        <if test="schemaName != null and schemaName!=''">
            and schemaName=#{schemaName}
        </if>
        <if test="sinkName != null and sinkName!=''">
            and sinkName=#{sinkName}
        </if>
    </select>

    <update id="updateEtlCollect">
        update etl_engine
        set sinkPk=#{sinkPk}, sinkFieldsType=REPLACE(sinkFieldsType,'rowKey;','')
        <if test="collectCode!='' and collectCode != null">
            , collectCode=#{collectCode}
        </if>
        where sinkName=#{sinkName}
    </update>

    <select id="selectEtlListByMap" resultType="com.digiwin.escloud.etl.model.EtlEngine">
        select id, appCode, collectCode, schemaName, etlJson, sinkType, sinkName, sinkPk, sinkFieldsJson, sinkExtend,
        sinkEnable
        from etl_engine
        WHERE 1 != 1
        <trim prefix=" OR (" prefixOverrides="AND" suffix=")">
            <if test="appCode != null and appCode != ''">
                AND appCode = #{appCode}
            </if>
            <if test="appCodeList">
                <foreach collection="appCodeList" item="item" open=" AND appCode IN(" separator=", " close=")">
                    #{item}
                </foreach>
            </if>
            <if test="collectCode != null and collectCode != ''">
                AND collectCode = #{collectCode}
            </if>
            <if test="collectCodeList != null">
                <foreach collection="collectCodeList" item="item" open=" AND collectCode IN(" separator=", " close=")">
                    #{item}
                </foreach>
            </if>
            <if test="schemaName != null and schemaName != ''">
                AND schemaName = #{schemaName}
            </if>
            <if test="schemaNameList != null">
                <foreach collection="schemaNameList" item="item" open=" AND schemaName IN(" separator=", " close=")">
                    #{item}
                </foreach>
            </if>
            <if test="sinkMode != null and sinkMode != ''">
                AND sinkMode = #{sinkMode}
            </if>
            <if test="sinkType != null and sinkType != ''">
                AND sinkType = #{sinkType}
            </if>
            <if test="sinkTypeList != null">
                <foreach collection="sinkTypeList" item="item" open=" AND sinkType IN(" separator=", " close=")">
                    #{item}
                </foreach>
            </if>
            <if test="sinkName != null and sinkName != ''">
                AND sinkName = #{sinkName}
            </if>
            <if test="sinkNameList != null">
                <foreach collection="sinkNameList" item="item" open=" AND sinkName IN(" separator=", " close=")">
                    #{item}
                </foreach>
            </if>
            <if test="sinkEnable != null">
                AND sinkEnable = #{sinkEnable}
            </if>
        </trim>
        <if test="orderBy != null and orderBy != ''">
            ORDER BY ${orderBy}
        </if>
    </select>

    <update id="enableEtlShowInDataLogStatus">
        update etl_engine
        set showInDataLog = #{status}
        where id=#{eeid}
    </update>

    <select id="selectEEIsShowByCollectedCode" resultType="com.digiwin.escloud.etl.model.EtlEngine">
        select collectCode,showInDataLog
        from etl_engine
        where showInDataLog = true
        <foreach collection="collectCodeList" item="item" open=" AND collectCode IN(" separator=", " close=")">
            #{item}
        </foreach>
    </select>
    <select id="getById" resultType="com.digiwin.escloud.etl.model.EtlEngine">
        select * from etl_engine where id=#{id}
    </select>
    <select id="getEtlTableByEtlId" resultType="com.digiwin.escloud.etl.model.EtlEngineTable">
        select * from etl_engine_table where etlId=#{etlId}
    </select>
    <select id="getEtlTableByCond" resultType="com.digiwin.escloud.aiocmdb.etl.dto.EtlEngineTableRespDTO">
        select eet.*,ee.collectCode modelCode,ee.changeOp from etl_engine_table eet,etl_engine ee
        <where>
            ee.id = eet.etlId
            <if test="sinkType != null and sinkType != ''">
                and eet.sinkType = #{sinkType}
            </if>
            <if test="schemaName != null and schemaName != ''">
                and eet.schemaName = #{schemaName}
            </if>
            <if test="sinkName != null and sinkName != ''">
                and eet.sinkName = #{sinkName}
            </if>
            <if test="storageSettingId != null and storageSettingId != ''">
                and ee.storageSettingId = #{storageSettingId}
            </if>
        </where>
    </select>

    <insert id="insertEtlTable" keyProperty="id" keyColumn="id" parameterType="com.digiwin.escloud.etl.model.EtlEngineTable">
        insert into etl_engine_table (etlId,sinkType,schemaName,sinkName)
        values
            (#{etlId},#{sinkType},#{schemaName},#{sinkName})
            ON DUPLICATE KEY UPDATE updateTime=NOW()
    </insert>
    <insert id="batchInsertEtlTable">
        insert into etl_engine_table (etlId,sinkType,schemaName,sinkName)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.etlId},#{item.sinkType},#{item.schemaName},#{item.sinkName})
        </foreach>
        ON DUPLICATE KEY UPDATE updateTime=NOW()
    </insert>

    <delete id="deleteEtlTableByEtlId">
        delete from etl_engine_table where etlId=#{etlId}
    </delete>

    <delete id="batchDeleteEtlTableById">
        delete from etl_engine_table
        <where>
            <if test="list != null">
                <foreach collection="list" item="item" open=" and id IN(" separator=", " close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </delete>

    <delete id="deleteEtlTableByCond">
        delete from etl_engine_table
        <where>
            <if test="sinkType != null and sinkType != ''">
                and sinkType = #{sinkType}
            </if>
            <if test="schemaName != null and schemaName != ''">
                and schemaName = #{schemaName}
            </if>
            <if test="sinkName != null and sinkName != ''">
                and sinkName = #{sinkName}
            </if>
        </where>
    </delete>

    <select id="getEtlEngineIsolation" resultType="com.digiwin.escloud.aiocmdb.etl.model.EtlEngineIsolation">
        select * from etl_engine_isolation
    </select>


    <select id="selectStarrocksPK" resultType="com.digiwin.escloud.etl.model.EtlEngine">
        select collectCode,sinkPk
        from etl_engine
        where sinkType='starrocks' and schemaName='default'
        <if test="modelCodeList != null">
            <foreach collection="modelCodeList" item="item" open=" and collectCode IN (" separator=", " close=")">
                #{item}
            </foreach>
        </if>
    </select>
</mapper>