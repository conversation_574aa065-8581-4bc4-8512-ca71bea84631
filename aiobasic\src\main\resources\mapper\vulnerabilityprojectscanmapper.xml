<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.digiwin.escloud.aiobasic.vulnerability.dao.VulnerabilityProjectScanMapper">

    <resultMap id="scanDataResultMap"
               type="com.digiwin.escloud.aiobasic.vulnerability.model.VulnerabilityProjectScanAsset">
        <result property="assetId" column="vpsa_assetId"/>
        <result property="assetIp" column="vpsa_assetIp"/>
        <result property="assetName" column="vpa_assetName"/>

        <collection property="vcList" ofType="com.digiwin.escloud.aiobasic.vulnerability.model.VulnerabilityCount">
            <result column="projectId" property="projectId"/>
            <result column="level" property="level"/>
            <result column="cnt" property="cnt"/>
        </collection>
    </resultMap>

    <resultMap id="scanVulnerabilityDataResultMap"
               type="com.digiwin.escloud.aiobasic.vulnerability.model.VulnerabilityProjectScanRecord">
        <result property="vulnerabilityNumber" column="vulnerabilityNumber"/>
        <result property="vulnerabilityLevel" column="vulnerabilityLevel"/>
        <result property="vulnerabilityLevelCode" column="vulnerabilityLevelCode"/>
        <result property="vulnerabilityName" column="vulnerabilityName"/>
        <result property="vulnerabilityRepairStatus" column="vulnerabilityRepairStatus"/>
        <result property="serverPort" column="serverPort"/>

        <collection property="vrprrList"
                    ofType="com.digiwin.escloud.aiobasic.vulnerability.model.VulnerabilityRepairRecord">
            <result column="vrprr_assetIp" property="assetIp"/>
            <result column="vrprr_vulnerabilityId" property="vulnerabilityId"/>
            <result column="vrprr_executorName" property="executorName"/>
            <result column="vrprr_executorId" property="executorId"/>
            <result column="vrprr_executorMail" property="executorMail"/>
            <result column="vrprr_repairDetail" property="repairDetail"/>
            <result column="vrprr_repairStatus" property="repairStatus"/>
        </collection>
    </resultMap>

    <!-- 插入记录的 insert 语句 -->
    <insert id="batchScanInsert" parameterType="java.util.List">
        INSERT INTO vulnerability_project_scan_record
        (id, projectId,eid, assetIp, vulnerabilityId, vulnerabilityNumber, vulnerabilityLevel,
        vulnerabilityName,vulnerabilityDesc,serverPort,
        remark,dataJson,vpsaId,vulnerabilityRepairStatus,cvssV3BaseScore,cvssV2TemporalScore,cvssV3TemporalScore,cvssV2BaseScore)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.id},#{item.eid}, #{item.projectId}, #{item.assetIp}, #{item.vulnerabilityId},
            #{item.vulnerabilityNumber}, #{item.vulnerabilityLevel},
            #{item.vulnerabilityName},#{item.vulnerabilityDesc},#{item.serverPort},
            #{item.remark},#{item.dataJson},#{item.vpsaId},#{item.vulnerabilityRepairStatus}
            , #{cvssV3BaseScore}, #{cvssV2TemporalScore}, #{cvssV3TemporalScore},#{cvssV2BaseScore} )
        </foreach>
    </insert>

    <insert id="insertScan"
            parameterType="com.digiwin.escloud.aiobasic.vulnerability.model.VulnerabilityProjectScanRecord">
        INSERT INTO vulnerability_project_scan_record
        (id, eid, projectId, assetIp, vulnerabilityId, vulnerabilityNumber, vulnerabilityLevel, vulnerabilityName,
         vulnerabilityDesc, serverPort,
         remark, dataJson, vpsaId, vulnerabilityRepairStatus, cvssV3BaseScore, cvssV2TemporalScore, cvssV3TemporalScore,
         cvssV2BaseScore)
        VALUES (#{id}, #{eid}, #{projectId}, #{assetIp}, #{vulnerabilityId}, #{vulnerabilityNumber},
                #{vulnerabilityLevel},
                #{vulnerabilityName}, #{vulnerabilityDesc}, #{serverPort}, #{remark}, #{dataJson}, #{vpsaId},
                #{vulnerabilityRepairStatus}, #{cvssV3BaseScore}, #{cvssV2TemporalScore}, #{cvssV3TemporalScore},
                #{cvssV2BaseScore})
    </insert>

    <!-- 删除数据 -->
    <delete id="deleteScanById" parameterType="String">
        DELETE
        FROM vulnerability_project_scan_record
        WHERE id = #{id}
    </delete>

    <!-- 更新数据 -->
    <update id="updateScan"
            parameterType="com.digiwin.escloud.aiobasic.vulnerability.model.VulnerabilityProjectScanRecord">
        UPDATE vulnerability_project_scan_record

        SET projectId                 = #{projectId},
            assetIp                   = #{assetIp},
            eid                       = #{eid},
            vulnerabilityId           = #{vulnerabilityId},
            vulnerabilityNumber       = #{vulnerabilityNumber},
            vulnerabilityLevel        = #{vulnerabilityLevel},
            vulnerabilityName         = #{vulnerabilityName},
            vulnerabilityDesc         = #{vulnerabilityDesc},
            serverPort                = #{serverPort},
            vpsaId                    = #{vpsaId},
            vulnerabilityRepairStatus = #{vulnerabilityRepairStatus},
            cvssV3BaseScore           = #{cvssV3BaseScore},
            cvssV2TemporalScore       = #{cvssV2TemporalScore},
            cvssV3TemporalScore       = #{cvssV3TemporalScore},
            cvssV2BaseScore           = #{cvssV2BaseScore},
            remark                    = #{remark}
        WHERE id = #{id}
    </update>

    <update id="updateScanStatus"
    >
        UPDATE vulnerability_project_scan_record

        SET vulnerabilityRepairStatus = #{vulnerabilityRepairStatus}
        WHERE projectId = #{projectId}
          and assetIp = #{assetIp}
          and vulnerabilityNumber = #{vulnerabilityNumber}
    </update>

    <!-- 查询数据 -->
    <select id="vulnerabilitySelectByNumber"
            resultType="com.digiwin.escloud.aiobasic.vulnerability.model.VulnerabilityStorehouse">
        SELECT *
        FROM vulnerability_storehouse
        WHERE vulnerabilityNumber in (
        <foreach collection="numbers" item="item" separator=",">

            #{item}

        </foreach>
        )
    </select>

    <select id="projectAssetSelectByIps"
            resultType="com.digiwin.escloud.aiobasic.vulnerability.model.VulnerabilityProjectAsset">
        SELECT *
        FROM vulnerability_project_asset
        WHERE assetIp in(
        <foreach collection="ips" item="item" separator=",">

            #{item}

        </foreach>
        ) and projectId = #{projectId}
    </select>

    <select id="scanRecordSelectByIpsAndProjectId"
            resultType="com.digiwin.escloud.aiobasic.report.model.VulnerabilityProjectScanRecord">
        SELECT id, projectId, eid, assetIp, vulnerabilityId, vulnerabilityNumber, vulnerabilityLevel, vulnerabilityName, dataJSON
        FROM vulnerability_project_scan_record
        WHERE assetIp in (
        <foreach collection="ips" item="item" separator=",">

            #{item}

        </foreach>
        )
        <if test="projectId != null and projectId != ''">
            and projectId = #{projectId}
        </if>
    </select>

    <select id="selectByParam"
            parameterType="com.digiwin.escloud.aiobasic.vulnerability.model.param.VulnerabilityProjectScanParam"
            resultType="com.digiwin.escloud.aiobasic.vulnerability.model.VulnerabilityProjectScanRecord">
        SELECT DISTINCT vpsr.*
        FROM vulnerability_project_scan_record as vpsr left join
            ( SELECT vulnerabilityLevel, vulnerabilityNumber, vulnerabilityName
        FROM vulnerability_storehouse)
        as vs on vpsr.vulnerabilityNumber = vs.vulnerabilityNumber
        WHERE 1=1
        <if test="assetIp != null and !assetIp.isEmpty()">
            and vpsr.assetIp like concat ('%',#{assetIp},'%')
        </if>
        <if test="projectId != null and !projectId.isEmpty()">
            and vpsr.projectId = #{projectId}
        </if>
        <if test="vulnerability != null and vulnerability !=''">
            and (vs.vulnerabilityNumber like concat ('%',#{vulnerability},'%') or vs.vulnerabilityName like concat
            ('%',#{vulnerability},'%'))
        </if>

        <if test="vulnerabilityLevel != null and vulnerabilityLevel.size() > 0">
            and vs.vulnerabilityLevel in
            <foreach collection="vulnerabilityLevel" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        order by vpsr.assetIp asc,
        FIELD(vpsr.vulnerabilityLevel, 'CRITICAL', 'HIGH', 'MEDIUM', 'LOW','INFO'),
        vpsr.createDate desc
    </select>

    <select id="selectScanByParam"
            parameterType="com.digiwin.escloud.aiobasic.vulnerability.model.param.VulnerabilityProjectScanParam"
            resultType="com.digiwin.escloud.aiobasic.vulnerability.model.VulnerabilityProjectScanRecord">
        SELECT vpsr.*
        FROM vulnerability_project_scan_record vpsr
        LEFT JOIN (SELECT vulnerabilityNumber,vulnerabilityLevel FROM vulnerability_storehouse GROUP BY
        vulnerabilityNumber,vulnerabilityLevel) vs
        on vs.vulnerabilityNumber = vpsr.vulnerabilityNumber
        WHERE 1=1
        <if test="assetIp != null and !assetIp.isEmpty()">
            and vpsr.assetIp = #{assetIp}
        </if>
        <if test="projectId != null and !projectId.isEmpty()">
            and vpsr.projectId = #{projectId}
        </if>
        <if test="vulnerabilityNumber != null and vulnerabilityNumber !=''">
            and vpsr.vulnerabilityNumber =#{vulnerabilityNumber}
        </if>

        <if test="vulnerabilityName != null and vulnerabilityName !=''">
            and vpsr.vulnerabilityName like concat ('%',#{vulnerabilityName},'%')
        </if>
        <if test="vulnerabilityLevel != null and vulnerabilityLevel.size() > 0">
            and vs.vulnerabilityLevel in
            <foreach collection="vulnerabilityLevel" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        order by createDate desc
    </select>

    <select id="selectById"
            resultType="com.digiwin.escloud.aiobasic.vulnerability.model.VulnerabilityProjectScanRecord">
        SELECT *
        FROM vulnerability_project_scan_record
        WHERE id = #{id}
    </select>

    <select id="selectReportByProjectId"
            resultType="com.digiwin.escloud.aiobasic.vulnerability.model.VulnerabilityReportRecord">
        SELECT *
        FROM vulnerability_report_record
        WHERE projectId = #{projectId}
    </select>

    <select id="selectVulnerabilityProjectScanRecord" resultType="java.lang.Integer">
        SELECT 1
        FROM vulnerability_project_scan_record
        WHERE projectId = #{projectId}
        limit 1
    </select>

    <delete id="deleteVulnerabilityProjectScanRecord">
        DELETE
        FROM vulnerability_project_scan_record
        WHERE projectId = #{projectId}
    </delete>

    <insert id="insertOrUpdateVulnerabilityProjectScanAsset"
            parameterType="com.digiwin.escloud.aiobasic.vulnerability.model.VulnerabilityProjectScanAsset">
        INSERT INTO vulnerability_project_scan_asset (id,
                                                      projectId,
                                                      assetIp,
                                                      assetId,
                                                      assetRepairStatus)
        VALUES (#{id},
                #{projectId},
                #{assetIp},
                #{assetId},
                #{assetRepairStatus})
        ON DUPLICATE KEY UPDATE assetRepairStatus = VALUES(assetRepairStatus),
                                id                = VALUES(id)
    </insert>

    <select id="selectVulnerabilityCount"
            resultType="com.digiwin.escloud.aiobasic.vulnerability.model.VulnerabilityCount">
        SELECT
        vpsr.projectId,
        lvl.level,
        COALESCE(SUM(CASE WHEN UPPER(vpsr.vulnerabilityLevel) = lvl.level THEN vpsr.cnt ELSE 0 END), 0) AS cnt
        FROM
        (SELECT 'INFO' AS level
        UNION ALL SELECT 'MEDIUM'
        UNION ALL SELECT 'HIGH'
        UNION ALL SELECT 'CRITICAL'
        UNION ALL SELECT 'LOW') AS lvl

        LEFT JOIN (select vulnerabilityLevel,projectId,count(distinct vulnerabilityNumber) cnt from vulnerability_project_scan_record
        WHERE  projectId IN
        <foreach collection="projectIdList" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        group by vulnerabilityLevel,projectId) vpsr ON  UPPER(vpsr.vulnerabilityLevel) =
        lvl.level
        GROUP BY
        vpsr.projectId,
        lvl.level

    </select>

    <select id="selectVulnerabilityCountByAssetId" resultMap="scanDataResultMap">
        SELECT
        vpa.assetId as vpsa_assetId,
        vpa.assetIp as vpsa_assetIp,
        vpa.assetName as vpa_assetName,
        vpsr.projectId,
        lvl.level,
        COALESCE(SUM(CASE WHEN UPPER(vpsr.vulnerabilityLevel) = lvl.level THEN 1 ELSE 0 END), 0) AS cnt
        FROM
        (SELECT 'INFO' AS level
        UNION ALL SELECT 'MEDIUM'
        UNION ALL SELECT 'HIGH'
        UNION ALL SELECT 'CRITICAL'
        UNION ALL SELECT 'LOW') AS lvl
        LEFT JOIN vulnerability_project_scan_record vpsr ON  UPPER(vpsr.vulnerabilityLevel) =
        lvl.level
        LEFT JOIN vulnerability_project_asset vpa on vpa.assetIp = vpsr.assetIp AND vpa.projectId = vpsr.projectId
        WHERE
        vpa.assetId in
        <foreach collection="assetIdList" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        and
        vpsr.projectId IN
        <foreach collection="projectIdList" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        GROUP BY
        vpa.assetId,
        vpa.assetIp,
        vpsr.projectId,
        lvl.level

    </select>

    <update id="updateMaxGroup">
        SET SESSION group_concat_max_len = 1024 * 1024;
    </update>
    <select id="selectVpsrAndVpsa"
            parameterType="com.digiwin.escloud.aiobasic.vulnerability.model.param.VulnerabilityProjectScanParam"
            resultType="com.digiwin.escloud.aiobasic.vulnerability.model.VulnerabilityAndAssetCount">
        SELECT
        vs.vulnerabilityNumber,
        vs.vulnerabilityLevel,
        vl.code as vulnerabilityLevelCode,
        vpsr.vulnerabilityName,
        GROUP_CONCAT(vpsr.assetIp) AS assetIps,
        GROUP_CONCAT(vpsr.vulnerabilityRepairStatus) AS vulnerabilityRepairStatusString
        FROM vulnerability_project_scan_record as vpsr
        LEFT JOIN (SELECT vulnerabilityNumber,vulnerabilityLevel,vulnerabilityName FROM vulnerability_storehouse) vs
        on vs.vulnerabilityNumber = vpsr.vulnerabilityNumber
        LEFT JOIN vulnerability_level vl on vs.vulnerabilityLevel = vl.code
        where vpsr.projectId = #{projectId}
        <if test="vulnerability != null and vulnerability != ''">
            and (vpsr.vulnerabilityNumber like concat('%',#{vulnerability},'%')or vs.vulnerabilityName like
            concat('%',#{vulnerability},'%'))
        </if>
        <if test="vulnerabilityLevel != null and vulnerabilityLevel.size() > 0">
            and vl.code in
            <foreach collection="vulnerabilityLevel" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="vulnerabilityLevel == null or vulnerabilityLevel.size() == 0">
            and vl.code != 5
        </if>
        GROUP BY vs.vulnerabilityNumber,
        vs.vulnerabilityLevel,
        vl.code ,
        vpsr.vulnerabilityName
        ORDER BY vl.code

    </select>


    <select id="selectScanVulnerabilityData"
            parameterType="com.digiwin.escloud.aiobasic.vulnerability.model.param.VulnerabilityProjectScanParam"
            resultMap="scanVulnerabilityDataResultMap">
        SELECT
        vpsr.vulnerabilityNumber,
        vs.vulnerabilityLevel,
        vl.code AS vulnerabilityLevelCode,
        vpsr.vulnerabilityName,
        vpsr.vulnerabilityRepairStatus,
        vpsr.serverPort,
        vrprr.assetIp AS vrprr_assetIp,
        vrprr.vulnerabilityId AS vrprr_vulnerabilityId,
        vrprr.executorName AS vrprr_executorName,
        vrprr.executorId AS vrprr_executorId,
        vrprr.executorMail AS vrprr_executorMail,
        vrprr.repairDetail AS vrprr_repairDetail,
        vrprr.repairStatus AS vrprr_repairStatus
        FROM
        vulnerability_project_scan_record vpsr
        LEFT JOIN (
        SELECT
        vrr.assetIp,
        vrr.vulnerabilityNumber,
        vrr.vulnerabilityId,
        vrr.executorName,
        vrr.executorId,
        vrr.executorMail,
        vrr.repairDetail,
        vrr.repairStatus
        FROM
        vulnerability_repair_record vrr
        INNER JOIN (
        SELECT
        assetIp,
        vulnerabilityNumber,
        MAX(createDate) AS maxCreateTime
        FROM
        vulnerability_repair_record
        WHERE
        projectId = #{projectId}
        GROUP BY
        assetIp,
        vulnerabilityNumber
        ) AS latest_vrr ON vrr.assetIp = latest_vrr.assetIp
        AND vrr.vulnerabilityNumber = latest_vrr.vulnerabilityNumber
        AND vrr.createDate = latest_vrr.maxCreateTime
        ) AS vrprr ON vrprr.assetIp = vpsr.assetIp
        AND vpsr.vulnerabilityNumber = vrprr.vulnerabilityNumber
        LEFT JOIN (select vulnerabilityNumber,vulnerabilityLevel from vulnerability_storehouse group by
        vulnerabilityNumber,vulnerabilityLevel) vs on vs.vulnerabilityNumber = vpsr.vulnerabilityNumber
        LEFT JOIN vulnerability_level vl ON vl.code = vs.vulnerabilityLevel
        where
        vpsr.projectId = #{projectId}
        and vpsr.assetIp = #{assetIp}
        <if test="vulnerability != null and vulnerability != ''">
            and (vpsr.vulnerabilityNumber like concat('%',#{vulnerability},'%')or vpsr.vulnerabilityName like
            concat('%',#{vulnerability},'%'))
        </if>
        <if test="vulnerabilityLevel != null and vulnerabilityLevel.size() > 0">
            and vl.code in
            <foreach collection="vulnerabilityLevel" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="vulnerabilityLevel == null or vulnerabilityLevel.size() == 0">
            and vl.code != 5
        </if>
        ORDER BY vl.code
    </select>

    <select id="selectScanAssetData"
            parameterType="com.digiwin.escloud.aiobasic.vulnerability.model.param.VulnerabilityProjectScanParam"
            resultType="com.digiwin.escloud.aiobasic.vulnerability.model.VulnerabilityScanAssetCount">
        SELECT vpa.id,
        vpa.assetIp,
        vpa.assetId,
        vpsa.assetRepairStatus,
        vpa.assetNumber,
        vpa.assetName,
        group_concat(vrsrl.vulnerabilityRepairStatus) as vulnerabilityRepairStatusString,
        group_concat(vrsrl.vulnerabilityNumber) as vulnerabilityNumbers

        FROM vulnerability_project_asset vpa
        LEFT JOIN vulnerability_project_scan_asset vpsa on  vpa.assetId = vpsa.assetId and vpa.projectId = vpsa.projectId
        LEFT JOIN (SELECT vrsr.* FROM vulnerability_project_scan_record vrsr
            LEFT JOIN (select vulnerabilityNumber,vulnerabilityLevel from vulnerability_storehouse
        group by vulnerabilityNumber,vulnerabilityLevel) vs on vrsr.vulnerabilityNumber = vs.vulnerabilityNumber
        LEFT JOIN vulnerability_level vl on vs.vulnerabilityLevel = vl.code
        WHERE  vl.code != 5) vrsrl on vpsa.id = vrsrl.vpsaId and vpsa.projectId = vrsrl.projectId
        where vpa.projectId = #{projectId}
#
        <if test="assetName != null and assetName !=''">
            and vpa.assetName like concat('%',#{assetName},'%')
        </if>
        <if test="assetIp != null and assetIp !=''">
            and vpsa.assetIp = #{assetIp}
        </if>
        <if test="assetId != null and assetId !=''">
            and vpsa.assetId = #{assetId}
        </if>
        <if test="assetRepairStatusList != null and assetRepairStatusList.size() >0">
            and vpsa.assetRepairStatus in
            <foreach collection="assetRepairStatusList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        group by  vpa.id,
        vpa.assetIp,
        vpa.assetId,
        vpsa.assetRepairStatus,
        vpa.assetNumber,
        vpa.assetName

    </select>


    <select id="selectVulnerabilityData"
            resultType="com.digiwin.escloud.aiobasic.vulnerability.model.VulnerabilityProjectScanRecord">
        SELECT vpsr.projectId,vpsr.eid,vpsr.vulnerabilityName as originVulnerabilityName,
        vpsr.cvssV3BaseScore,vpsr.cvssV2TemporalScore,vpsr.cvssV2BaseScore,vpsr.cvssV3TemporalScore,
        vs.vulnerabilityLevel,
        vl.code as vulnerabilityLevelCode,
        vpsr.vulnerabilityNumber
        ,group_concat(vpa.assetId) as assetIds
        ,group_concat(vpa.assetIp) as assetIps
        ,group_concat(vpa.assetName) as assetNames
        ,group_concat(vpsr.serverPort) as serverPorts
        ,group_concat(vpsr.vulnerabilityRepairStatus) as vulnerabilityRepairStatusString
        FROM vulnerability_project_scan_record vpsr
        LEFT JOIN (select vulnerabilityLevel,vulnerabilityNumber from vulnerability_storehouse group by vulnerabilityLevel,vulnerabilityNumber) vs on vs.vulnerabilityNumber = vpsr.vulnerabilityNumber
        LEFT JOIN vulnerability_level vl on vs.vulnerabilityLevel = vl.code
        LEFT JOIN vulnerability_project_asset vpa on  vpa.assetIp = vpsr.assetIp and
        vpa.projectId = vpsr.projectId
        WHERE   vpsr.projectId = #{projectId} and vpsr.vulnerabilityNumber = #{vulnerabilityNumber}
        <if test="assetId != null and assetId !=''">
            AND vpa.assetId = #{assetId}
        </if>
        <if test="eid != null">
           AND vpsr.eid = #{eid}
        </if>
        GROUP BY vpsr.projectId,vpsr.eid,vpsr.vulnerabilityName ,
        vpsr.cvssV3BaseScore,vpsr.cvssV2TemporalScore,vpsr.cvssV2BaseScore,vpsr.cvssV3TemporalScore,
        vs.vulnerabilityLevel,
        vl.code ,
        vpsr.vulnerabilityNumber

    </select>

    <select id="getVulnerabilityProjectScanRecord"
            parameterType="com.digiwin.escloud.aiobasic.vulnerability.model.VulnerabilityRiskCount"
            resultType="com.digiwin.escloud.aiobasic.vulnerability.model.VulnerabilityProjectScanRecord">
        select distinct projectId, eid, vulnerabilityLevel
        from vulnerability_project_scan_record
        where projectId = #{projectId}
        <if test="eid != null">
            and eid = #{eid}
        </if>
    </select>

    <insert id="insertScans" parameterType="java.util.List">
        INSERT INTO vulnerability_project_scan_record
        (id, eid, projectId, assetIp, vulnerabilityId, vulnerabilityNumber, vulnerabilityLevel, vulnerabilityName,
        vulnerabilityDesc, serverPort,
        remark, dataJson, vpsaId, vulnerabilityRepairStatus, cvssV3BaseScore, cvssV2TemporalScore, cvssV3TemporalScore,
        cvssV2BaseScore) VALUES
        <foreach collection="projectScanRecord" item="item" separator=",">
            (
                #{item.id}, #{item.eid}, #{item.projectId}, #{item.assetIp}, #{item.vulnerabilityId}, #{item.vulnerabilityNumber},
                #{item.vulnerabilityLevel},
                #{item.vulnerabilityName}, #{item.vulnerabilityDesc}, #{item.serverPort}, #{item.remark}, #{item.dataJson}, #{item.vpsaId},
                #{item.vulnerabilityRepairStatus}, #{item.cvssV3BaseScore}, #{item.cvssV2TemporalScore}, #{item.cvssV3TemporalScore},
                #{item.cvssV2BaseScore}
            )
        </foreach>
    </insert>

    <select id="selectMinVulnerabilityLevelByProjectId" resultType="com.digiwin.escloud.aiobasic.vulnerability.model.VulnerabilityProjectScanRecord">
        SELECT assetIp,MIN(vs.vulnerabilityLevel) AS vulnerabilityLevelCode FROM vulnerability_project_scan_record vpsr
        LEFT JOIN (SELECT vulnerabilityNumber,vulnerabilityLevel FROM vulnerability_storehouse GROUP BY vulnerabilityNumber,vulnerabilityLevel)
            vs ON vpsr.vulnerabilityNumber = vs.vulnerabilityNumber
        WHERE projectId = #{projectId}
        GROUP BY assetIp
    </select>

    <update id="updateVulnerabilitySuggest" parameterType="com.digiwin.escloud.aiobasic.vulnerability.model.param.VulnerabilitySuggestParam">
        <foreach collection="assetList" item="asset">
            UPDATE vulnerability_project_scan_record
            SET suggest = #{asset.suggest}
            WHERE eid = #{eid} AND assetIp = #{asset.assetIp} AND projectId = #{projectId} AND vulnerabilityNumber = #{vulnerabilityNumber};
        </foreach>
    </update>
    <select id="selectSuggest" resultType="java.lang.String">
        SELECT suggest
        FROM vulnerability_project_scan_record
        WHERE assetIp = #{assetIp} and projectId = #{projectId} and vulnerabilityNumber = #{vulnerabilityNumber} and serverPort = #{serverPort}
    </select>
</mapper>
