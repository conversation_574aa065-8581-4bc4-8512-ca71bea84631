package com.digiwin.escloud.aiocmdb.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONPath;
import com.alibaba.fastjson.parser.Feature;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.jayway.jsonpath.Configuration;
import com.jayway.jsonpath.DocumentContext;
import com.jayway.jsonpath.JsonPath;
import com.jayway.jsonpath.Option;
import com.digiwin.escloud.aiocmdb.assetmaintenance.bigdata.BigDataUtil;
import com.digiwin.escloud.aiocmdb.cache.ModelCache;
import com.digiwin.escloud.aiocmdb.constant.Constant;
import com.digiwin.escloud.aiocmdb.assetmaintenance.bigdata.BigDataUtil;
import com.digiwin.escloud.aiocmdb.etl.dao.EtlMapper;
import com.digiwin.escloud.aiocmdb.etl.dto.EtlEngineSaveReqDTO;
import com.digiwin.escloud.aiocmdb.etl.model.EsHbase;
import com.digiwin.escloud.aiocmdb.etl.model.EtlModelField;
import com.digiwin.escloud.aiocmdb.etl.service.EtlService;
import com.digiwin.escloud.aiocmdb.model.dao.ModelMapper;
import com.digiwin.escloud.aiocmdb.model.model.ModelFieldGroup;
import com.digiwin.escloud.aioitms.model.bigdata.StarRocksEntity;
import com.digiwin.escloud.aiocmdb.field.model.Field;
import com.digiwin.escloud.aiocmdb.field.model.FieldType;
import com.digiwin.escloud.aiocmdb.fieldset.dao.FieldSetMapper;
import com.digiwin.escloud.aiocmdb.fieldset.model.FieldSet;
import com.digiwin.escloud.aiocmdb.model.dao.ModelMapper;
import com.digiwin.escloud.aiocmdb.model.model.Model;
import com.digiwin.escloud.aiocmdb.model.model.ModelFieldMapping;
import com.digiwin.escloud.aiocmdb.model.model.ModelGroupAutoSink;
import com.digiwin.escloud.aiocmdb.model.model.ModelSettingType;
import com.digiwin.escloud.common.constant.AioConstant;
import com.digiwin.escloud.common.response.BaseResponse;
import com.digiwin.escloud.common.util.LongUtil;
import com.digiwin.escloud.common.util.RequestUtil;
import com.digiwin.escloud.common.util.SerializeUtil;
import com.digiwin.escloud.etl.model.EtlEngine;
import com.digiwin.escloud.etl.model.SinkType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.client.RestTemplate;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.digiwin.escloud.aiocmdb.etl.service.StarRocksStoreService.DEFAULT_TTL_UNIT;
import static com.digiwin.escloud.common.constant.BigDataConstant.*;
import static com.digiwin.escloud.common.constant.MrConstant.MR_ROW_KEY;

@Service
@Slf4j
public class CommonUtils {

    @Autowired
    private RestTemplate restTemplate;
    @Value("${esc.integration.datauri}")
    private String dataUri;
    @Autowired
    private ModelCache modelCache;
    @Autowired
    private FieldSetMapper fieldSetMapper;
    @Autowired
    private ModelMapper modelMapper;
    @Autowired
    private EtlMapper etlMapper;
    @Autowired
    private EtlService etlService;
    @Autowired
    private BigDataUtil bigDataUtil;

    public void asyncRun(Runnable runnable) {
        ExecutorService executorService = Executors.newSingleThreadExecutor();
        try {
            executorService.execute(runnable);
        } catch (Exception ex) {
            log.error("asyncRun", ex);
        } finally {
            executorService.shutdown();
        }
    }

    /**
     * 新增资产 合并到模型结构中
     *
     * @param sourceJsonObject
     * @param targetJsonObject
     */
    public void dealCollectDataV2(JSONObject sourceJsonObject, JSONObject targetJsonObject) {
        for (String key : sourceJsonObject.keySet()) {
            Object o2 = sourceJsonObject.get(key);
            log.debug(key + "----" + o2);
            if (o2 == null) {
                continue;
            } else if (o2 instanceof String || o2 instanceof Long || o2 instanceof Integer || o2 instanceof BigDecimal) {//处理普通字段
                for (String groupKey : targetJsonObject.keySet()) {
                    Map<String, Object> groupMap = (Map) targetJsonObject.get(groupKey);
                    for (String targetKey : groupMap.keySet()) {
                        if (targetKey.equals(key)) {
                            groupMap.put(targetKey, o2);
                            break;
                        }
                    }
                }
            } else if (o2 instanceof JSONObject) {//处理字段集，非集合属性 todo huly:

            } else if (o2 instanceof JSONArray) {//处理字段集，集合属性 todo huly:
            }
        }
    }

    public void dealCollectData(JSONObject sourceJsonObject, JSONObject targetJsonObject) {
        if (ObjectUtils.isEmpty(sourceJsonObject) || ObjectUtils.isEmpty(targetJsonObject)) {
            return;
        }
        for (String targetKey : targetJsonObject.keySet()) {
            Object targetJson = targetJsonObject.get(targetKey);
            if (targetJson == null) {
                continue;
            }
            if (targetJson instanceof JSONObject) {
                dealCollectData(sourceJsonObject.getJSONObject(targetKey), (JSONObject) targetJson);
            } else if (targetJson instanceof JSONArray) {
                JSONArray sourceJsonArr = sourceJsonObject.getJSONArray(targetKey);
                if (CollectionUtils.isEmpty(sourceJsonArr)) {
                    continue;
                }
                JSONArray targetJsonArr = (JSONArray) targetJson;
                Object targetEmptyObject = targetJsonArr.get(0);
                if (targetJsonArr.size() == 1) {
                    targetJsonArr.clear();
                }
                for (int i = 0; i < sourceJsonArr.size(); i++) {
                    Object sourceObject = sourceJsonArr.get(i);
                    if (sourceObject instanceof JSONObject && targetEmptyObject instanceof JSONObject) {
                        dealCollectData((JSONObject) sourceObject, (JSONObject) targetEmptyObject);
                        //必须要构造一个新的对象，重新给key赋值，如果不构造新的，会把之前targetJsonArr的JSONObject重新赋值
                        //因为JSONObject的内存地址指向的都是同一个地址
                        JSONObject newJsonObject = new JSONObject();
                        for (String sourceKey : ((JSONObject) targetEmptyObject).keySet()) {
                            newJsonObject.put(sourceKey, ((JSONObject) sourceObject).get(sourceKey));
                        }
                        targetJsonArr.add(newJsonObject);
                    } else {
                        targetJsonArr.add(sourceObject);
                    }
                }
            } else {
                targetJsonObject.put(targetKey, sourceJsonObject.get(targetKey) == null ? (targetJsonObject.get(targetKey) == null ? "" : targetJsonObject.get(targetKey)) : sourceJsonObject.get(targetKey));
            }
        }
    }

    /**
     * 集合字段集
     *
     * @param sourceJsonObject
     * @param targetJsonObject
     */
    public void dealCollectDataNew(JSONObject sourceJsonObject, JSONObject targetJsonObject) {
        if (ObjectUtils.isEmpty(sourceJsonObject) || ObjectUtils.isEmpty(targetJsonObject)) {
            return;
        }
        for (String targetKey : targetJsonObject.keySet()) {
            Object targetJson = targetJsonObject.get(targetKey);
            if (targetJson == null) {
                continue;
            }
            if (targetJson instanceof JSONObject) {
                dealCollectDataNew(sourceJsonObject.getJSONObject(targetKey), (JSONObject) targetJson);
            } else if (targetJson instanceof JSONArray) {
                JSONArray sourceJsonArr = sourceJsonObject.getJSONArray(targetKey);
                if (CollectionUtils.isEmpty(sourceJsonArr)) {
                    JSONArray targetJsonArr = (JSONArray) targetJson;
                    if (targetJsonArr.size() >= 1) {
                        targetJsonArr.clear();
                    }
                    continue;
                }
                JSONArray targetJsonArr = (JSONArray) targetJson;
                Object targetEmptyObject = targetJsonArr.get(0);
                if (targetJsonArr.size() >= 1 && sourceJsonArr.size() >= 1) {
                    targetJsonArr.clear();
                }
                for (int i = 0; i < sourceJsonArr.size(); i++) {
                    Object sourceObject = sourceJsonArr.get(i);
                    if (sourceObject instanceof JSONObject && targetEmptyObject instanceof JSONObject) {
                        dealCollectDataNew((JSONObject) sourceObject, (JSONObject) targetEmptyObject);
                        //必须要构造一个新的对象，重新给key赋值，如果不构造新的，会把之前targetJsonArr的JSONObject重新赋值
                        //因为JSONObject的内存地址指向的都是同一个地址
                        JSONObject newJsonObject = new JSONObject();
                        for (String sourceKey : ((JSONObject) targetEmptyObject).keySet()) {
                            newJsonObject.put(sourceKey, ((JSONObject) sourceObject).get(sourceKey));
                        }
                        targetJsonArr.add(newJsonObject);
                    } else {
                        targetJsonArr.add(sourceObject);
                    }
                }
            } else {
                if (sourceJsonObject.get(targetKey) != null) {
                    targetJsonObject.put(targetKey, sourceJsonObject.get(targetKey) == null ? (targetJsonObject.get(targetKey) == null ? "" : targetJsonObject.get(targetKey)) : sourceJsonObject.get(targetKey));
                }
            }
        }
    }

    public List<Map<String, Object>> getRelationList(Map<String, Object> oldMap, String relateDeviceId, String relateDeviceName, String sourceModelCode, String targetModelCode) {
        List<Map<String, Object>> relationList = new ArrayList<>();
        if (oldMap.get("relation") == null) {
            return relationList;
        }
        relationList = JSONObject.parseObject(oldMap.get("relation").toString().replaceAll("\\\\", "\\\\\\\\"), List.class, Feature.OrderedField);
        if (CollectionUtils.isEmpty(relationList)) {
            Map<String, Object> map = new HashMap<>();

            List relationItems = new ArrayList();
            JSONObject addJO = new JSONObject();
            addJO.put("deviceId", relateDeviceId);
            addJO.put("deviceName", relateDeviceName);
            relationItems.add(addJO);

            map.put("relationItems", relationItems);
            map.put("relationCode", sourceModelCode + "_" + targetModelCode);
            map.put("relationModel", targetModelCode);
            relationList.add(map);
        } else {
            //循环关联的模型
            boolean isExist = false;
            for (int i = 0; i < relationList.size(); i++) {
                LinkedHashMap<String, Object> relationMap = JSONObject.parseObject(JSONObject.toJSONString(relationList.get(i)), LinkedHashMap.class);
                for (String key : relationMap.keySet()) {
                    if (key.equals("relationModel") && "Software_Hardware".equals(relationMap.get(key).toString())) {
                        log.info("找到关联模型了");
                        List relationItems = JSONObject.parseObject(JSONObject.toJSONString(relationMap.get("relationItems")), List.class);
                        if (CollectionUtils.isEmpty(relationItems)) {
                            log.info("关联模型里关联设备全部删除了，那就新增关联设备");
                            isExist = true;

                            JSONObject addJO = new JSONObject();
                            addJO.put("deviceId", relateDeviceId);
                            addJO.put("deviceName", relateDeviceName);
                            relationItems.add(addJO);
                            relationMap.put("relationItems", relationItems);
                            relationList.set(i, relationMap);
                        } else {
                            log.info("关联模型里关联设备还存在，那就看看该设备是否已经被关联");
                            isExist = false;
                            for (int j = 0; j < relationItems.size(); j++) {
                                LinkedHashMap<String, Object> relationItemMap = JSONObject.parseObject(JSONObject.toJSONString(relationItems.get(j)), LinkedHashMap.class);
                                for (String relationItemKey : relationItemMap.keySet()) {
                                    if (relationItemKey.equals("deviceId") && relateDeviceId.equals(relationItemMap.get(relationItemKey).toString())) {
                                        log.info("关联模型里已经关联该设备啦");
                                        isExist = true;
                                        break;
                                    }
                                }
                            }
                            if (!isExist) {
                                log.info("关联模型里没有关联该设备，开始关联啦");
                                JSONObject addJO = new JSONObject();
                                addJO.put("deviceId", relateDeviceId);
                                addJO.put("deviceName", relateDeviceName);
                                relationItems.add(addJO);
                                relationMap.put("relationItems", relationItems);
                                relationList.set(i, relationMap);
                                isExist = true;
                            }
                        }
                    }
                }
            }
            if (!isExist) {
                Map<String, Object> map = new HashMap<>();

                List relationItems = new ArrayList();
                JSONObject addJO = new JSONObject();
                addJO.put("deviceId", relateDeviceId);
                addJO.put("deviceName", relateDeviceName);
                relationItems.add(addJO);

                map.put("relationItems", relationItems);
                map.put("relationCode", sourceModelCode + "_" + targetModelCode);
                map.put("relationModel", targetModelCode);
                relationList.add(map);
            }
        }

        return relationList;
    }

    public JSONObject addKeyToTargetJson(List<JSONObject> keys, JSONObject targetJsonObject) {
        String key = keys.stream().sorted((key1, key2) -> key1.getIntValue("sort") - key2.getIntValue("sort"))
                .map(o -> JSONPath.eval(targetJsonObject, "$." + o.getString("key")).toString())
                .reduce((key1, key2) -> key1 + key2).get();
        targetJsonObject.put("rowKey", key);
        return targetJsonObject;
    }

    public Object getMrDetail(String modelCode, String id, Long eid) {
        if (LongUtil.isEmpty(eid)) {
            eid = RequestUtil.getHeaderEid();
        }
        return getMrDetailByKey(modelCode, eid + MR_ROW_KEY + id);
    }

    public Object getMrDetailByKey(String modelCode, String rowKey) {
        HashMap hashMap = restTemplate.getForObject(dataUri + "/hbase/getByRowKey?tableName=" +
                modelCode + "&rowKey=" + rowKey, HashMap.class);
        if (!ObjectUtils.isEmpty(hashMap)) {
            return hashMap.get("model");
        } else {
            return null;
        }
    }

    private <T> Optional<T> executePost(String url, Object data, Class<T> resultClass) {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<Object> request = new HttpEntity<>(data, headers);
        try {
            return Optional.ofNullable(restTemplate.postForObject(url, request, resultClass));
        } catch (Exception ex) {
            log.error(String.format("executePost url:%s, data:%s error:", url, SerializeUtil.JsonSerialize(data)), ex);
            return Optional.empty();
        }
    }
//
//    public List<Object> getMrDetailByMap(String modelCode, Map<String, Object> conditionMap) {
//        return getMrDetailByMapCore("/data/model/query", modelCode, conditionMap);
//    }
//
//    private List<Object> getMrDetailByMapCore(String partUrl, String modelCode, Map<String, Object> map) {
//        List<Map<String, Object>> hashMapList = executePost(
//                String.join("", dataUri, "/hbase", partUrl, "?modelCode=", modelCode),
//                map, List.class);
//        if (CollectionUtils.isEmpty(hashMapList)) {
//            return null;
//        }
//        return hashMapList.stream().filter(x -> !ObjectUtils.isEmpty(x.get("model")))
//                .map(x -> x.get("model"))
//                .collect(Collectors.toList());
//    }
//
//    private <T> T executePost(String url, Object data, Class<T> resultClass) {
//        HttpHeaders headers = new HttpHeaders();
//        headers.setContentType(MediaType.APPLICATION_JSON);
//        HttpEntity<Object> request = new HttpEntity<>(data, headers);
//        return restTemplate.postForObject(url, request, resultClass);
//    }
//
//    public List<Object> getMrDetailByGroupMap(String modelCode, Map<String, Object> groupConditionMap) {
//        return getMrDetailByMapCore("/data/model/query/v2", modelCode, groupConditionMap);
//    }

    public HashMap saveMrDetail(String modelCode, String id, Object targetValue) {
        HashMap<String, Object> map = new HashMap<>();
        map.put("tableName", modelCode);
        map.put("rowKey", RequestUtil.getHeaderEid() + MR_ROW_KEY + id);
        map.put("cf", "info");
        map.put("cn", "model");
        HashMap<String, Object> dataMap = new HashMap<>();
        dataMap.put("model", targetValue);
        map.put("data", dataMap);
        return restTemplate.postForObject(dataUri + "/hbase/save", map, HashMap.class);
    }

    public HashMap saveMrDetail(String modelCode, Long eid, String id, Object targetValue) {
        HashMap<String, Object> map = new HashMap<>();
        map.put("tableName", modelCode);
        map.put("rowKey", eid + MR_ROW_KEY + id);
        map.put("cf", "info");
        map.put("cn", "model");
        HashMap<String, Object> dataMap = new HashMap<>();
        dataMap.put("model", targetValue);
        map.put("data", dataMap);
        return restTemplate.postForObject(dataUri + "/hbase/save", map, HashMap.class);
    }

    /**
     * 更新HBase中存储的JSON数据的指定字段
     *
     * @param modelCode 模型代码
     * @param id 数据ID
     * @param fieldPath 字段路径，支持嵌套路径，如 "updateTime" 或 "field.DataContent.networkSecurityProductName"
     * @param newValue 新的字段值
     * @return 更新结果
     */
    public HashMap<String, Object> updateMrDetailField(String modelCode, String id, String fieldPath, Object newValue) {
        return updateMrDetailFieldByKey(modelCode, RequestUtil.getHeaderEid() + MR_ROW_KEY + id, fieldPath, newValue);
    }

    /**
     * 根据rowKey更新HBase中存储的JSON数据的指定字段
     *
     * @param modelCode 模型代码
     * @param rowKey 行键
     * @param fieldPath 字段路径，支持嵌套路径，如 "updateTime" 或 "field.DataContent.networkSecurityProductName"
     * @param newValue 新的字段值
     * @return 更新结果
     */
    public HashMap<String, Object> updateMrDetailFieldByKey(String modelCode, String rowKey, String fieldPath, Object newValue) {
        try {
            // 1. 获取现有数据
            Object existingData = getMrDetailByKey(modelCode, rowKey);
            if (existingData == null) {
                log.warn("No data found for modelCode: {}, rowKey: {}", modelCode, rowKey);
                return null;
            }

            // 2. 解析现有数据为JSON
            JSONObject jsonData;
            if (existingData instanceof String) {
                jsonData = JSONObject.parseObject((String) existingData);
            } else {
                jsonData = JSONObject.parseObject(JSONObject.toJSONString(existingData));
            }

            // 3. 更新指定字段
            updateJsonField(jsonData, fieldPath, newValue);

            // 4. 保存更新后的数据
            String updatedJsonString = JSONObject.toJSONString(jsonData);
            return saveMrDetailByKey(modelCode, rowKey, updatedJsonString);

        } catch (Exception e) {
            log.error("Error updating field {} for modelCode: {}, rowKey: {}", fieldPath, modelCode, rowKey, e);
            return null;
        }
    }

    /**
     * 批量更新多个字段
     *
     * @param modelCode 模型代码
     * @param id 数据ID
     * @param fieldUpdates 字段更新映射，key为字段路径，value为新值
     * @return 更新结果
     */
    public HashMap<String, Object> updateMrDetailFields(String modelCode,Long eid, String id, Map<String, Object> fieldUpdates) {
        return updateMrDetailFieldsByKey(modelCode, eid + MR_ROW_KEY + id, fieldUpdates);
    }

    /**
     * 根据rowKey批量更新多个字段
     *
     * @param modelCode 模型代码
     * @param rowKey 行键
     * @param fieldUpdates 字段更新映射，key为字段路径，value为新值
     * @return 更新结果
     */
    public HashMap<String, Object> updateMrDetailFieldsByKey(String modelCode, String rowKey, Map<String, Object> fieldUpdates) {
        try {
            // 1. 获取现有数据
            Object existingData = getMrDetailByKey(modelCode, rowKey);
            if (existingData == null) {
                log.warn("No data found for modelCode: {}, rowKey: {}", modelCode, rowKey);
                return null;
            }

            // 2. 解析现有数据为JSON
            JSONObject jsonData;
            if (existingData instanceof String) {
                jsonData = JSONObject.parseObject((String) existingData);
            } else {
                jsonData = JSONObject.parseObject(JSONObject.toJSONString(existingData));
            }

            // 3. 批量更新字段
            for (Map.Entry<String, Object> entry : fieldUpdates.entrySet()) {
                updateJsonField(jsonData, entry.getKey(), entry.getValue());
            }

            // 4. 保存更新后的数据
            String updatedJsonString = JSONObject.toJSONString(jsonData);
            return saveMrDetailByKey(modelCode, rowKey, updatedJsonString);

        } catch (Exception e) {
            log.error("Error updating fields for modelCode: {}, rowKey: {}", modelCode, rowKey, e);
            return null;
        }
    }

    /**
     * 根据rowKey保存数据到HBase
     *
     * @param modelCode 模型代码
     * @param rowKey 行键
     * @param targetValue 目标值
     * @return 保存结果
     */
    public HashMap<String, Object> saveMrDetailByKey(String modelCode, String rowKey, Object targetValue) {
        HashMap<String, Object> map = new HashMap<>();
        map.put("tableName", modelCode);
        map.put("rowKey", rowKey);
        map.put("cf", "info");
        map.put("cn", "model");
        HashMap<String, Object> dataMap = new HashMap<>();
        dataMap.put("model", targetValue);
        map.put("data", dataMap);
        return restTemplate.postForObject(dataUri + "/hbase/save", map, HashMap.class);
    }

    /**
     * 更新JSON对象中的指定字段
     * 使用JsonPath支持嵌套路径，如 "updateTime" 或 "field.DataContent.networkSecurityProductName"
     *
     * @param jsonObject 要更新的JSON对象
     * @param fieldPath 字段路径
     * @param newValue 新值
     */
    private void updateJsonField(JSONObject jsonObject, String fieldPath, Object newValue) {
        if (StringUtils.isEmpty(fieldPath)) {
            return;
        }

        try {
            // 转换为JsonPath格式（添加$前缀）
            String jsonPath = fieldPath.startsWith("$") ? fieldPath : "$." + fieldPath;

            // 创建配置，支持异常抑制
            Configuration config = Configuration.defaultConfiguration()
                    .addOptions(Option.SUPPRESS_EXCEPTIONS);

            // 解析JSON文档
            DocumentContext documentContext = com.jayway.jsonpath.JsonPath.using(config).parse(jsonObject);

            // 尝试读取路径以检查它是否存在
            Object existingValue = documentContext.read(jsonPath);

            if (existingValue == null) {
                // 路径不存在，需要创建路径
                createJsonPath(documentContext, jsonPath, newValue);
            } else {
                // 路径存在，直接设置值
                documentContext.set(jsonPath, newValue);
            }

            // 更新原始JSONObject
            String updatedJsonString = documentContext.jsonString();
            JSONObject updatedJson = JSONObject.parseObject(updatedJsonString);

            // 清空原对象并复制更新后的内容
            jsonObject.clear();
            jsonObject.putAll(updatedJson);

            log.debug("Updated field {} to value: {} using JsonPath", fieldPath, newValue);

        } catch (Exception e) {
            log.error("Failed to update field {} with JsonPath, fallback to manual method", fieldPath, e);
            // 如果JsonPath失败，回退到原来的手动方法
            updateJsonFieldManually(jsonObject, fieldPath, newValue);
        }
    }

    /**
     * 创建JsonPath路径
     *
     * @param documentContext JSON文档上下文
     * @param jsonPath JSON路径
     * @param value 要设置的值
     */
    private void createJsonPath(DocumentContext documentContext, String jsonPath, Object value) {
        try {
            // 解析路径
            String[] pathParts = jsonPath.replace("$.", "").split("\\.");

            // 逐级检查并创建路径
            StringBuilder currentPath = new StringBuilder("$");
            for (int i = 0; i < pathParts.length - 1; i++) {
                currentPath.append(".").append(pathParts[i]);

                Object pathValue = documentContext.read(currentPath.toString());
                if (pathValue == null) {
                    // 创建父路径
                    String parentPath = currentPath.substring(0, currentPath.lastIndexOf("."));
                    if (parentPath.equals("$")) {
                        parentPath = "$";
                    }
                    documentContext.put(parentPath, pathParts[i], new JSONObject());
                }
            }

            // 设置最终值
            String parentPath = jsonPath.substring(0, jsonPath.lastIndexOf("."));
            String finalKey = pathParts[pathParts.length - 1];
            documentContext.put(parentPath, finalKey, value);

        } catch (Exception e) {
            log.warn("Failed to create JsonPath: {}, error: {}", jsonPath, e.getMessage());
            // 如果创建路径失败，尝试直接设置
            try {
                documentContext.set(jsonPath, value);
            } catch (Exception ex) {
                log.error("Failed to set value directly for path: {}", jsonPath, ex);
            }
        }
    }

    /**
     * 手动更新JSON字段的回退方法（原来的实现）
     *
     * @param jsonObject 要更新的JSON对象
     * @param fieldPath 字段路径
     * @param newValue 新值
     */
    private void updateJsonFieldManually(JSONObject jsonObject, String fieldPath, Object newValue) {
        if (StringUtils.isEmpty(fieldPath)) {
            return;
        }

        String[] pathParts = fieldPath.split("\\.");
        JSONObject currentObject = jsonObject;

        // 导航到目标字段的父对象
        for (int i = 0; i < pathParts.length - 1; i++) {
            String part = pathParts[i];
            Object nextObject = currentObject.get(part);

            if (nextObject == null) {
                // 如果路径不存在，创建新的JSONObject
                JSONObject newObject = new JSONObject();
                currentObject.put(part, newObject);
                currentObject = newObject;
            } else if (nextObject instanceof JSONObject) {
                currentObject = (JSONObject) nextObject;
            } else if (nextObject instanceof String) {
                // 如果是字符串，尝试解析为JSON
                try {
                    JSONObject parsedObject = JSONObject.parseObject((String) nextObject);
                    currentObject.put(part, parsedObject);
                    currentObject = parsedObject;
                } catch (Exception e) {
                    // 如果解析失败，创建新的JSONObject
                    JSONObject newObject = new JSONObject();
                    currentObject.put(part, newObject);
                    currentObject = newObject;
                }
            } else {
                // 其他类型，无法继续导航
                log.warn("Cannot navigate through field path: {}, stopped at: {}", fieldPath, part);
                return;
            }
        }

        // 设置最终字段的值
        String finalField = pathParts[pathParts.length - 1];
        currentObject.put(finalField, newValue);

        log.debug("Updated field {} to value: {} using manual method", fieldPath, newValue);
    }


    public boolean removeMrDetailByIdList(String modelCode, List<String> idList,Long eid) {
        if (CollectionUtils.isEmpty(idList)) {
            return false;
        }
        Long finalEid = LongUtil.isNotEmpty(eid) ? eid : RequestUtil.getHeaderEid();
        List<String> rowKeyList = idList.stream().map(x -> finalEid + MR_ROW_KEY + x).collect(Collectors.toList());
        String url = dataUri + "/hbase/deleteByRowKeyList?tableName=" + modelCode;
        try {
            restTemplate.postForObject(url, rowKeyList, Void.class);
            return true;
        } catch (Exception ex) {
            log.error("removeMrDetailByIdList modelCode:" + modelCode +
                    " idList:" + String.join(",", idList) +
                    " error:", ex);
            return false;
        }
    }

    public void saveEtlEngine(List<String> modelCodes) {
        try {
            HashMap<String, Object> map = modelCache.getModelJson(modelCodes);
            if (CollectionUtils.isEmpty(map)) {
                return;
            }
            List<Map<String, String>> appCodeMaps = modelMapper.selectAppCodeByCodeList(modelCodes);
            if (CollectionUtils.isEmpty(appCodeMaps)) {
                return;
            }
            List<EtlModelField> etlModelFields = Arrays.stream(EsHbase.values())
                    .map(value -> {
                        String valuePath = value.getName();
                        if (!valuePath.equals(MODEL) && !valuePath.equals(ROW_KEY)) {
                            valuePath = Constant.BASE_GROUP_CODE + "." + valuePath;
                        }
                        return new EtlModelField(value.getName(), valuePath, FieldType.VARCHAR.toString());
                    }).collect(Collectors.toList());
            List<EtlEngine> etlEngines = new ArrayList<>();
            for (String modelCode : modelCodes) {
                Object modelJson = map.get(modelCode);
                if (ObjectUtils.isEmpty(modelJson)) {
                    continue;
                }
                Map<String, String> appCodeMap = appCodeMaps.stream()
                        .filter(o -> modelCode.equals(o.get("modelCode")))
                        .findAny()
                        .orElseGet(() -> null);
                if (CollectionUtils.isEmpty(appCodeMap)) {
                    continue;
                }
                String finalAppCode = appCodeMap.get("appCode");
                HashMap<String, Object> etlMap = new HashMap<>();
                etlMap.put("appCode", finalAppCode);
                etlMap.put("modelCode", modelCode);
                etlMap.put("schemaName", DEFAULT_SCHEMA);
                etlMap.put("sinkType", SinkType.ESHBASE.getName());
                EtlEngine mainEtlEngine = etlMapper.getMainEtlEngine(etlMap);
                if (ObjectUtils.isEmpty(mainEtlEngine)) {
                    EtlEngine etlEngine = new EtlEngine(finalAppCode, modelCode, DEFAULT_SCHEMA, JSONObject.toJSONString(modelJson),
                            SinkType.ESHBASE.getName(), modelCode, ROW_KEY, 1);
                    etlEngine.setSinkFieldsJson(JSON.toJSONString(etlModelFields));
                    etlEngines.add(etlEngine);
                    etlMapper.saveEtlEngine(etlEngines);
                } else {
                    etlMapper.updateEtlJson(JSONObject.toJSONString(modelJson, SerializerFeature.WriteMapNullValue), modelCode);
                }
            }
        } catch (Exception e) {
            log.error("saveEtlEngine", e);
        }
    }

    public void saveEsHbaseEtlEngine(String appCode, List<String> modelCodes) {
        try {
            HashMap<String, Object> map = modelCache.getModelJson(modelCodes);
            if (ObjectUtils.isEmpty(map) || map.size() <= 0) {
                return;
            }
            appCode = StringUtils.isEmpty(appCode) ? AioConstant.DEFAULT_APP : appCode;
            String finalAppCode = appCode;
            List<EtlModelField> etlModelFields = Arrays.stream(EsHbase.values())
                    .map(value -> {
                        String valuePath = value.getName();
                        if (!valuePath.equals(MODEL) && !valuePath.equals(ROW_KEY)) {
                            valuePath = Constant.BASE_GROUP_CODE + "." + valuePath;
                        }
                        return new EtlModelField(value.getName(), valuePath, FieldType.VARCHAR.toString());
                    }).collect(Collectors.toList());
            List<EtlEngine> etlEngines = map.entrySet().stream()
                    .map(o -> {
                        EtlEngine etlEngine = new EtlEngine(finalAppCode, o.getKey(), DEFAULT_SCHEMA, JSONObject.toJSONString(o.getValue()),
                                SinkType.ESHBASE.getName(), o.getKey(), ROW_KEY, 1);
                        etlEngine.setSinkFieldsJson(JSON.toJSONString(etlModelFields));
                        return etlEngine;
                    })
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(etlEngines)) {
                return;
            }
            etlMapper.saveEtlEngine(etlEngines);
        } catch (Exception e) {
            log.error("saveEtlEngine", e);
        }
    }

    public void autoCreateTable(List<String> modelCodes) {
        try {
            HashMap<String, Object> map = modelCache.getModelJson(modelCodes);
            if (CollectionUtils.isEmpty(map)) {
                return;
            }
            List<Map<String, String>> appCodeMaps = modelMapper.selectAppCodeByCodeList(modelCodes);
            if (CollectionUtils.isEmpty(appCodeMaps)) {
                return;
            }
            Map<String, String> appMap = appCodeMaps.stream().collect(Collectors.toMap(o -> o.get("modelCode").toString(),
                    o -> o.get("appCode")
            ));
            List<Map<String, String>> groupCodeMaps = modelMapper.getModelGroupCodeByModelCode(modelCodes);
            if (CollectionUtils.isEmpty(groupCodeMaps)) {
                return;
            }
            Map<String, String> groupCodeMap = groupCodeMaps.stream().collect(Collectors.toMap(o -> o.get("modelCode").toString(),
                    o -> o.get("modelGroupCode")
            ));
            List<String> modelGroupCodeList = groupCodeMaps.stream().map(o -> o.get("modelGroupCode")).distinct().collect(Collectors.toList());
            if (CollectionUtils.isEmpty(modelGroupCodeList)) {
                return;
            }
            List<ModelGroupAutoSink> autoSinkList = modelMapper.getModelGroupAutoSinkByGroupCode(modelGroupCodeList);
            if (CollectionUtils.isEmpty(autoSinkList)) {
                return;
            }
            for (String modelCode : modelCodes) {
                Object modelJson = map.get(modelCode);
                if (ObjectUtils.isEmpty(modelJson)) {
                    continue;
                }
                String finalAppCode = appMap.get(modelCode);
                String modelGroupCode = groupCodeMap.get(modelCode);

                List<ModelGroupAutoSink> modelGroupAutoSinkList = autoSinkList.stream().filter(o -> modelGroupCode.equals(o.getModelGroupCode()))
                        .collect(Collectors.toList());
                if (CollectionUtils.isEmpty(modelGroupAutoSinkList)) {
                    continue;
                }
                autoCreateTableCore(finalAppCode, modelCode, modelGroupAutoSinkList);
            }
        } catch (Exception e) {
            log.error("autoCreateTable error", e);
        }
    }

    private void autoCreateTableCore(String appCode, String modelCode, List<ModelGroupAutoSink> modelGroupAutoSinkList) {
        for (ModelGroupAutoSink modelGroupAutoSink : modelGroupAutoSinkList) {
            if (!modelGroupAutoSink.isCanAutoSink()) {
                continue;
            }
            if (SinkType.ESHBASE.getName().equals(modelGroupAutoSink.getSinkType())) {
                //创建模型之后第一步已经创建了hbase etl，此处只需要启用即可
                HashMap<String, Object> etlMap = new HashMap<>();
                etlMap.put("appCode", appCode);
                etlMap.put("collectCode", modelCode);
                etlMap.put("schemaName", DEFAULT_SCHEMA);
                etlMap.put("sinkType", SinkType.ESHBASE.getName());
                etlMap.put("sinkName", modelCode);
                etlMap.put("sinkEnable", true);
                etlMapper.updateEtlStatusBySink(etlMap);
            } else if (SinkType.STARROCKS.getName().equals(modelGroupAutoSink.getSinkType())) {
                EtlEngineSaveReqDTO etlEngineSaveReqDTO = buildEtlEngineSaveReqDTO(appCode, modelCode, modelGroupAutoSink);
                BaseResponse<Long> baseResponse = etlService.saveEtl(etlEngineSaveReqDTO);
                if (!baseResponse.checkIsSuccess()) {
                    continue;
                }
                Long etlId = baseResponse.getData();
                if (LongUtil.isEmpty(etlId)) {
                    continue;
                }
                BaseResponse sink = etlService.createSink(etlId);
            } else {
                log.warn("not support sinkType:{}", modelGroupAutoSink.getSinkType());
            }
        }

    }

    private EtlEngineSaveReqDTO buildEtlEngineSaveReqDTO(String appCode, String modelCode, ModelGroupAutoSink modelGroupAutoSink) {
        EtlEngineSaveReqDTO etlEngineSaveReqDTO = new EtlEngineSaveReqDTO();
        etlEngineSaveReqDTO.setAppCode(appCode);
        etlEngineSaveReqDTO.setCollectCode(modelCode);
        etlEngineSaveReqDTO.setSchemaName(DEFAULT_SCHEMA);
        etlEngineSaveReqDTO.setSinkType(modelGroupAutoSink.getSinkType());
        etlEngineSaveReqDTO.setSinkName(modelCode);
        etlEngineSaveReqDTO.setSinkPk(modelGroupAutoSink.getSinkPk());
        List<ModelFieldMapping> mappingList = modelMapper.getFieldsByFieldGroup(modelCode, null);
        List<EtlModelField> etlModelFields = new ArrayList<>();
        for (ModelFieldMapping mapping : mappingList) {
            String modelSettingType = mapping.getModelSettingType();
            String targetCode = mapping.getTargetCode();
            String modelFieldGroupCode = mapping.getModelFieldGroupCode();
            if (ModelSettingType.field.name().equals(modelSettingType)) {
                Field field = mapping.getField();
                if (ObjectUtils.isEmpty(field)) {
                    continue;
                }
                String fieldCode = field.getFieldCode();
                String fieldType = field.getFieldType();
                String valuePath = modelFieldGroupCode + "." + fieldCode;
                EtlModelField etlModelField = new EtlModelField(fieldCode, valuePath, fieldType);
                etlModelFields.add(etlModelField);
            } else {
                FieldSet fieldSet = mapping.getFieldSet();
                if (ObjectUtils.isEmpty(fieldSet)) {
                    continue;
                }
                String fieldSetCode = fieldSet.getFieldSetCode();
                for (Field field : fieldSet.getFieldList()) {
                    String fieldCode = field.getFieldCode();
                    String fieldType = field.getFieldType();
                    String valuePath = modelFieldGroupCode + "." + fieldSetCode + "." + fieldCode;
                    EtlModelField etlModelField = new EtlModelField(fieldCode, valuePath, fieldType);
                    etlModelFields.add(etlModelField);
                }
            }
        }
        etlEngineSaveReqDTO.setSinkFieldsJson(JSON.toJSONString(etlModelFields));
        etlEngineSaveReqDTO.setSinkTableTtl("");
        etlEngineSaveReqDTO.setShowInDataLog(false);
        etlEngineSaveReqDTO.setSinkTableTtlField("");
        etlEngineSaveReqDTO.setSinkTableTtlUnit(DEFAULT_TTL_UNIT);
        etlEngineSaveReqDTO.setSinkNameCreatedSelf(true);
        etlEngineSaveReqDTO.setSinkSchemaCreatedSelf(true);
        etlEngineSaveReqDTO.setSchemaNameDynamic("[]");
        etlEngineSaveReqDTO.setSinkNameDynamic("[]");
        return etlEngineSaveReqDTO;
    }

    public void autoDeleteTable(List<String> modelCodes) {
        try {
            List<Map<String, String>> appCodeMaps = modelMapper.selectAppCodeByCodeList(modelCodes);
            if (CollectionUtils.isEmpty(appCodeMaps)) {
                return;
            }
            Map<String, String> appMap = appCodeMaps.stream().collect(Collectors.toMap(o -> o.get("modelCode").toString(),
                    o -> o.get("appCode")
            ));
            List<Map<String, String>> groupCodeMaps = modelMapper.getModelGroupCodeByModelCode(modelCodes);
            if (CollectionUtils.isEmpty(groupCodeMaps)) {
                return;
            }
            Map<String, String> groupCodeMap = groupCodeMaps.stream().collect(Collectors.toMap(o -> o.get("modelCode").toString(),
                    o -> o.get("modelGroupCode")
            ));
            List<String> modelGroupCodeList = groupCodeMaps.stream().map(o -> o.get("modelGroupCode")).distinct().collect(Collectors.toList());
            if (CollectionUtils.isEmpty(modelGroupCodeList)) {
                return;
            }
            List<ModelGroupAutoSink> autoSinkList = modelMapper.getModelGroupAutoSinkByGroupCode(modelGroupCodeList);
            if (CollectionUtils.isEmpty(autoSinkList)) {
                return;
            }
            for (String modelCode : modelCodes) {
                String finalAppCode = appMap.get(modelCode);
                String modelGroupCode = groupCodeMap.get(modelCode);
                List<ModelGroupAutoSink> modelGroupAutoSinkList = autoSinkList.stream().filter(o -> modelGroupCode.equals(o.getModelGroupCode()))
                        .collect(Collectors.toList());
                if (CollectionUtils.isEmpty(modelGroupAutoSinkList)) {
                    continue;
                }
                autoDeleteTableCore(finalAppCode, modelCode, modelGroupAutoSinkList);
            }
        } catch (Exception e) {
            log.error("autoDeleteTable error", e);
        }
    }

    private void autoDeleteTableCore(String appCode, String modelCode, List<ModelGroupAutoSink> modelGroupAutoSinkList) {
        for (ModelGroupAutoSink modelGroupAutoSink : modelGroupAutoSinkList) {
            if (!modelGroupAutoSink.isCanAutoSink()) {
                continue;
            }
            if (SinkType.ESHBASE.getName().equals(modelGroupAutoSink.getSinkType())) {
                bigDataUtil.dropHbaseTable(modelCode);
            } else if (SinkType.STARROCKS.getName().equals(modelGroupAutoSink.getSinkType())) {
                HashMap<String, Object> map = new HashMap<>();
                map.put("appCode", appCode);
                map.put("modelCode", modelCode);
                map.put("sinkName", modelCode);
                map.put("schemaName", DEFAULT_SCHEMA);
                map.put("sinkType", modelGroupAutoSink.getSinkType());
                EtlEngine mainEtlEngine = etlMapper.getMainEtlEngine(map);
                if (ObjectUtils.isEmpty(mainEtlEngine)) {
                    continue;
                }
                etlService.deleteSink(mainEtlEngine.getId(), false);
            } else {
                log.warn("not support sinkType:{}", modelGroupAutoSink.getSinkType());
            }
        }
    }

    public long asyncClearModelCache(String modelCode) {
        long effectCount = modelCache.clearCurrentModel(modelCode);
        Runnable runnable = () -> {
            saveEtlEngine(Stream.of(modelCode).collect(Collectors.toList()));
        };
        asyncRun(runnable);
        return effectCount;
    }

    public long asyncBatchClearModelCache(List<String> modelCodes) {
        long effectCount = modelCache.batchClearModelCache(modelCodes);
        Runnable runnable = () -> {
            saveEtlEngine(modelCodes);
        };
        asyncRun(runnable);
        return effectCount;
    }

    public void asyncClearModelDetailCache(String modelCode) {
        modelCache.clearModelDetail(modelCode);
        Runnable runnable = () -> {
            saveEtlEngine(Stream.of(modelCode).collect(Collectors.toList()));
        };
        asyncRun(runnable);
    }

    public String getModelByFieldSet(long id) {
        List<Model> models = fieldSetMapper.getFieldSetExistModel(id);
        return models.stream().map(o -> o.getModelName()).collect(Collectors.joining(","));
    }

    public List<EtlModelField> buildEtlModelFields(String sinkName, String sinkFieldsType) {
        return Arrays.stream(sinkFieldsType.split(";"))
                .filter(o -> !org.apache.commons.lang.StringUtils.isEmpty(o))
                .map(o -> {
                    String[] fields = o.split(":");
                    String fieldCode = fields[0];
                    String fieldType;
                    if (fields.length == 1) {
                        fieldType = FieldType.VARCHAR.toString();
                    } else {
                        fieldType = fields[1];
                    }
                    String groupCode = "";
                    String targetCode = fieldCode;
                    List<ModelFieldMapping> modelFieldMappings = etlMapper.getModelFieldGroupCode(sinkName, fieldCode);
                    if (!CollectionUtils.isEmpty(modelFieldMappings)) {
                        ModelFieldMapping modelFieldMapping = modelFieldMappings.stream()
                                .filter(fieldMapping -> Constant.DATA_CONTENT.equals(fieldMapping.getModelFieldGroupCode()))
                                .findAny()
                                .orElseGet(() -> modelFieldMappings.get(0));
                        groupCode = modelFieldMapping.getModelFieldGroupCode() + ".";
                        targetCode = modelFieldMapping.getTargetCode();
                    }

                    return new EtlModelField(targetCode, groupCode + targetCode, fieldType);

                }).collect(Collectors.toList());
    }
}
