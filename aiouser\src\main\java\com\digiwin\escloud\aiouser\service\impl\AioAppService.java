package com.digiwin.escloud.aiouser.service.impl;

import com.digiwin.escloud.aiouser.dao.IAioAppDao;
import com.digiwin.escloud.aiouser.model.tenant.TenantInfo;
import com.digiwin.escloud.aiouser.service.IAioAppService;
import com.digiwin.escloud.common.model.App;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date: 2022-04-27 10:23
 * @Description
 */
@Service
public class AioAppService implements IAioAppService {
    @Resource
    private IAioAppDao aioAppDao;

    public static final int subscribedStatus = 3;

    @Override
    @Deprecated
    public List<App> getAioApps(Long tenantSid) {
        return aioAppDao.getAioApps(tenantSid);
    }

    @Override
    public List<App> getAioApps(Long tenantSid, Long sid) {
        Map<String, Object> map = new HashMap<>();
        map.put("tenantSid", tenantSid);
        map.put("sid", sid);
        return aioAppDao.selectAioAppByMap(map);
    }

    @Override
    public List<TenantInfo> getSubscribedTenantByAppCode(String appCode) {
        return aioAppDao.getTenantInfoByApp(appCode, subscribedStatus);
    }

}
