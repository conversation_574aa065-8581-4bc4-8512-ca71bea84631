package com.digiwin.escloud.aioitms.bigdata.v3;

import com.digiwin.escloud.aioitms.bigdata.model.Query;
import com.digiwin.escloud.aioitms.bigdata.model.SchemaPasswordExpiryDateParam;
import com.digiwin.escloud.aioitms.bigdata.service.IOracleV3Service;
import com.digiwin.escloud.common.model.ResponseBase;
import com.digiwin.escloud.common.model.ResponseCode;
import com.digiwin.escloud.common.model.UnitType;
import com.digiwin.escloud.common.response.BaseResponse;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date: 2022-10-10 15:04
 * @Description
 */
@Slf4j
@RestController
@RequestMapping("/oracle/v3")
public class OracleV3Controller {

    @Autowired
    private IOracleV3Service oracleV3Service;


    @ApiOperation(value = "缓冲区命中率")
    @GetMapping("/bufferHitRate")
    public List<Map<String, Object>> getBufferHitRate(@RequestParam String startTime, @RequestParam String endTime, @RequestParam String dbId) {
        return oracleV3Service.getBufferHitRate(startTime, endTime, dbId);
    }

    @ApiOperation(value = "共享池命中率")
    @GetMapping("/sharedPoolHitRate")
    public List<Map<String, Object>> getSharedPoolHitRate(@RequestParam String startTime, @RequestParam String endTime, @RequestParam String dbId) {
        return oracleV3Service.getSharedPoolHitRate(startTime, endTime, dbId);
    }

    @ApiOperation(value = "排序池")
    @GetMapping("/sortedPoolHitRate")
    public List<Map<String, Object>> getSortedPoolHitRate(@RequestParam String startTime, @RequestParam String endTime, @RequestParam String dbId) {
        return oracleV3Service.getSortedPoolHitRate(startTime, endTime, dbId);
    }

    @ApiOperation(value = "日志缓存区")
    @GetMapping("/logBufferRate")
    public List<Map<String, Object>> getLogBufferRate(@RequestParam String startTime, @RequestParam String endTime, @RequestParam String dbId) {
        return oracleV3Service.getLogBufferRate(startTime, endTime, dbId);
    }

    @ApiOperation(value = "最后一次采集时间")
    @GetMapping("/last/collectedTime")
    public ResponseBase getLastCollectedTime(@RequestParam String dbId,
                                             @RequestParam String memoryName) {
        return ResponseBase.ok(oracleV3Service.getLastCollectedTime(dbId, memoryName));
    }

    @ApiOperation(value = "Oracle资料库内存")
    @GetMapping("/memory")
    public List<Map<String, Object>> getOracleMemory(@RequestParam String startTime,
                                                     @RequestParam String endTime,
                                                     @RequestParam String dbId,
                                                     @RequestParam String memoryName) {
        return oracleV3Service.getOracleMemory(startTime, endTime, dbId, memoryName);
    }

    @ApiOperation(value = "数据库基本信息")
    @GetMapping("/info")
    public Map<String, Object> getOracleInfo(@RequestParam String startTime,
                                             @RequestParam String endTime,
                                             @RequestParam String dbId) {
        return oracleV3Service.getOracleInfo(startTime, endTime, dbId);
    }

    @ApiOperation(value = "数据库参数")
    @GetMapping("/parameter")
    public Map<String, Object> getOracleParameter(@RequestParam(required = false, defaultValue = "") String startTime,
                                                  @RequestParam(required = false, defaultValue = "") String endTime,
                                                  @RequestParam String dbId,
                                                  @RequestParam(required = false, defaultValue = "") String parameterName,
                                                  @RequestParam int pageNum,
                                                  @RequestParam int pageSize) {
        return oracleV3Service.getOracleParameter(startTime, endTime, dbId, parameterName, pageNum, pageSize);
    }

    @ApiOperation(value = "数据库资源使用_活动会话/当前进程")
    @GetMapping("/resource/usage")
    public List<Map<String, Object>> getResourceUsage(@RequestParam String startTime,
                                                      @RequestParam String endTime,
                                                      @RequestParam String dbId,
                                                      @RequestParam String resourceName) {
        return oracleV3Service.getResourceUsage(startTime, endTime, dbId, resourceName);
    }

    @ApiOperation(value = "数据文件")
    @GetMapping("/file/status")
    public Map<String, Object> getFileStatus(@RequestParam(required = false, defaultValue = "") String startTime,
                                             @RequestParam(required = false, defaultValue = "") String endTime,
                                             @RequestParam String dbId,
                                             @RequestParam int pageNum,
                                             @RequestParam int pageSize) {
        return oracleV3Service.getFileStatus(startTime, endTime, dbId, pageNum, pageSize);
    }

    @ApiOperation(value = "控制文件")
    @GetMapping("/control/document")
    public Map<String, Object> getControlDocument(@RequestParam(required = false, defaultValue = "") String startTime,
                                                  @RequestParam(required = false, defaultValue = "") String endTime,
                                                  @RequestParam String dbId,
                                                  @RequestParam int pageNum,
                                                  @RequestParam int pageSize) {
        return oracleV3Service.getControlDocument(startTime, endTime, dbId, pageNum, pageSize);
    }

    @ApiOperation(value = "重做日志文件")
    @GetMapping("/redo/logFile")
    public Map<String, Object> getRedoLogFile(@RequestParam(required = false, defaultValue = "") String startTime,
                                              @RequestParam(required = false, defaultValue = "") String endTime,
                                              @RequestParam String dbId,
                                              @RequestParam int pageNum,
                                              @RequestParam int pageSize) {
        return oracleV3Service.getRedoLogFile(startTime, endTime, dbId, pageNum, pageSize);
    }

    @ApiOperation(value = "无效对象")
    @GetMapping("/invalid/object")
    public Map<String, Object> getInvalidObject(@RequestParam(required = false, defaultValue = "") String startTime,
                                                @RequestParam(required = false, defaultValue = "") String endTime,
                                                @RequestParam(required = false, defaultValue = "") String content,
                                                @RequestParam(required = false, defaultValue = "") String objType,
                                                @RequestParam String dbId,
                                                @RequestParam int pageNum,
                                                @RequestParam int pageSize) {
        return oracleV3Service.getInvalidObject(startTime, endTime, content, objType, dbId, pageNum, pageSize);
    }

    @ApiOperation(value = "大表")
    @GetMapping("/bigTable")
    public BaseResponse getBigTable(@RequestParam(required = false, defaultValue = "") String startTime,
                                           @RequestParam(required = false, defaultValue = "") String endTime,
                                           @RequestParam String dbId,
                                           @RequestParam(required = false, defaultValue = "") String tsName,
                                           @RequestParam(required = false, defaultValue = "") String tbName,
                                           @RequestParam(required = false, defaultValue = "") String tbType,
                                           @RequestParam int pageNum,
                                           @RequestParam int pageSize) {
        return BaseResponse.ok(oracleV3Service.getBigTable(startTime, endTime, dbId, tsName, tbName, tbType, pageNum, pageSize));
    }

    @ApiOperation(value = "归档空间")
    @GetMapping("/archiveSpace")
    public Map<String, Object> getArchiveSpace(@RequestParam(required = false, defaultValue = "") String startTime,
                                               @RequestParam(required = false, defaultValue = "") String endTime,
                                               @RequestParam String dbId) {
        return oracleV3Service.getArchiveSpace(startTime, endTime, dbId);
    }

    @ApiOperation(value = "表空间收集")
    @GetMapping("/tablespace")
    public BaseResponse getTableSpace(@RequestParam(required = false, defaultValue = "") String startTime,
                                             @RequestParam(required = false, defaultValue = "") String endTime,
                                             @RequestParam String dbId,
                                             @RequestParam int pageNum,
                                             @RequestParam int pageSize) {
        return BaseResponse.ok(oracleV3Service.getTableSpace(startTime, endTime, dbId, pageNum, pageSize));
    }
    @ApiOperation(value = "schema使用量成長趨勢")
    @PostMapping("/schema/usage")
    public BaseResponse getSchemaUsage(@RequestBody Query query) {
        try {
            return BaseResponse.ok(oracleV3Service.getSchemaUsage(query));
        }
        catch (Exception e) {
            log.error("",e);
            return BaseResponse.error(e);
        }
    }

    @ApiOperation(value = "取得資料庫所有schema")
    @GetMapping("/schema/all")
    public BaseResponse getAllSchema(@RequestParam String dbId) {
        try {
            return BaseResponse.ok(oracleV3Service.getAllSchema(dbId));
        }
        catch (Exception e) {
            log.error("",e);
            return BaseResponse.error(e);
        }
    }

    @ApiOperation(value = "取得資料庫所有tablespace")
    @GetMapping("/tablespace/all")
    public BaseResponse getAllTablespace(@RequestParam String dbId) {
        try {
            return BaseResponse.ok(oracleV3Service.getAllTablespace(dbId));
        }
        catch (Exception e) {
            log.error("",e);
            return BaseResponse.error(e);
        }
    }

    @ApiOperation(value = "取得schema中的所有預設表空間")
    @GetMapping("/tablespace/default")
    public BaseResponse getAllDefaultTablespace(@RequestParam String dbId) {
        try {
            return BaseResponse.ok(oracleV3Service.getAllDefaultTablespace(dbId));
        }
        catch (Exception e) {
            log.error("",e);
            return BaseResponse.error(e);
        }
    }



    @ApiOperation(value = "重做日志切换次数")
    @GetMapping("/redo/cnt")
    public List<Map<String, Object>> getRedoCnt(@RequestParam String startTime,
                                                @RequestParam String endTime,
                                                @RequestParam String dbId) {
        return oracleV3Service.getRedoCnt(startTime, endTime, dbId, 1, UnitType.HOUR);
    }

    @ApiOperation(value = "重做日志切换次数日汇总")
    @GetMapping("/redo/day/cnt")
    public List<Map<String, Object>> getRedoDayCnt(@RequestParam String startTime,
                                                   @RequestParam String endTime,
                                                   @RequestParam String dbId) {
        return oracleV3Service.getRedoDayCnt(startTime, endTime, dbId);
    }

    @ApiOperation(value = "連線活動會話總次數")
    @GetMapping("/total/ActiveSession")
    //ActiveSession -> 連線統計數量的"活動會話"(計算LINETOTEL是否超過90%)
    public Map<String, Object> getRedoLineTotelLog(
            @RequestParam Integer page,
            @RequestParam Integer size,
            @RequestParam String dbId,
            @RequestParam(required = false, defaultValue = "") String startTime,
            @RequestParam(required = false, defaultValue = "") String endTime,
            @RequestParam(required = false) Integer maxConnection,
            @RequestParam String resourceName
    )      {
        return oracleV3Service.getRedoLineTotelLog(page, size, dbId, startTime, endTime, maxConnection, resourceName);
    }

    @ApiOperation(value = "連線重做日誌 總次數")
    @GetMapping("/redo/log")
    //重做日誌 -> 切換次數的數量，警戒值為：高於前14天平均值300%
    public Map<String, Object> getRedoSwitchLog(
            @RequestParam(value = "page", defaultValue = "1") int page,
            @RequestParam(value = "size", defaultValue = "10") int size,
            @RequestParam String dbId,
            @RequestParam String startTime,
            @RequestParam String endTime
    )      {
        return oracleV3Service.getRedoSwitchLog(page, size, dbId, startTime, endTime);
    }


    @ApiOperation(value = "锁等待次数")
    @GetMapping("/lock/wait/cnt")
    public List<Map<String, Object>> getLockWait(@RequestParam String startTime,
                                                 @RequestParam String endTime,
                                                 @RequestParam String dbId) {
        return oracleV3Service.getLockWait(startTime, endTime, dbId);
    }

    @ApiOperation(value = "锁等待列表")
    @PostMapping("/lock/wait/list")
    public Map<String, Object> getLockWaitList(@RequestParam String startTime,
                                               @RequestParam String endTime,
                                               @RequestParam String dbId,
                                               @RequestParam(required = false, defaultValue = "") String content,
                                               @RequestParam int pageNum,
                                               @RequestParam int pageSize,
                                               @RequestBody List<Query.QueryOrder> orders) {
        return oracleV3Service.getLockWaitList(startTime, endTime, dbId, content, pageNum, pageSize, orders);
    }

    @ApiOperation(value = "top10耗时sql")
    @GetMapping("/top10/execution/sql")
    public List<Map<String, Object>> getTop10ExecutionSql(@RequestParam String startTime,
                                                          @RequestParam String endTime,
                                                          @RequestParam String dbId) {
        return oracleV3Service.getTop10ExecutionSql(startTime, endTime, dbId);
    }

    @ApiOperation(value = "schema連線密碼狀態")
    @PostMapping("schema/password/expiryDate/{type}")
    public BaseResponse getSchemaPasswordExpiryDate(@PathVariable(value = "type") String type,
                                                    @RequestBody SchemaPasswordExpiryDateParam param) {
        try {
            return BaseResponse.ok(oracleV3Service.getSchemaPasswordExpiryDate(type, param));
        } catch (Exception e) {
            return BaseResponse.error(ResponseCode.GET_SCHEMA_PASSWORD_EXPIRY_DATE_FAILED);
        }
    }

    @ApiOperation(value = "取得RMAN備份資料")
    @PostMapping("/rman/detail")
    public BaseResponse getRMANDetail(@RequestBody Query query) {
        try {
            return BaseResponse.ok(oracleV3Service.getRMANDetail(query));
        } catch (Exception e) {
            log.error("Get RMAN Detail Error", e);
            return BaseResponse.error(ResponseCode.INTERNAL_ERROR);
        }
    }

    @ApiOperation("Oracle SQL Top10")
    @PostMapping("/sqltop10")
    public BaseResponse getSQLTop10(@RequestBody Query query) {
        try {
            return BaseResponse.ok(oracleV3Service.getSQLTop10(query));
        } catch (Exception ex) {
            log.error("Get Oracle SQL Top 10 Error", ex);
            return BaseResponse.error(ResponseCode.GET_ORACLE_SQL_TOP10_FAILED);
        }
    }

    @ApiOperation("Oracle EXP看板")
    @GetMapping("/exp/dashboard")
    public BaseResponse getExpDashBoard(@RequestParam Long eid,
                                        @RequestParam String dbId,
                                        @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime startTime,
                                        @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime endTime) {
        try {
            return BaseResponse.ok(oracleV3Service.getExpDashBoard(eid, dbId, startTime, endTime, true));
        } catch (Exception ex) {
            log.error("Get Oracle Exp DashBoard Error", ex);
            return BaseResponse.error(ResponseCode.GET_ORACLE_EXP_DASHBOARD_FAILED);
        }
    }

    @ApiOperation(value = "取得RMAN看板資料")
    @GetMapping("/rman/dashboard")
    public BaseResponse getRMANDashboard(@RequestParam String dbId,
                                         @RequestParam String startTime,
                                         @RequestParam String endTime) {
        try {
            return BaseResponse.ok(oracleV3Service.getRMANDashboard(dbId, startTime, endTime));
        } catch (Exception e) {
            log.error("Get Oracle RMAN DashBoard Error",e);
            return BaseResponse.error(ResponseCode.INTERNAL_ERROR);
        }
    }

    @ApiOperation(value = "查詢回收桶抽屜資料")
    @GetMapping("/recycle")
    public BaseResponse getRecycle(@RequestParam String dbId,
                                   @RequestParam(required = false, defaultValue = "") String oracle_schema,
                                   @RequestParam  @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")  LocalDateTime endTime,
                                   @RequestParam List<String> sortField,
                                   @RequestParam List<String> sortOrder,
                                   @RequestParam Integer pageNum,
                                   @RequestParam Integer pageSize) {
        try {
            return BaseResponse.ok(oracleV3Service.getRecycle(dbId, oracle_schema, endTime, sortField, sortOrder, pageNum, pageSize));
        } catch (Exception e) {
            log.error("Get Oracle RMAN DashBoard Error",e);
            return BaseResponse.error(ResponseCode.INTERNAL_ERROR);
        }
    }

}
