package com.digiwin.escloud.aiocmdb.asset.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * StarRocks查询请求参数
 */
@Data
@ApiModel(value = "StarRocksQueryRequest对象", description = "StarRocks查询请求参数")
public class StarRocksQueryRequest {

    @ApiModelProperty("模型编码列表")
    private List<String> modelCodeList;

    @ApiModelProperty("查询列列表，如：aiId, assetId")
    private List<String> selectColumns;

    @ApiModelProperty("过滤条件，如：aiId IS NOT NULL")
    private String whereCondition;

    @ApiModelProperty("是否去重，默认false")
    private Boolean distinct = false;
}
