package com.digiwin.escloud.common.feign;

import com.digiwin.escloud.aiochat.model.IssueDescRequest;
import com.digiwin.escloud.common.model.ResponseBase;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(value = "aioai", url = "${feign.aioai.url:}")
public interface AioAiFeignClient {

    @PostMapping(value = "/issue/chat/getIssueDescForReport")
    ResponseBase getIssueDescForReport(@RequestBody IssueDescRequest request);


}