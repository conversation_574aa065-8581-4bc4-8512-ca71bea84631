package com.digiwin.escloud.aiocmdb.asset.model;

import com.digiwin.escloud.aiocmdb.asset.utils.AssetNoProduceType;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class AssetCategoryCodingRuleSimple {

    @ApiModelProperty("规则编号")
    private AssetNoProduceType ruleNumber;

    @ApiModelProperty("规则设定值")
    private String ruleSettingValue;

    private String modelCode;
    private String sinkName;
    private long classificationId;

    // 由程式填入
    private String mainCode; // 大類別的編號
    private String currentFlowNumber;

    public String getCurrentFlowNumber() {
        // 去掉所有的 '-', 且規則的文字, 不能有 '-', 前端需卡控掉
        return currentFlowNumber.replaceAll("-", "");
    }
}
