package com.digiwin.escloud.aioitms.authorize.controller;

import com.digiwin.escloud.aioitms.model.authorize.AiopsAuthStatus;
import com.digiwin.escloud.aioitms.authorize.model.AuthorizeResponse;
import com.digiwin.escloud.aioitms.instance.model.AiopsItemContext;
import com.digiwin.escloud.aioitms.authorize.service.IAiopsAuthorizeV2Service;
import com.digiwin.escloud.common.controller.ControllerBase;
import com.digiwin.escloud.common.response.BaseResponse;
import com.digiwin.escloud.common.util.BooleanUtil;
import com.digiwin.escloud.common.util.RequestUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Api(value = "/v2/authorize", protocols = "HTTP", tags = {"授权相关v2接口"}, description = "授权相关v2接口")
@RestController
@RequestMapping("/v2/authorize")
public class AiopsAuthorizeV2Controller extends ControllerBase {
    @Autowired
    IAiopsAuthorizeV2Service aiopsAuthorizeService;

    @ApiOperation("AiopsKit客户端EID验证及获取token")
    @PostMapping(value = "/aiopskit/{deviceId}")
    public AuthorizeResponse getAuthorizeToken(
            @ApiParam(value = "设备Id", required = true)
            @PathVariable("deviceId") String deviceId) {
        Long eid = RequestUtil.getHeaderEid();
        return getResponse(AuthorizeResponse.class, () -> aiopsAuthorizeService.getAuthorizeToken(eid, deviceId),
                false, null, GENERAL_FAIL_CODE);
    }

    @ApiOperation("异动运维实例授权")
    @PutMapping(value = "/aiops/instance")
    @Transactional(rollbackFor = Exception.class)
    public BaseResponse modifyAiopsInstanceAuth(
            @ApiParam(value = "租户Id", required = true)
            @RequestParam(value = "eid") Long eid,
            @ApiParam(value = "运维授权状态", required = true)
            @RequestParam(value = "aiopsAuthStatus") AiopsAuthStatus aiopsAuthStatus,
            @ApiParam(value = "是否修改资产状态")
            @RequestParam(value = "modifyAsset",required = false) Boolean modifyAsset,
            @ApiParam(value = "运维项目上下文列表", required = true)
            @RequestBody() List<AiopsItemContext> aicList
            ) {
        return getBaseResponse(() -> {
            //检查并创建运维实例
            BaseResponse response = aiopsAuthorizeService.checkAndCreateAiopsInstanceByAiopsItemContext(eid,
                    aiopsAuthStatus, aicList);
            if (!response.checkIsSuccess() || aiopsAuthStatus == null) {
                return response;
            }

            //如果修改资产状态
            if (BooleanUtil.objectToBoolean(modifyAsset)) {
                aicList.forEach(aic -> aic.setModifyAsset(modifyAsset));
            }
            //实际异动授权状态
            return aiopsAuthorizeService.modifyInstanceAuthStatus(eid, aiopsAuthStatus, aicList);
        }, false, false, null);
    }
}
