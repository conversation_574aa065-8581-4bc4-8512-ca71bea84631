<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.digiwin.escloud.aioitms.bcp.dao.BcpMapper">
    <resultMap id="BcpSurveyData" type="com.digiwin.escloud.aioitms.bcp.model.BcpSurveyData">
        <result property="title" column="title"/>
        <result property="description" column="description"/>

        <collection property="questions" resultMap="BcpSurveyQuestion"/>
    </resultMap>

    <resultMap id="BcpSurveyQuestion" type="com.digiwin.escloud.aioitms.bcp.model.BcpSurveyQuestion">
        <id property="id" column="q_id"/>
        <result property="question" column="q_question"/>
        <result property="remark" column="q_remark"/>
        <result property="multiple" column="q_multiple"/>
        <result property="sort" column="q_sort"/>

        <collection property="answers" resultMap="BcpSurveyAnswer"/>
    </resultMap>

    <resultMap id="BcpSurveyAnswer" type="com.digiwin.escloud.aioitms.bcp.model.BcpSurveyAnswer">
        <id property="id" column="ans_id"/>
        <result property="description" column="ans_description"/>
        <result property="sort" column="ans_sort"/>
    </resultMap>

    <resultMap id="BcpSurveyResult" type="com.digiwin.escloud.aioitms.bcp.model.BcpSurveyResult">
        <result property="version" column="version"/>
        <result property="id" column="id"/>
        <result property="expectedValue" column="expectedValue"/>
        <result property="score" column="score"/>
        <result property="recordTime" column="recordTime"/>

        <collection property="survey" resultMap="BcpSurveyResultCategory"/>
    </resultMap>

    <resultMap id="BcpSurveyResultCategory" type="com.digiwin.escloud.aioitms.bcp.model.BcpSurveyResultCategory">
        <result property="title" column="survy_title"/>
        <result property="description" column="survy_description"/>
        <result property="score" column="survy_score"/>
        <result property="value" column="survy_value"/>

        <collection property="tags" resultMap="BcpSurveyResultTag"/>
    </resultMap>

    <resultMap id="BcpSurveyResultTag" type="com.digiwin.escloud.aioitms.bcp.model.BcpSurveyResultTag">
        <result property="title" column="tag_title"/>
        <result property="score" column="tag_score"/>
        <result property="sort" column="tag_sort"/>
    </resultMap>

    <select id="selectBcpSurveyData" resultMap="BcpSurveyData">
        SELECT bsc.title, bsc.description,
               bsq.id AS q_id, bsq.question AS q_question, bsq.remark AS q_remark,
               bsq.multiple AS q_multiple, bsq.sort AS q_sort,
               bsa.id AS ans_id, bsa.description AS ans_description, bsa.sort AS ans_sort
        FROM bcp_survey_category bsc
        LEFT JOIN bcp_survey_question bsq ON bsc.id = bsq.categoryId AND bsq.version = bsc.version
        LEFT JOIN bcp_survey_answer bsa ON bsq.id = bsa.questionId AND bsa.version = bsc.version
        WHERE bsc.version = #{version}
        ORDER BY bsc.id ASC, bsq.sort ASC, bsa.sort ASC
    </select>

    <select id="selectBcpSurveyResult" resultMap="BcpSurveyResult">
        SELECT bsc.version, bsr.id, total.totalScore AS score, expected.expectedValue,
               bsc.title survy_title, bsc.description survy_description, bsres.categoryScore survy_score,
               (bsres.categoryScore/(bsc.weight/100)) AS survy_value,
               bsq.tag tag_title, bsd.answerScore tag_score, bsq.sort tag_sort
        FROM bcp_survey_record bsr
        INNER JOIN bcp_survey_result bsres ON bsres.bsrId = bsr.id
        INNER JOIN bcp_survey_data bsd ON bsd.bsrId = bsr.id
        INNER JOIN bcp_survey_category bsc ON bsc.id = bsres.categoryId
        INNER JOIN bcp_survey_answer bsa ON bsa.id = bsd.answerId
        INNER JOIN bcp_survey_question bsq ON bsq.id = bsa.questionId AND bsq.categoryId = bsc.id AND bsq.weight > 0
        LEFT JOIN (
            SELECT bsrId, SUM(categoryScore) AS totalScore
            FROM bcp_survey_result
            GROUP BY bsrId
        ) total ON total.bsrId = bsr.id
        LEFT JOIN (
            SELECT bsres.bsrId, bsres.categoryScore AS expectedValue
            FROM bcp_survey_result bsres
            JOIN bcp_survey_category bsc ON bsc.id = bsres.categoryId
            JOIN bcp_survey_question bsq ON bsq.categoryId = bsc.id
            WHERE bsq.weight = -100
        ) expected ON expected.bsrId = bsr.id
        WHERE bsr.id = #{surveyId}
    </select>

    <select id="selectBcpSurveyRecord" parameterType="com.digiwin.escloud.aioitms.bcp.model.BcpRequestParam"
            resultType="com.digiwin.escloud.aioitms.bcp.model.BcpSurveyRecord">
        SELECT bsr.recordTime, bsr.eid, bsr.serviceCode, bsr.id AS surveyId, bsr.companyName,
               bsr.taxId, bsr.userId, bsr.userName, bsr.userTel, total.totalScore AS surveyScore
        FROM bcp_survey_record bsr
        LEFT JOIN (
            SELECT bsrId, SUM(categoryScore) AS totalScore
            FROM bcp_survey_result
            GROUP BY bsrId
        ) total ON total.bsrId = bsr.id
        WHERE 1=1
        <if test="eid != null and eid != ''">
            AND bsr.eid = #{eid}
        </if>
        <if test="accountReset != null and accountReset != ''">
            <if test="accountReset == 0">
                AND bsr.eid IS NULL AND (bsr.serviceCode IS NULL OR bsr.serviceCode = '')
            </if>
            <if test="accountReset == 1">
                AND bsr.eid IS NOT NULL AND (bsr.serviceCode IS NOT NULL OR bsr.serviceCode != '')
            </if>
        </if>
        <if test="recordStartTime != null and recordStartTime != ''">
            AND bsr.recordTime &gt;= #{recordStartTime}
        </if>
        <if test="recordEndTime != null and recordEndTime != ''">
            AND bsr.recordTime &lt;= #{recordEndTime}
        </if>
        <if test="companyName != null and companyName != ''">
            AND UPPER(bsr.companyName) LIKE CONCAT('%',UPPER(#{companyName}),'%')
        </if>
        <if test="taxId != null and taxId != ''">
            AND bsr.taxId LIKE CONCAT('%',#{taxId},'%')
        </if>
        <if test="userName != null and userName != ''">
            AND bsr.userName LIKE CONCAT('%',#{userName},'%')
        </if>
        <if test="userId != null and userId != ''">
            AND UPPER(bsr.userId) LIKE CONCAT('%',UPPER(#{userId}),'%')
        </if>
    </select>

    <select id="selectTenantInfo" resultType="com.digiwin.escloud.aioitms.bcp.model.TenantInfo" parameterType="com.digiwin.escloud.aioitms.bcp.model.BcpSurveyRecord">
        SELECT a.eid,c.serviceCode
        FROM user_tenant_map a
                 LEFT JOIN user b ON a.userSid=b.sid
                 LEFT JOIN supplier_tenant_map c ON c.eid=a.eid
                 left join tenant d on d.sid=a.eid
        where b.email = #{userId} and c.sid = '***************'
    </select>

    <select id="getAccountResetCount" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM bcp_survey_record
        WHERE eid IS NOT NULL
        AND (serviceCode IS NOT NULL OR serviceCode != '')
    </select>

    <select id="getNonResetCount" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM bcp_survey_record
        WHERE eid IS NULL
        AND (serviceCode IS NULL OR serviceCode = '')
    </select>

    <select id="getCustomerList" resultType="com.digiwin.escloud.aioitms.bcp.model.BcpCustomer">
        SELECT DISTINCT bsr.eid, bsr.serviceCode, t.name AS companyShortName, t.customerFullNameCH AS customerFullName
        FROM bcp_survey_record bsr
        LEFT JOIN tenant t ON t.id = bsr.serviceCode AND t.sid = bsr.eid
        WHERE bsr.eid IS NOT NULL AND (bsr.serviceCode IS NOT NULL OR bsr.serviceCode != '')
    </select>

    <select id="getLastResult" resultMap="BcpSurveyResult">
        SELECT bsc.version, bsr.id, total.totalScore AS score, expected.expectedValue, bsr.recordTime,
               bsc.title survy_title, bsc.description survy_description, bsres.categoryScore survy_score,
               (bsres.categoryScore/(bsc.weight/100)) AS survy_value
        FROM bcp_survey_record bsr
        INNER JOIN bcp_survey_result bsres ON bsres.bsrId = bsr.id
        INNER JOIN bcp_survey_data bsd ON bsd.bsrId = bsr.id
        INNER JOIN bcp_survey_category bsc ON bsc.id = bsres.categoryId
        LEFT JOIN (
            SELECT bsrId, SUM(categoryScore) AS totalScore
            FROM bcp_survey_result
            GROUP BY bsrId
        ) total ON total.bsrId = bsr.id
        LEFT JOIN (
            SELECT bsres.bsrId, bsres.categoryScore AS expectedValue
            FROM bcp_survey_result bsres
            JOIN bcp_survey_category bsc ON bsc.id = bsres.categoryId
            JOIN bcp_survey_question bsq ON bsq.categoryId = bsc.id
            WHERE bsq.weight = -100
        ) expected ON expected.bsrId = bsr.id
        WHERE bsr.recordTime = (SELECT MAX(recordTime) FROM bcp_survey_record WHERE eid = #{eid})
    </select>

    <insert id="saveBcpSurveyRecord" parameterType="com.digiwin.escloud.aioitms.bcp.model.BcpSurveyRecord">
        INSERT INTO bcp_survey_record (id, eid, serviceCode, companyName, taxId, userId, userName, userTel)
        VALUES (#{surveyId}, #{eid}, #{serviceCode}, #{companyName}, #{taxId}, #{userId}, #{userName}, #{userTel})
            ON DUPLICATE KEY UPDATE
            id = #{surveyId},
            eid = IF(VALUES(eid) IS NULL, eid, VALUES(eid)),
            serviceCode = IF(VALUES(serviceCode) IS NULL, serviceCode, VALUES(serviceCode)),
            companyName = VALUES(companyName),
            taxId = VALUES(taxId),
            userId = VALUES(userId),
            userName = VALUES(userName),
            userTel = IF(VALUES(userTel) IS NULL, userTel, VALUES(userTel))
    </insert>

    <select id="getBcpSurveyScore" resultType="java.util.Map">
        SELECT answerId,
            weightScore,
            CASE
                WHEN questionWeight = 0 THEN 0
                WHEN questionWeight = -100 THEN 0
                ELSE weightScore * (100 / questionWeight)
            END AS answerScore
        FROM (SELECT bsa.id AS answerId,
                CASE
                    WHEN bsq.weight = -100 THEN 0
                    WHEN bsa.sort = 1 THEN bsq.weight * 3 / 3
                    WHEN bsa.sort = 2 THEN bsq.weight * 2 / 3
                    WHEN bsa.sort = 3 THEN bsq.weight * 1 / 3
                    ELSE 0
                END AS weightScore,
                bsq.weight AS questionWeight
              FROM bcp_survey_answer bsa
              LEFT JOIN bcp_survey_question bsq ON bsa.questionId = bsq.id) source
        WHERE 1 = 1
        <if test="answerIdList != null and answerIdList.size() > 0">
            AND answerId IN
            <foreach collection="answerIdList" item="answerId" separator="," open="(" close=")">
                #{answerId}
            </foreach>
        </if>
    </select>

    <insert id="insertBcpSurveyDataRecord">
        INSERT INTO bcp_survey_data (bsrId, answerId, extratAnswer1, extratAnswer2, weightScore, answerScore)
        <foreach collection="bcpSurveyDataRecordList" open="VALUES" item="item" separator=",">
            (#{item.bsrId}, #{item.answerId}, #{item.extratAnswer1}, #{item.extratAnswer2}, #{item.weightScore}, #{item.answerScore})
        </foreach>
    </insert>

    <select id="getBcpSurveyCategoryScore" resultType="java.util.Map">
        SELECT bsq.categoryId, sum(bsd.weightScore) * (bsc.weight / 100.0) AS categoryScore
        FROM bcp_survey_data bsd
                 LEFT JOIN bcp_survey_answer bsa on bsd.answerId = bsa.id
                 LEFT JOIN bcp_survey_question bsq on bsa.questionId = bsq.id
                 LEFT JOIN bcp_survey_category bsc on bsq.categoryId = bsc.id
        WHERE bsd.bsrId = #{bsrId}
        GROUP BY bsq.categoryId
    </select>

    <insert id="insertBcpSurveyResultRecord">
        INSERT INTO bcp_survey_result (bsrId, categoryId, categoryScore)
        <foreach collection="bcpSurveyResultRecordList" open="VALUES" item="item" separator=",">
            (#{item.bsrId}, #{item.categoryId}, #{item.categoryScore})
        </foreach>
    </insert>
</mapper>