package com.digiwin.escloud.aiobasic.authorizationCollected.service.impl;

import com.digiwin.escloud.aiobasic.authorizationCollected.dao.AuthorizationMapper;
import com.digiwin.escloud.aiobasic.authorizationCollected.factory.AuthorizationFactoryService;
import com.digiwin.escloud.aiobasic.authorizationCollected.model.AiopsItemEidMapping;
import com.digiwin.escloud.aiobasic.authorizationCollected.model.AuthorizationColumnMapping;
import com.digiwin.escloud.aiobasic.authorizationCollected.service.IAuthorizationService;
import com.digiwin.escloud.aiobasic.util.BigDataUtil;
import com.digiwin.escloud.aioitms.model.bigdata.UploadBigDataContext;
import com.digiwin.escloud.common.feign.AioItmsFeignClient;
import com.digiwin.escloud.common.feign.AioUserFeignClient;
import com.digiwin.escloud.common.model.ResponseCode;
import com.digiwin.escloud.common.response.BaseResponse;
import com.digiwin.escloud.common.util.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class AuthorizationService implements IAuthorizationService {
    @Value("${digiwin.supplier.defaultsid:241199971893824}")
    private Long defaultSid;
    @Autowired
    AioUserFeignClient aioUserFeignClient;
    @Autowired
    AioItmsFeignClient aioItmsFeignClient;
    @Autowired
    BigDataUtil bigDataUtil;
    @Autowired
    AuthorizationFactoryService authorizationFactoryService;
    @Autowired
    AuthorizationMapper authorizationMapper;

    @Override
    public BaseResponse uploadAuthorization() {
        Long sid = RequestUtil.getHeaderSidOrDefault(defaultSid);
        List<Map<String, Object>> uploadBigDataContextList = new ArrayList<>();

        // 取得需檢查eid清單
        List<AiopsItemEidMapping> eidList = authorizationMapper.getAuthorizationEidList();

        eidList.forEach(mapping -> {
            // 取得租戶授權模組及使用量清單
            List<Map<String, Object>> result = getTenantModuleContractClassDetailByAiopsItem(sid, mapping.getEid());
            if (CollectionUtil.isEmpty(result)) {
                log.error("Get TenantModuleContractClassDetailByAiopsItem Error, eid:{}", mapping.getEid());
                return;
            }

            // 取得實例資料，暫時以Sentinelone_Agents為主，後續添加其他項目超裝預警時需調整此處
            // 及到authorization_column_mapping映射表添加數據
            BaseResponse datass = aioItmsFeignClient.getAiopsEaiCloudInstanceCollectDetail(null, mapping.getUploadDataModelCode(), mapping.getAiopsItem(), null);
            Map<String, Object> instanceCollectedDetail = getListDataByBaseResponse(
                    aioItmsFeignClient.getAiopsEaiCloudInstanceCollectDetail(null, mapping.getUploadDataModelCode(), mapping.getAiopsItem(), null)
            ).stream()
                    .filter(res -> Objects.equals(StringUtil.toString(res.get("eid")), StringUtil.toString(mapping.getEid())))
                    .findFirst()
                    .orElse(null);
            if (Objects.isNull(instanceCollectedDetail)) {
                log.error("Get InstanceCollectedDetail Error, uploadDataModelCode:{}, aiopsItem:{}", mapping.getUploadDataModelCode(), mapping.getAiopsItem());
                return;
            }

            // 組合要上報的數據
            Long accId = LongUtil.objectToLong(instanceCollectedDetail.get("collectConfigId"), null);
            Long aiId = LongUtil.objectToLong(instanceCollectedDetail.get("aiId"), null);
            String aiopsItemId = Objects.toString(instanceCollectedDetail.get("aiopsItemId"), null);
            Long deviceCollectDetailId = LongUtil.objectToLong(instanceCollectedDetail.get("deviceCollectDetailId"), null);
            UploadBigDataContext uploadBigDataContext = new UploadBigDataContext(mapping.getEid(), null, accId, deviceCollectDetailId, aiopsItemId, aiId, "AuthorizationCollected", false, result);
            uploadBigDataContextList.addAll(bigDataUtil.createParentUploadStruct(uploadBigDataContext));
        });

        // 上報數據
        bigDataUtil.simulateLocalUploadData(uploadBigDataContextList);

        return BaseResponse.ok();
    }

    @Override
    public BaseResponse addModuleMapping(List<AuthorizationColumnMapping> params) {

        Integer affectedRow = authorizationMapper.addModuleMapping(params);
        if (affectedRow <= 0) {
            return BaseResponse.error(ResponseCode.ADD_MODULE_MAPPING_ERROR);
        }

        return BaseResponse.ok();
    }

    @Override
    public BaseResponse deleteModuleMappingByOriginClassName(String originClassName) {

        Integer affectedRow = authorizationMapper.deleteModuleMappingByOriginClassName(originClassName);
        if (affectedRow <= 0) {
            return BaseResponse.error(ResponseCode.DELETE_MODULE_MAPPING_ERROR);
        }

        return BaseResponse.ok();
    }

    private List<Map<String, Object>> getTenantModuleContractClassDetailByAiopsItem(Long sid, Long eid) {
        // 取得租戶授權模組
        List<Map<String, Object>> moduleContract = getListDataByBaseResponse(aioUserFeignClient.getTenantModuleContractClassDetailByAiopsItemListV2(sid, eid, new ArrayList<>()));

        // 取得欄位映射，轉換成Map(直轉橫)
        List<AuthorizationColumnMapping> columnMapping = authorizationMapper.getModuleColumnMapping();
        Map<String, String> classToKeyMap = columnMapping.stream()
                .collect(Collectors.toMap(
                        AuthorizationColumnMapping::getOriginClassName,
                        AuthorizationColumnMapping::getKeyName
                ));

        // 循環租戶模組，只回傳有取得使用量的模組授權資訊
        return moduleContract.stream()
                .filter(Objects::nonNull)
                .filter(moduleData -> Objects.nonNull(moduleData.get("tenantModuleContractDetailList")))
                .map(moduleData -> {
                    Map<String, Object> resultData = new HashMap<>();
                    String moduleCode = Objects.toString(moduleData.get("moduleCode"));
                    resultData.put("moduleCode", moduleCode);
                    resultData.put("moduleName", moduleData.get("moduleName"));
                    resultData.put("moduleEndDate", moduleData.get("endDate"));

                    // 取得授權數
                    Optional.ofNullable((List<Map<String, Object>>) moduleData.get("tenantModuleContractDetailList"))
                            .orElse(Collections.emptyList()) // 如果為 null，返回空列表
                            .stream()
                            .filter(Objects::nonNull)
                            .forEach(detail -> {
                                String classCode = Objects.toString(detail.get("classCode"));
                                Integer availableCount = IntegerUtil.objectToInteger(detail.get("availableCount"), 0);
                                String availableKeyName = classToKeyMap.getOrDefault(classCode, "");
                                if (StringUtil.isEmpty(availableKeyName)) {
                                    return;
                                }

                                resultData.put(availableKeyName, availableCount);
                            });

                    // 取得授權已使用量
                    List<Map<String, Object>> usedCountDataList = authorizationFactoryService.getAuthorizationCount(moduleCode, eid);
                    if (CollectionUtils.isEmpty(usedCountDataList)) {
                        resultData.put("hostUsedCount", 0);
                        resultData.put("clientUsedCount", 0);
                        resultData.put("hostOverCount", 0);
                        resultData.put("clientOverCount", 0);
                        return resultData;
                    }

                    usedCountDataList.forEach(data -> {
                        String usedClassCode = Objects.toString(data.get("usedClassCode"));
                        Integer usedCount = IntegerUtil.objectToInteger(data.get("usedCount"), 0);
                        String usedKeyName = classToKeyMap.getOrDefault(usedClassCode, "");

                        resultData.put(usedKeyName, usedCount);
                    });

                    // 計算超裝數
                    Integer hostAvailableCount = IntegerUtil.objectToInteger(resultData.get("hostAvailableCount"), 0);
                    Integer clientAvailableCount = IntegerUtil.objectToInteger(resultData.get("clientAvailableCount"), 0);
                    Integer hostUsedCount = IntegerUtil.objectToInteger(resultData.get("hostUsedCount"), 0);
                    Integer clientUsedCount = IntegerUtil.objectToInteger(resultData.get("clientUsedCount"), 0);

                    // 若 已用數-已購數 大於 0，回傳相減值，否則回傳0
                    resultData.put("hostOverCount", Math.max(hostUsedCount - hostAvailableCount, 0));
                    resultData.put("clientOverCount", Math.max(clientUsedCount - clientAvailableCount, 0));

                    return resultData;
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    private List<Map<String, Object>> getListDataByBaseResponse(BaseResponse res) {
        Object obj;
        if (Objects.isNull(res) || Objects.isNull(obj = res.getData())) {
            return new ArrayList<>(0);
        }
        if (!(obj instanceof List)) {
            return new ArrayList<>(0);
        }
        List<Map<String, Object>> data = MapUtil.convertToListMap((List) obj);
        if (CollectionUtils.isEmpty(data)) {
            return new ArrayList<>(0);
        }
        return data;
    }
}