package com.digiwin.escloud.aiouser.dao;

import com.digiwin.escloud.aiouser.model.tenant.TenantInfo;
import com.digiwin.escloud.common.model.App;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date: 2022-04-27 10:26
 * @Description
 */
public interface IAioAppDao {
    @Deprecated
    List<App> getAioApps(Long tenantSid);
    /**
     * 依据条件字典查询应用信息列表
     * @param map 条件字典
     * @return 应用信息列表
     */
    List<App> selectAioAppByMap(Map<String, Object> map);

    /**
     * 依据应用代号和状态查询租户信息
     * @param appCode 应用代号
     * @param status 状态
     * @return 租户信息列表
     */
    List<TenantInfo> getTenantInfoByApp(@Param("appCode")String appCode, @Param("status")Integer status);
}
