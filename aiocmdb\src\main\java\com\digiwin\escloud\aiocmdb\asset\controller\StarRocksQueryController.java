package com.digiwin.escloud.aiocmdb.asset.controller;

import com.digiwin.escloud.aiocmdb.asset.model.StarRocksQueryRequest;
import com.digiwin.escloud.aiocmdb.asset.service.IAssetCategoryService;
import com.digiwin.escloud.common.model.ResponseBase;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

@Api(tags = "StarRocks查询API")
@RestController
@RequestMapping("/api/starrocks")
public class StarRocksQueryController {

    @Autowired
    private IAssetCategoryService assetCategoryService;

    /**
     * 功能3：外部可调用的StarRocks查询API
     * 支持传入查询列和过滤条件，因为这是union all所以查询列和过滤条件列每个表都需要有
     *
     * @param request 查询请求参数
     * @return 查询结果
     */
    @ApiOperation(value = "StarRocks数据查询", notes = "支持传入查询列和过滤条件的StarRocks查询API")
    @PostMapping("/union/query")
    public ResponseBase<List<Map<String, Object>>> queryStarRocksData(@RequestBody StarRocksQueryRequest request) {
        return assetCategoryService.queryStarRocksData(request);
    }
}
