package com.digiwin.escloud.aioitms.report.service.db.impl.product;

import com.digiwin.escloud.aioitms.device.dao.DeviceV2Mapper;
import com.digiwin.escloud.aioitms.device.model.AiopsKitDeviceDataSource;
import com.digiwin.escloud.aioitms.device.model.DeviceInfo;
import com.digiwin.escloud.aioitms.report.dao.SqlServerErpReportMapper;
import com.digiwin.escloud.aioitms.report.model.db.DbReportRecord;
import com.digiwin.escloud.aioitms.report.model.db.SqlServerErpReport;
import com.digiwin.escloud.aioitms.report.service.db.AdvBaseDbData;
import com.digiwin.escloud.aiouser.model.supplier.SupplierProduct;
import com.digiwin.escloud.common.util.IntegerUtil;
import com.digiwin.escloud.common.util.StringUtil;
import org.apache.commons.lang.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.Callable;
import java.util.function.Supplier;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.digiwin.escloud.aioitms.report.constant.SqlServerEdrReportConst.*;

public class SqlServerErpBase<T extends SqlServerErpReport> extends AdvBaseDbData<T> {

    @Autowired
    private DeviceV2Mapper deviceV2Mapper;
    @Autowired
    private SqlServerErpReportMapper sqlServerErpReportMapper;

    protected Callable<Object> buildProductInfo(DbReportRecord dbReportRecord) {
        String customerCode = dbReportRecord.getCustomerCode();

        SqlServerErpReport.ProductInfo productInfo = new SqlServerErpReport.ProductInfo();

        SqlServerErpReport.Erp erp = new SqlServerErpReport.Erp();
        SqlServerErpReport.Erp2 erp2 = new SqlServerErpReport.Erp2();

        List<String> erpProductCodeList = ERP_PRODUCT_LIST.stream()
                .map(SqlServerErpReport.Product.ProductModel::getCode)
                .collect(Collectors.toList());
        List<String> erp2ProductCodeList = ERP2_PRODUCT_LIST.stream()
                .map(SqlServerErpReport.Product.ProductModel::getCode)
                .collect(Collectors.toList());
        List<Map<String, String>> erpPurchasedResult = sqlServerErpReportMapper.getProductCode(customerCode, erpProductCodeList);
        List<String> erpPurchased = erpPurchasedResult.stream()
                .flatMap(map -> map.values().stream())
                .collect(Collectors.toList());
        List<Map<String, String>> erp2PurchasedResult = sqlServerErpReportMapper.getProductCode(customerCode, erp2ProductCodeList);
        List<String> erp2Purchased = erp2PurchasedResult.stream()
                .flatMap(map -> map.values().stream())
                .collect(Collectors.toList());
                
        erp.setAllPurchased(erpPurchased);
        erp.setAllProduct(ERP_PRODUCT_LIST);
        erp2.setAllPurchased(erp2Purchased);
        erp2.setAllProduct(ERP2_PRODUCT_LIST);

        productInfo.setErp(erp);
        productInfo.setErp2(erp2);

        return () -> productInfo;
    }

    protected Callable<Object> buildProductCheck(DbReportRecord dbReportRecord) {
        List<SqlServerErpReport.ProductCheck> productCheckList = new ArrayList<>();
        dbReportRecord.getDeviceIdList().forEach(deviceId -> {
            Map<String, Object> param = new HashMap<>();
            param.put("eid", dbReportRecord.getEid());
            param.put("deviceNameOrId", deviceId);

            DeviceInfo deviceInfo = getDeviceInfo(param);
            if (deviceInfo != null) {
                List<SupplierProduct> supplierProductList = deviceInfo.getSupplierProductList();

                if (!supplierProductList.isEmpty()) {
                    SqlServerErpReport.ProductCheck productCheck = new SqlServerErpReport.ProductCheck();

                    String productValues = getProductValues(supplierProductList.stream()
                            .map(SupplierProduct::getProductCode).collect(Collectors.toList()));

                    productCheck.setDeviceName(deviceInfo.getDeviceName());
                    productCheck.setProducts(productValues);
                    productCheck.setRole("");
                    productCheck.setIpAddress(deviceInfo.getIpAddress());

                    productCheckList.add(productCheck);
                }
            }
        });

        return () -> productCheckList;
    }

    protected Callable<Object> buildDeviceCheck(DbReportRecord dbReportRecord) {
        String eid = StringUtil.toString(dbReportRecord.getEid());
        String customerCode = dbReportRecord.getCustomerCode();
        // 取得設備 deviceId, 以及相對應 MSSQL -> dbId
        List<Map<String, Object>> deviceIdAndDbIdList = getDeviceIdAndDbId(dbReportRecord.getDeviceIdList());

        List<SqlServerErpReport.DeviceCheck> deviceCheckList = new ArrayList<>();
        deviceIdAndDbIdList.forEach(deviceIdAndDbId -> {
            String deviceId = ObjectUtils.toString(deviceIdAndDbId.get("deviceId"), "");
            List<String> dbIdList = new ArrayList<>();
            Object dbIdObj = deviceIdAndDbId.get("scoreDbId");
            if (dbIdObj instanceof List) {
                for (Object dbId : (List<?>) dbIdObj) {
                    dbIdList.add(String.valueOf(dbId));
                }
            }

            Map<String, Object> param = new HashMap<>();
            param.put("eid", dbReportRecord.getEid());
            param.put("deviceNameOrId", deviceId);
            DeviceInfo deviceInfo = getDeviceInfo(param);

            SqlServerErpReport.ProductPurchased productPurchased = deviceInfo == null ?
                    new SqlServerErpReport.ProductPurchased() : getProductCodeForDeviceId(deviceInfo);
            boolean erp = !productPurchased.getErp().isEmpty();
            boolean erp2 = !productPurchased.getErp2().isEmpty();

            SqlServerErpReport.DeviceCheck deviceCheck = new SqlServerErpReport.DeviceCheck();
            deviceCheck.setProductPurchased(productPurchased);
            deviceCheck.setEnvironment(getEnvironment(eid, customerCode, deviceId, productPurchased.getErp()));
            if (erp || erp2) {
                deviceCheck.setDatabase(getDatabase(eid, deviceId, dbIdList));
            }

            deviceCheckList.add(deviceCheck);
        });

        return () -> deviceCheckList;
    }

    protected Callable<Object> buildErp2Check(String customerCode) {
        List<String> erp2ProductCodeList = ERP2_PRODUCT_LIST.stream()
                .map(SqlServerErpReport.Product.ProductModel::getCode)
                .collect(Collectors.toList());
        List<Map<String, String>> productCodeListResult = sqlServerErpReportMapper.getProductCode(customerCode, erp2ProductCodeList);
        List<String> productCodeList = productCodeListResult.stream()
                .flatMap(map -> map.values().stream())
                .collect(Collectors.toList());

        List<SqlServerErpReport.DataCheck.ProductsInstallInfo> productsInfoList = new ArrayList<>();
        List<SqlServerErpReport.Version> versionList = new ArrayList<>();
        productCodeList.forEach(productCode -> {
            SqlServerErpReport.DataCheck.ProductsInstallInfo productsInfo = new SqlServerErpReport.DataCheck.ProductsInstallInfo();
            productsInfo.setProductName(productCode);
            productsInfo.setFolderPath("");
            productsInfo.setFolderSize("");
            productsInfo.setFileCount("");
            productsInfoList.add(productsInfo);

            SqlServerErpReport.Version version = new SqlServerErpReport.Version();
            version.setProductName(productCode);
            version.setProductVersion("-");
            version.setLicensesCount("-");
            versionList.add(version);
        });

        SqlServerErpReport.DataCheck.HrmInfo hrmInfo = createInfo(SqlServerErpReport.DataCheck.HrmInfo::new);
        if (productCodeList.contains("HRM")) {
            hrmInfo.setIsWith(YES);
        }

        SqlServerErpReport.DataCheck dataCheck = new SqlServerErpReport.DataCheck();
        dataCheck.setProductsInstallInfo(productsInfoList);
        dataCheck.setHrmInfo(hrmInfo);
        dataCheck.setIsoModule(createInfo(SqlServerErpReport.DataCheck.IsoModule::new));

        SqlServerErpReport.Erp2Check erp2Check = new SqlServerErpReport.Erp2Check();
        erp2Check.setDataCheck(dataCheck);
        erp2Check.setVersionList(versionList);

        // 建立 erp2Check(預設為null)
        return () -> erp2Check;
    }

    protected Callable<Object> buildOtherCheck(String eid) {
        SqlServerErpReport.CheckItems checkItems = new SqlServerErpReport.CheckItems();
        checkItems.setOtherConnection(createInfo(SqlServerErpReport.CheckItems.OtherConnection::new));
        checkItems.setHasReplication(createInfo(SqlServerErpReport.CheckItems.HasReplication::new));
        checkItems.setHasDecimal(hasDecimal(eid));
        checkItems.setCrossConnection(createInfo(SqlServerErpReport.CheckItems.CrossConnection::new));
        checkItems.setErpEtimatedHours("");
        checkItems.setErp2EtimatedHours("");

        SqlServerErpReport.OtherCheck otherCheck = new SqlServerErpReport.OtherCheck();
        otherCheck.setBaseSpecification(ALL_SPECIFICATION);
        otherCheck.setCheckItems(checkItems);

        // 建立 otherCheck(預設為null)
        return () -> otherCheck;
    }

    private List<Map<String, Object>> getDeviceIdAndDbId(List<String> deviceIdList) {
        List<Map<String, Object>> result = new ArrayList<>();

        deviceIdList.forEach(deviceId -> {
            Map<String, Object> map = new HashMap<>();
            map.put("deviceId", deviceId);
            List<String> dbIdList = deviceV2Mapper.selectDeviceDataSourceByDeviceId(deviceId).stream()
                    .filter(data -> "mssql_v2".equals(data.getDbType()) && !data.getIsDelete())
                    .map(AiopsKitDeviceDataSource::getDbId)
                    .collect(Collectors.toList());
            if (!dbIdList.isEmpty()) {
                map.put("scoreDbIdList", dbIdList);
            }
            result.add(map);
        });

        return result;
    }

    private SqlServerErpReport.Environment getEnvironment(String eid, String customerCode, String deviceId, String erp) {
        SqlServerErpReport.Environment environment = new SqlServerErpReport.Environment();

        // 加入 environment 資料
        environment.setDeviceId(deviceId);
        environment.setSrvSpec(getSrvSpec(eid, deviceId));
        environment.setWebSites(getWebSites(eid, deviceId));
        environment.setNetworks(getNetworks(eid, deviceId));
        if (!erp.isEmpty()) {
            environment.setErpSetting(getErpSetting(eid, customerCode, deviceId, erp));
        }

        return environment;
    }

    private List<SqlServerErpReport.Database> getDatabase(String eid, String deviceId, List<String> dbIdList) {
        List<SqlServerErpReport.Database> databaseList = new ArrayList<>();

        // 加入 database 資料
        dbIdList.forEach(dbId -> {
            SqlServerErpReport.Database database = new SqlServerErpReport.Database();
            database.setDeviceId(deviceId);
            database.setSourceDbId(dbId);
            database.setServerInfo(getServerInfo(eid, dbId));
            database.setDbNameSize(getDbNameSize(eid, dbId));
            databaseList.add(database);
        });
        return databaseList;
    }

    private Map<String, Object> getSrvSpec(String eid, String deviceId) {
        Map<String, Object> srvSpec = new HashMap<>();
        srvSpec.put("osInfo", getOsInfo(eid, deviceId));
        srvSpec.put("hardwareInfo", getHardwareInfo(eid, deviceId));

        return srvSpec;
    }

    private List<Map<String, Object>> getWebSites(String eid, String deviceId) {
        StringBuilder sb = new StringBuilder();
        sb.append("select ");
        sb.append("SiteName as siteName, propertyName, propertyLocation, state, bindings ");
        sb.append("from servicecloud.Windows_IISSitesInfo_sr_primary ");
        sb.append("where ");
        sb.append("eid = '").append(eid).append("' and ");
        sb.append("deviceId = '").append(deviceId).append("' and ");
        sb.append("collectedTime = (");
        sb.append("select max(collectedTime) ");
        sb.append("from servicecloud.Windows_IISSitesInfo_sr_primary ");
        sb.append("where ");
        sb.append("eid = '").append(eid).append("' and ");
        sb.append("deviceId = '").append(deviceId).append("' and ");
        sb.append("collectedTime >= '").append(getSevenDayAgo()).append("'");
        sb.append(") ");
        String sql = StringUtil.toString(sb);

        return bigDataUtil.srQuery(sql);
    }

    private List<Map<String, Object>> getNetworks(String eid, String deviceId) {
        StringBuilder sb = new StringBuilder();
        sb.append("select ");
        sb.append("get_json_string(parse_json(network_adapter_info.value), '$.network_adapter_name') as network_adapter_name, ");
        sb.append("get_json_string(parse_json(network_adapter_info.value), '$.network_adapter_mac') as network_adapter_mac, ");
        sb.append("get_json_string(parse_json(network_adapter_info.value), '$.network_adapter_dhcp_enabled') as network_adapter_dhcp_enabled, ");
        sb.append("get_json_string(parse_json(network_adapter_info.value), '$.network_adapter_ip[1]') as network_adapter_ip, ");
        sb.append("get_json_string(parse_json(network_adapter_info.value), '$.network_adapter_subnet_mask[0]') as network_adapter_subnet_mask, ");
        sb.append("get_json_string(parse_json(network_adapter_info.value), '$.network_adapter_gateway') as network_adapter_default_gateway, ");
        sb.append("array_join(cast(get_json_string(parse_json(network_adapter_info.value), '$.network_adapter_dns') as array<string>), ',') as network_adapter_dns ");
        sb.append("from servicecloud.DeviceAdvancedInfoCollected_sr_primary, ");
        sb.append("json_each(parse_json(network_adapter_info)) as network_adapter_info ");
        sb.append("where ");
        sb.append("eid = '").append(eid).append("' and ");
        sb.append("deviceId = '").append(deviceId).append("'");
        String sql = StringUtil.toString(sb);

        return bigDataUtil.srQuery(sql);
    }

    private Map<String, Object> getErpSetting(String eid, String customerCode, String deviceId, String erp) {
        Map<String, Object> erpSetting = new HashMap<>();
        erpSetting.put("software", getSoftware(eid, customerCode, deviceId, erp));
        erpSetting.put("modules", getModules(eid, deviceId));
        erpSetting.put("dgwModules", getDgwModules(customerCode, erp));
        erpSetting.put("companyNameSize", getCompanyNameSize(eid, deviceId));

        return erpSetting;
    }

    private Map<String, Object> getServerInfo(String eid, String sourceDbId) {
        StringBuilder sb = new StringBuilder();
        sb.append("select ");
        sb.append("server_name, concat(product, ' ', edition) as product_name, platform, product_version, language, ");
        sb.append("physical_memory_total_gb as physical_memory, processor_count as processors, collation, dataPath, ");
        sb.append("logPath, backupPath, '' as sqlBinarySort ");
        sb.append("from servicecloud.MsSQLInstanceInfo msli ");
        sb.append("left join ");
        sb.append("(select * from servicecloud.MsSQL_PresetPathInfo ");
        sb.append("where eid = '").append(eid).append("' and ");
        sb.append("source_db_id = '").append(sourceDbId).append("' and ");
        sb.append("collectedTime = (");
        sb.append("select max(collectedTime) ");
        sb.append("from servicecloud.MsSQL_PresetPathInfo ");
        sb.append("where ");
        sb.append("eid = '").append(eid).append("' and ");
        sb.append("source_db_id = '").append(sourceDbId).append("' and ");
        sb.append("collectedTime >= '").append(getSevenDayAgo()).append("'");
        sb.append(")) msppi ");
        sb.append("on msli.eid = msppi.eid and msli.source_db_id = msppi.source_db_id ");
        sb.append("where ");
        sb.append("msli.eid = '").append(eid).append("' and ");
        sb.append("msli.source_db_id = '").append(sourceDbId).append("' and ");
        sb.append("msli.collectedTime = (");
        sb.append("select max(collectedTime) ");
        sb.append("from servicecloud.MsSQLInstanceInfo ");
        sb.append("where ");
        sb.append("eid = '").append(eid).append("' and ");
        sb.append("source_db_id = '").append(sourceDbId).append("' and ");
        sb.append("collectedTime >= '").append(getSevenDayAgo()).append("'");
        sb.append(") ");
        String sql = StringUtil.toString(sb);

        List<Map<String, Object>> data = bigDataUtil.srQuery(sql);
        if (data.isEmpty()) {
            return Collections.emptyMap();
        }

        return data.get(0);
    }

    private Map<String, Object> getDbNameSize(String eid, String sourceDbId) {
        StringBuilder sb = new StringBuilder();
        sb.append("select ");
        sb.append("database_name as database_id, ");
        sb.append("(size / 1024) as database_size, ");
        sb.append("physical_name, ");
        sb.append("case ");
        sb.append("when disk_fstype = 0 then 'ROWS' ");
        sb.append("when disk_fstype = 1 then 'LOG' ");
        sb.append("when disk_fstype = 2 then 'FILESTREAM' ");
        sb.append("when disk_fstype = 3 then 'FULLTEXT' ");
        sb.append("end as type_desc ");
        sb.append("from servicecloud.MsSQLDataBaseFileCollected ");
        sb.append("where ");
        sb.append("eid = '").append(eid).append("' and ");
        sb.append("source_db_id = '").append(sourceDbId).append("' and ");
        sb.append("collectedTime = (");
        sb.append("select max(collectedTime) ");
        sb.append("from servicecloud.MsSQLDataBaseFileCollected ");
        sb.append("where ");
        sb.append("eid = '").append(eid).append("' and ");
        sb.append("source_db_id = '").append(sourceDbId).append("' and ");
        sb.append("collectedTime >= '").append(getSevenDayAgo()).append("'");
        sb.append(") order by binary(lower(database_name)) ");
        String sql = StringUtil.toString(sb);

        List<Map<String, Object>> data = bigDataUtil.srQuery(sql);

        Map<String, Object> map = new HashMap<>();
        map.put("totalCount", data.size());
        map.put("totalSize", data.stream().map(m -> new BigDecimal(ObjectUtils.toString(m.get("database_size")))).reduce(BigDecimal.ZERO, BigDecimal::add));
        map.put("list", data);

        return map;
    }

    private Map<String, Object> getOsInfo(String eid, String deviceId) {
        StringBuilder sb = new StringBuilder();
        sb.append("select ");
        sb.append("get_json_string(parse_json(domain_info), '$.domain_machine_name') as domain_machine_name, ");
        sb.append("'' as erp2products, ");
        sb.append("'' as db_or_ap, ");
        sb.append("get_json_string(parse_json(domain_info), '$.domain_groups') as domain_groups, ");
        sb.append("get_json_string(parse_json(os_info), '$.os_product_name') as os_product_name, ");
        sb.append("case ");
        sb.append("when get_json_string(parse_json(os_info), '$.os_bits') = 'win64' then '64-bit' ");
        sb.append("when get_json_string(parse_json(os_info), '$.os_bits') = 'win32' then '32-bit' ");
        sb.append("else get_json_string(parse_json(os_info), '$.os_bits') end as os_bits, ");
        sb.append("get_json_string(parse_json(processor_info), '$.processor_name') as processor_name, ");
        sb.append("get_json_string(parse_json(physical_memory_info), '$.[0].physical_memory_total_gb') as physical_memory_total_gb, ");
        sb.append("'' as ipAddress, ");
        sb.append("concat(msli.product, ");
        sb.append("case ");
        sb.append("when starts_with(msli.product_version, '17') then ' 2025 ' ");
        sb.append("when starts_with(msli.product_version, '16') then ' 2022 ' ");
        sb.append("when starts_with(msli.product_version, '15') then ' 2019 ' ");
        sb.append("when starts_with(msli.product_version, '14') then ' 2017 ' ");
        sb.append("when starts_with(msli.product_version, '13') then ' 2016 ' ");
        sb.append("when starts_with(msli.product_version, '12') then ' 2014 ' ");
        sb.append("when starts_with(msli.product_version, '11') then ' 2012 ' ");
        sb.append("when starts_with(msli.product_version, '10') then ' 2008 ' ");
        sb.append("when starts_with(msli.product_version, '9') then ' 2005 ' ");
        sb.append("when starts_with(msli.product_version, '8') then ' 2000 ' ");
        sb.append("else 'Unknown' end, msli.edition) as sql_version ");
        sb.append("from servicecloud.DeviceAdvancedInfoCollected_sr_primary daicsp ");
        sb.append("left join servicecloud.MsSQLInstanceInfo msli ");
        sb.append("on daicsp.deviceId = msli.deviceId ");
        sb.append("where ");
        sb.append("daicsp.eid = '").append(eid).append("' and ");
        sb.append("daicsp.deviceId = '").append(deviceId).append("'");
        String sql = StringUtil.toString(sb);

        List<Map<String, Object>> data = bigDataUtil.srQuery(sql);
        if (data.isEmpty()) {
            return Collections.emptyMap();
        }

        return data.get(0);
    }

    private List<Map<String, Object>> getHardwareInfo(String eid, String deviceId) {
        StringBuilder sb = new StringBuilder();
        sb.append("select ");
        sb.append("disk__path, disk__fstype, disk__total, disk__free, disk__used, disk__used_percent ");
        sb.append("from servicecloud.DiskCollected_sr_duplicate ");
        sb.append("where ");
        sb.append("eid = '").append(eid).append("' and ");
        sb.append("deviceId = '").append(deviceId).append("' and ");
        sb.append("collectedTime = (");
        sb.append("select max(collectedTime) ");
        sb.append("from servicecloud.DiskCollected_sr_duplicate ");
        sb.append("where ");
        sb.append("eid = '").append(eid).append("' and ");
        sb.append("deviceId = '").append(deviceId).append("' and ");
        sb.append("collectedTime >= '").append(getSevenDayAgo()).append("'");
        sb.append(") ");
        String sql = StringUtil.toString(sb);

        return bigDataUtil.srQuery(sql);
    }

    private Map<String, Object> getSoftware(String eid, String customerCode, String deviceId, String erp) {
        Map<String, Object> result = new HashMap<>();

        StringBuilder sb = new StringBuilder();
        sb.append("select ");
        sb.append("productVersion, clientModi, serverModi, ");
        sb.append("case when admnt_id = 'VoucherServer' then 'WebDAV' else rtBuilderDataOption end as rtBuilderDataOption, ");
        sb.append("case when admnt_id = 'VoucherServer' ");
        sb.append("then concat('主機類型:', ServerType '<br/>網址:', Path, '<br/>通訊埠:', Port, '<br/>WebDAV類型:', WebDAVType, '<br/>站台名稱:', SiteName, <br/>帳號名稱:, UserName) ");
        sb.append("else rtBuilderDataPath end as rtBuilderDataPath, ");
        sb.append("case when admnt_id = 'DMSServer' then 'WebDAV' else relationDataOption end as relationDataOption, ");
        sb.append("case when admnt_id = 'DMSServer' ");
        sb.append("then concat('主機類型:', ServerType '<br/>網址:', Path, '<br/>通訊埠:', Port, '<br/>WebDAV類型:', WebDAVType, '<br/>站台名稱:', SiteName, <br/>帳號名稱:, UserName) ");
        sb.append("else relationDataPath end as relationDataPath, ");
        sb.append("case when admnt_id = 'DMSServer' then 'WebDAV' else fileServerUNCOption end as fileServerUNCOption, ");
        sb.append("case when admnt_id = 'DMSServer' ");
        sb.append("then concat('主機類型:', ServerType '<br/>網址:', Path, '<br/>通訊埠:', Port, '<br/>WebDAV類型:', WebDAVType, '<br/>站台名稱:', SiteName, <br/>帳號名稱:, UserName) ");
        sb.append("else fileServerUNCPath end as fileServerUNCPath, ");
        sb.append("isExistERP ");
        sb.append("from servicecloud.ERP_SoftSetting_sr_primary esssp");
        sb.append("left join ");
        sb.append("(select * from servicecloud.ERP_WebDAVInfo_sr_primary ");
        sb.append("where ");
        sb.append("eid = '").append(eid).append("' and ");
        sb.append("deviceId = '").append(deviceId).append("' and ");
        sb.append("collectedTime = (");
        sb.append("select max(collectedTime) ");
        sb.append("from servicecloud.ERP_WebDAVInfo_sr_primary ");
        sb.append("where ");
        sb.append("eid = '").append(eid).append("' and ");
        sb.append("deviceId = '").append(deviceId).append("' and ");
        sb.append("collectedTime >= '").append(getSevenDayAgo()).append("'");
        sb.append(")) ewdisp ");
        sb.append("on esssp.eid = ewdisp.eid and esssp.deviceId = ewdisp.deviceId");
        sb.append("where ");
        sb.append("esssp.eid = '").append(eid).append("' and ");
        sb.append("esssp.deviceId = '").append(deviceId).append("' and ");
        sb.append("esssp.collectedTime = (");
        sb.append("select max(collectedTime) ");
        sb.append("from servicecloud.ERP_SoftSetting_sr_primary ");
        sb.append("where ");
        sb.append("eid = '").append(eid).append("' and ");
        sb.append("deviceId = '").append(deviceId).append("' and ");
        sb.append("collectedTime >= '").append(getSevenDayAgo()).append("'");
        sb.append(") ");


        String sql = StringUtil.toString(sb);

        List<Map<String, Object>> data = bigDataUtil.srQuery(sql);
        if (data.isEmpty()) {
            return result;
        }
        result.putAll(data.get(0));

        // 憑證設定選項(WebDAV)
        sb = new StringBuilder();
        sb.append("select ");
        sb.append("SiteName as name, UserName as account, '' as password, '' as ip, Port as port, Path as path ");
        sb.append("from servicecloud.ERP_WebDAVInfo_sr_primary ");
        sb.append("where ");
        sb.append("eid = '").append(eid).append("' and ");
        sb.append("deviceId = '").append(deviceId).append("' and ");
        sb.append("admnt_id = 'VoucherServer' and ");
        sb.append("collectedTime = (");
        sb.append("select max(collectedTime) ");
        sb.append("from servicecloud.ERP_WebDAVInfo_sr_primary ");
        sb.append("where ");
        sb.append("eid = '").append(eid).append("' and ");
        sb.append("deviceId = '").append(deviceId).append("' and ");
        sb.append("admnt_id = 'VoucherServer' and ");
        sb.append("collectedTime >= '").append(getSevenDayAgo()).append("'");
        sb.append(") ");
        sql = StringUtil.toString(sb);

        data = bigDataUtil.srQuery(sql);
        result.put("relationDataWebDAV", data.isEmpty() ? new HashMap<>() : data.get(0));

        // 圖檔設定選項(WebDAV) & 文檔設定選項(WebDAV)
        sb = new StringBuilder();
        sb.append("select ");
        sb.append("SiteName as name, UserName as account, '' as password, '' as ip, Port as port, Path as path ");
        sb.append("from servicecloud.ERP_WebDAVInfo_sr_primary ");
        sb.append("where ");
        sb.append("eid = '").append(eid).append("' and ");
        sb.append("deviceId = '").append(deviceId).append("' and ");
        sb.append("admnt_id = 'DMSServer' and ");
        sb.append("collectedTime = (");
        sb.append("select max(collectedTime) ");
        sb.append("from servicecloud.ERP_WebDAVInfo_sr_primary ");
        sb.append("where ");
        sb.append("eid = '").append(eid).append("' and ");
        sb.append("deviceId = '").append(deviceId).append("' and ");
        sb.append("admnt_id = 'DMSServer' and ");
        sb.append("collectedTime >= '").append(getSevenDayAgo()).append("'");
        sb.append(") ");
        sql = StringUtil.toString(sb);

        data = bigDataUtil.srQuery(sql);
        result.put("relationDataWebDAV", data.isEmpty() ? new HashMap<>() : data.get(0));
        result.put("fileServerUNCWebDAV", data.isEmpty() ? new HashMap<>() : data.get(0));

        // ERP產品線的授權人數
        Integer licensesCount = sqlServerErpReportMapper.getLicensesCount(customerCode, erp);
        result.put("licensesCount", licensesCount);

        // POS產品線的授權人數
        Integer posLicensesCount = sqlServerErpReportMapper.getLicensesCount(customerCode, "POS");
        result.put("posLicensesCount", posLicensesCount);

        // AUS(自動更新) 欄位
        sb = new StringBuilder();
        sb.append("select ");
        sb.append("protocol, name, account, password, ip, port, path, enabled, '' as isWith");
        sb.append("from servicecloud.ERP_AUSInfo_sr_primary ");
        sb.append("where ");
        sb.append("eid = '").append(eid).append("' and ");
        sb.append("deviceId = '").append(deviceId).append("' and ");
        sb.append("protocol not in ('', '-99') and ");
        sb.append("enabled <> 'N'");
        sb.append("collectedTime = (");
        sb.append("select max(collectedTime) ");
        sb.append("from servicecloud.ERP_AUSInfo_sr_primary ");
        sb.append("where ");
        sb.append("eid = '").append(eid).append("' and ");
        sb.append("deviceId = '").append(deviceId).append("' and ");
        sb.append("collectedTime >= '").append(getSevenDayAgo()).append("' and ");
        sb.append("protocol not in ('', '-99') and ");
        sb.append("enabled <> 'N'");
        sb.append(") ");
        sql = StringUtil.toString(sb);

        data = bigDataUtil.srQuery(sql);
        result.put("aus", data.isEmpty() ? new HashMap<>() : data.get(0));

        return result;
    }

    private Map<String, Object> getModules(String eid, String deviceId) {
        Map<String, Object> result;

        StringBuilder sb = new StringBuilder();
        sb.append("select ");
        sb.append("moduleName as title, moduleCode as code");
        sb.append("from servicecloud.ERPModuleInfo_sr_primary ");
        sb.append("where ");
        sb.append("eid = '").append(eid).append("' and ");
        sb.append("deviceId = '").append(deviceId).append("' and ");
        sb.append("collectedTime = (");
        sb.append("select max(collectedTime) ");
        sb.append("from servicecloud.ERPModuleInfo_sr_primary ");
        sb.append("where ");
        sb.append("eid = '").append(eid).append("' and ");
        sb.append("deviceId = '").append(deviceId).append("' and ");
        sb.append("collectedTime >= '").append(getSevenDayAgo()).append("'");
        sb.append(") ");
        String sql = StringUtil.toString(sb);

        // 已購模組
        List<Map<String, Object>> data = bigDataUtil.srQuery(sql);
        result = getModulesInfo(data);

        return result;
    }

    private Map<String, Object> getDgwModules(String customerCode, String erp) {
        Map<String, Object> result;

        List<Map<String, Object>> data = sqlServerErpReportMapper.getDgwModules(customerCode, erp);
        result = getModulesInfo(data);

        return result;
    }

    private Map<String, Object> getCompanyNameSize(String eid, String deviceId) {
        StringBuilder sb = new StringBuilder();
        sb.append("select ");
        sb.append("database_erp, database_id, erp_dscmb002, database_size ");
        sb.append("from servicecloud.ERP_CompanyDatabaseInfo_sr_primary ");
        sb.append("where ");
        sb.append("eid = '").append(eid).append("' and ");
        sb.append("deviceId = '").append(deviceId).append("' and ");
        sb.append("collectedTime = (");
        sb.append("select max(collectedTime) ");
        sb.append("from servicecloud.ERP_CompanyDatabaseInfo_sr_primary ");
        sb.append("where ");
        sb.append("eid = '").append(eid).append("' and ");
        sb.append("deviceId = '").append(deviceId).append("' and ");
        sb.append("collectedTime >= '").append(getSevenDayAgo()).append("'");
        sb.append(") ");
        String sql = StringUtil.toString(sb);

        List<Map<String, Object>> data = bigDataUtil.srQuery(sql);

        Map<String, Object> map = new HashMap<>();
        map.put("totalCount", data.size());
        map.put("totalSize", data.stream().map(m -> new BigDecimal(ObjectUtils.toString(m.get("database_size")))).reduce(BigDecimal.ZERO, BigDecimal::add));
        map.put("list", data);

        return map;
    }

    private DeviceInfo getDeviceInfo(Map<String, Object> param) {
        return deviceV2Mapper.getDeviceList(param).isEmpty() ? null : deviceV2Mapper.getDeviceList(param).get(0);
    }

    private String getProductValues(List<String> productCodeList) {
        List<SqlServerErpReport.Product.ProductModel> productList =
                Stream.concat(ERP_PRODUCT_LIST.stream(), ERP2_PRODUCT_LIST.stream()).collect(Collectors.toList());

        List<String> productValueList = new ArrayList<>();
        productCodeList.forEach(productCode -> {
            String value = productList.stream().filter(item -> productCode.equals(item.getCode()))
                    .findFirst()
                    .map(SqlServerErpReport.Product.ProductModel::getValue)
                    .orElse("");
            if (value.isEmpty()) {
                return;
            }
            productValueList.add(value);
        });

        return String.join("、", productValueList);
    }

    private SqlServerErpReport.ProductPurchased getProductCodeForDeviceId(DeviceInfo deviceInfo) {
        SqlServerErpReport.ProductPurchased productPurchased = new SqlServerErpReport.ProductPurchased();
        List<String> erp2 = new ArrayList<>();

        deviceInfo.getSupplierProductList().stream()
                .map(SupplierProduct::getProductCode)
                .forEach(productCode -> {
                    if (ERP_PRODUCT_LIST.stream().anyMatch(item -> item.getCode().equals(productCode))) {
                        productPurchased.setErp(productCode);
                    }
                    if (ERP2_PRODUCT_LIST.stream().anyMatch(item -> item.getCode().equals(productCode))) {
                        erp2.add(productCode);
                    }
                });
        productPurchased.setErp2(erp2);

        return productPurchased;
    }

    private String getSevenDayAgo() {
        LocalDate sevenDaysAgo = LocalDate.now().minusDays(7);
        LocalDateTime startOfDay = sevenDaysAgo.atStartOfDay();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

        return startOfDay.format(formatter);
    }

    private Map<String, Object> getModulesInfo(List<Map<String, Object>> data) {
        Map<String, Object> result = new HashMap<>();

        // 判定是否有 EIN(電子發票系統) 模組
        SqlServerErpReport.Info eInvoiceContent = createInfo(SqlServerErpReport.Info::new);
        boolean eInvoiceIsWith = data.stream().anyMatch(item -> "EIN".equals(item.get("code")));
        eInvoiceContent.setIsWith(eInvoiceIsWith ? YES : NO);

        // 判定是否有 FTS(傳輸系統) 模組
        SqlServerErpReport.Info transmitSystemContent = createInfo(SqlServerErpReport.Info::new);
        boolean useTransmitSystemIsWith = data.stream().anyMatch(item -> "FTS".equals(item.get("code")));
        transmitSystemContent.setIsWith(useTransmitSystemIsWith ? YES : NO);

        // 判定是否有 MTP(多角貿易系統) 模組
        SqlServerErpReport.Info triangleTradeContent = createInfo(SqlServerErpReport.Info::new);
        boolean triangleTradeIsWith = data.stream().anyMatch(item -> "MTP".equals(item.get("code")));
        triangleTradeContent.setIsWith(triangleTradeIsWith ? YES : NO);

        result.put("purchased", data);
        result.put("eInvoice", eInvoiceContent);
        result.put("eInvoiceUsed", createInfo(SqlServerErpReport.Info::new));
        result.put("transmitSystem", transmitSystemContent);
        result.put("transmitSystemUsed", createInfo(SqlServerErpReport.Info::new));
        result.put("triangleTrade", triangleTradeContent);
        result.put("triangleTradeUsed", createInfo(SqlServerErpReport.Info::new));

        return result;
    }

    private SqlServerErpReport.CheckItems.HasDecimal hasDecimal(String eid) {
        StringBuilder sb = new StringBuilder();
        sb.append("select count(*) as count ");
        sb.append("from servicecloud.ERP_CompanyDatabaseInfo_sr_primary ");
        sb.append("where ");
        sb.append("eid = '").append(eid).append("' and ");
        sb.append("collectedTime = (");
        sb.append("select max(collectedTime) ");
        sb.append("from servicecloud.ERP_CompanyDatabaseInfo_sr_primary ");
        sb.append("where ");
        sb.append("eid = '").append(eid).append("' and ");
        sb.append("collectedTime >= '").append(getSevenDayAgo()).append("'");
        sb.append(") ");
        String sql = StringUtil.toString(sb);

        SqlServerErpReport.CheckItems.HasDecimal hasDecimal = createInfo(SqlServerErpReport.CheckItems.HasDecimal::new);
        if (IntegerUtil.objectToInteger(bigDataUtil.srQuery(sql).get(0).get("count")) > 0) {
            hasDecimal.setIsWith(YES);
        }

        return hasDecimal;
    }

    private <U extends SqlServerErpReport.Info> U createInfo(Supplier<U> constructor) {
        U info = constructor.get();
        info.setIsWith("");
        info.setComment("");
        return info;
    }

    @Override
    protected Map<String, Object> getOtherDeviceInfo(String appCode, long eid, String deviceId, String dbId) {
        return Collections.emptyMap();
    }
}
