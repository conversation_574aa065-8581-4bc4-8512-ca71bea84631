package com.digiwin.escloud.aiobasic.vulnerability.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.digiwin.escloud.aiobasic.assetrisk.model.enums.*;
import com.digiwin.escloud.aiobasic.assetrisk.model.enums.VulnerabilityLanguage;
import com.digiwin.escloud.aiobasic.assetrisk.service.CommonEnumRegistry;
import com.digiwin.escloud.aiobasic.edr.util.DateUtils;
import com.digiwin.escloud.aiobasic.report.model.*;
import com.digiwin.escloud.aiobasic.report.model.VulnerabilityLevel;
import com.digiwin.escloud.aiobasic.report.model.VulnerabilityProjectScanRecord;
import com.digiwin.escloud.aiobasic.report.model.VulnerabilityStorehouse;
import com.digiwin.escloud.aiobasic.vulnerability.dao.VulnerabilityProjectAssetMapper;
import com.digiwin.escloud.aiobasic.vulnerability.dao.VulnerabilityProjectMapper;
import com.digiwin.escloud.aiobasic.vulnerability.dao.VulnerabilityProjectScanMapper;
import com.digiwin.escloud.aiobasic.vulnerability.dao.VulnerabilityReportRecordMapper;
import com.digiwin.escloud.aiobasic.vulnerability.model.*;
import com.digiwin.escloud.aiobasic.vulnerability.model.VulnerabilityProjectAsset;
import com.digiwin.escloud.aiobasic.vulnerability.model.enums.VulnerabilityProjectStatus;
import com.digiwin.escloud.aiobasic.vulnerability.model.excel.VulnerabilityScanData;
import com.digiwin.escloud.aiobasic.vulnerability.model.param.VulnerabilityReportRecordParam;
import com.digiwin.escloud.aiobasic.vulnerability.service.VulnerabilityProjectService;
import com.digiwin.escloud.aiobasic.vulnerability.service.VulnerabilityReportRecordService;
import com.digiwin.escloud.aiouser.model.tenant.TenantMapDTO;
import com.digiwin.escloud.common.feign.AioItmsFeignClient;
import com.digiwin.escloud.common.feign.AioUserFeignClient;
import com.digiwin.escloud.common.model.ResponseCode;
import com.digiwin.escloud.common.response.BaseResponse;
import com.digiwin.escloud.common.util.*;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.text.ParseException;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.digiwin.escloud.common.model.ResponseCode.VULNERABILITY_SCAN_DATA;
import static com.digiwin.escloud.common.util.DateUtil.DATE_TIME_FORMATTER;

@Service
public class VulnerabilityReportRecordServiceImpl implements VulnerabilityReportRecordService {
    @Resource
    private VulnerabilityReportRecordMapper vulnerabilityReportRecordMapper;
    @Resource
    private ServiceMaintenanceService serviceMaintenanceService;

    @Resource
    private CommonEnumRegistry commonEnumRegistry;
    @Resource
    private VulnerabilityProjectMapper vulnerabilityProjectMapper;

    @Resource
    private VulnerabilityProjectService vulnerabilityProjectService;
    @Resource
    private VulnerabilityProjectAssetMapper vulnerabilityProjectAssetMapper;
    @Resource
    private VulnerabilityProjectScanMapper vulnerabilityProjectScanMapper;
    @Resource
    private AioUserFeignClient aioUserFeignClient;
    @Resource
    private AioItmsFeignClient aioItmsFeignClient;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public BaseResponse reportGenerate(VulnerabilityReportRecord record) throws ParseException {
        // region 校驗參數
        if (Objects.isNull(record.getEid())) {
            return BaseResponse.error(ResponseCode.RISK_EID_EMPTY);
        }

        if (StringUtils.isBlank(record.getProjectId())) {
            return BaseResponse.error(ResponseCode.VULNERABILITY_PROJECT_ID_IS_NEED);
        }

        if (StringUtils.isBlank(record.getReport())) {
            return BaseResponse.error(ResponseCode.VULNERABILITY_REPORT_MAN_IS_NEED);
        }

        if (StringUtils.isBlank(record.getReportDateString())) {
            return BaseResponse.error(ResponseCode.VULNERABILITY_REPORT_DATE_IS_NEED);
        }
        if (Objects.isNull(record.getLanguage())) {
            return BaseResponse.error(ResponseCode.VULNERABILITY_LANGUAGE);
        }
        // endregion

        // 1.取得租戶客代
        TenantMapDTO tenantMapDTO = getTenantMap(record.getEid());
        if (Objects.isNull(tenantMapDTO)) {
            return BaseResponse.error(ResponseCode.TENANT_EMPTY);
        }

        VulnerabilityProject vulnerabilityProject = vulnerabilityProjectMapper.selectVulnerabilityProjectById(record.getProjectId());

        // 2.保存报告表,状态为生成中
        buildReportRecord(record, tenantMapDTO, vulnerabilityProject);

        // 3.保存设备mapping表
        List<VulnerabilityProjectAsset> projectAssets = getProjectAssets(record);
        List<VulnerabilityReportAssetMapping> mappings = buildMappings(record, projectAssets);
        List<com.digiwin.escloud.aiobasic.report.model.VulnerabilityProjectAsset> commonProjectAssets =
                buildCommonProjectAssets(record, projectAssets);
        if (CollectionUtil.isEmpty(mappings)) {
            return BaseResponse.error(VULNERABILITY_SCAN_DATA);
        }

        // 4.根据设备mapping表查询设备信息-漏洞信息 和 漏洞信息
        Set<String> assetIps = projectAssets.stream().map(VulnerabilityProjectAsset::getAssetIp).collect(Collectors.toSet());
        List<VulnerabilityProjectScanRecord> vulnerabilityProjectScanRecords = scanRecordGetByProjectId(record.getProjectId(), assetIps);
        Set<String> vulnerabilityNumber = vulnerabilityProjectScanRecords.stream()
                .map(VulnerabilityProjectScanRecord::getVulnerabilityNumber)
                .collect(Collectors.toSet());
        if (CollectionUtil.isEmpty(vulnerabilityNumber)) {
            return BaseResponse.error(VULNERABILITY_SCAN_DATA);
        }

        // 5.保存數據
        vulnerabilityReportRecordMapper.insertVulnerabilityReportRecord(record);
        vulnerabilityReportRecordMapper.insertVulnerabilityReportAssetMapping(mappings);

        // 6.獲取弱點內容
        List<VulnerabilityStorehouse> vulnerabilityStorehouses = vulnerabilityGetByNumber(record.getLanguage(), vulnerabilityNumber);

        // 7.保存es
        VulnerabilityEsReport vulnerabilityEsReport = buildEsReport(
                record, vulnerabilityProject, record.getLanguage(),
                commonProjectAssets, vulnerabilityStorehouses, vulnerabilityProjectScanRecords
        );
        aioItmsFeignClient.vulnerabilityReportGenerate(vulnerabilityEsReport);

        // 8.保存到es就是算成完成，修改状态
        record.setReportStatus(VulnerabilityReportStatus.ALREADY_GENERATED);
        vulnerabilityReportRecordMapper.updateVulnerabilityReportRecord(record);
        vulnerabilityProjectService.updateVulnerabilityProjectStatus(vulnerabilityProject.getEid());
        return BaseResponse.ok(record.getId());
    }

    @Override
    public BaseResponse reportGet(VulnerabilityReportRecordParam record) {
        record.setEidList(serviceMaintenanceService.getServiceEidList(vulnerabilityProjectMapper.selectVulnerabilityProjectEidList()));

        PageHelper.startPage(record.getPageNum(), record.getPageSize());

        if (StringUtils.isNotBlank(record.getReportEndDate())) {
            record.setReportEndDateD(combineDate(record.getReportEndDate()));
        }
        if (StringUtils.isNotBlank(record.getReportStartDate())) {
            record.setReportStartDateD(combineDate(record.getReportStartDate()));
        }

        List<VulnerabilityReportRecord> vulnerabilityReportRecords = vulnerabilityReportRecordMapper
                .selectReportByParam(record);
        Map<String, List<VulnerabilityProjectAsset>> reportIdMap = reportRecordAssetGet(vulnerabilityReportRecords.stream().map(VulnerabilityReportRecord::getId).collect(Collectors.toList()));
        vulnerabilityReportRecords.forEach(
                i -> i.setAssetCount(reportIdMap.getOrDefault(i.getId(),new ArrayList<>()).size())
        );
        return BaseResponse.ok(new PageInfo<>(vulnerabilityReportRecords));
    }

    @Override
    public BaseResponse reportAssetGet(String recordId) {

        Map<String, List<VulnerabilityProjectAsset>> reportIdMap = reportRecordAssetGet(Stream.of(recordId).collect(Collectors.toList()));

        return BaseResponse.ok(reportIdMap.getOrDefault(recordId, new ArrayList<>()));
    }

    @Override
    public BaseResponse reportDelete(String recordId) {
        VulnerabilityReportRecord record = vulnerabilityReportRecordMapper.selectVulnerabilityReportRecordById(recordId);
        if (Objects.nonNull(record)) {
            record.setReportStatus(VulnerabilityReportStatus.DELETE);
            vulnerabilityReportRecordMapper.updateVulnerabilityReportRecord(record);
            vulnerabilityProjectService.fixProjectStatus(Stream.of(record.getEid()).collect(Collectors.toList()));

        }
        return BaseResponse.ok();
    }

    @Override
    public BaseResponse reportSend(String recordId) {
        VulnerabilityReportRecord record = vulnerabilityReportRecordMapper.selectVulnerabilityReportRecordById(recordId);
        if (Objects.nonNull(record)) {
            record.setReportStatus(VulnerabilityReportStatus.ALREADY_SEND);
            vulnerabilityReportRecordMapper.updateVulnerabilityReportRecord(record);
        }
        return BaseResponse.ok();
    }

    @Override
    public BaseResponse reportRecordGet(String recordId) {
        VulnerabilityReportRecord record = vulnerabilityReportRecordMapper.selectVulnerabilityReportRecordById(recordId);
        return BaseResponse.ok(record);
    }

    @Override
    public BaseResponse reportStatus() {
        Map<String, List<CommonEnum>> nameDict = this.commonEnumRegistry.getNameDict();
        List<Map<String, Object>> vulnerabilityReportStatus = nameDict.get("vulnerabilityReportStatus").stream()
                .filter(status -> !status.equals(VulnerabilityReportStatus.DELETE))
                .map(
                        i -> {
                            VulnerabilityReportStatus status = (VulnerabilityReportStatus) i;
                            Map<String, Object> map = new HashMap<>();
                            map.put("code", status.getCode());
                            map.put("name", status.getDescription());
                            map.put("name_tw", status.getDescription_TW());
                            return map;

                        }
                ).collect(Collectors.toList());

        return BaseResponse.ok(vulnerabilityReportStatus);
    }

    /**
     * 是否有报告生成
     *
     * @param projectId
     * @return true 有 false 没有
     */
    @Override
    public Boolean isReportGenerate(String projectId) {
        Integer vulnerabilityReportRecord = vulnerabilityReportRecordMapper
                .selectVulnerabilityReportRecord(projectId);
        return Objects.nonNull(vulnerabilityReportRecord);
    }

    @Override
    public BaseResponse projectGet(Long eid) {
        Map<String, Object> map = new HashMap<>();
        map.put("eid", eid);
        map.put("size", 0);
        map.put("notEqualsProjectStatus", VulnerabilityProjectStatus.NO_SCAN.getCode());
        List<VulnerabilityProject> vulnerabilityProjects = vulnerabilityProjectMapper.selectVulnerabilityProject(map);
        return BaseResponse.ok(vulnerabilityProjects);
    }

    private List<com.digiwin.escloud.aiobasic.report.model.VulnerabilityProjectScanRecord> scanRecordGetByProjectId(String projectId, Set<String> deviceIps) {
        return vulnerabilityProjectScanMapper.scanRecordSelectByIpsAndProjectId(deviceIps, projectId);
    }

    public List<VulnerabilityStorehouse> vulnerabilityGetByNumber(Integer language, Set<String> number) {
        //这里优先选中参数传入得语言别，如果没有默认选中英文
        List<VulnerabilityStorehouse> vulnerabilityStorehouses = vulnerabilityReportRecordMapper.selectVulnerabilityByLanguageAndNumber(number);

        // 根據弱點編號分組
        Map<String, List<VulnerabilityStorehouse>> numberMap = vulnerabilityStorehouses.stream()
                .collect(Collectors.groupingBy(VulnerabilityStorehouse::getVulnerabilityNumber));

        // 定義語言順序
        List<Integer> fallbackLanguages;
        if (IntegerUtil.isNotEmpty(language)) {
            fallbackLanguages = Arrays.asList(language, VulnerabilityLanguage.TW.getCode(), VulnerabilityLanguage.ZH.getCode(), VulnerabilityLanguage.ENGLISH.getCode());
        } else {
            fallbackLanguages = Arrays.asList(VulnerabilityLanguage.TW.getCode(), VulnerabilityLanguage.ZH.getCode(), VulnerabilityLanguage.ENGLISH.getCode());
        }

        List<VulnerabilityStorehouse> retVulnerability = new ArrayList<>();
        numberMap.values().forEach(values -> {
            // 找出優先語言版本
            fallbackLanguages.stream()
                    .map(lang -> values.stream()
                            .filter(v -> v.getVulnerabilityLanguage().equals(lang))
                            .collect(Collectors.toList()))
                    .filter(CollectionUtil::isNotEmpty)
                    .findFirst()
                    .ifPresent(retVulnerability::addAll);
        });

        retVulnerability.forEach(v -> {
            Integer lang = v.getVulnerabilityLanguage();
            if (lang.equals(VulnerabilityLanguage.ZH.getCode())) {
                v.setVulnerabilityLevelString(v.getLevel_zh());
            } else {
                v.setVulnerabilityLevelString(v.getLevel_tw());
            }
        });

        return retVulnerability;
    }

    private void buildReportRecord(VulnerabilityReportRecord record, TenantMapDTO tenantMapDTO, VulnerabilityProject vulnerabilityProject) {
        record.setId(SnowFlake.getInstance().newIdStr());
        record.setCustomerName(StringUtil.isNotEmpty(tenantMapDTO.getFullName()) ? tenantMapDTO.getFullName() : tenantMapDTO.getName());
        record.setServiceCode(tenantMapDTO.getServiceCode());
        record.setProjectName(vulnerabilityProject.getProjectName());
        record.setReportStatus(VulnerabilityReportStatus.GENERATING);
        record.setReportDate(DateUtil.tryParseDate(record.getReportDateString(), DateUtil.DATE_FORMATTER).get());
        record.setReportGenDate(DateUtil.getDate());
        record.setScanBatch(vulnerabilityProject.getScanBatch());
    }

    private TenantMapDTO getTenantMap(Long eid) {
        List<TenantMapDTO> tenants = aioUserFeignClient.tenantServiceCodeGet(Lists.newArrayList(eid)).getTenantMapDTO();
        if (CollectionUtil.isEmpty(tenants)) {
            return null;
        }
        return tenants.stream().filter(t -> Objects.equals(t.getEid(), eid)).findFirst().orElse(null);
    }

    private List<VulnerabilityProjectAsset> getProjectAssets(VulnerabilityReportRecord record) {
        AtomicInteger assetNumber = new AtomicInteger(1);
        List<VulnerabilityProjectAsset> projectAssets;

        // 如果没有设备信息就是专案下扫描设备详情的所有设备
        if (CollectionUtil.isEmpty(record.getDeviceIds())) {
            //所有设备
            projectAssets = vulnerabilityProjectAssetMapper.projectAssetGetByProjectIdAndAssetIp(record.getProjectId(), null);
        } else {
            projectAssets = vulnerabilityProjectAssetMapper.projectAssetGetByIds(record.getDeviceIds());
        }

        // 添加 Number
        return projectAssets.stream()
                .peek(asset -> asset.setAssetNumber(String.valueOf(assetNumber.getAndIncrement())))
                .collect(Collectors.toList());
    }

    private List<VulnerabilityReportAssetMapping> buildMappings(
            VulnerabilityReportRecord record, List<VulnerabilityProjectAsset> projectAssets
    ) {
        return projectAssets.stream().map(vpa -> {
                    VulnerabilityReportAssetMapping mapping = new VulnerabilityReportAssetMapping();
                    mapping.setId(SnowFlake.getInstance().newIdStr());
                    mapping.setReportId(record.getId());
                    mapping.setProjectAssetId(vpa.getId());
                    return mapping;
                })
                .collect(Collectors.toList());
    }

    private List<com.digiwin.escloud.aiobasic.report.model.VulnerabilityProjectAsset> buildCommonProjectAssets(
            VulnerabilityReportRecord record, List<VulnerabilityProjectAsset> projectAssets
    ) {

        return projectAssets.stream().map(vpa -> {
                    com.digiwin.escloud.aiobasic.report.model.VulnerabilityProjectAsset asset
                            = new com.digiwin.escloud.aiobasic.report.model.VulnerabilityProjectAsset();
                    BeanUtils.copyProperties(vpa, asset);
                    asset.setAssetType(record.getLanguage().equals(VulnerabilityLanguage.ZH.getCode())
                            ? vpa.getTypeName_zh() : vpa.getTypeName_tw());
                    return asset;
                })
                .collect(Collectors.toList());
    }

    private VulnerabilityEsReport buildEsReport(VulnerabilityReportRecord record
            , VulnerabilityProject project
            , Integer language
            , List<com.digiwin.escloud.aiobasic.report.model.VulnerabilityProjectAsset> projectAssetList
            , List<VulnerabilityStorehouse> vulnerabilityStorehouseList
            , List<VulnerabilityProjectScanRecord> vulnerabilityProjectScanRecordList

    ) {
        // 數據預處理
        Boolean isZH = language.equals(VulnerabilityLanguage.ZH.getCode());
        String summary = StringUtil.toString(vulnerabilityProjectService.getProjectSummary(project.getId(), language).getData());
        VulnerabilityReportContent content = vulnerabilityReportRecordMapper.selectReportContentByLanguage(language);
        List<VulnerabilityLevel> vulnerabilityLevels = vulnerabilityReportRecordMapper.selectLevel();
        Map<String, VulnerabilityLevel> enLevelMap =
                vulnerabilityLevels.stream()
                        .collect(Collectors.toMap(VulnerabilityLevel::getLevelEn, Function.identity()));

        Map<String, List<VulnerabilityStorehouse>> numberMap =
                vulnerabilityStorehouseList.stream()
                        .collect(Collectors.groupingBy(VulnerabilityStorehouse::getVulnerabilityNumber));

        Map<String, List<com.digiwin.escloud.aiobasic.report.model.VulnerabilityProjectAsset>> ipMap =
                projectAssetList.stream()
                        .collect(Collectors.groupingBy(com.digiwin.escloud.aiobasic.report.model.VulnerabilityProjectAsset::getAssetIp));

        // 處理附录二数据
        List<VulnerabilityProjectScanRecord2> vulnerabilityProjectScanRecordAppend =
                convertToAppendRecordList(isZH, numberMap, ipMap, enLevelMap, vulnerabilityProjectScanRecordList);

        //添加附录二
        VulnerabilityEsReport esReport = new VulnerabilityEsReport();
        esReport.setVulnerabilityProjectScanRecordAppend(vulnerabilityProjectScanRecordAppend);
        esReport.setId(record.getId());
        esReport.setReportId(record.getId());
        esReport.setCustomerName(record.getCustomerName());
        esReport.setReportName(record.getCustomerName() + (isZH ? "弱点扫描报告" : "弱點掃描報告"));
        esReport.setProjectName(record.getProjectName());
        esReport.setProjectExecuteDate(project.getProjectExecuteTime());
        esReport.setReportGenerateDate(DateUtil.getSomeDateFormatString(record.getReportDate(), DateUtils.DATE_FORMATTER));
        esReport.setScanTool(project.getScanTool());
        esReport.setYear(LocalDate.now().getYear() + content.getYear());
        esReport.setScanBatch(isZH ?
                ScanBatchEnum.getByCode(record.getScanBatch()).getDescription() :
                ScanBatchEnum.getByCode(record.getScanBatch()).getDescription_tw());
        esReport.setVersion(record.getVersion());
        esReport.setScanRangeDesc(project.getScanRangeDesc());
        esReport.setGenerateType(content.getGenerateType());
        esReport.setProjectPurpose(content.getProjectPurpose());
        esReport.setProjectExecute(content.getProjectExecute());
        esReport.setScanExecuteTool(content.getScanExecuteTool());
        esReport.setScanType(content.getScanType());
        esReport.setRepairSuggestion(content.getRepairSuggestion());
        esReport.setVulnerabilityAbstractDesc(content.getVulnerabilityAbstractDesc());
        esReport.setRepairAbstractSuggestion(content.getRepairAbstractSuggestion());
        esReport.setScanExecuteToolResult(summary);
        esReport.setProjectAssetList(projectAssetList);
        esReport.setVulnerabilityStorehouseList(vulnerabilityStorehouseList);
        esReport.setVulnerabilityLevelList(getLevelSizeMap(vulnerabilityProjectScanRecordList));
        esReport.setVulnerabilityProjectAssetLevelList(scanGroupBy(vulnerabilityProjectScanRecordList, vulnerabilityLevels));
        esReport.setCreateDate(DateUtil.getNowFormatString(DATE_TIME_FORMATTER));
        esReport.setUpdateDate(DateUtil.getNowFormatString(DATE_TIME_FORMATTER));
        esReport.setVulnerabilityLanguage(language);

        // 因內容與VulnerabilityProjectScanRecordAppend相同，故不保存
//        esReport.setVulnerabilityProjectScanRecordList(vulnerabilityProjectScanRecordList);

        return esReport;

    }

    private Map<String, Integer> getLevelSizeMap(List<VulnerabilityProjectScanRecord> vulnerabilityProjectScanRecordList) {
        Map<String, List<VulnerabilityProjectScanRecord>> levelMap =
                vulnerabilityProjectScanRecordList.stream()
                        .collect(Collectors
                                .groupingBy(VulnerabilityProjectScanRecord::getVulnerabilityLevel));

        Map<String, Integer> levelSizeMap = new HashMap<>();
        levelSizeMap.put(VulnerabilityLevelEnum.CRITICAL.getName(),
                levelMap.getOrDefault("CRITICAL", new ArrayList<>()).size());
        levelSizeMap.put(VulnerabilityLevelEnum.HIGH.getName(),
                levelMap.getOrDefault("HIGH", new ArrayList<>()).size());
        levelSizeMap.put(VulnerabilityLevelEnum.MEDIUM.getName(),
                levelMap.getOrDefault("MEDIUM", new ArrayList<>()).size());
        levelSizeMap.put(VulnerabilityLevelEnum.LOW.getName(),
                levelMap.getOrDefault("LOW", new ArrayList<>()).size());

        return levelSizeMap;
    }

    List<VulnerabilityProjectScanRecord2> convertToAppendRecordList(
            Boolean isZH,
            Map<String, List<VulnerabilityStorehouse>> numberMap,
            Map<String, List<com.digiwin.escloud.aiobasic.report.model.VulnerabilityProjectAsset>> ipMap,
            Map<String, VulnerabilityLevel> enLevelMap,
            List<VulnerabilityProjectScanRecord> vulnerabilityProjectScanRecordList
    ) {
        return vulnerabilityProjectScanRecordList.stream().map(i -> {
                    // 拷貝一份清單
                    VulnerabilityProjectScanRecord2 record2 = new VulnerabilityProjectScanRecord2();
                    BeanUtils.copyProperties(i, record2);
                    VulnerabilityScanData vulnerabilityScanData = JSONObject.parseObject(i.getDataJson(), VulnerabilityScanData.class);

                    // DataJSON 清空不保存
                    i.setDataJson(null);
                    record2.setDataJson(null);
                    record2.setVulnerabilityPort(vulnerabilityScanData.getPort());

                    // 取得目標語言
                    Integer targetLang = isZH ? VulnerabilityLanguage.ZH.getCode() : VulnerabilityLanguage.TW.getCode();

                    // 取得弱點清單
                    List<VulnerabilityStorehouse> vulnerabilityStorehouses = numberMap.getOrDefault(i.getVulnerabilityNumber(), Collections.emptyList());
                    vulnerabilityStorehouses.stream()
                            .filter(v -> v.getVulnerabilityLanguage().equals(targetLang))
                            .findFirst()
                            .ifPresent(vs -> {
                                record2.setRepairSuggestion(vs.getRepairSuggestion());
                                record2.setVulnerabilityDesc(vs.getVulnerabilityDesc());
                            });

                    // 取得弱點等級名稱
                    String level = i.getVulnerabilityLevel().toUpperCase();
                    Optional.ofNullable(enLevelMap.get(level)).ifPresent(vl -> {
                        i.setVulnerabilityLevelName(isZH ? vl.getLevel_zh() : vl.getLevel_tw());
                        record2.setVulnerabilityLevelName(isZH ? vl.getLevel_zh() : vl.getLevel_tw());
                    });

                    // 理論上一個ip對應一個資產
                    List<com.digiwin.escloud.aiobasic.report.model.VulnerabilityProjectAsset> assets = ipMap.getOrDefault(i.getAssetIp(), Collections.emptyList());
                    Optional.ofNullable(assets).ifPresent(a -> {
                        record2.setAssetName(a.get(0).getAssetName());
                    });

                    return record2;
                })
                .collect(Collectors.toList());
    }


    private List<Map<String, Object>> scanGroupBy(List<com.digiwin.escloud.aiobasic.report.model.VulnerabilityProjectScanRecord> vulnerabilityProjectScanRecordList
            , List<VulnerabilityLevel> vulnerabilityLevels) {
        Map<String, List<com.digiwin.escloud.aiobasic.report.model.VulnerabilityProjectScanRecord>> projectAssetIpMap
                = vulnerabilityProjectScanRecordList.stream().collect(Collectors.groupingBy(
                com.digiwin.escloud.aiobasic.report.model.VulnerabilityProjectScanRecord::getAssetIp
        ));
        List<Map<String, Object>> retList = new ArrayList<>();
        int num = 0;
        for (Map.Entry<String, List<com.digiwin.escloud.aiobasic.report.model.VulnerabilityProjectScanRecord>> entry
                : projectAssetIpMap.entrySet()) {
            num++;
            Map<String, Object> retMap = new HashMap<>();
            retMap.put("assetIp", entry.getKey());
            Map<String, List<com.digiwin.escloud.aiobasic.report.model.VulnerabilityProjectScanRecord>> levelMap
                    = entry.getValue().stream().collect(Collectors.groupingBy(
                    vulnerabilityProjectScanRecord -> vulnerabilityProjectScanRecord.getVulnerabilityLevel().toUpperCase()
            ));
            int total = 0;
            for (VulnerabilityLevel level : vulnerabilityLevels) {
                int size = levelMap.getOrDefault(level.getLevelEn(), new ArrayList<>()).size();
                retMap.put(level.getLevelEn(), size);
                if (!level.getLevelEn().equals(VulnerabilityLevelEnum.INFO.getName())) {
                    total += size;
                }
            }
            retMap.put("total", total);
            retMap.put("orderNum", num);
            retList.add(retMap);

        }
        return retList;
    }

    private Date combineDate(String dateString) {
        DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        LocalDate date = LocalDate.parse(dateString, dateFormatter);
        return Date.from(date.atStartOfDay(ZoneId.systemDefault()).toInstant());
    }

    private Map<String,List<VulnerabilityProjectAsset>> reportRecordAssetGet(List<String> recordIdList) {
        if (CollectionUtils.isEmpty(recordIdList)) {
            return new HashMap<>(1);
        }
        return vulnerabilityReportRecordMapper.selectVulnerabilityReportAssetMapping(recordIdList)
                .stream().collect(Collectors.groupingBy(VulnerabilityProjectAsset::getReportId));
    }


    private String getDate(String datetimeString) {
        DateTimeFormatter inputFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        LocalDateTime datetime = LocalDateTime.parse(datetimeString, inputFormatter);

        DateTimeFormatter outputFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        return datetime.format(outputFormatter);
    }

}
