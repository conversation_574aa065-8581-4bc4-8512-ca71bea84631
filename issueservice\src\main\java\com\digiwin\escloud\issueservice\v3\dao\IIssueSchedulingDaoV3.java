package com.digiwin.escloud.issueservice.v3.dao;

import com.digiwin.escloud.issueservice.t.model.issuekb.IssueKbCondition;
import com.digiwin.escloud.issueservice.t.model.issuekb.IssueKbExecuteRecord;
import com.digiwin.escloud.issueservice.t.model.issuekb.IssueKbKeyWord;
import com.digiwin.escloud.issueservice.t.model.issuekb.IssueKbOverview;

import java.util.List;
import java.util.Map;


public interface IIssueSchedulingDaoV3 {
    /**
     * 日期字符串格式 '2019-10-18 23:00:00'
     */
    List<Long> selectUnCloseCaseList(String dateTime);

    List<IssueKbCondition> getIssueKbConditionList(Map<String,Object> param);
    List<IssueKbKeyWord> getIssueKbKeyWordList();
    List<IssueKbOverview> getCNIssueList(String closeDate, Long ikcId);
    List<IssueKbOverview> getTWIssueList(String closeDate, Long ikcId);
    int batchInsertIssueKbOverviews(List<IssueKbOverview> issueKbOverview);
    int saveIssueKbOverview(IssueKbOverview issueKbOverview);
    int updateIssueKbOverview(IssueKbOverview issueKbOverview);
    int saveIssueKbExecuteRecord(IssueKbExecuteRecord issueKbExecuteRecord);
    int selectIssueKbAIStatus(Long ikerId);
    int updateIssueKbExecuteRecord(Long ikerId, int status);
}
