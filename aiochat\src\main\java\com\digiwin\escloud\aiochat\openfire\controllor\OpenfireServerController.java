package com.digiwin.escloud.aiochat.openfire.controllor;

import com.digiwin.escloud.aiochat.openfire.common.OpenfireServerInfo;
import com.digiwin.escloud.aiochat.openfire.common.XMPPConnectionManager;
import com.digiwin.escloud.aiochat.openfire.controllor.dto.OpenfireBusinessCode;
import com.digiwin.escloud.aiochat.openfire.controllor.dto.OpenfireServerResponse;
import com.digiwin.escloud.aiochat.openfire.service.IOpenfireServerService;
import com.digiwin.escloud.aiochat.openfire.service.OpenFireUtilsService;
import com.digiwin.escloud.common.controller.ControllerBase;
import com.digiwin.escloud.common.model.ResponseBase;
import com.digiwin.escloud.common.util.EncryptionUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Base64;

@Api(value = "/openfire/server", protocols = "HTTP", tags = {"Openfire服务器相关接口"}, description = "Openfire服务器相关接口")
@RestController
@RequestMapping("/openfire/server")
@Slf4j
public class OpenfireServerController extends ControllerBase {

    @Autowired
    private IOpenfireServerService openfireServerService;

    @Autowired
    private OpenFireUtilsService openFireUtilsService;

    @ApiOperation("取得Openfire服务器信息")
    @RequestMapping(value = "", method = RequestMethod.GET)
    public OpenfireServerResponse getOpenfireServer() {
        return this.getResponse(OpenfireServerResponse.class, x -> {
            OpenfireServerInfo serverInfo = openfireServerService.getConnectionInfo();
            if (serverInfo == null) {
                setErrorCode(x, OpenfireBusinessCode.NOT_FOUND_CONFIG);
                return x;
            }
            try {
                //true时，第一期：aes加密 base64加密----》第二期：先查mysql 在解密 在aes加密
                //false时，第一期：直接查mysql后明文返回 ---》第二期：先查mysql 在解密 明文返回
                //2025-07-16 对mysql查到的openfire配置，进行加密返回
                serverInfo.setSystemPassword(Base64.getEncoder().encodeToString(EncryptionUtil.encrypt(serverInfo.getSystemPassword().getBytes(), EncryptionUtil.key.getBytes())));
            }catch (Exception ex) {
                log.error("openfire/server SystemPassword加密失败");
                serverInfo.setSystemPassword("");
            }
            x.setCode(GENERAL_SUCCESS_CODE);
            x.setOpenfireServerInfo(serverInfo);
            return x;
        }, false, null, GENERAL_FAIL_CODE);
    }

    private void setErrorCode(ResponseBase response, OpenfireBusinessCode code, Object... params) {
        response.setCode(code.getCode());
        response.setErrMsg(code.getDynamicDescription(params));
    }

    @ApiOperation("刷新Openfire服务器信息")
    @RequestMapping(value = "/refresh", method = RequestMethod.GET)
    public OpenfireServerResponse refreshConfigCache() {
        return this.getResponse(OpenfireServerResponse.class, x -> {
            OpenfireServerInfo serverInfo = openfireServerService.refreshConfigCache();
            if (serverInfo == null) {
                setErrorCode(x, OpenfireBusinessCode.NOT_FOUND_CONFIG);
                return x;
            }
            x.setCode(GENERAL_SUCCESS_CODE);
            x.setOpenfireServerInfo(serverInfo);
            return x;
        }, false, null, GENERAL_FAIL_CODE);
    }

    /**
     * 主機端用的帳號, 重新連線
     * 06/05, 06/07 發現 stock 連線會異常, 需要重連後, 會恢復正常
     * @return
     */
    @ApiOperation("admin 的帳號, 重新連線 openfire")
    @RequestMapping(value = "/account/admin/reConnect", method = RequestMethod.PATCH)
    public String reConnectOpenfire() {

        XMPPConnectionManager.getInstance().reConnect();
        return "OK";
    }

    @ApiOperation("重新取得建 [OPENFIRE] 帳號的相關資料")
    @RequestMapping(value = "/account/refresh/cache/key", method = RequestMethod.PATCH)
    public String refreshCacheKey() {
        openFireUtilsService.refreshCacheKey();
        return "OK";
    }

    @ApiOperation("用特定帐户检测openfire连线状态，true:成功；false:失败")
    @PostMapping(value = "/check/by/account")
    public Boolean checkConnectOpenfire(
            @ApiParam("用户名，为空将使用admin用户")
            @RequestParam(value = "userName", required = false) String userName,
            @ApiParam("用户密码，为空将使用admin用户密码")
            @RequestParam(value = "userPwd", required = false) String userPwd,
            @ApiParam("资源键")
            @RequestParam(value = "resourceKey", required = false) String resourceKey) {
        return XMPPConnectionManager.getInstance().checkConnect(userName, userPwd, resourceKey);
    }
}
