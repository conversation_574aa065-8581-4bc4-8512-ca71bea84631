package com.digiwin.escloud.issueservice.v3.dao.impl;

import com.digiwin.escloud.issueservice.t.model.issuekb.IssueKbOverview;
import com.digiwin.escloud.issueservice.v3.dao.IIssuekbDaoV3;
import org.apache.ibatis.session.SqlSession;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;


@Service
public class IssuekbDaoV3 implements IIssuekbDaoV3 {
    @Autowired
    private SqlSession sqlSession;

    @Override
    public List<IssueKbOverview> getFailIssuesKbOverviews(long id) {
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("id", id);
        return sqlSession.selectList("escloud.issuekbmapperv3.getFailIssuesKbOverviews",map);
    }

    @Override
    public int updateIssueKbExecuteRecordStatus(long id, int status){
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("id", id);
        map.put("status", status);
        return sqlSession.update("escloud.issuekbmapperv3.updateIssueKbExecuteRecordStatus",map);
    }
}
