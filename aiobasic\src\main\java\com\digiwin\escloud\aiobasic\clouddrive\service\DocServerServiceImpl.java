package com.digiwin.escloud.aiobasic.clouddrive.service;

import com.digiwin.escloud.aiobasic.clouddrive.controller.dto.DocBusinessCode;
import com.digiwin.escloud.aiobasic.clouddrive.controller.dto.DocServerConfigResponse;
import com.digiwin.escloud.aiobasic.clouddrive.dao.DocServerMapper;
import com.digiwin.escloud.aiobasic.clouddrive.model.DocServerConfig;
import com.digiwin.escloud.aiouser.model.user.UserRolesGetResponse;
import com.digiwin.escloud.common.controller.ControllerBase;
import com.digiwin.escloud.common.feign.AioUserFeignClient;
import com.digiwin.escloud.common.model.ResponseCode;
import com.digiwin.escloud.common.util.EncryptionUtil;
import com.digiwin.escloud.common.util.RandomStringWithOneDigitUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Base64;
import java.util.List;
import java.util.Map;

/**
 * 文档服务器服务器
 */
@Service
@Slf4j
public class DocServerServiceImpl implements DocServerService {

    @Autowired
    DocServerMapper docServerMapper;
    @Autowired
    AioUserFeignClient aioUserFeignClient;

    @Override
    public DocServerConfigResponse getDocServerConfig(Long sid, Long userSid, String token, Boolean isIntranet, String serviceRegion) {
        DocServerConfigResponse response = new DocServerConfigResponse();
        //region 参数检查

        if (sid == null || sid <= 0) {
            DocBusinessCode code = DocBusinessCode.HEADER_IS_EMPTY;
            response.setCode(code.getCode());
            response.setErrMsg(code.getDynamicDescription("sid"));
            return response;
        }

        if (userSid == null || userSid <= 0) {
            DocBusinessCode code = DocBusinessCode.PARAM_IS_EMPTY;
            response.setCode(code.getCode());
            response.setErrMsg(code.getDynamicDescription("sid"));
        }

        //endregion

        if (isIntranet == null) {
            isIntranet = getIsIntranet(userSid, token);
        }

        DocServerConfig config = docServerMapper.selectDocServerConfig(isIntranet, sid, serviceRegion);
        if (config == null) {
            config = docServerMapper.selectDefaultDocServerConfig(isIntranet);
        }
        //用config的里面的盐值解密,salt1解密password，salt2解密passwordHash
        try {
            String salt1 = config.getSalt1();
            String password = config.getPassword();
            //取salt里面的数字
            String number1 = RandomStringWithOneDigitUtil.getNumberFromString(salt1);
            String decryptPassword = new String(EncryptionUtil.decrypt(Base64.getDecoder().decode(password), ("DIGIWIN"+salt1).getBytes()));
            String decryptPasswordNew = RandomStringWithOneDigitUtil.deleteString(decryptPassword,Integer.parseInt(number1),Integer.parseInt(number1)+ salt1.length());
            config.setPassword(decryptPasswordNew);

            String salt2 = config.getSalt2();
            String passwordHash = config.getPasswordHash();
            //取salt里面的数字
            String number2 = RandomStringWithOneDigitUtil.getNumberFromString(salt2);
            String decryptPasswordHash = new String(EncryptionUtil.decrypt(Base64.getDecoder().decode(passwordHash), ("DIGIWIN"+salt2).getBytes()));
            String decryptPasswordHashNew = RandomStringWithOneDigitUtil.deleteString(decryptPasswordHash,Integer.parseInt(number2),Integer.parseInt(number2)+ salt2.length());
            config.setPasswordHash(decryptPasswordHashNew);

            try {
                //2025-07-16 对mysql查到的server配置，进行加密返回
                config.setPassword(Base64.getEncoder().encodeToString(EncryptionUtil.encrypt(config.getPassword().getBytes(), EncryptionUtil.key.getBytes())));
            }catch (Exception ex) {
                log.error("getDocServerConfig Password加密失败");
                config.setPassword("");
            }



        } catch (Exception e) {
            e.printStackTrace();
        }



        response.setDocServerConfig(config);
        response.setCode(ControllerBase.GENERAL_SUCCESS_CODE);
        return response;
    }

    private boolean getIsIntranet(Long userSid, String token) {
        try {
            UserRolesGetResponse response = aioUserFeignClient.getUserRoles(userSid, token);
            if (response == null || !ResponseCode.SUCCESS.getCode().equals(response.getCode())){
                return false;
            }
            List<Map<String, String>> userRoles = response.getRoles();
            if (CollectionUtils.isEmpty(userRoles)) {
                return false;
            }
            return userRoles.stream().filter(x -> x.containsKey("customerService") || x.containsKey("superManager"))
                    .findFirst().isPresent();
        } catch (Exception ex) {
            System.out.print(userSid + "获取文档服务器配置，判断是否企业内部网路错误：");
            ex.printStackTrace();
        }
        return false;
    }
}
