package com.digiwin.escloud.aioitms.amqp;

import com.digiwin.escloud.aioitms.constants.MqConstant;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.*;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class RabbitInitializer implements InitializingBean {

    @Autowired
    private AmqpAdmin rabbitAdmin1;

    /**
     * 初始化交换机，队列，绑定关系
     */
    @Override
    public void afterPropertiesSet() {
        init();
    }

    public void init(){
        DirectExchange directExchange = declareDirectExchange(MqConstant.ASSET_MAINTENANCE_EXCHANGE);
        rabbitAdmin1.declareExchange(directExchange);
        Queue queue = declareQueue(MqConstant.ASSET_MAINTENANCE_QUEUE,false);
        rabbitAdmin1.declareQueue(queue);
        Binding binding = declareDirectBinding(queue, directExchange, MqConstant.ASSET_MAINTENANCE_ROUTING_KEY);
        rabbitAdmin1.declareBinding(binding);

        DirectExchange reportExchange = declareDirectExchange(MqConstant.REPORT_EXCHANGE);
        rabbitAdmin1.declareExchange(reportExchange);
        Queue serviceReportQueue = declareQueue(MqConstant.SERVICE_REPORT_QUEUE,false);
        rabbitAdmin1.declareQueue(serviceReportQueue);
        Binding serviceReportBinding = declareDirectBinding(serviceReportQueue, reportExchange, MqConstant.SERVICE_REPORT_ROUTING_KEY);
        rabbitAdmin1.declareBinding(serviceReportBinding);
    }

    public Queue declareQueue(String queueName,boolean durable){
        return new Queue(queueName,durable,false,false,null);
    }

    public DirectExchange declareDirectExchange(String exchangeName){
        return new DirectExchange(exchangeName,true,false,null);
    }

    public Binding declareDirectBinding(Queue queue, DirectExchange exchange,String routingKey){
        return BindingBuilder.bind(queue).to(exchange).with(routingKey);
    }
}