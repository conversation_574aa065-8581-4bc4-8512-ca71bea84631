package com.digiwin.escloud.issueservice.v3.service.impl;

import com.alibaba.fastjson.JSON;
import com.digiwin.escloud.common.util.CollectionUtil;
import com.digiwin.escloud.issueservice.model.Issue;
import com.digiwin.escloud.issueservice.t.externalapi.service.DataFixService;
import com.digiwin.escloud.issueservice.t.issuedetail.dao.IssueProcessMapper;
import com.digiwin.escloud.issueservice.t.model.cases.CasesEmail;
import com.digiwin.escloud.issueservice.t.model.common.BaseResponse;
import com.digiwin.escloud.issueservice.t.model.common.ResponseStatus;
import com.digiwin.escloud.issueservice.t.model.issuekb.*;
import com.digiwin.escloud.issueservice.v3.dao.IIssueSchedulingDaoV3;
import com.digiwin.escloud.issueservice.v3.service.IIndepthAIService;
import com.digiwin.escloud.issueservice.v3.service.IIssueDetailServiceV3;
import com.digiwin.escloud.issueservice.v3.service.IIssueSchedulingServiceV3;
import com.github.pagehelper.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.http.codec.ServerSentEvent;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import reactor.core.publisher.Flux;

import java.text.SimpleDateFormat;
import java.time.LocalTime;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;

/**
 * Created by Administrator on 2019-09-17.
 */
@Slf4j
@Service
public class IssueSchedulingServiceV3 implements IIssueSchedulingServiceV3 {
    @Autowired
    IIssueDetailServiceV3 issueDetailServiceV3;
    @Autowired
    private IssueProcessMapper issueProcessMapper;
    @Autowired
    IssueServiceV3 issueServiceV3;
    @Autowired
    private DataFixService dataFixService;
    @Autowired
    IIssueSchedulingDaoV3 iIssueSchedulingDaoV3;
    @Autowired
    IIndepthAIService aiService;
    @Value("${digiwin.issue.defaultlanguage}")
    private String defaultLanguage;
    @Value("${digiwin.issue.connectarea}")
    private String connectArea;

    private static final String HTML_CONTENT_FIRST = "<td width=75 height=40 style=\"padding-left:10px\">";
    private static final String HTML_CONTENT_SECOND = "<td width=780 height=40 style=\"padding-left:10px\">";
    private static final String HTML_TR_OPEN = "<tr>";
    private static final String HTML_TR_CLOSE = "</tr>";
    private static final String HTML_TD_OPEN = "<td>";
    private static final String HTML_TD_CLOSE = "</td>";
    //自动结案间隔天数
    // 案件'反馈人验证'中的状态持续 AUTO_CLOSE_CASE_DAY 天后(自然天)，自动结案
    private static final int AUTO_CLOSE_CASE_DAY = 7;
    /**
     * 自动结案
     */
    @Override
    public void autoCloseIssue(){
        List<Long> unCloseCaseList = iIssueSchedulingDaoV3.selectUnCloseCaseList(getAutoCloseCaseDateTime());
        if(CollectionUtils.isEmpty(unCloseCaseList)) {
            return;
        }
        /**
         * 反馈人邮箱：发送给反馈人的邮件信息
         */
        HashMap<String,List<Issue>> convertData = new HashMap<>();
        for (Long issueId: unCloseCaseList) {
            BaseResponse baseResponse = issueDetailServiceV3.autoClose("TSCloud", issueId);
            if(ResponseStatus.OK.getCode() == baseResponse.getStatus()){
                Issue issue = (Issue)(baseResponse.getResponse());
                String receiver = issue.getUserContact().getEmail();
                if (!StringUtils.isEmpty(receiver)) {
                    if (convertData.containsKey(receiver)) {
                        List<Issue> autoCloseIssueEmailList = convertData.get(receiver);
                        autoCloseIssueEmailList.add(issue);
                    } else {
                        List<Issue> autoCloseIssueEmailList = new ArrayList<>();
                        autoCloseIssueEmailList.add(issue);
                        convertData.put(receiver,autoCloseIssueEmailList);
                    }
                }
            }


        }
        /**
         * 合并同个收件人的邮件
         */
        for (Map.Entry<String, List<Issue>> entry : convertData.entrySet()) {
            String receiver = entry.getKey();
            String language = defaultLanguage;
            CasesEmail casesEmail = new CasesEmail();
            HashSet<String> ccMailSet = new HashSet<>();
            casesEmail.setEmergency("Y");
            StringBuilder sb = new StringBuilder();
            List<Issue> autoCloseIssueEmailList = entry.getValue();
            for (Issue issue : autoCloseIssueEmailList) {
                // 邮件内容，用户名称
                if (StringUtils.isEmpty(casesEmail.getUsername())) {
                    casesEmail.setUsername(issue.getUserContact().getName());
                }
                // 邮件语言别
                if (!StringUtils.isEmpty(issue.getSubmiterLanguage())) {
                    language = issue.getSubmiterLanguage();
                }
                // 邮件抄送人
                if(!StringUtils.isEmpty(issue.getCcUser())){
                    ccMailSet.addAll(Collections.singletonList(issue.getCcUser()));
                }
                // 邮件内容
                //组合案件详情
                String htmlCases = "<tr>" + HTML_CONTENT_FIRST + issue.getCrmId() + HTML_TD_CLOSE +
                        HTML_CONTENT_SECOND + issue.getIssueDescription() + HTML_TD_CLOSE + "</tr>";
                sb.append(htmlCases);
            }
            casesEmail.setHtmlCases(sb.toString());
            log.info("发送自动结案的案件邮件收件人:{}，抄送人：{}",receiver,ccMailSet.toString());
            log.info("内容:{}",casesEmail.getHtmlCases());
            ArrayList ccMailList = new ArrayList(ccMailSet);

            issueServiceV3.SendAutoCloseMailToSubmiter(casesEmail,Collections.singletonList(receiver),ccMailList, language);
        }
    }

    /**
     * 获取自动结案的过滤时间
     * @return 日期时间字符串，格式必须为 '2019-10-18 23:00:00'
     */
    private String getAutoCloseCaseDateTime(){
        //当前日期（精确到年月日）
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
        Calendar calendar = Calendar.getInstance();
        //往前推3天（昨天，前天，大前天...检查每一天是否为工作日，当检查到后，确认时间）
        calendar.setTime(new Date());
        calendar.add(Calendar.DAY_OF_MONTH, -AUTO_CLOSE_CASE_DAY);
        String d = simpleDateFormat.format(calendar.getTime());
        Map<String, Object> map = new HashMap<String, Object>();
        return d + " " + "18:00:00";
    }

    @Override
    public void rePostCaseProcessRecords(){
        //先查出抛转187失败的案件
        List<Long> issueIds = issueProcessMapper.query187ErrorIssues();
        if(CollectionUtil.isNotEmpty(issueIds)){
            log.info("本次有抛转187异常的案件，准备抛砖，开始时间：{}", LocalTime.now());
            issueIds.forEach(k->{
                dataFixService.repostCaseProcessRecords(k);
            });
            log.info("本次抛转187异常的案件完成，完成时间：{}", LocalTime.now());
        }
    }

    /**
     * 案件生成知识
     */
    @Override
    public void buildIssueKb(String closeDate, Long ikcId){
        log.info("获取昨天结案的案件，生成知识 开始");
        Map<String,Object> map = new HashMap<>();
        map.put("status","1");//查已生效的筛选条件
        map.put("ikcId",ikcId);
        List<IssueKbCondition> issueKbConditions = iIssueSchedulingDaoV3.getIssueKbConditionList(map);
        if(CollectionUtils.isEmpty(issueKbConditions)){
            log.info("没有已生效的筛选条件");
            return;
        }

        //查询所有关键词设定，用于过滤使用
        List<IssueKbKeyWord> issueKbKeyWords = iIssueSchedulingDaoV3.getIssueKbKeyWordList();

        //查询所有满足条件的案件，当案件满足多个筛选条件时，需要去重
        List<IssueKbOverview> issueKbOverviews = getIssues(closeDate, ikcId);

        Runnable runnable = () -> dealIssueKbOverviews(closeDate, issueKbConditions, issueKbKeyWords, issueKbOverviews);
        ExecutorService executorService = Executors.newSingleThreadExecutor();
        try {
            executorService.execute(runnable);
        } catch (Exception ex) {
            log.error("asyncRun", ex);
        } finally {
            executorService.shutdown();
            log.info("获取昨天结案的案件，生成知识 结束");
        }
    }

    private void dealIssueKbOverviews(String closeDate, List<IssueKbCondition> issueKbConditions, List<IssueKbKeyWord> issueKbKeyWords, List<IssueKbOverview> issueKbOverviews) {

        for (IssueKbCondition issueKbCondition : issueKbConditions) {
            Long ikcId = issueKbCondition.getId();

            if(CollectionUtils.isEmpty(issueKbOverviews)){
                log.info("当前没有案件需要转AI");
                saveIssueKbExecuteRecord(closeDate, issueKbCondition.getServiceRegion(),issueKbCondition.getProductCode(),ikcId,0);
                continue ;
            }

            //过滤满足该筛选条件的案件
            List<IssueKbOverview> list = issueKbOverviews.stream().filter(k->k.getIkcId().equals(ikcId)).collect(Collectors.toList());
            if(CollectionUtils.isEmpty(list)){
                log.info("当日没有满足筛选条件：---- "+ issueKbCondition.getConditionName() +" ---的案件");
                saveIssueKbExecuteRecord(closeDate, issueKbCondition.getServiceRegion(),issueKbCondition.getProductCode(),ikcId,0);
                continue ;
            }
            //根据faq+crmId 进行分组，
            // 大陆：如果faq是0，知識標題=案件描述,知識解答=單身處理描述汇总
            // 台湾：如果faq是1且内部说明不空时，知識標題=內部說明,知識解答=單身處理描述
            // 台湾：如果faq是1且内部说明空或者faq=0时时，知識標題=案件描述,知識解答=單身處理描述汇总

            //过滤单头问题描述含有关键词id  单身处理描述含有的关键词id，不需要转AI
            list = list
                    .stream()
                    .filter(k->filterIssueDesp(issueKbCondition,k,issueKbKeyWords))
                    .filter(k->filterProcessDesp(issueKbCondition,k,issueKbKeyWords))
                    .filter(k->fillterHour(issueKbCondition,k))
                    .distinct()
                    .collect(Collectors.toList());

            if(issueKbCondition.getFaq()){
                //筛选条件勾选faq
                list = selectFAQ(list);
            }else {
                //筛选条件未勾选faq
                list = notSelectFAQ(list);
            }
            int count = CollectionUtils.isEmpty(list) ? 0 : list.size();
            //记录案件生成知识执行状况
            long ikerId = saveIssueKbExecuteRecord(closeDate, issueKbCondition.getServiceRegion(),issueKbCondition.getProductCode(),ikcId,count);
            //生成知识，并记录案件知识一览表中
            saveIssueKbOverview(list, ikerId);
        }
    }

    //生成知识，并记录案件知识一览表中
    private void saveIssueKbOverview(List<IssueKbOverview> issueKbOverviews, long ikerId) {
        if(CollectionUtil.isEmpty(issueKbOverviews)){
            return;
        }
        issueKbOverviews = issueKbOverviews.stream().sorted(Comparator.comparing(IssueKbOverview::getIssueCode)).collect(Collectors.toList());
        int size = issueKbOverviews.size();
        for(int i=0 ; i<size;i++){
            IssueKbOverview issueKbOverview = issueKbOverviews.get(i);
            issueKbOverview.setIkerId(ikerId);
            issueKbOverview.setStatusAI(IssueKbAIStatus.INIT.getIndex());

            //循环每个案件，调用智能体，将访问的A1标题、A1解答、A1关键字取出来，入库
            Map<String,Object> question = new HashMap<>();
            question.put("title",issueKbOverview.getIssueDespToAI());
            question.put("language",issueKbOverview.getLang());
            question.put("description",issueKbOverview.getProcessDespToAI_arr());

            issueKbOverview.setIndex(i);
            issueKbOverview.setSize(size);
            //生成知识，并记录案件知识一览表中
            try{
                issueToAI(JSON.toJSONString(question),issueKbOverview).blockLast();
            }catch (Exception e){
                log.error("案件生成知识失败，ikerId：{},issueKbOverview:{}",ikerId,issueKbOverview);
            }

        }
    }

    private List<IssueKbOverview> getIssues(String closeDate, Long ikcId) {
        return "CN".equals(connectArea) ? iIssueSchedulingDaoV3.getCNIssueList(closeDate, ikcId) : iIssueSchedulingDaoV3.getTWIssueList(closeDate, ikcId);
    }
    public ResponseEntity<Flux<ServerSentEvent<Object>>> issueToAI(String askMessage){
        HttpHeaders headers = new HttpHeaders();
        headers.add("X-Accel-Buffering", "no");
        headers.add("Content-Type", "text/event-stream");
        headers.add("Cache-Control", "no-cache");
        return ResponseEntity.ok()
                .headers(headers)
                .body(aiService.generateTextStream(askMessage, "",null));

    }

    public Flux<ServerSentEvent<Object>> issueToAI(String askMessage,IssueKbOverview issueKbOverview){
        return aiService.generateTextStream(askMessage, "", issueKbOverview);

    }

    /**试用于大陆
     * 未勾选faq选项，知識標題=案件描述,知識解答=單身處理描述汇总
     * @param list
     * @return
     */
    private List<IssueKbOverview> notSelectFAQ(List<IssueKbOverview> list) {
        List<IssueKbOverview> result = new ArrayList<>();
        if(CollectionUtils.isEmpty(list)){
            return result;
        }

        Map<String,List<IssueKbOverview>> map = list.stream().collect(Collectors.groupingBy(IssueKbOverview::getIssueCode));

        for(Map.Entry<String, List<IssueKbOverview>> entry : map.entrySet()) {
            IssueKbOverview view = new IssueKbOverview();
            BeanUtils.copyProperties(entry.getValue().get(0), view);
            List<String> processDesps= entry.getValue().stream().map(k->k.getProcessDespToAI()).distinct().collect(Collectors.toList());
            view.setProcessDespToAI(JSON.toJSONString(processDesps));//取问题描述的汇总
            view.setProcessDespToAI_arr(processDesps);//取问题描述的汇总
            result.add(view);
        }
        return result;
    }
    /*
     * 用于分组条件
     */
    private String fetchGroupKey(IssueKbOverview view){
        return view.getIssueCode() + ":" + (view.getFaq()?"true":"false");
    }

    /**试用于台湾
     * 筛选条件勾选faq选项时
     * 如果单身faq=1且内部说明不空时，知識標題=內部說明,知識解答=單身處理描述
     * 如果单身faq=1且内部说明空或者单身faq=0时，知識標題=案件描述,知識解答=單身處理描述汇总
     * @param list
     * @return
     */
    private List<IssueKbOverview> selectFAQ(List<IssueKbOverview> list) {
        List<IssueKbOverview> result = new ArrayList<>();
        if(CollectionUtils.isEmpty(list)){
            return result;
        }
        //1先取list里面单身faq=1且内部说明不空的数据
        List<IssueKbOverview> list1= list
                .stream()
                .filter(k->k.getFaq().equals(true))
                .filter(k->!StringUtils.isEmpty(k.getInnerDes()))
                .distinct()
                .collect(Collectors.toList());
        if(!CollectionUtils.isEmpty(list1)){
            for (IssueKbOverview issueKbOverview : list1) {
                issueKbOverview.setIssueDespToAI(issueKbOverview.getInnerDes()); //知識標題=內部說明
                issueKbOverview.setProcessDespToAI(issueKbOverview.getProcessDespToAI());//知識解答=單身處理描述
                issueKbOverview.setProcessDespToAI_arr(Arrays.asList(issueKbOverview.getProcessDespToAI()));//取问题描述的汇总
                result.add(issueKbOverview);
            }
        }

        //2取list单身faq=1且内部说明空的数据
        List<IssueKbOverview> list2= list
                .stream()
                .filter(k->k.getFaq().equals(true))
                .filter(k->StringUtils.isEmpty(k.getInnerDes()))
                .distinct()
                .collect(Collectors.toList());
        if(!CollectionUtils.isEmpty(list2)){
            Map<String,List<IssueKbOverview>> map = list2.stream().collect(Collectors.groupingBy(IssueKbOverview::getIssueCode));
            for(Map.Entry<String, List<IssueKbOverview>> entry : map.entrySet()) {
                IssueKbOverview view = new IssueKbOverview();
                BeanUtils.copyProperties(entry.getValue().get(0), view);
                List<String> processDesps= entry.getValue().stream().map(k->k.getProcessDespToAI()).distinct().collect(Collectors.toList());
                view.setProcessDespToAI(JSON.toJSONString(processDesps));//知識解答=單身處理描述汇总
                view.setProcessDespToAI_arr(processDesps);//取问题描述的汇总
                result.add(view);
            }
        }

        //3取list单身faq=0 的数据,前提是没有replyType=F的，如果某个案件有replyType=F ，这时候忽略掉这个案件
        List<IssueKbOverview> list3= list
                .stream()
                .filter(k->(k.getFaq().equals(false)))
                .filter(k->(!result.stream().filter(m->k.getIssueCode().equals(m.getIssueCode())).findAny().isPresent()))
                .distinct()
                .collect(Collectors.toList());
        if(!CollectionUtils.isEmpty(list3)){
            Map<String,List<IssueKbOverview>> map = list3.stream().collect(Collectors.groupingBy(IssueKbOverview::getIssueCode));
            for(Map.Entry<String, List<IssueKbOverview>> entry : map.entrySet()) {
                IssueKbOverview view = new IssueKbOverview();
                BeanUtils.copyProperties(entry.getValue().get(0), view);
                List<String> processDesps= entry.getValue().stream().map(k->k.getProcessDespToAI()).distinct().collect(Collectors.toList());
                view.setProcessDespToAI(JSON.toJSONString(processDesps));//知識解答=單身處理描述汇总
                view.setProcessDespToAI_arr(processDesps);//取问题描述的汇总
                result.add(view);
            }
        }
        return result;
    }

    /**
     * 案件标题包含筛选条件中问题描述的关键词
     * @param issueKbCondition
     * @param issueKbOverview
     * @param issueKbKeyWords
     * @return
     */
    private boolean filterIssueDesp(IssueKbCondition issueKbCondition,IssueKbOverview issueKbOverview,List<IssueKbKeyWord> issueKbKeyWords){
        if(StringUtils.isEmpty(issueKbCondition.getIssueDespKeyId())){
            return true;
        }
        if(CollectionUtils.isEmpty(issueKbKeyWords)){
            return true;
        }

        List<String> keywords = issueKbKeyWords
                .stream()
                .filter(k-> StringUtil.isNotEmpty(k.getKeyword()))
                .filter(k-> Arrays.asList(issueKbCondition.getIssueDespKeyId().split(",")).contains(String.valueOf(k.getId())))
                .map(k->k.getKeyword())
                .distinct()
                .collect(Collectors.toList());

        if(CollectionUtils.isEmpty(keywords)){
            return true;
        }

        Optional<String> op =  keywords
                .stream()
                .filter(keyword-> filterKeyWordInStr(Arrays.asList(keyword.split(",")),issueKbOverview.getIssueDespToAI()))
                .findFirst();

        return op.isPresent();
    }

    /**
     * 在关键词数组里面，看str是否含有关键词
     * @param keywords
     * @param str
     * @return
     */
    private boolean filterKeyWordInStr(List<String> keywords,String str){
        for (String keyword:keywords) {
            if(str.contains(keyword)){
                return true;
            }
        }
        return false;
    }

    /**
     * 单身处理描述排查筛选条件里设定的关键词的单身
     * @param issueKbCondition
     * @param issueKbOverview
     * @param issueKbKeyWords
     * @return
     */
    private boolean filterProcessDesp(IssueKbCondition issueKbCondition,IssueKbOverview issueKbOverview,List<IssueKbKeyWord> issueKbKeyWords){
        if(StringUtils.isEmpty(issueKbCondition.getIpDespKeyId())){
            return true;
        }
        if(CollectionUtils.isEmpty(issueKbKeyWords)){
            return true;
        }

        List<String> keywords = issueKbKeyWords
                .stream()
                .filter(k->StringUtil.isNotEmpty(k.getKeyword()))
                .filter(item1->Arrays.asList(issueKbCondition.getIpDespKeyId().split(",")).contains(String.valueOf(item1.getId())))
                .map(k->k.getKeyword())
                .distinct()
                .collect(Collectors.toList());

        if(CollectionUtils.isEmpty(keywords)){
            return true;
        }

        Optional<String> op =  keywords
                .stream()
                .filter(keyword->filterKeyWordInStr(Arrays.asList(keyword.split(",")),issueKbOverview.getProcessDespToAI()))
                .findFirst();

        return !op.isPresent();
    }

    /**
     * 筛选符合工时的暑假
     * @param issueKbCondition
     * @param issueKbOverview
     * @return
     */
    private boolean fillterHour(IssueKbCondition issueKbCondition, IssueKbOverview issueKbOverview){
        if(StringUtils.isEmpty(issueKbCondition.getHourCondition()) || StringUtils.isEmpty(issueKbCondition.getHour()) ){
            return true;
        }

        switch (issueKbCondition.getHourCondition()){
            case ">=":
                return Double.compare(issueKbOverview.getHour(),issueKbCondition.getHour()) >= 0;
            case "<=":
                return Double.compare(issueKbOverview.getHour(),issueKbCondition.getHour()) <= 0;
            default:
                return false;
        }

    }

    //保存执行状况记录
    private Long saveIssueKbExecuteRecord(String closeDate,String serviceRegion,String productCode,Long ikcId,int count){
        IssueKbExecuteRecord record = new IssueKbExecuteRecord();
        record.setServiceRegion(serviceRegion);
        record.setProductCode(productCode);
        record.setCloseDate(closeDate);
        record.setIkcId(ikcId);
        record.setCount(count);
        //当这一批次是0个案件，直接记录状态为完成；如果批次大于0，默认是生产中的状态
        record.setStatus(count != 0 ? IssueKbExecuteRecordStatus.INIT.getIndex() : IssueKbExecuteRecordStatus.SUCCESS.getIndex());
        iIssueSchedulingDaoV3.saveIssueKbExecuteRecord(record);
        return record.getId();
    }

}
