package com.digiwin.escloud.issueservice.t.excelimport.service.impl;


import com.digiwin.escloud.issueservice.cache.GrantProductCache;
import com.digiwin.escloud.issueservice.model.*;
import com.digiwin.escloud.issueservice.services.IIssueService;
import com.digiwin.escloud.issueservice.services.ITagService;
import com.digiwin.escloud.issueservice.t.excelimport.dao.ExcelImportMapper;
import com.digiwin.escloud.issueservice.t.excelimport.service.ExcelImportService;
import com.digiwin.escloud.issueservice.t.integration.fuguan.mq.issue.IssueFGProducer;
import com.digiwin.escloud.issueservice.t.integration.workday.model.IssueKey;
import com.digiwin.escloud.issueservice.t.integration.workday.mq.issue.IssueWorkDayMQConstant;
import com.digiwin.escloud.issueservice.t.integration.workday.mq.issue.IssueWorkDayProducer;
import com.digiwin.escloud.issueservice.t.issuedetail.dao.IssueProcessMapper;
import com.digiwin.escloud.issueservice.t.issuedetail.service.impl.IssueProcessServiceImpl;
import com.digiwin.escloud.issueservice.t.issuesubmit.dao.IssueSubmitMapper;
import com.digiwin.escloud.issueservice.t.issuesubmit.service.impl.IssueSubmitServiceImpl;
import com.digiwin.escloud.issueservice.t.model.cases.Cases;
import com.digiwin.escloud.issueservice.t.model.cases.CasesEmail;
import com.digiwin.escloud.issueservice.t.model.cases.IssueClassification;
import com.digiwin.escloud.issueservice.t.model.cases.Module;
import com.digiwin.escloud.issueservice.t.model.cases.constants.CaseProcessType;
import com.digiwin.escloud.issueservice.t.model.cases.constants.CaseStatus;
import com.digiwin.escloud.issueservice.t.model.cases.constants.SubmitWay;
import com.digiwin.escloud.issueservice.t.model.common.BaseResponse;
import com.digiwin.escloud.issueservice.t.model.common.ResponseStatus;
import com.digiwin.escloud.issueservice.t.model.constant.ERPProductCode;
import com.digiwin.escloud.issueservice.t.model.constant.JiaoFuUserType;
import com.digiwin.escloud.issueservice.t.model.excel.BatchResultCollector;
import com.digiwin.escloud.issueservice.t.model.excel.ExcelDTO;
import com.digiwin.escloud.issueservice.t.model.mail.MailSendSituation;
import com.digiwin.escloud.issueservice.t.service.IMailService;
import com.digiwin.escloud.issueservice.t.utils.CasesProcessUtils;
import com.digiwin.escloud.issueservice.t.utils.DateUtils;
import com.digiwin.escloud.issueservice.t.utils.StringUtil;
import com.digiwin.escloud.issueservice.v3.dao.impl.IssueDaoV3;
import com.digiwin.escloud.issueservice.v3.dao.impl.IssueDetailDaoV3;
import com.digiwin.escloud.issueservice.v3.service.impl.IssueDetailServiceV3;
import com.digiwin.escloud.userapi.model.customer.CustomerDetailInfo;
import com.digiwin.escloud.userapi.model.customer.ServiceStaffCond;
import com.digiwin.escloud.userapi.model.user.UserDetailInfo;
import com.digiwin.escloud.userapi.service.UserServiceFeignClient;
import com.google.gson.Gson;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.ImmutableTriple;
import org.apache.commons.lang3.tuple.Triple;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.amqp.core.AmqpTemplate;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.InputStream;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.time.LocalTime;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 案件详情接口实现类
 * Created by huly on 2019-7-25
 */
@Service
@Slf4j
public class ExcelImportServiceImpl implements ExcelImportService {
    private static final Pattern pattern = Pattern.compile("^(([1-9]{1}\\d*)|([0]{1}))(\\.(\\d){0,2})?$"); //工时的格式
    private static final String sep = System.getProperty("file.separator");
    private static final String HTML_TR_OPEN = "<tr>";
    private static final String HTML_TR_CLOSE = "</tr>";
    private static final String HTML_TD_OPEN = "<td>";
    private static final String HTML_TD_CLOSE = "</td>";
    private static final List<String> productCodeList = Stream.of(ERPProductCode.values()).map(ERPProductCode::getCode)
            .collect(Collectors.toList());

    //定义一个切割list，限制其最大长度的常量
    private static final int MAX_LIMIT = 300;
    @Autowired
    private ExcelImportMapper excelImportMapper;
    @Autowired
    private GrantProductCache grantProductCache;
    @Autowired
    private UserServiceFeignClient userServiceFeignClient;
    @Autowired
    private IssueProcessServiceImpl issueProcessService;
    @Autowired
    private IssueSubmitServiceImpl issueSubmitService;
    @Autowired
    private IssueFGProducer fgMq;
    @Autowired
    private IssueWorkDayProducer workDayProducerMq;
    @Autowired
    private AmqpTemplate amqpTemplate;
    @Autowired
    private IssueProcessMapper issueProcessMapper;
    @Autowired
    private IMailService mailService;
    @Autowired
    private CasesProcessUtils casesProcessUtils;
    @Autowired
    private IIssueService issueService;
    @Autowired
    private IssueSubmitMapper submitMapper;

    @Autowired
    private IssueDetailServiceV3 issueDetailServiceV3;

    @Autowired
    private IssueDaoV3 issueDaoV3;
    @Autowired
    private IssueDetailDaoV3 issueDetailDaoV3;
    @Autowired
    private ITagService tagService;
    /**
     * @Description: 批量上传
     * @Params:
     * @Return:
     **/
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackForClassName = "Exception")
    public BaseResponse excelUpload(String userId, MultipartFile file) throws Exception{

        if (file == null) {
            return BaseResponse.error(ResponseStatus.FILE_IS_NULL);
        }
        String fileName = file.getOriginalFilename();
        if ( !fileName.endsWith(".xls") && !fileName.endsWith(".xlsx")) {
            return BaseResponse.error(ResponseStatus.INTERNAL_SERVER_ERROR,"请上传正确格式文件！");
        }

        return importFileToDBNow(userId,fileName, file);
    }
    @Transactional(propagation = Propagation.REQUIRED, rollbackForClassName = "Exception")
    public BaseResponse importFileToDBNow(String userId, String fileName, MultipartFile file) throws Exception {
        BaseResponse baseResponse = new BaseResponse();
        log.info("进入服务的时间：" + DateUtils.getCurrentTime());
        log.info("切分长度:" + MAX_LIMIT);
        //服务最终返回结果集
        Map<String, Object> result = new HashMap<String, Object>();

        //错误信息收集容器
        BatchResultCollector collectors = new BatchResultCollector();
        //读取excel文件
        List<Map<String, Object>> listData = new ArrayList<>();
        //笔数
        int rowIndex = 0;
        Workbook wb = null;

        //载入档案
        baseResponse = createWorkbookByUrl(fileName, file);
        if(baseResponse.getStatus() != ResponseStatus.OK.getCode()){
            return baseResponse;
        }
        wb = (Workbook)baseResponse.getResponse();
        //取出导入资料页(第一个sheet)
        /*Sheet realSheet = wb.getSheetAt(0);
        int lastRowIndex = getExcelRealLine(realSheet);*/

        Sheet sheet = wb.getSheetAt(0);
        int lastRowIndex = sheet.getLastRowNum();
        //获取所有的行
        Iterator<Row> rowIterator = sheet.iterator();
        Row row = null;
//        所有列
        Iterator<Cell> cellIterator = null;
        Cell cell = null;
        Map<Integer, String> rowData = new HashMap<>();
        Map<String, Object> data;
        String uuid = StringUtil.getUUID();
        Map<Integer, String> questionMap = new HashMap<>();
        log.info("excel有" + lastRowIndex + "行数据");
        Map<String, List<String>> issueTypeMap= new HashMap<>();
        for(String p : productCodeList){
            issueTypeMap.put(p, excelImportMapper.queryIssueClassificationCodes(p));
        }
        //初始化行数为0，目的是忽略第一行中文
        while (rowIterator.hasNext()) {
            row = rowIterator.next();
            if (rowIndex > 0 && rowIndex <= lastRowIndex) {//rowIndex=0时，不执行，直接+1
                cellIterator = row.cellIterator();
                rowData.clear();
                //不可为空的进行格式校验
                while (cellIterator.hasNext()) {
                    cell = cellIterator.next();
                    if (cell == null) continue;
                    cell.setCellType(CellType.STRING);
                    rowData.put(cell.getColumnIndex(), cell.getStringCellValue());
                }
                if(rowData.size() < 1){
                    rowIndex++;
                    continue;
                }
                data = parseData(issueTypeMap, rowData, rowIndex, userId, uuid, collectors, questionMap);
                if (data == null) {
                    rowIndex++;
                    continue;
                }
                listData.add(data);
                log.info("listData 的数量 ： " + listData.size());
            } else if (rowIndex > lastRowIndex) {
                break;
            }
            rowIndex++;
        }
        //问题描述的重复检查
        List<String> questionList = new ArrayList<>();
        if (questionMap.size() != 0) {//问题描述map 收集到的 数量不为0时，遍历
            for (Map.Entry<Integer, String> entry : questionMap.entrySet()) {
                questionList.add(entry.getValue());
            }
            System.out.println("问题描述map：" + new Gson().toJson(questionMap));
            System.out.println("所有问题描述：" + new Gson().toJson(questionList));
            if (CasesProcessUtils.isRepeat(questionList)) {//有重复元素
                //查出重复的元素
                List<String> repeatElements = CasesProcessUtils.getDuplicateElements(questionList);
                log.info("重复元素：" + repeatElements);
                if (repeatElements.size() != 0) {
                    for (String repeat : repeatElements) {
                        List<Integer> allKey = CasesProcessUtils.getKey(questionMap, repeat);
                        if (allKey.size() != 0) {
                            collectors.addFailRecord("第" + allKey.get(0) + "行", "重复元素", "与" + StringUtils.join(allKey.subList(1, allKey.size()).toArray(), ",") + "行问题描述重复");
                        }
                    }
                }
            }
        }
        log.info("读完excel的时间：" + LocalTime.now());
        if (collectors.haveFailRecord()) {
            result.put("collectors", collectors.getBatchResult());
            result.put("status", false);
            return BaseResponse.error(ResponseStatus.UPLOAD_FAILD,result);
        }
//        开始入参到临时表
        int list_size = listData.size();
        if (list_size > MAX_LIMIT) {
            //定义循环次数
            int count = list_size / MAX_LIMIT;
            log.info("[for]开始写临时表的时间：" + LocalTime.now());
            for (int i = 0; i < count; i++) {
                List<Map<String, Object>> listData_part = listData.stream().skip(i * MAX_LIMIT * 1L).limit(MAX_LIMIT).collect(Collectors.toList()); //huly: 修复漏洞/bug i * MAX_LIMIT 改成 i * MAX_LIMIT * 1L
                excelImportMapper.insertTmp(listData_part);
//                dao.insert("dao.ExcelMapper.insertTmp", map_write_tmp);
            }
            List<Map<String, Object>> list_rest = listData.stream().skip(count * MAX_LIMIT * 1L).collect(Collectors.toList()); //huly: 修复漏洞/bug count * MAX_LIMIT 改成 count * MAX_LIMIT * 1L
            if (list_rest != null && !list_rest.isEmpty()) {
                excelImportMapper.insertTmp(list_rest);
//                dao.insert("dao.ExcelMapper.insertTmp", map_write_tmp02);
            }
        } else {
            log.info("不走[for]开始写临时表的时间：" + LocalTime.now());
            excelImportMapper.insertTmp(listData);
//            dao.insert("dao.ExcelMapper.insertTmp", map_write_tmp);
        }
        log.info("写完临时表的时间：" + LocalTime.now());
        //返回参数
        result.put("fileId", uuid);
        result.put("status", true);
        log.info("导入excel到临时表服务结束时间：" + LocalTime.now());
        return BaseResponse.ok(result);
    }

    /**
     * @Description: 批量上传V3
     * @Params:
     * @Return:
     **/
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackForClassName = "Exception")
    public BaseResponse excelUploadV3(String userId, String language, MultipartFile file, Boolean consultant) throws Exception{

        if (file == null) {
            return BaseResponse.error(ResponseStatus.FILE_IS_NULL);
        }
        String fileName = file.getOriginalFilename();
        if ( !fileName.endsWith(".xls") && !fileName.endsWith(".xlsx")) {
            if("zh-CN".equals(language)){
                return BaseResponse.error(ResponseStatus.INTERNAL_SERVER_ERROR,"请上传正确格式文件！");
            }else {
                return BaseResponse.error(ResponseStatus.INTERNAL_SERVER_ERROR,"請上傳正確格式文件！");
            }
        }
        return importFileToDBNowV3(userId,fileName, file, language,consultant);
    }
    @Transactional(propagation = Propagation.REQUIRED, rollbackForClassName = "Exception")
    public BaseResponse importFileToDBNowV3(String userId, String fileName, MultipartFile file, String language,Boolean consultant) throws Exception {
        BaseResponse baseResponse = new BaseResponse();
        log.info("进入服务的时间：" + DateUtils.getCurrentTime());
        log.info("切分长度:" + MAX_LIMIT);
        //服务最终返回结果集
        Map<String, Object> result = new HashMap<String, Object>();

        //错误信息收集容器
        BatchResultCollector collectors = new BatchResultCollector();
        //读取excel文件
        List<Map<String, Object>> listData = new ArrayList<>();
        //笔数
        int rowIndex = 0;
        Workbook wb = null;

        //载入档案
        baseResponse = createWorkbookByUrl(fileName, file);
        if(baseResponse.getStatus() != ResponseStatus.OK.getCode()){
            return baseResponse;
        }
        wb = (Workbook)baseResponse.getResponse();
        //取出导入资料页(第一个sheet)
        /*Sheet realSheet = wb.getSheetAt(0);
        int lastRowIndex = getExcelRealLine(realSheet);*/

        Sheet sheet = wb.getSheetAt(0);
        int lastRowIndex = sheet.getLastRowNum();
        if(lastRowIndex==0){
            return BaseResponse.error(ResponseStatus.UPLOAD_FAILD);
        }

        //获取所有的行
        Iterator<Row> rowIterator = sheet.iterator();
        Row row = null;
//        所有列
        Iterator<Cell> cellIterator = null;
        Cell cell = null;
        Map<Integer, String> rowData = new HashMap<>();
        Map<String, Object> data;
        String uuid = StringUtil.getUUID();
        Map<Integer, String> questionMap = new HashMap<>();
        log.info("excel有" + lastRowIndex + "行数据");
        Map<String, List<IssueClassification>> issueTypeMap= excelImportMapper.queryIssueClassificationCodesV3().stream().collect(Collectors.groupingBy(IssueClassification::getProductCode));
        List<String> productList = excelImportMapper.productList();//所有产品线
        //顾问只能提交事业部的数据
        String acpCode = "";
        if(Boolean.TRUE.equals(consultant) && StringUtils.isNotEmpty(userId)){ //修复bug 修改为equals比较
             acpCode = excelImportMapper.getAcpCode(userId);
             if(StringUtils.isEmpty(acpCode)){
                 BaseResponse.error(ResponseStatus.INTERNAL_SERVER_ERROR,"顾问请先维护事业部编号！");
             }
        }
        /*Map<String, List<String>> issueTypeMap= new HashMap<>();
        for(String p : productCodeList){
            issueTypeMap.put(p, excelImportMapper.queryIssueClassificationCodes(p));
        }*/
        //初始化行数为0，目的是忽略第一行中文
        while (rowIterator.hasNext()) {
            row = rowIterator.next();
            if (rowIndex > 0 && rowIndex <= lastRowIndex) {//rowIndex=0时，不执行，直接+1
                cellIterator = row.cellIterator();
                rowData.clear();
                //不可为空的进行格式校验
                while (cellIterator.hasNext()) {
                    cell = cellIterator.next();
                    if (cell == null) continue;
                    cell.setCellType(CellType.STRING);
                    rowData.put(cell.getColumnIndex(), cell.getStringCellValue());
                }
                if(rowData.size() < 1){
                    rowIndex++;
                    continue;
                }
                data = parseDataV3(issueTypeMap, rowData, rowIndex, userId, uuid, collectors, questionMap, productList, language,consultant,acpCode);
                if (data == null) {
                    rowIndex++;
                    continue;
                }
                listData.add(data);
                log.info("listData 的数量 ： " + listData.size());
            } else if (rowIndex > lastRowIndex) {
                break;
            }
            rowIndex++;
        }
        //问题描述的重复检查
        List<String> questionList = new ArrayList<>();
        if (questionMap.size() != 0) {//问题描述map 收集到的 数量不为0时，遍历
            for (Map.Entry<Integer, String> entry : questionMap.entrySet()) {
                questionList.add(entry.getValue());
            }
            System.out.println("问题描述map：" + new Gson().toJson(questionMap));
            System.out.println("所有问题描述：" + new Gson().toJson(questionList));
            if (CasesProcessUtils.isRepeat(questionList)) {//有重复元素
                //查出重复的元素
                List<String> repeatElements = CasesProcessUtils.getDuplicateElements(questionList);
                log.info("重复元素：" + repeatElements);
                if (repeatElements.size() != 0) {
                    for (String repeat : repeatElements) {
                        List<Integer> allKey = CasesProcessUtils.getKey(questionMap, repeat);
                        if (allKey.size() != 0) {
                            collectors.addFailRecord("第" + allKey.get(0) + "行", "重复元素", "与" + StringUtils.join(allKey.subList(1, allKey.size()).toArray(), ",") + "行问题描述重复");
                        }
                    }
                }
            }
        }
        log.info("读完excel的时间：" + LocalTime.now());
        if (collectors.haveFailRecord()) {
            result.put("collectors", collectors.getBatchResult());
            result.put("status", false);
            return BaseResponse.error(ResponseStatus.UPLOAD_FAILD,result);
        }
//        开始入参到临时表
        int list_size = listData.size();
        if (list_size > MAX_LIMIT) {
            //定义循环次数
            int count = list_size / MAX_LIMIT;
            log.info("[for]开始写临时表的时间：" + LocalTime.now());
            for (int i = 0; i < count; i++) {
                List<Map<String, Object>> listData_part = listData.stream().skip(i * MAX_LIMIT * 1L).limit(MAX_LIMIT).collect(Collectors.toList()); //huly: 修复漏洞/bug i * MAX_LIMIT 改成 i * MAX_LIMIT * 1L
                excelImportMapper.insertTmp(listData_part);
//                dao.insert("dao.ExcelMapper.insertTmp", map_write_tmp);
            }
            List<Map<String, Object>> list_rest = listData.stream().skip(count * MAX_LIMIT * 1L).collect(Collectors.toList()); //huly: 修复漏洞/bug count * MAX_LIMIT 改成 count * MAX_LIMIT * 1L
            if (list_rest != null && !list_rest.isEmpty()) {
                excelImportMapper.insertTmp(list_rest);
//                dao.insert("dao.ExcelMapper.insertTmp", map_write_tmp02);
            }
        } else {
            log.info("不走[for]开始写临时表的时间：" + LocalTime.now());
            excelImportMapper.insertTmp(listData);
//            dao.insert("dao.ExcelMapper.insertTmp", map_write_tmp);
        }
        log.info("写完临时表的时间：" + LocalTime.now());
        //返回参数
        result.put("fileId", uuid);
        result.put("status", true);
        log.info("导入excel到临时表服务结束时间：" + LocalTime.now());
        return BaseResponse.ok(result);
    }
    @Deprecated
    private void checkIfCaseIdRepeat(String uuid) throws Exception {
        do {
            List<String> repeatCaseIdCases = excelImportMapper.findAllRepeatCaseIdCases(uuid);
//                    dao.query("dao.ExcelMapper.findAllRepeatCaseIdCases", uuid);
            if (repeatCaseIdCases != null && repeatCaseIdCases.size() != 0) {
                for (String repeatCaseId : repeatCaseIdCases) {
                    //确认有多少条数据，然后批次将他们的更改掉
                    Map<String, String> map = new HashMap<>();
                    map.put("caseId", repeatCaseId);
                    map.put("fileId", uuid);
                    List<ExcelDTO> caseIdAndIdList = excelImportMapper.findRepeatCasesDetailByCaseId(map);
//                            dao.query("dao.ExcelMapper.findRepeatCasesDetailByCaseId", map);
                    for (ExcelDTO excelDTO : caseIdAndIdList) {
                        excelDTO.setCaseId(StringUtil.getCaseId());
                        excelDTO.setFileId(uuid);
                        excelImportMapper.updateRepeatCaseId(excelDTO);
//                        dao.update("dao.ExcelMapper.updateRepeatCaseId", excelDTO);
                    }
                }
            } else break;
        } while (true);
    }
    /**
     * 表格数据转为map
     *
     * @param rowData  行数据
     * @param rowIndex 行号
     * @param userId   制定人员
     * @param issueTypeCodeMap 问题类型编码，key为产品编号，value为问题分类编码列表
     * @return 返回值
     * @throws Exception 异常
     */
    private Map<String, Object> parseData(Map<String, List<String>> issueTypeCodeMap, Map<Integer, String> rowData, int rowIndex, String userId, String uuid, BatchResultCollector collectors, Map<Integer, String> questionMap) throws Exception {
	    log.info("此时的行是：" + (rowIndex + 1));
    	if (rowData.values().stream().allMatch(StringUtils::isEmpty)) {
            log.info("数据中存在null");
            return null;
        }
        Map<String, Object> map = new HashMap<>();
        map.put("fileId", uuid);
        map.put("item", rowIndex);
        String line = String.valueOf(rowIndex + 1); //错误提示的行号与xlsx中的一致
        //检验客服代号
        String custId = rowData.get(0);
        //设置产品信息
        String productCode = null;
        if (StringUtils.isEmpty(rowData.get(0))) {
            collectors.addFailRecord("第" + line + "行", "必填字段", "客服代号不能为空.");
            map = null;
        } else {
            // excel中的产品信息为编号.名称的方式：100.T100
            String productInfo = rowData.get(6);
            if (StringUtils.isEmpty(productInfo)) {
                collectors.addFailRecord("第" + line + "行", "错误数据", "产品名称不能为空");
                map = null;
            } else {
                if (!productInfo.contains(".")) {
                    collectors.addFailRecord("第" + line + "行", "错误数据", "请检查产品名称是否正确.");
                    map = null;
                }
                int indexOf = productInfo.indexOf(".");
                if (indexOf != -1) {
                    productCode = productInfo.substring(0, indexOf);
                    if (!productCodeList.contains(productCode)) {
                        collectors.addFailRecord("第" + line + "行", "错误数据", "不支持的产品代号.");
                        productCode = null;
                        map = null;
                    }
                    CustomerDetailInfo customerInfo = userServiceFeignClient.getCustomerInfoByIdAndProduct(custId, productCode);
                    if (customerInfo == null) {
                        collectors.addFailRecord("第" + line + "行", "错误数据", "无法找到客户信息:客服代号或产品代号不存在.");
                        map = null;
                    } else {
                        putMapping(map, "project", productCode);
                        putMapping(map, "cust_id", custId);//客服代号
                        putMapping(map, "cust_name", customerInfo.getCustomerName());
                    }
                } else {
                    collectors.addFailRecord("第" + line + "行", "错误数据", "请检查产品名称是否正确.");
                    map = null;
                }
            }
        }

        //检验作业编号
        String program = null;
        String moduleCode = null;
        if (!StringUtils.isEmpty(rowData.get(1))) {
            //替换空白
            program = rowData.get(1).replaceAll("\\s+", "");
            putMapping(map, "program", program);//作业编号
            moduleCode = program.length() >= 3 ? program.substring(0, 3).toUpperCase() : program.toUpperCase();//模组编号(如果大于3就取0-3，如果小于就取全部）
            putMapping(map, "module_code", moduleCode);
        } else {
            collectors.addFailRecord("第" + line + "行", "错误数据", "作业编号不能为空");
            map = null;
        }
        //检验问题标题
        if (!StringUtils.isEmpty(rowData.get(2))) {
            if (rowData.get(2).length() > 50) {
                collectors.addFailRecord("第" + line + "行", "格式规范", "问题标题长度不能大于50.");
                map = null;
            } else {
                putMapping(map, "question_title", rowData.get(2));//问题
            }
        } else {
            collectors.addFailRecord("第" + line + "行", "必填字段", "问题标题不能为空.");
            map = null;
        }
        //检验问题描述
        if (!StringUtils.isEmpty(rowData.get(3))) {
            putMapping(map, "question", rowData.get(3));//问题
            questionMap.put((rowIndex + 1), rowData.get(3));
        } else {
            collectors.addFailRecord("第" + line + "行", "必填字段", "问题描述不能为空.");
            map = null;
        }
        putMapping(map, "submited_id", userId);
        putMapping(map, "followed", "N");
        //检验反馈人是否存在
        String applicantItCode = rowData.get(4);
        if(applicantItCode == null){
            collectors.addFailRecord("第" + line + "行", "错误数据", "反馈人不能为空.");
            map = null;
        } else {
            UserDetailInfo applicant = userServiceFeignClient.getUserDetailByItCode(applicantItCode);
            if (applicant == null) {//如果导入时的反馈人在服务云上没有资料，那么就直接插入到username中
                collectors.addFailRecord("第" + line + "行", "错误数据", "反馈人在服务云不存在.");
                map = null;
            } else {
                putMapping(map, "applicant_id", applicant.getUserId());
                //设置反馈人信息
                putMapping(map, "email", applicant.getEmail());
                putMapping(map, "username", applicant.getName());
                putMapping(map, "phone", applicant.getPhone());
            }
        }

        putMapping(map, "create_time", StringUtils.isEmpty(rowData.get(5)) ? DateUtils.getCurrentTime() :
                rowData.get(5).contains("-") ? rowData.get(5) : DateUtils.excelDateTransfer(Double.parseDouble(rowData.get(5))));
        //紧急度
        putMapping(map, "emergency", StringUtils.isEmpty(rowData.get(7)) ? "N" : rowData.get(7));
        // 问题类型
        String questionType = StringUtils.isEmpty(rowData.get(9)) ? "" : rowData.get(9).split("\\.")[0];
        //判断问题类型是否存在服务云上
        List<String> questionTypeStringList = issueTypeCodeMap.get(productCode);
        if(!CollectionUtils.isEmpty(questionTypeStringList)){
            if (!StringUtils.isEmpty(questionType)){
                if(questionTypeStringList.contains(questionType)) {
                    putMapping(map, "question_type", questionType);
                } else {
                    collectors.addFailRecord("第" + line + "行", "错误数据", "问题类型编号不存在服务云中.");
                    map = null;
                }
            }
        }
        //处理人员，栏位为空值则抓窗口人员，不为空时要检查是否存在
        UserDetailInfo handlerDetail = null;
        if (StringUtils.isEmpty(rowData.get(10))) {
            if (!StringUtils.isEmpty(custId) && !StringUtils.isEmpty(productCode)) {//custId 不为空才继续往下走
                //查询module-->领域;
                String bigModuleCode = StringUtils.isEmpty(moduleCode) ? "" : moduleCode.toUpperCase();
                Module module = casesProcessUtils.dealArea(bigModuleCode, productCode);
                // 查询客户窗口人员列表
                ServiceStaffCond cond = new ServiceStaffCond();
                cond.setCustomerServiceCode(custId);
                cond.setProductCode(productCode);
                cond.setErpSystemCode(moduleCode);
                if(module == null) {
                    cond.setArea(null);
                } else {
                    cond.setArea(module.getArea());
                }
                List<String> windowStaffs = userServiceFeignClient.getServiceStaff(cond);
                if (CollectionUtils.isEmpty(windowStaffs)) {
                    collectors.addFailRecord("第" + line + "行", "缺漏数据",
                            String.format("客服代号%s产品编号%s:没有维护处理问题的窗口人员.", custId, productCode));
                    map = null;
                } else {
                    String handlerId = windowStaffs.get(0);
                    handlerDetail = userServiceFeignClient.getUserDetailByWorkNo(handlerId);
                    if (handlerDetail == null) {
                        collectors.addFailRecord("第" + line + "行", "错误数据", "窗口处理人员在服务云上没有资料.");
                        map = null;
                    }
                }
            } else {
                collectors.addFailRecord("第" + line + "行", "错误数据", "没有找到问题处理人员.");
                map = null;
            }
        } else {
        	// excel中是itcode. 将itcode换成workno
	        String itcode = rowData.get(10);
            handlerDetail = userServiceFeignClient.getUserDetailByItCode(itcode);
	        if(handlerDetail == null){
                collectors.addFailRecord("第" + line + "行", "错误数据", "问题处理人员在服务云不存在.");
                map = null;
            }
        }

        String initStatus = StringUtils.isEmpty(rowData.get(13)) ? "1" : rowData.get(13).substring(0, 1);
        if (!initStatus.equals("1") && !initStatus.equals("2")) {
            collectors.addFailRecord("第" + line + "行", "格式规范", "案件状态格式不正确，必须：1.未结案 或 2.已结案.");
            map = null;
        } else {
            putMapping(map, "init_status", initStatus);
            //当初始状态为2.已结案时，问题类型，处理回复，工时不能为空
            log.info("工时值为：" + rowData.get(11));
            switch (initStatus) {
                case "2":
                    if (StringUtils.isEmpty(rowData.get(9)) || StringUtils.isEmpty(rowData.get(10))
                            || StringUtils.isEmpty(rowData.get(11)) || StringUtils.isEmpty(rowData.get(12))) {
                        collectors.addFailRecord("第" + line + "行", "必填字段",
                                "案件状态为已结案，问题类型、处理人、处理工时、处理回复不能为空.");
                        map = null;
                    }
                    putMapping(map, "comes", SubmitWay.EXCELCLOSE.getSymbol());
                    //如果是已结案，就直接带完所有的流程
                    putMapping(map, "status", CaseStatus.CLOSED.getStatus());
                    if (handlerDetail != null) {
                        putMapping(map, "handler_id", handlerDetail.getUserId());
                    }
                    break;
                case "1":
                    if (handlerDetail != null) {
                        // 表中handler_id需要存userid
                        putMapping(map, "handler_id", handlerDetail.getUserId());
                        if (handlerDetail.getJiaofuType() == JiaoFuUserType.JIAOFUFUWU.getValue()) {
                            putMapping(map, "status", CaseStatus.JIAOFU_SERVICE_HANDLING.getStatus());
                        } else {
                            // 默认处理人员角色为顾问
                            putMapping(map, "status", CaseStatus.JIAOFU_CONSULTANT_HANDLING.getStatus());
                        }
                        putMapping(map, "comes", SubmitWay.EXCELUNCLOSE.getSymbol());
                    }
                    break;
            }
        }
        //判断工时的格式是否符合规范
        if (!StringUtils.isEmpty(rowData.get(11))) {
            //        数字判断
            if (!pattern.matcher(rowData.get(11)).matches()) {
                collectors.addFailRecord("第" + line + "行", "格式规范", "工时必须填写为数字.");
                map = null;
            }
            putMapping(map, "total_work_hours", StringUtils.isEmpty(rowData.get(11)) ? "" : rowData.get(11));
        }
        putMapping(map, "handler_detail", StringUtils.isEmpty(rowData.get(12)) ? "" : rowData.get(12));
        //判断【是否已经现场处理】字段
        if (StringUtils.isEmpty(rowData.get(14))) {
            collectors.addFailRecord("第" + line + "行", "必填字段", "【是否已经现场处理】为必填字段，不能为空.");
            map = null;
        } else {
            putMapping(map, "is_live_process", rowData.get(14).substring(0, 1).equals("1") ? "Y" : "N");//是否现场处理过,1是，2否
        }
        putMapping(map, "update_time", StringUtils.isEmpty(rowData.get(15)) ? DateUtils.getCurrentTime() :
                rowData.get(15).contains("-") ? rowData.get(15) : DateUtils.excelDateTransfer(Double.parseDouble(rowData.get(15))));
        //比较两个时间差
        boolean timeFlag = excelImportMapper.compareTime(map);
        if (timeFlag) {
            collectors.addFailRecord("第" + line + "行", "格式规范", "结案时间不可以小于反馈时间.");
            map = null;
        }
        if("8".equalsIgnoreCase(questionType)){
            // 客制Bug负责人.
            String bugOwner = rowData.get(16);
            if (StringUtils.isEmpty(bugOwner)) {
                collectors.addFailRecord("第" + line + "行", "错误数据", "问题类型是8.客制BUG, 必须添加客制Bug负责人");
                map = null;
            } else {
                // itcode-->userId
                UserDetailInfo bugOwnerUser = userServiceFeignClient.getUserDetailByItCode(bugOwner);
                if (bugOwnerUser != null && !StringUtils.isEmpty(bugOwnerUser.getUserId())) {
                    putMapping(map, "bug_owner", bugOwnerUser.getUserId());
                } else {
                    collectors.addFailRecord("第" + line + "行", "错误数据", "客制Bug负责人在服务云不存在.");
                    map = null;
                }
            }
        } else {
            putMapping(map, "bug_owner", null);
        }
        //2023-11-03 先去掉校验，等确认
        /*if("T0341".equalsIgnoreCase(questionType) || "T05C1".equalsIgnoreCase(questionType)){
            // 问题类型T0341、T05C1 当选择这两种问题类型时：卡控项目号、合同号触发必填
            String contractNo = rowData.get(17);
            if (StringUtils.isEmpty(contractNo)) {
                collectors.addFailRecord("第" + line + "行", "必填字段", "当问题类型为T0341或T05C1时，【合同编号】为必填字段，不能为空.");
                map = null;
            } else {
                putMapping(map, "contractNo", rowData.get(17));
            }

            String projectNo = rowData.get(18);
            if (StringUtils.isEmpty(projectNo)) {
                collectors.addFailRecord("第" + line + "行", "必填字段", "当问题类型为T0341或T05C1时，【项目编号】为必填字段，不能为空.");
                map = null;
            } else {
                putMapping(map, "projectNo", rowData.get(18));
            }
        }*/

        return map;
    }

    /**
     * 表格数据转为map
     *
     * @param rowData  行数据
     * @param rowIndex 行号
     * @param userId   制定人员
     * @param issueTypeCodeMap 问题类型编码，key为产品编号，value为问题分类编码列表
     * @return 返回值
     * @throws Exception 异常
     */
    private Map<String, Object> parseDataV3(Map<String, List<IssueClassification>> issueTypeCodeMap, Map<Integer, String> rowData, int rowIndex, String userId, String uuid, BatchResultCollector collectors, Map<Integer, String> questionMap, List<String> productList, String language,Boolean consultant,String acpCode) throws Exception {
        log.info("此时的行是：" + (rowIndex + 1));
        if (rowData.values().stream().allMatch(StringUtils::isEmpty)) {
            log.info("数据中存在null");
            return null;
        }
        Map<String, Object> map = new HashMap<>();
        map.put("fileId", uuid);
        map.put("item", rowIndex);
        String line = String.valueOf(rowIndex + 1); //错误提示的行号与xlsx中的一致
        //检验客服代号
        String custId = rowData.get(0);
        if (StringUtils.isEmpty(custId)) {
            if("zh-CN".equals(language)){
                collectors.addFailRecord("第" + line + "行", "必填字段", "客服代号不能为空.");
            }else {
                collectors.addFailRecord("第" + line + "行", "必填字段", "客服代號不能爲空.");
            }
            map = null;
        }
        //检验产品信息
        // excel中的产品信息为编号.名称的方式：E10_37
        String productInfo = rowData.get(1);
        String productCode = null;
        if (StringUtils.isEmpty(productInfo)) {
            if("zh-CN".equals(language)){
                collectors.addFailRecord("第" + line + "行", "必填字段", "产品名称不能为空");
            }else {
                collectors.addFailRecord("第" + line + "行", "必填字段", "產品名稱不能爲空.");
            }
            map = null;
        } else {
            int indexOf = productInfo.indexOf("_");
            if (indexOf != -1) {
                productCode = productInfo.substring(indexOf+1, productInfo.length());
                if (!productList.contains(productCode)) {
                    if("zh-CN".equals(language)){
                        collectors.addFailRecord("第" + line + "行", "错误数据", "不支持的产品代号.");
                    }else {
                        collectors.addFailRecord("第" + line + "行", "錯誤數據", "不支持的產品代號.");
                    }
                    productCode = null;
                    map = null;
                }

                if(!StringUtils.isEmpty(productCode) && !StringUtils.isEmpty(custId)){
                    CustomerDetailInfo customerInfo = new CustomerDetailInfo();
                    //顾问只能提交事业部的数据
                    if(Boolean.TRUE.equals(consultant)){ //修复bug 修改为equals比较
                        customerInfo = excelImportMapper.getCustomerInfoByIdAndProductAndAcpCode(custId, productCode, acpCode);
                    }else {
                        customerInfo = userServiceFeignClient.getCustomerInfoByIdAndProduct(custId, productCode);
                    }
                    if (customerInfo == null) {
                        if("zh-CN".equals(language)){
                            collectors.addFailRecord("第" + line + "行", "错误数据", "无法找到客户信息：客服代号或产品代号不存在.");
                        }else {
                            collectors.addFailRecord("第" + line + "行", "錯誤數據", "無法找到客戶信息：客服代號或產品代號不存在.");
                        }
                        map = null;
                    } else {
                        putMapping(map, "project", productCode);
                        putMapping(map, "cust_id", custId);//客服代号
                        putMapping(map, "cust_name", customerInfo.getCustomerName());
                    }
                }

            } else {
                if("zh-CN".equals(language)){
                    collectors.addFailRecord("第" + line + "行", "错误数据", "请检查产品名称是否正确.");
                }else {
                    collectors.addFailRecord("第" + line + "行", "錯誤數據", "請檢查產品名稱是否正確.");
                }
                map = null;
            }
        }

        //检验模组，导入产品为“装配制造”时，模组/应用应必填
        if("161".equals(productCode)){
            if (StringUtils.isEmpty(rowData.get(2))) {
                if("zh-CN".equals(language)){
                    collectors.addFailRecord("第" + line + "行", "错误数据", "161产品线，模组不能为空.");
                }else {
                    collectors.addFailRecord("第" + line + "行", "錯誤數據", "161產品綫，模組不能爲空.");
                }
                map = null;
            }
            if (StringUtils.isEmpty(rowData.get(3))) {
                if("zh-CN".equals(language)){
                    collectors.addFailRecord("第" + line + "行", "错误数据", "161产品线，作业不能为空.");
                }else {
                    collectors.addFailRecord("第" + line + "行", "錯誤數據", "161產品綫，作業不能爲空.");
                }
                map = null;
            }
        }

        if (!StringUtils.isEmpty(rowData.get(2))) {
            //替换空白
            String moduleCode = rowData.get(2).replaceAll("\\s+", "");
            //校验模组的合法性
            if(!StringUtils.isEmpty(productCode)){
                ErpSystemCodeData module = excelImportMapper.getModuleInfoByProduct(productCode,moduleCode);
                if (module == null) {
                    if("zh-CN".equals(language)){
                        collectors.addFailRecord("第" + line + "行", "错误数据", "请检查模组是否正确.");
                    }else {
                        collectors.addFailRecord("第" + line + "行", "錯誤數據", "請檢查模組是否正確.");
                    }
                    map = null;
                } else {
                    putMapping(map, "module_code", moduleCode);
                    //检验作业的合法性
                    String programCode = null;
                    if (!StringUtils.isEmpty(rowData.get(3))) {
                        //替换空白
                        programCode = rowData.get(3).replaceAll("\\s+", "");
                        //校验模组的合法性
                        ErpProgramData program = excelImportMapper.getProgramInfoByProduct(productCode,moduleCode,programCode);
                        if (program == null) {
                            if("zh-CN".equals(language)){
                                collectors.addFailRecord("第" + line + "行", "错误数据", "请检查作业是否正确.");
                            }else {
                                collectors.addFailRecord("第" + line + "行", "錯誤數據", "請檢查作業是否正確.");
                            }
                            map = null;
                        } else {
                            putMapping(map, "program", programCode);
                        }
                    }
                }
            }

        }else{
            if(!StringUtils.isEmpty(rowData.get(3))){
                if("zh-CN".equals(language)){
                    collectors.addFailRecord("第" + line + "行", "错误数据", "有作业编号时，模组不能为空.");
                }else {
                    collectors.addFailRecord("第" + line + "行", "錯誤數據", "有作業編號時，模組不能為空.");
                }
            }
        }

        //检验问题描述
        if (!StringUtils.isEmpty(rowData.get(4))) {
            putMapping(map, "question", rowData.get(4));//问题
            questionMap.put((rowIndex + 1), rowData.get(4));
        } else {
            if("zh-CN".equals(language)){
                collectors.addFailRecord("第" + line + "行", "必填字段", "问题描述不能为空.");
            }else {
                collectors.addFailRecord("第" + line + "行", "必填字段", "問題描述不能爲空.");
            }
            map = null;
        }
        putMapping(map, "submited_id", userId);
        putMapping(map, "followed", "N");
        //检验反馈人是否存在
        String applicantItCode = rowData.get(5);
        if(!StringUtils.isEmpty(applicantItCode)){
            putMapping(map, "applicant_id", applicantItCode);
        } else {
            if("zh-CN".equals(language)){
                collectors.addFailRecord("第" + line + "行", "错误数据", "反馈人姓名不能为空.");
            }else {
                collectors.addFailRecord("第" + line + "行", "錯誤數據", "反饋人姓名不能爲空.");
            }
            map = null;
        }
        //反馈时间
        if (!StringUtils.isEmpty(rowData.get(6))) {
            putMapping(map, "create_time", rowData.get(6).contains("/") ? rowData.get(6) : DateUtils.excelDateTransfer(Double.parseDouble(rowData.get(6))));
        } else {
            if("zh-CN".equals(language)){
                collectors.addFailRecord("第" + line + "行", "必填字段", "反馈时间不能为空.");
            }else {
                collectors.addFailRecord("第" + line + "行", "必填字段", "反饋時間不能爲空.");
            }
            map = null;
        }
        //紧急度
        putMapping(map, "emergency", StringUtils.isEmpty(rowData.get(7)) ? "N" : rowData.get(7));
        // 问题类型
        if(!StringUtils.isEmpty(rowData.get(8))){
            String[] arr = rowData.get(8).split("\\.");
            if(arr != null){
                if(arr.length != 3){
                    if("zh-CN".equals(language)){
                        collectors.addFailRecord("第" + line + "行", "错误数据", "问题类型格式不正确.");
                    }else {
                        collectors.addFailRecord("第" + line + "行", "錯誤數據", "問題類型格式不正確中.");
                    }
                    map = null;
                }else {
                    String questionType = StringUtils.isEmpty(rowData.get(8)) ? "" : rowData.get(8).split("\\.")[1];
                    //判断问题类型是否存在服务云上
                    List<IssueClassification> questionTypeStringList = issueTypeCodeMap.get(productCode);
                    if(!CollectionUtils.isEmpty(questionTypeStringList)){
                        if (!StringUtils.isEmpty(questionType)){
                            List<String> tempList = questionTypeStringList.stream().map(o->o.getIssueClassification()).collect(Collectors.toList());
                            if(tempList.contains(questionType)) {
                                putMapping(map, "question_type", questionType);
                            } else {
                                if("zh-CN".equals(language)){
                                    collectors.addFailRecord("第" + line + "行", "错误数据", "问题类型编号不存在服务云中.");
                                }else {
                                    collectors.addFailRecord("第" + line + "行", "錯誤數據", "問題類型編號不存在服務雲中.");
                                }
                                map = null;
                            }
                        }
                    }

                    //9 标签
                    if(!StringUtils.isEmpty(rowData.get(9))){
                        //标签数据不为空时，校验，
                        String questionTypeName = StringUtils.isEmpty(rowData.get(8)) ? "" : rowData.get(8).split("\\.")[2];
                        if(StringUtils.isEmpty(questionTypeName)){
                            if("zh-CN".equals(language)){
                                collectors.addFailRecord("第" + line + "行", "错误数据", "问题类型为空时，不能录入标签.");
                            }else {
                                collectors.addFailRecord("第" + line + "行", "錯誤數據", "問題類型為空時，不能錄入標簽.");
                            }
                            map = null;
                        }else {
                            //校验标签的准确性
                            String[] tagArr =rowData.get(9).split(";");
                            if(tagArr != null && tagArr.length > 0){
                                for(int k = 0 ;k < tagArr.length;k++){
                                    com.digiwin.escloud.issueservice.model.BaseResponse baseResponse = tagService.checkTagInGroup(questionTypeName,tagArr[k]);
                                    if(baseResponse.getStatus() != ResponseStatus.OK.getCode()){
                                        if("zh-CN".equals(language)){
                                            collectors.addFailRecord("第" + line + "行", "错误数据", "该问题类型不能录入此标签:" + tagArr[k]);
                                        }else {
                                            collectors.addFailRecord("第" + line + "行", "錯誤數據", "該問題類型不能錄入此標簽:" + tagArr[k]);
                                        }
                                        map = null;
                                        break;
                                    }
                                }
                            }
                        }
                        putMapping(map, "tag", rowData.get(9));
                    }
                }
            }
        }


        //处理人员，不为空时要检查是否存在
        UserDetailInfo handlerDetail = null;
        if (!StringUtils.isEmpty(rowData.get(10))) {
            // excel中是itcode. 将itcode换成workno
            String itcode = rowData.get(10);
            handlerDetail = userServiceFeignClient.getUserDetailByItCode(itcode);
            if(handlerDetail == null){
                if("zh-CN".equals(language)){
                    collectors.addFailRecord("第" + line + "行", "错误数据", "问题处理人员不存在服务云.");
                }else {
                    collectors.addFailRecord("第" + line + "行", "錯誤數據", "問題處理人員不存在服務雲中.");
                }
                map = null;
            }else {
                //处理人为空时，默认是操作人本人，不为空时，为导入的人
                putMapping(map, "handler_id", handlerDetail.getUserId());
                //校验授权产品线
                List<String> grantProductList = grantProductCache.grantProductList(handlerDetail.getUserId());//客服的所有授权产品线
                if (!StringUtils.isEmpty(productCode) && !grantProductList.contains(productCode)) {
                    if("zh-CN".equals(language)){
                        collectors.addFailRecord("第" + line + "行", "错误数据", "不是授权产品线.");
                    }else {
                        collectors.addFailRecord("第" + line + "行", "錯誤數據", "不是授權產品綫.");
                    }
                    productCode = null;
                    map = null;
                }
            }
        }else {
            putMapping(map, "handler_id", userId);
            //校验授权产品线
            List<String> grantProductList = grantProductCache.grantProductList(userId);//客服的所有授权产品线
            if (!StringUtils.isEmpty(productCode) && !grantProductList.contains(productCode)) {
                if("zh-CN".equals(language)){
                    collectors.addFailRecord("第" + line + "行", "错误数据", "不是授权产品线.");
                }else {
                    collectors.addFailRecord("第" + line + "行", "錯誤數據", "不是授權產品綫.");
                }
                productCode = null;
                map = null;
            }
        }



        putMapping(map, "handler_detail", StringUtils.isEmpty(rowData.get(12)) ? "" : rowData.get(12));
       //判断工时>0,格式是否符合规范
        if (!StringUtils.isEmpty(rowData.get(11))) {
            if("0".equals(rowData.get(11))){
                if("zh-CN".equals(language)){
                    collectors.addFailRecord("第" + line + "行", "格式规范", "工时必须大于0.");
                }else {
                    collectors.addFailRecord("第" + line + "行", "格式規範", "工時必須大於0.");
                }
                map = null;
            }else{
                if(!pattern.matcher(rowData.get(11)).matches()) {
                    if("zh-CN".equals(language)){
                        collectors.addFailRecord("第" + line + "行", "格式规范", "工时必须填写为大于0且小数位最多为两位的数字.");
                    }else {
                        collectors.addFailRecord("第" + line + "行", "錯誤數據", "工時必須填寫为大於0且小數位最多兩位的數字.");
                    }
                    map = null;
                }else{
                    if (StringUtils.isEmpty(rowData.get(12))) {
                        if("zh-CN".equals(language)){
                            collectors.addFailRecord("第" + line + "行", "错误数据", "工时填写了，处理回复不能为空.");
                        }else {
                            collectors.addFailRecord("第" + line + "行", "格式規範", "工時填寫了，處理回覆不能為空.");
                        }
                        map = null;
                    }
                }

                putMapping(map, "total_work_hours", StringUtils.isEmpty(rowData.get(11)) ? "" : rowData.get(11));
            }

        }else {
            if (!StringUtils.isEmpty(rowData.get(12))) {
                if("zh-CN".equals(language)){
                    collectors.addFailRecord("第" + line + "行", "错误数据", "处理回复填写了，工时不能为空.");
                }else {
                    collectors.addFailRecord("第" + line + "行", "格式規範", "處理回覆填寫了，工時不能為空.");
                }
                map = null;
            }
        }

        //案件来源，EASY_TALK
        putMapping(map, "comes", "EASY_TALK");

        //案件状态
        if (StringUtils.isEmpty(rowData.get(13))) {
            if("zh-CN".equals(language)){
                collectors.addFailRecord("第" + line + "行", "错误数据", "案件状态不能为空.");
            }else {
                collectors.addFailRecord("第" + line + "行", "錯誤數據", "案件狀態不能爲空.");
            }
            map = null;
        } else {
            if(rowData.get(13).split("\\.").length != 2){
                if("zh-CN".equals(language)){
                    collectors.addFailRecord("第" + line + "行", "格式规范", "案件状态格式不正确，必须：N.处理中 或 Y.已结案.");
                }else {
                    collectors.addFailRecord("第" + line + "行", "格式規範", "案件狀態格式不正確，必須：N.處理中 或 Y.已結案.");
                }
                map = null;
            }else
            {
                String initStatus = rowData.get(13).substring(0,rowData.get(13).indexOf("."));
                if (!initStatus.equals("N") && !initStatus.equals("Y")) {
                    if("zh-CN".equals(language)){
                        collectors.addFailRecord("第" + line + "行", "格式规范", "案件状态格式不正确，必须：N.处理中 或 Y.已结案.");
                    }else {
                        collectors.addFailRecord("第" + line + "行", "格式規範", "案件狀態格式不正確，必須：N.處理中 或 Y.已結案.");
                    }
                    map = null;
                } else {
                    putMapping(map, "init_status", initStatus);
                    //当初始状态为2.已结案时，问题类型，处理回复，工时不能为空
                    log.info("工时值为：" + rowData.get(11));
                    switch (initStatus) {
                        case "Y":
                            putMapping(map, "status", IssueStatus.Closed.toString());

                            if (StringUtils.isEmpty(rowData.get(8))) {

                                if("zh-CN".equals(language)){
                                    collectors.addFailRecord("第" + line + "行", "必填字段","案件状态为已结案，问题类型不能为空.");
                                }else {
                                    collectors.addFailRecord("第" + line + "行", "必填字段","案件狀態為已結案，問題類型不能爲空.");
                                }
                                map = null;
                            }
                            if (StringUtils.isEmpty(rowData.get(11))) {
                                if("zh-CN".equals(language)){
                                    collectors.addFailRecord("第" + line + "行", "必填字段","案件状态为已结案，工时不能为空.");
                                }else {
                                    collectors.addFailRecord("第" + line + "行", "必填字段","案件狀態為已結案，工時不能爲空.");
                                }
                                map = null;
                            }
                            if (StringUtils.isEmpty(rowData.get(12))) {
                                if("zh-CN".equals(language)){
                                    collectors.addFailRecord("第" + line + "行", "必填字段","案件状态为已结案，处理回复不能为空.");
                                }else {
                                    collectors.addFailRecord("第" + line + "行", "必填字段","案件狀態為已結案，處理回覆不能爲空.");
                                }
                                map = null;
                            }

                            if (StringUtils.isEmpty(rowData.get(15))) {
                                if("zh-CN".equals(language)){
                                    collectors.addFailRecord("第" + line + "行", "必填字段","案件状态为已结案，结案时间不能为空.");
                                }else {
                                    collectors.addFailRecord("第" + line + "行", "必填字段","案件狀態為已結案，結案時間不能爲空.");
                                }
                                map = null;
                            }else {
                                putMapping(map, "update_time", rowData.get(15).contains("/") ? rowData.get(15) : DateUtils.excelDateTransfer(Double.parseDouble(rowData.get(15))));
                            }

                            // 已結案，則要用服務中心、產品線去查是否有定義調整係數，有的話，調整係數欄位必填。且也要去查模組是否有輸入，且有定義level
                            String serviceRegion=rowData.get(16).replaceAll("\\s+", "");
                            if(StringUtils.isEmpty(serviceRegion)){
                                if("zh-CN".equals(language)){
                                    collectors.addFailRecord("第" + line + "行", "必填字段","案件状态为已结案，服务中心不能为空.");
                                }else {
                                    collectors.addFailRecord("第" + line + "行", "必填字段","案件狀態為已結案，服務中心不能爲空.");
                                }
                                map = null;
                            } else if (!serviceRegion.equals("CN") && !serviceRegion.equals("TW") && !serviceRegion.equals("TH") && !serviceRegion.equals("VN") && !serviceRegion.equals("MY") ){
                                if("zh-CN".equals(language)){
                                    collectors.addFailRecord("第" + line + "行", "必填字段","服务中心格式不正确，必须：CN、TW、TH、VN、MY之一");
                                }else {
                                    collectors.addFailRecord("第" + line + "行", "必填字段","服務中心格式不正確，必須：CN、TW、TH、VN、MY之一");
                                }
                                map = null;
                            }  else {
                                //依產品線、服務中心去查是否有定義調整係數
                                String product = excelImportMapper.getCoefficientSettingProduct(productCode,serviceRegion);
                                if(!StringUtils.isEmpty(product)){
                                    // 表示需要設定
                                    //檢查是否有輸入
                                    if(StringUtils.isEmpty(rowData.get(17))){
                                        if("zh-CN".equals(language)){
                                            collectors.addFailRecord("第" + line + "行", "必填字段","案件状态为已结案且有定义难易程度，故难易程度不能为空.");
                                        }else {
                                            collectors.addFailRecord("第" + line + "行", "必填字段","案件狀態為已結案且有定義難易程度，故難易程度不能爲空.");
                                        }
                                        map = null;
                                    } else {
                                        //檢查是否是有效的难易程度以及是否有level
                                        Coefficient coefficient=excelImportMapper.getCoefficientInfo(productCode,serviceRegion,rowData.get(17).replaceAll("\\s+", ""));
                                        if(coefficient == null){
                                            if("zh-CN".equals(language)){
                                                collectors.addFailRecord("第" + line + "行", "必填字段","案件状态为已结案且有定义难易程度，该难易程度数据未定义，请客服主管先至客服平台设定.");
                                            }else {
                                                collectors.addFailRecord("第" + line + "行", "必填字段","案件狀態為已結案且有定義難易程度，該難易程度資料未定義，請客服主管先至客服平台設定.");
                                            }
                                            map = null;
                                        } else {
                                            //檢查模組是否有定義level
                                            String moduleLevel=excelImportMapper.getModuleInfo(productCode,serviceRegion, rowData.get(2).replaceAll("\\s+", ""));
                                            if(StringUtils.isEmpty(moduleLevel)){
                                                if("zh-CN".equals(language)){
                                                    collectors.addFailRecord("第" + line + "行", "必填字段","案件状态为已结案且有定义难易程度，该模组数据未定义，请客服主管先至客服平台设定.");
                                                }else {
                                                    collectors.addFailRecord("第" + line + "行", "必填字段","案件狀態為已結案且有定義難易程度，該模組資料未定義，請客服主管先至客服平台設定.");
                                                }
                                                map = null;
                                            } else {
                                                putMapping(map, "service_region", serviceRegion);
                                                putMapping(map, "coefficient_id", String.valueOf(coefficient.getId()));
                                                putMapping(map, "issue_level", String.valueOf(coefficient.getLevel()+Integer.parseInt(moduleLevel)));
                                            }
                                        }
                                    }
                                }
                            }
                            break;
                        case "N":
                            putMapping(map, "status", IssueStatus.Processing.toString());
                            break;
                    }
                }
            }

        }

        //判断【是否已经现场处理】字段
        if (!StringUtils.isEmpty(rowData.get(14))) {
            if(rowData.get(14).split("\\.").length == 2){
                putMapping(map, "is_live_process", rowData.get(14).substring(0, 1).equals("1") ? "Y" : "N");//是否现场处理过,1是，2否
            }else{
                putMapping(map, "is_live_process", "N");//默认N
            }

        }

        //比较两个时间差
        boolean timeFlag = excelImportMapper.compareTime(map);
        if (timeFlag) {
            if("zh-CN".equals(language)){
                collectors.addFailRecord("第" + line + "行", "格式规范", "结案时间不可以小于反馈时间.");
            }else {
                collectors.addFailRecord("第" + line + "行", "格式規範","結案時間不可以小於反饋時間.");
            }
            map = null;
        }

        return map;
    }

    private void putMapping(Map<String, Object> map, String key, String value){
        if(map != null){
            map.put(key, value);
        }
    }

    /**
     * 创建Excel工作簿
     *
     * @return Excel工作簿
     */
    private BaseResponse createWorkbookByUrl(String fileName, MultipartFile multipartFile) throws Exception {
        log.info("excel档名 ： " + fileName);
        Workbook book;
        try {
            InputStream inputStream = multipartFile.getInputStream();
            //xls文件读取方式
            if (fileName.matches("^.*\\.xls$")) {
                book = new HSSFWorkbook(inputStream);
            } else {//xlsx文件读取方式
                book = new XSSFWorkbook(inputStream);
            }
            return BaseResponse.ok(book);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return BaseResponse.error(ResponseStatus.EXCEL_IS_NULL);
    }

    @Override
    public List<ExcelDTO> selectAllCasesDetail(String fileId) {
        return excelImportMapper.selectAllCasesDetail(fileId);
    }

    @Override
    public List<Map<String, Object>> selectCasesDetailTemp(String fileId) {
        return excelImportMapper.selectCasesDetailTemp(fileId);
    }

    @Override
    public Long insertIssue(Map<String,Object> params) {
        excelImportMapper.insertIssue(params);
        long issueId = (long)params.get("issueId");
        String crmId = CasesProcessUtils.genCrmId(issueId);
        submitMapper.saveCrmId(issueId, crmId);
        return issueId;
    }

    public Long insertIssueV3(Map<String,Object> params) {
        excelImportMapper.insertIssue(params);
        long issueId = (long)params.get("issueId");
        String crmId = IssueCodeRoleStart.ESV.toString() + String.format("%09d", issueId);;
        submitMapper.saveCrmId(issueId, crmId);
        return issueId;
    }
    @Override
    public int insertIssueCaseDetail(Map<String, Object> params) {
        return excelImportMapper.insertIssueCaseDetail(params);
    }

    @Override
    public List<Map<String, Object>> findCasesDetailTempByFileId(String fileId) {
        return excelImportMapper.findCasesDetailTempByFileId(fileId);
    }

    /**
     * 将excel中解析出并存在临时表里的数据导入到业务表
     *
     * @param userId
     * @param fileId
     */
    @Override
    public BaseResponse batchImport(String userId, String fileId) {
        List<Triple<Long, Integer, Double>> queue = new ArrayList<>();
        LinkedList<Triple<Long, Integer, Double>> workdayQueue = new LinkedList<>();
        batchImportToDB(userId, fileId, queue, workdayQueue);
        for(Triple<Long, Integer, Double> t : queue){
            //发送抛砖187的MQ
            fgMq.send(t.getLeft(), t.getMiddle());
        }

        Runnable runnable = () -> sendToWorkday(workdayQueue);
        ExecutorService executorService = Executors.newSingleThreadExecutor();
        try {
            executorService.execute(runnable);
        } catch (Exception ex) {
            log.error("sendToWorkday", ex);
        } finally {
            executorService.shutdown();
        }

        return BaseResponse.ok();
    }

    private void sendToWorkday(LinkedList<Triple<Long, Integer, Double>> workdayQueue){
        for(Triple<Long, Integer, Double> t : workdayQueue){
            try {
                Thread.sleep(2500); // 休眠2.5秒钟（单位为毫秒）
            } catch (Exception e) { //修复bug InterruptedException 调整为Exception
                e.printStackTrace();
            }
            log.info("WorkDayProducer Send MQ msg: issueId:{}, sequenceNum:{}", t.getLeft(), t.getMiddle());
            //发送抛砖workday的MQ
            amqpTemplate.convertAndSend(IssueWorkDayMQConstant.exchange, IssueWorkDayMQConstant.routingkey, new IssueKey(t.getLeft(), t.getMiddle(), t.getRight()));
//            workDayProducerMq.produceMsg(new IssueKey(t.getLeft(), t.getMiddle(), t.getRight()));
        }
    }
    
    @Transactional(rollbackFor = RuntimeException.class)
    public void batchImportToDB(String userId, String fileId, List<Triple<Long, Integer, Double>> queue, LinkedList<Triple<Long, Integer, Double>> workdayQueue){
        log.info("插入issue");
        List<Map<String, Object>> tempDataList = selectCasesDetailTemp(fileId);
        List<Cases> unclosedCases = new ArrayList<>();
        Map<String, UserDetailInfo> unclosedCasesHandlers = new HashMap<>();
        for (Map<String, Object> tempData : tempDataList) {
            String applicantId = tempData.get("applicant_id").toString();
            UserDetailInfo userDetail = userServiceFeignClient.findUserDetailById(applicantId);
            tempData.put("userId", applicantId);
            tempData.put("serviceDepartment", userDetail.getDepartmentCode());
            tempData.put("syncStatus", SyncStatus.UnSync.toString());
            tempData.put("issueType", IssueType.Issue.toString());
            tempData.put("userContactId", casesProcessUtils.getUserContactId(tempData.get("userId").toString(),
                    Optional.ofNullable(tempData.get("username")).map(Object::toString).orElse(null),
                    Optional.ofNullable(tempData.get("email")).map(Object::toString).orElse(null),
                    Optional.ofNullable(tempData.get("phone")).map(Object::toString).orElse(null)));
            Long issueId = insertIssue(tempData);

            tempData.put("turn_to_t100", "N");
            Object total_work_hours = tempData.get("total_work_hours");
            String totalWorkHours = "";
            if (total_work_hours != null) {
                totalWorkHours = String.valueOf(total_work_hours);
            }
            if (StringUtils.isEmpty(totalWorkHours)) {
                tempData.put("total_work_hours", 0.0);
            }
            log.info("插入issue_casedetail表");
            tempData.put("syncStatus", SyncStatus.EditUnSync.toString());
            int count = insertIssueCaseDetail(tempData);
            //插入历史记录(issue_progress表)
            if (count > 0) {
                String initStatus = tempData.get("status").toString();
                Cases cases = issueProcessService.getLatestCase(userId, issueId);
                if (!initStatus.equals(CaseStatus.CLOSED.getStatus())) {//未结案
                    //反馈人处理中
                    cases.setProcessType(CaseProcessType.OPEN.getProcessType());
                    cases.setWorkHours(cases.getTotalWorkHours());
                    cases.setMaxSequenceNum(1);
                    UserDetailInfo handler = userServiceFeignClient.findUserDetailById(tempData.get("handler_id").toString());
                    String processStatus = "";
                    if (handler.getJiaofuType() == JiaoFuUserType.JIAOFUGUWEN.getValue()) {
                        processStatus = CaseStatus.JIAOFU_CONSULTANT_HANDLING.getStatus();
                    } else {
                        processStatus = CaseStatus.JIAOFU_SERVICE_HANDLING.getStatus();
                    }
                    cases.setCurrentStatus(processStatus);
                    cases.setServiceId(handler.getUserId());
                    cases.setServiceName(handler.getName());
                    cases.setServiceEmail(handler.getEmail());
                    cases.setServicePhone(handler.getPhone());
                    cases.setServiceDepartment(handler.getDepartmentCode());
                    //存表前先判断是否需要同步到CRM，以设置同步标志位
//                    boolean syncToCrm = issueService.checkIssueIsSyncCrm(cases.getServiceRegion(), cases.getProductCode(),cases.getCurrentStatus());
//                    cases.setSyncToCrm(syncToCrm);
                    issueSubmitService.saveIssueProgress(cases);
                    unclosedCases.add(cases);
                    unclosedCasesHandlers.put(handler.getUserId(), handler);
                    //抛转187
                    queue.add(new ImmutableTriple<>(cases.getIssueId(), cases.getMaxSequenceNum(), cases.getWorkHours()));
                    //抛转workDay
                    workdayQueue.add(new ImmutableTriple<>(cases.getIssueId(), cases.getMaxSequenceNum(), cases.getWorkHours()));
//                    workDayProducerMq.produceMsg(new IssueKey(cases.getIssueId(), cases.getMaxSequenceNum(),cases.getWorkHours()));
                } else {//已结案
                    UserDetailInfo userDetailInfo = userServiceFeignClient.findUserDetailById(tempData.get("handler_id").toString());
                    String processStatus;
                    if (userDetailInfo.getJiaofuType() == JiaoFuUserType.JIAOFUGUWEN.getValue()) {
                        processStatus = CaseStatus.JIAOFU_CONSULTANT_HANDLING.getStatus();
                    } else {
                        processStatus = CaseStatus.JIAOFU_SERVICE_HANDLING.getStatus();
                    }
                    //1.开立--》交付处理中
                    cases.setProcessType(CaseProcessType.OPEN.getProcessType());
                    cases.setCurrentStatus(processStatus);
                    //开立工时为0
                    cases.setWorkHours(0.0);
                    cases.setMaxSequenceNum(1);
                    //存表前先判断是否需要同步到CRM，以设置同步标志位
                    //boolean syncToCrm = issueService.checkIssueIsSyncCrm(cases.getServiceRegion(), cases.getProductCode(),CaseStatus.CLOSED.getStatus());
                    //cases.setSyncToCrm(syncToCrm);
                    issueSubmitService.saveIssueProgress(cases);
                    Cases oldCase = new Cases();
                    BeanUtils.copyProperties(cases, oldCase);
                    //抛转workDay
                    workdayQueue.add(new ImmutableTriple<>(cases.getIssueId(), cases.getMaxSequenceNum(), cases.getWorkHours()));
//                    workDayProducerMq.produceMsg(new IssueKey(cases.getIssueId(), cases.getMaxSequenceNum(),cases.getWorkHours()));
                    //2.交付结案
                    //问题提报人
                    UserDetailInfo applicantInfo = userServiceFeignClient.findUserDetailById(tempData.get("applicant_id").toString());
                    cases.setProcessType(CaseProcessType.JIAOFU_CLOSED.getProcessType());
                    cases.setCurrentStatus(CaseStatus.CLIENT_VERIFICATION.getStatus());
                    //交付处理工时
                    oldCase.setWorkHours(cases.getTotalWorkHours());
                    //设置处理意见
                    oldCase.setHandlerDetail(tempData.get("handler_detail").toString());
                    cases.setUpdateTime(tempData.get("update_time").toString());
                    if (applicantInfo != null) {
                        cases.setServiceId(applicantInfo.getUserId());
                        cases.setServiceDepartment(applicantInfo.getDepartmentCode());
                    }
                    //问题处理人
                    UserDetailInfo processor = userServiceFeignClient.findUserDetailById(tempData.get("handler_id").toString());
                    issueProcessService.updateIssueProgress(cases, oldCase, processor);
                    //处理完后当前sequenceNum+1
                    cases.setMaxSequenceNum(2);
                    //抛转187
                    queue.add(new ImmutableTriple<>(cases.getIssueId(), cases.getMaxSequenceNum(), oldCase.getWorkHours()));
                    //抛转workDay
                    workdayQueue.add(new ImmutableTriple<>(cases.getIssueId(), cases.getMaxSequenceNum(), cases.getWorkHours()));
//                    workDayProducerMq.produceMsg(new IssueKey(cases.getIssueId(), cases.getMaxSequenceNum(),cases.getWorkHours()));
                    //3.反馈人结案
                    cases.setProcessType(CaseProcessType.APPLICANT_END.getProcessType());
                    cases.setCurrentStatus(CaseStatus.CLOSED.getStatus());
                    //结案操作工时置0
                    cases.setWorkHours(0.0);
                    if (applicantInfo != null) {
                        cases.setUserId(applicantInfo.getUserId());
                    }
                    cases.setServiceId(null);//结案时下一步处理人为空
                    cases.setServiceDepartment(null);
                    issueProcessService.updateIssueProgress(cases, cases, applicantInfo);

                    //记录服务结案时间
                    casesProcessUtils.saveIssueSummaryV3( userId, issueId, IssueProcessType.Close.toString(), tempData.get("update_time").toString());
                    //记录同意结案时间
                    casesProcessUtils.saveIssueSummaryV3(userId, issueId, IssueProcessType.AgreeClose.toString(), tempData.get("update_time").toString());

                    //处理完后当前sequenceNum+1
                    cases.setMaxSequenceNum(3);
                    //抛转187
                    queue.add(new ImmutableTriple<>(cases.getIssueId(), cases.getMaxSequenceNum(), cases.getWorkHours()));
                    //抛转workDay
                    workdayQueue.add(new ImmutableTriple<>(cases.getIssueId(), cases.getMaxSequenceNum(), cases.getWorkHours()));
//                    workDayProducerMq.produceMsg(new IssueKey(cases.getIssueId(), cases.getMaxSequenceNum(),cases.getWorkHours()));
                   // 已结案时，清空主表部分字段
                    Cases clear = new Cases();
                    clear.setIssueId(issueId);
                }
            }
        }
        sendUnclosedEmailTOServiceStaff(userId, unclosedCases, unclosedCasesHandlers);
    }

    private void sendUnclosedEmailTOServiceStaff(String userId, List<Cases> caseList, Map<String, UserDetailInfo> handlers) {
        Map<String, List<Cases>> cases = caseList.stream().collect(Collectors.groupingBy(Cases::getServiceId));
        UserDetailInfo loginUser = userServiceFeignClient.findUserDetailById(userId);
        for(Map.Entry<String, List<Cases>> entry : cases.entrySet()){
            CasesEmail casesEmail = new CasesEmail();
            casesEmail.setLastHandlerName(loginUser.getName());
            casesEmail.setHtmlCases(constructCaseListHtml(entry.getValue()));
            UserDetailInfo handler = handlers.get(entry.getKey());
            if (handler == null){
                log.error("excel导入未完成案件发送邮件失败:没有收件人信息:userid:{}", entry.getKey());
                continue;
            }
            if(StringUtils.isEmpty(handler.getEmail())){
                log.error("excel导入未完成案件发送邮件失败:收件人没有邮箱:userid:{}", handler.getUserId());
                continue;
            }
            casesEmail.setServiceName(handler.getName());
            HashSet<String> ccMailSet = new HashSet<>();
            //是否抄送主管
            ccMailSet.addAll(casesProcessUtils.getCcManagerEmailList(Collections.singletonList(handler)));
            //抄送小组长
            ccMailSet.addAll(casesProcessUtils.getVirtualTeamLeadersEmail(handler.getUserId()));
            ccMailSet.remove(handler.getEmail());
            mailService.SendCasesMail(casesEmail, Collections.singletonList(handler.getEmail()),
                    new ArrayList<>(ccMailSet), MailSendSituation.EXCEL_IMPORT_UNCLOSE, handler.getLanguage());
        }
    }
    @Override
    public BaseResponse batchImportV3(String userId, String fileId) {
        batchImportToDBV3(userId, fileId);
        return BaseResponse.ok();
    }
    private String applicantKey(Map<String,Object> map){
        return map.get("applicant_id").toString();
    }
    private long getUserContactId(String applicant_id, Map<String, List<Map<String, Object>>> listMap){
        for(Map.Entry<String, List<Map<String, Object>>> entry : listMap.entrySet()){
            if(applicant_id.equals(entry.getKey())){
                List<Map<String, Object>> list = entry.getValue();
                return Long.valueOf(list.get(0).get("userContactId").toString());
            }
        }
        return 0;
    }
    @Transactional(rollbackFor = RuntimeException.class)
    public void batchImportToDBV3(String userId, String fileId){
        log.info("插入issue");
        List<Map<String, Object>> tempDataList = selectCasesDetailTemp(fileId);
        Map<String, List<Map<String, Object>>> listMap= tempDataList.stream().collect(Collectors.groupingBy(this::applicantKey));
        //新建联系方式
        for(Map.Entry<String, List<Map<String, Object>>> entry : listMap.entrySet()){
            long userContactId = casesProcessUtils.getUserContactId(userId,entry.getKey(),"","");
            entry.getValue().forEach(o->{
                o.put("userContactId",userContactId);
            });
        }
        for (Map<String, Object> tempData : tempDataList) {
            String applicant_id = tempData.get("applicant_id").toString();
            long userContactId = getUserContactId(applicant_id, listMap);

            String handler_id = tempData.get("handler_id").toString();
            UserDetailInfo userDetail = userServiceFeignClient.findUserDetailById(handler_id);
            tempData.put("userId", userId);
            tempData.put("serviceDepartment", userDetail.getDepartmentCode());
            tempData.put("syncStatus", SyncStatus.DontNeedSync.toString());
            tempData.put("issueType", IssueType.Issue.toString());
            tempData.put("userContactId", userContactId);

            Long issueId = insertIssueV3(tempData);

            //标签
            String tag = tempData.get("tag") != null?tempData.get("tag").toString(): "";
            if(!StringUtils.isEmpty(tag)){
                tagService.addTagMappingByName(String.valueOf(issueId), "issue", Arrays.asList(tag.split(";")));
            }

            tempData.put("turn_to_t100", "N");
            Object total_work_hours = tempData.get("total_work_hours");
            String totalWorkHours = "";
            if (total_work_hours != null) {
                totalWorkHours = String.valueOf(total_work_hours);
            }
            if (StringUtils.isEmpty(totalWorkHours)) {
                tempData.put("total_work_hours", 0.0);
            }
            log.info("插入issue_casedetail表");
            int count = insertIssueCaseDetail(tempData);
            //插入历史记录(issue_progress表)
            if (count > 0) {
                String initStatus = tempData.get("status").toString();
                Cases cases = issueProcessService.getLatestCase(userId, issueId);
                if (!initStatus.equals(IssueStatus.Closed.toString())) {//未结案
                    //提交
                    saveSubmitProgress(cases, userId);
                    //有回复内容 就写回复的单身  没有回复内容，不用写
                    DateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                    String processTime = sdf.format(new Date());
                    saveProcessProgress(cases,processTime, tempData.get("handler_detail").toString(), handler_id);
                    // 更新单头客服最新回复
                    issueDaoV3.updateIssueNewReply(String.valueOf(issueId), NewReplyEnum.ServiceNewReply.toString(), 1);
                    //记录响应时间
                    casesProcessUtils.saveIssueSummaryV3( handler_id, issueId, IssueProcessType.Accept.toString(), processTime);
                } else {//已结案
                    saveSubmitProgress(cases, userId);
                    saveProcessProgress(cases, tempData.get("update_time").toString(),tempData.get("handler_detail").toString(), handler_id);
                    saveCloseProgress(cases, handler_id, tempData.get("update_time").toString());
                    //记录服务结案时间
                    casesProcessUtils.saveIssueSummaryV3( handler_id, issueId, IssueProcessType.Close.toString(), tempData.get("update_time").toString());
                    //记录同意结案时间
                    casesProcessUtils.saveIssueSummaryV3( handler_id, issueId, IssueProcessType.AgreeClose.toString(), tempData.get("update_time").toString());
                    //更新reply_min
                    issueProcessService.updateIssueReplyMin(issueId, tempData.get("create_time").toString(), tempData.get("update_time").toString());
                    //更新end_min
                    issueProcessService.updateIssueEndMin(issueId, tempData.get("create_time").toString(), tempData.get("update_time").toString());
                }
            }
        }
    }

    /**
     * 立案单身
     * @param cases
     * @param userId
     */
    private void saveSubmitProgress(Cases cases, String userId){
        IssueProgress issueProgress = new IssueProgress();
        issueProgress.setProcessTime(cases.getSubmitTime());
        issueProgress.setReplyType(ReplyType.Q.toString());
        issueProgress.setProcessor(userId);
        UserDetailInfo processor = userServiceFeignClient.findUserDetailById(userId);

        if(processor != null){
            issueProgress.setWorkno(processor.getWorkno());
        }
        issueProgress.setSequeceNum(0);
        issueProgress.setProcessType(IssueProcessType.Submit.toString());
        issueProgress.setProcessHours(0.0);
        issueProgress.setOpenToSubmit(true);
        issueProgress.setDescription("Submit Issue");
        if("CN".equals(cases.getServiceRegion())){
            issueProgress.setDescription("提交问题");
        }
//        issueProgress.setSyncStatus(issueDetailServiceV3.getIssueProcessSyncStatusV3(cases.getProductCode(),cases.getServiceCode(), cases.getCurrentStatus()));
        issueDaoV3.InsertIssueProgress(cases.getIssueId(),cases.getCrmId(),issueProgress);
    }

    /**
     * 回复单身
     * @param cases
     * @param handler_detail
     * @param handler_id
     */
    private void saveProcessProgress(Cases cases, String progressTime, String handler_detail, String handler_id){
        if(!StringUtils.isEmpty(handler_detail)){
            //回复
            IssueProgress issueProgress = new IssueProgress();
            issueProgress.setProcessTime(progressTime);
            issueProgress.setReplyType(ReplyType.A.toString());
            issueProgress.setProcessor(handler_id);
            UserDetailInfo processor = userServiceFeignClient.findUserDetailById(handler_id);

            if(processor != null){
                issueProgress.setWorkno(processor.getWorkno());
            }
            issueProgress.setSequeceNum(1);
            issueProgress.setProcessType(IssueProcessType.Process.toString());
            issueProgress.setProcessHours(cases.getTotalWorkHours());
            issueProgress.setOpenToSubmit(true);
            issueProgress.setDescription(handler_detail);
            if(StringUtils.isNotBlank(handler_detail)){
                issueProgress.setSyncStatus(SyncStatus.EditUnSync.toString());
            }else{
                issueProgress.setSyncStatus(SyncStatus.DontNeedSync.toString());
            }
            issueDaoV3.InsertIssueProgress(cases.getIssueId(),cases.getCrmId(),issueProgress);
        }
    }

    /**
     * 结案单身
     * @param cases
     * @param handler_id
     * @param closeTime
     */
    private void saveCloseProgress(Cases cases, String handler_id, String closeTime){
        //结案
        IssueProgress issueProgress = new IssueProgress();
        issueProgress.setProcessTime(closeTime);
        issueProgress.setReplyType(ReplyType.A.toString());
        issueProgress.setProcessor(handler_id);
        UserDetailInfo processor = userServiceFeignClient.findUserDetailById(handler_id);

        if(processor != null){
            issueProgress.setWorkno(processor.getWorkno());
        }
        issueProgress.setSequeceNum(2);
        issueProgress.setProcessType(IssueProcessType.Close.toString());
        issueProgress.setProcessHours(0.0);
        issueProgress.setOpenToSubmit(true);
        issueProgress.setDescription("Close Issue");
        if("CN".equals(cases.getServiceRegion())){
            issueProgress.setDescription("结案");
        }
        issueProgress.setSyncStatus(SyncStatus.EditUnSync.toString());
        issueDaoV3.InsertIssueProgress(cases.getIssueId(),cases.getCrmId(),issueProgress);
    }


    /**
     * 组合成案件清单的html字符串
     */
    private String constructCaseListHtml(List<Cases> cases){
        StringBuffer sbf = new StringBuffer();
        for(Cases c: cases) {
            String handlerDetail = HTML_TR_OPEN
                    + HTML_TD_OPEN +c.getCrmId() +HTML_TD_CLOSE
                    + HTML_TD_OPEN +c.getServiceCode() +HTML_TD_CLOSE
                    + HTML_TD_OPEN + c.getCustName() + HTML_TD_CLOSE
                    + HTML_TD_OPEN + c.getProductCategory() + HTML_TD_CLOSE
                    + HTML_TD_OPEN + c.getProgramCode() + HTML_TD_CLOSE
                    + HTML_TD_OPEN + c.getQuestionTitle() + HTML_TD_CLOSE
                    + HTML_TD_OPEN + c.getUsername() + HTML_TD_CLOSE
                    + HTML_TR_CLOSE;
            sbf.append(handlerDetail);
        }
        return sbf.toString();
    }
}
