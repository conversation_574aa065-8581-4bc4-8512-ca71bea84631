<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.digiwin.escloud.aiocmdb.asset.dao.CmdbModelShowFieldUserMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.digiwin.escloud.aiocmdb.asset.model.CmdbModelShowFieldUser">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="sid" property="sid" jdbcType="BIGINT"/>
        <result column="modelCode" property="modelCode" jdbcType="VARCHAR"/>
        <result column="userId" property="userId" jdbcType="VARCHAR"/>
        <result column="fieldCode" property="fieldCode" jdbcType="VARCHAR"/>
        <result column="sort" property="sort" jdbcType="INTEGER"/>
        <result column="modelFieldGroupCode" property="modelFieldGroupCode" jdbcType="VARCHAR"/>
    </resultMap>

    <!-- 批量插入用户字段配置 -->
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO cmdb_model_show_field_user 
        (id, sid, modelCode, userId, fieldCode, sort, modelFieldGroupCode,customHide)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.id}, #{item.sid}, #{item.modelCode}, #{item.userId}, 
             #{item.fieldCode}, #{item.sort}, #{item.modelFieldGroupCode},#{item.customHide})
        </foreach>
    </insert>

    <!-- 删除用户字段配置 -->
    <delete id="deleteByModelCodeAndUserId">
        DELETE FROM cmdb_model_show_field_user 
        WHERE modelCode = #{modelCode}
        AND userId = #{userId}
        AND sid = #{sid}
    </delete>

    <delete id="deleteByModelCode">
        DELETE FROM cmdb_model_show_field_user
        WHERE modelCode = #{modelCode}
          AND sid = #{sid}
    </delete>

    <!-- 查询用户字段配置 -->
    <select id="selectByModelCodeAndUserId" resultMap="BaseResultMap">
        SELECT id, sid, modelCode, userId, fieldCode, sort, modelFieldGroupCode
        FROM cmdb_model_show_field_user
        WHERE modelCode = #{modelCode}
        AND userId = #{userId}
        AND sid = #{sid}
        ORDER BY sort ASC
    </select>

    <!-- 根据用户ID删除用户字段配置 -->
    <delete id="deleteByUserId">
        DELETE FROM cmdb_model_show_field_user
        WHERE userId = #{userId} AND modelCode = #{modelCode}
    </delete>

    <!-- 批量删除用户字段配置 -->
    <delete id="batchDeleteByModelCodesAndUserId">
        DELETE FROM cmdb_model_show_field_user
        WHERE userId = #{userId}
        AND sid = #{sid}
        <if test="modelCodes != null and !modelCodes.isEmpty()">
            AND modelCode IN
            <foreach collection="modelCodes" item="modelCode" open="(" separator="," close=")">
                #{modelCode}
            </foreach>
        </if>
    </delete>

    <!-- 批量查询用户字段配置 -->
    <select id="batchSelectByModelCodesAndUserId" resultMap="BaseResultMap">
        SELECT id, sid, modelCode, userId, fieldCode, sort, modelFieldGroupCode
        FROM cmdb_model_show_field_user
        WHERE userId = #{userId}
        AND sid = #{sid}
        <if test="modelCodes != null and !modelCodes.isEmpty()">
            AND modelCode IN
            <foreach collection="modelCodes" item="modelCode" open="(" separator="," close=")">
                #{modelCode}
            </foreach>
        </if>
        ORDER BY modelCode, sort ASC
    </select>

    <!-- 根据模型编码和sid查询所有不同的用户ID -->
    <select id="selectDistinctUserIdsByModelCodeAndSid" resultType="java.lang.String">
        SELECT DISTINCT userId
        FROM cmdb_model_show_field_user
        WHERE modelCode = #{modelCode}
        AND sid = #{sid}
    </select>

</mapper>
