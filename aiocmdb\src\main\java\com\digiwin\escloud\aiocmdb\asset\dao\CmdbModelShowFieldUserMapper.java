package com.digiwin.escloud.aiocmdb.asset.dao;

import com.digiwin.escloud.aiocmdb.asset.model.CmdbModelShowFieldUser;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 用户模型显示字段Mapper接口
 */
@Mapper
public interface CmdbModelShowFieldUserMapper {

    /**
     * 批量插入用户字段配置
     * 
     * @param fieldUserList 用户字段配置列表
     * @return 插入成功的记录数
     */
    int batchInsert(@Param("list") List<CmdbModelShowFieldUser> fieldUserList);

    /**
     * 删除用户字段配置
     * 
     * @param modelCode 模型编码
     * @param userId 用户ID
     * @param sid 运维商ID
     * @return 删除的记录数
     */
    int deleteByModelCodeAndUserId(@Param("modelCode") String modelCode, 
                                   @Param("userId") String userId, 
                                   @Param("sid") long sid);

    int deleteByModelCode(@Param("modelCode") String modelCode,
                          @Param("sid") long sid);

    /**
     * 查询用户字段配置
     *
     * @param modelCode 模型编码
     * @param userId 用户ID
     * @param sid 运维商ID
     * @return 用户字段配置列表
     */
    List<CmdbModelShowFieldUser> selectByModelCodeAndUserId(@Param("modelCode") String modelCode,
                                                             @Param("userId") String userId,
                                                             @Param("sid") long sid);

    /**
     * 根据用户ID删除用户字段配置
     *
     * @param userId 用户ID
     * @return 删除的记录数
     */
    int deleteByUserId(@Param("userId") String userId,@Param("modelCode") String modelCode);

    /**
     * 批量删除用户字段配置
     *
     * @param modelCodes 模型编码列表
     * @param userId 用户ID
     * @param sid 运维商ID
     * @return 删除的记录数
     */
    int batchDeleteByModelCodesAndUserId(@Param("modelCodes") List<String> modelCodes,
                                         @Param("userId") String userId,
                                         @Param("sid") long sid);

    /**
     * 批量查询用户字段配置
     *
     * @param modelCodes 模型编码列表
     * @param userId 用户ID
     * @param sid 运维商ID
     * @return 用户字段配置列表
     */
    List<CmdbModelShowFieldUser> batchSelectByModelCodesAndUserId(@Param("modelCodes") List<String> modelCodes,
                                                                   @Param("userId") String userId,
                                                                   @Param("sid") long sid);

    /**
     * 根据模型编码和sid查询所有不同的用户ID
     *
     * @param modelCode 模型编码
     * @param sid 运维商ID
     * @return 用户ID列表
     */
    List<String> selectDistinctUserIdsByModelCodeAndSid(@Param("modelCode") String modelCode,
                                                         @Param("sid") long sid);
}
