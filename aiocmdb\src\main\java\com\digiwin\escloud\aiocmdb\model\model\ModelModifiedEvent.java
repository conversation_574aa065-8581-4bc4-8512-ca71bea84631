package com.digiwin.escloud.aiocmdb.model.model;

import org.springframework.context.ApplicationEvent;

public class ModelModifiedEvent extends ApplicationEvent {
    private final String modelCode;
    private final long sid;

    /**
     * @param source    事件发布者，通常是 'this'
     * @param modelCode 被修改模型的唯一编码
     */
    public ModelModifiedEvent(Object source, String modelCode, long sid) {
        super(source);
        this.modelCode = modelCode;
        this.sid = sid;
    }

    public String getModelCode() {
        return modelCode;
    }

    public long getSid() {
        return sid;
    }
}
