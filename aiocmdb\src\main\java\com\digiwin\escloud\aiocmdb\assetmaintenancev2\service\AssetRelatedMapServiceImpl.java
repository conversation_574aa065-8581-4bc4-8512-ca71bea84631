package com.digiwin.escloud.aiocmdb.assetmaintenancev2.service;

import cn.hutool.core.lang.Pair;
import com.digiwin.escloud.aiocmdb.asset.dao.AssetMaintenanceV2Mapper;
import com.digiwin.escloud.aiocmdb.asset.model.AssetCategoryCodingRuleSimple;
import com.digiwin.escloud.aiocmdb.asset.model.AssetRelatedCategoryClassification;
import com.digiwin.escloud.aiocmdb.asset.utils.AssetNoUtil;
import com.digiwin.escloud.aiocmdb.assetmaintenance.bigdata.BigDataUtil;
import com.digiwin.escloud.aiocmdb.assetmaintenancev2.model.AssetAttribute;
import com.digiwin.escloud.aiocmdb.assetmaintenancev2.model.AssetRelatedMap;
import com.digiwin.escloud.aiocmdb.assetmaintenancev2.model.AssociatedWithAssets;
import com.digiwin.escloud.aiocmdb.etl.service.IEtlService;
import com.digiwin.escloud.aiocmdb.maintenancerecord.service.IMrService;
import com.digiwin.escloud.aioitms.model.bigdata.StarRocksEntity;
import com.digiwin.escloud.common.response.BaseResponse;
import com.digiwin.escloud.common.util.DateUtil;
import com.digiwin.escloud.common.util.MapUtil;
import com.digiwin.escloud.common.util.SnowFlake;
import com.digiwin.escloud.etl.model.EtlEngine;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
public class AssetRelatedMapServiceImpl implements AssetRelatedMapService {
    @Autowired
    private BigDataUtil bigDataUtil;

    @Autowired
    private IMrService mrService;

    @Resource
    private IEtlService etlService;

    @Autowired
    private AssetMaintenanceV2Mapper assetMaintenanceV2Mapper;

    public Integer deleteRelatedAsset(String eid, long assetRelatedId) {
        String deleteSql = String.format(
                "delete from servicecloud.AssetsRelated where eid = '%s' and id = %d", eid, assetRelatedId);
        return bigDataUtil.srSave(deleteSql);
    }

    /**
     * 使用所提供的列表將相關的資產數據添加到數據處理系統中 {@link AssetRelatedMap}.
     * 將輸入列表轉換為兼容數據結構並執行UPSERT操作.
     *
     * @param assetRelatedMaps {@link AssetRelatedMap} 對象的列表，其中包含有關相關資產的詳細信息, 例如模型代碼，水槽名稱和資產標識符
     * @return 包含數據處理操作結果的地圖，包括潛在的成功或錯誤詳細信息
     */
    public Map<String, Object> addRelatedAsset(List<AssetRelatedMap> assetRelatedMaps) {

        // 計算 ID
        assetRelatedMaps.forEach(row -> {
            if (row.getId() <= 0) {
                row.setId(SnowFlake.getInstance().newId()); //產生雪花 id
            }
        });

        // 取得 etl_endgine.sinkFieldsJson 的設定
        String schemaName = "default";
        String sinkType = "starrocks";
        String tableName = "AssetsRelated";
        List<EtlEngine> etlList = this.getEtlEngineInfo(schemaName, sinkType, tableName);
        String sinkFieldsJson = etlList.get(0).getSinkFieldsJson();

        // 處理數據
//        List<LinkedHashMap<String, Object>> rows = MapUtil.convertToListLinkedHashMap(assetRelatedMaps);
        List<LinkedHashMap<String, Object>> rows = MapUtil.convertToListLinkedHashMap(sinkFieldsJson, assetRelatedMaps);
        rows.forEach(row -> {
            Optional.ofNullable(row.get("collectedTime"))
                    .filter(collectedTime -> !collectedTime.toString().isEmpty()) // 不為 null 才會執行
                    .orElseGet(
                            () -> row.put("collectedTime",
                                    DateUtil.getNowFormatString(DateUtil.DATE_T_TIME_MIL_FORMATTER))
                    );
            // 只要有異動, 就要更新時間
            row.put("flumeTimestamp", DateUtil.getNowFormatString(DateUtil.DATE_T_TIME_MIL_FORMATTER));
        });

        // 生成 StarRocksEntity
        StarRocksEntity starRocksEntity = getStarRocksEntity(rows);
        starRocksEntity.setDatabase(bigDataUtil.getSrDbName());
        starRocksEntity.setTable(tableName);

        return bigDataUtil.srStreamLoadThrowsException(starRocksEntity);
    }

    /**
     * 通過執行一系列操作來保存資產屬性，包括保存到HBase，
     * 檢索和處理數據以保存在水槽存儲庫中，並與Starrocks數據庫集成。
     *
     * @param assetAttribute 資產屬性對象，其中包含用於HBase，接收器和 Starrocks 操作的必要數據。
     *                       它包括諸如接收器字段的HBase模型代碼，行ID，目標值和JSON數據之類的屬性。
     * @return 來自 Starrocks 數據流操作的結果圖。該地圖通常包含有關操作狀態以及潛在的其他相關信息。
     * @throws RuntimeException 如果任何操作失敗了，例如無法檢索 sinkfieldsjson 數據或錯誤期間的錯誤
     *                          數據處理或保存。
     */
    public Pair<Map<String, Object>, List<LinkedHashMap<String, Object>>> saveAssetAttribute(String modelCode, AssetAttribute assetAttribute) {

        // save to Sr
        // 取出 sinkFieldsJson
//        String dataJson = assetAttribute.getSrJson();
//        JSONObject dataObject = JSONObject.parseObject(dataJson);
//        String tableName = dataObject.getString("tableName");
//        String schemaName = Optional.ofNullable(dataObject.getString("schemaName")).orElse("");
//        String sinkType = Optional.ofNullable(dataObject.getString("sinkType")).orElse("");
//        String sinkName = Optional.ofNullable(dataObject.getString("sinkName")).orElse("");

        String tableName = assetAttribute.getSrData().getTableName();
        String schemaName = assetAttribute.getSrData().getSchemaName();
        String sinkType = assetAttribute.getSrData().getSinkType();
        String dbName = assetAttribute.getSrData().getDbName();
        if (dbName == null || dbName.isEmpty()) {
            throw new RuntimeException("dbName is null or empty");
        }

        // 取得 etl_endgine.sinkFieldsJson 的設定
        List<EtlEngine> etlList = this.getEtlEngineInfo(schemaName, sinkType, tableName);
        String sinkFieldsJson = etlList.get(0).getSinkFieldsJson();

        // 生成 StarRocksEntity
        // 轉換 json 資料
//        List<LinkedHashMap<String, Object>> rows = bigDataUtil.extractMultipleDataFromJson(sinkFieldsJson, dataJson);
        List<LinkedHashMap<String, Object>> rows = MapUtil.convertToListLinkedHashMap(sinkFieldsJson, assetAttribute.getSrData().getData());

        //處理主鍵及其它資料的資料
        rows.forEach(row -> {
            // 處理 assetId
            Optional.ofNullable(row.get("assetId"))
                    .filter(assetId -> !assetId.toString().isEmpty())// 不為 null 才會執行
                    .filter(assetId -> {
                        if (assetId instanceof Long) {
                            return (long)assetId > 0;
                        } else if (assetId instanceof Integer) {
                            return (int)assetId > 0;
                        }
                        return false; //如果非數字, 全都要重算
                    })
                    .orElseGet(() -> row.put("assetId", SnowFlake.getInstance().newId()));

            // 處理資產編號
            //FIXME 如果資產編號有傳入, 要不要再重新檢查資產編號, 是不是己存 ?
            String eid = row.getOrDefault("eid", "").toString();
            Optional.ofNullable(row.get("assetCode"))
                    .filter(assetCode -> !assetCode.toString().isEmpty())// 不為 null 才會執行
                    .orElseGet(
                            () -> this.getNewAssetCode(modelCode, eid, assetAttribute)
                                    .map(newAssetCode -> row.put("assetCode", newAssetCode))
                                    .orElse("")
                    );
        
            Optional.ofNullable(row.get("collectedTime"))
                    .filter(collectedTime -> !collectedTime.toString().isEmpty()) // 不為 null 才會執行
                    .orElseGet(
                            () -> row.put("collectedTime",
                                    DateUtil.getNowFormatString(DateUtil.DATE_T_TIME_MIL_FORMATTER))
                    );

            // 只要有異動, 就要更新時間
            row.put("flumeTimestamp", DateUtil.getNowFormatString(DateUtil.DATE_T_TIME_MIL_FORMATTER));
        });

        // save to SR
        StarRocksEntity starRocksEntity = getStarRocksEntity(rows);
        starRocksEntity.setDatabase(dbName);
        starRocksEntity.setTable(tableName);
        Map<String, Object> result = bigDataUtil.srStreamLoadThrowsException(starRocksEntity);

        // save to hBase
        String hBaseId;
        if (assetAttribute.getHbaseRowId() <= 0) {
            hBaseId = rows.get(0).get("assetId").toString();
        } else {
            hBaseId = assetAttribute.getHbaseRowId().toString();
        }
        String hBaseModelCode = assetAttribute.getHbaseModelCode();
        String hBaseTargetValue = assetAttribute.getHbaseTargetValue();
        mrService.saveMrDetail(hBaseModelCode, hBaseId, hBaseTargetValue);

        // save to SR
        return Pair.of(result, rows);
    }

    /**
     * 取新的資產編號
     * @param modelCode
     * @param eid
     * @param assetAttribute
     * @return
     */
    public Optional<String> getNewAssetCode(String modelCode, String eid, AssetAttribute assetAttribute) {
        String sinkName = assetAttribute.getSrData().getTableName();

        // 取出編碼規則
        List<AssetCategoryCodingRuleSimple> codingRuleSimples =
                assetMaintenanceV2Mapper.selectAssetCodingRuleSetting(modelCode, sinkName);
        if (codingRuleSimples.isEmpty()) {
//            throw new RuntimeException("Not Found Asset Category Coding Rule Setting, modelCode:" + modelCode);
            return Optional.empty(); // 有些資產是不用 assetCode, 所以如果查不到編碼規則, 就表示不需要
        }

        //取出大類別
        long classificationId = codingRuleSimples.get(0).getClassificationId();
        String mainCode = assetMaintenanceV2Mapper.selectAssetPrefixCoding(classificationId);
        if (mainCode == null || mainCode.isEmpty()) {
            throw new RuntimeException("AssetCode prefix coding is null or empty, classificationId:" + classificationId);
        }

        if (eid == null || eid.isEmpty()) {
            throw new RuntimeException("Cal AssetCode found eid is null or empty");
        }

        //取出當前資產最大的編號
        String curFlowNumber = "";
        String sql = String.format(
                "select assetCode as curFn from servicecloud.%s where eid = '%s' order by collectedTime desc limit 1",
                sinkName, eid);
        List<Map<String, Object>> maxFnList = bigDataUtil.srQuery(sql);
        if (!maxFnList.isEmpty()) {
            curFlowNumber = Optional.ofNullable(maxFnList.get(0).get("curFn")).orElse("0").toString();
        }

        //計算新的資產編號
        return Optional.of(
                this.calAssetNo(eid, sinkName, mainCode, curFlowNumber, codingRuleSimples, 0)
        );
    }

    /**
     * 根據主要資產編號和處理其分類和詳細信息，與查詢相關的資產。
     *
     * @param primaryAssetId 用於查詢相關資產的主要資產編號。
     * @return AssetRealedCategoryClassification對象的列表，包含分類和分組的詳細信息相關資產。
     */
    public List<AssetRelatedCategoryClassification> queryRelatedAssets(String eid, String primaryAssetId) {
        // step1. 從 关联资产 取出 关联资产資料
        String relatedAssetsSql = String.format(
                "select * from servicecloud.AssetsRelated where eid = '%s' and primaryAssetId = %s ", eid, primaryAssetId);
        List<Map<String, Object>> relatedAssets = bigDataUtil.srQuery(relatedAssetsSql);
        List<AssociatedWithAssets> associatedWithAssets = relatedAssets.stream()
                .map(row -> {
                    String assetRelatedId = Optional.ofNullable(row.get("id")).orElse("").toString();
                    String qEid = Optional.ofNullable(row.get("eid")).orElse("").toString();
                    String associatedAssetId = Optional.ofNullable(row.get("associatedAssetId")).orElse("").toString();
                    String associatedModelCode = Optional.ofNullable(row.get("associatedModelCode")).orElse("").toString();
                    String associatedSinkName = Optional.ofNullable(row.get("associatedSinkName")).orElse("").toString();
                    return new AssociatedWithAssets(
                            qEid, assetRelatedId, associatedAssetId, associatedModelCode, associatedSinkName);
                })
                .collect(Collectors.toList());

        // step2.1 要過濾重復 的 資產類別類no, 到 mysql 查
        List<String> modelCodes = associatedWithAssets.stream()
                .map(AssociatedWithAssets::getAssociatedModelCode)
                .distinct()
                .collect(Collectors.toList());
        List<String> sinkNames = associatedWithAssets.stream()
                .map(AssociatedWithAssets::getAssociatedSinkName)
                .distinct()
                .collect(Collectors.toList());

        Map<String, Object> params = new LinkedHashMap<>();
        params.put("modelCodes", modelCodes);
        params.put("sinkNames", sinkNames);
        List<AssetRelatedCategoryClassification> relatedClassifications =
                assetMaintenanceV2Mapper.selectAssetCategoryClassification(params);

        // step2.2 分組關聯的資產訊息
        associatedWithAssets.forEach(associatedWithAsset -> {
            relatedClassifications.forEach(relatedClassification -> {
                relatedClassification.groupRelateAssets(associatedWithAsset);
            });
        });

        // step3. 查詢相關資產的資料
        return relatedClassifications.stream()
                .peek(categoryClassification -> { // 實際操作的 logic
                    categoryClassification.getListAssociatedWithAssets().forEach(awa -> {
                        // step3.1. 從 关联资产 所設定的資產表( 用 关联资产编号 去查), 取出資料 (by 各表查詢)
                        String assetRelatedId = awa.getAssetRelatedId();
                        String qEid = awa.getEid();
                        String modelCode = awa.getAssociatedModelCode();
                        String relateTable = awa.getAssociatedSinkName();
                        String assetId = awa.getAssociatedAssetId();
                        String sql = String.format(
                                "select '%s' as assetRelatedId, main.* from servicecloud.%s main where eid = '%s' and assetId = '%s' ",
                                assetRelatedId, relateTable, qEid, assetId
                        );
                        List<Map<String, Object>> assetData = bigDataUtil.srQuery(sql);

                        // step4. 依取出的 資產類別類 分類資料
                        categoryClassification.addStoreData(modelCode, assetData);
                    });
                })
                .peek(categoryClassification -> {
                    //模擬 finally 的操作(fake)
                    categoryClassification.finalProcessCategoryClassification();
                    categoryClassification.clearUnnecessaryInfo(); // 清除不用外傳的資訊
                })
                .collect(Collectors.toList());
    }

    private List<EtlEngine> getEtlEngineInfo(String schemaName, String sinkType, String tableName)  {
        Map<String, Object> map = new LinkedHashMap<>();
        map.put("schemaName", schemaName);
        map.put("sinkType", sinkType);
        map.put("sinkName", tableName);
        BaseResponse etlInfo = etlService.getEtlListByMap(map);
        if (!etlInfo.checkIsSuccess()) {
            throw new RuntimeException(String.format("code:%s, errMsg:%s", etlInfo.getCode(), etlInfo.getErrMsg()));
        }
        if (etlInfo.getData() == null) {
            throw new RuntimeException("Not Found sinkFieldsJson data, err1");
        }
        List<EtlEngine> etlList = (List<EtlEngine>) etlInfo.getData();
        if (etlList.isEmpty()) {
            throw new RuntimeException("Not Found sinkFieldsJson data, err2");
        }
        return etlList;
    }

    private StarRocksEntity getStarRocksEntity(List<LinkedHashMap<String, Object>> rows) {
        StarRocksEntity starRocksEntity = new StarRocksEntity();

        // 設定數據
        starRocksEntity.setRows(rows);

        // 取出欄位名稱
        LinkedHashMap<String, Object> map = rows.stream().findFirst().get();
        String[] fieldArray = map.keySet().toArray(new String[0]);
        starRocksEntity.setFieldNames(fieldArray);

        // 設定其它參數
        Map<String, String> optProperties = new LinkedHashMap<>();
        optProperties.put("partial_update", "true");
        starRocksEntity.setOptProperties(optProperties);

        return starRocksEntity;
    }

    private String calAssetNo(
            String eid,
            String sinkName, String mainCode, String curFlowNum,
            List<AssetCategoryCodingRuleSimple> codingRuleSimples, int reTryCount
    ) {
        // 設定參考資料
        AssetNoUtil.getInstance().setAssetNoRuleInfo(codingRuleSimples, mainCode, curFlowNum);

        //計算新的資產編號
        String newAssetNo = AssetNoUtil.getInstance().getAssetNo(
                codingRuleSimples.toArray(new AssetCategoryCodingRuleSimple[0])
        );
        if (this.chkAssetNoIsExist(eid, sinkName, newAssetNo)) {
            if (reTryCount > 5) {
                throw new RuntimeException(
                        String.format(
                                "AssetNo is exist, eid:%s, sinkName:%s, assetMainCode:%s, curMaxAssetNo:%s, reTryCount:%s",
                                eid, sinkName, mainCode, curFlowNum, reTryCount)
                );
            }
            reTryCount++;
            return calAssetNo(eid, sinkName, mainCode, newAssetNo, codingRuleSimples, reTryCount);
        } else {
            return newAssetNo;
        }
    }

    private boolean chkAssetNoIsExist(String eid, String sinkName, String assetNo) {
        String sql = String.format("select assetCode from servicecloud.%s where assetCode = '%s' and eid = '%s' "
                ,sinkName, assetNo, eid);
        return !bigDataUtil.srQuery(sql).isEmpty();
    }
}
