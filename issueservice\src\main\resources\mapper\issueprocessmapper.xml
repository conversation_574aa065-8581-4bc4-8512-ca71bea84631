<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.digiwin.escloud.issueservice.t.issuedetail.dao.IssueProcessMapper">
    <select id="queryCaseHisotriesForTimeLine" resultType="com.digiwin.escloud.issueservice.t.model.cases.dto.CaseTimeAxisDTO">
        select p.IssueId as issueId,p.CrmId as crmId,p.SequenceNum as sequenceNum,o.operation_name as processType,
        ud.name as processorName, ud2.name as handlerName,
        s.status_name as currentStatus,p.ProcessHours as processHours,p.Description as description,p.ProcessTime as processTime,
        p.t100_case_id as t100CaseId, p.handlerId, p.Processor
        from issue_progress p
        left join issue_operation_info o
        on p.ProcessType = o.operation_id and o.language =#{language}
        left join issue_status_info s
        on p.CurrentStatus = s.status_id and s.language =#{language}
        left join mars_userpersonalinfo ud
        on p.Processor = ud.userid
        left join mars_userpersonalinfo ud2
        on p.handlerId = ud2.userid
        where p.IssueId = #{issueId} and p.SequenceNum is not null
        order by p.SequenceNum DESC, p.ProcessTime DESC
    </select>
    <select id="queryCaseRemarksForTimeLine"
            resultType="com.digiwin.escloud.issueservice.t.model.cases.dto.CaseRemarkTimeAxisDTO">
        select crd.sequence_num as sequenceNum, crd.remark_title as remarkHeading,crd.remark_detail as remarkDetail,
        crd.create_time as createTime,ud.name as nickname
        from issue_remark_detail crd
        left join mars_userpersonalinfo ud
        on ud.userid = crd.handler_id
        where IssueId = #{issueId} and crd.sequence_num is not null
        order by sequence_num DESC, create_time DESC
    </select>
    <select id="queryCaseRemarksBySequenceNum"
            resultType="com.digiwin.escloud.issueservice.t.model.cases.dto.CaseRemarkTimeAxisDTO">
        select crd.sequence_num as sequenceNum, crd.remark_title as remarkHeading,crd.remark_detail as remarkDetail,
        crd.create_time as createTime,ud.name as nickname
        from issue_remark_detail crd
        left join mars_userpersonalinfo ud
        on ud.userid = crd.handler_id
        where IssueId = #{issueId} and crd.sequence_num = #{sequenceNum}
        order by sequence_num DESC, create_time DESC
    </select>
    <select id="innerFindById" resultType="com.digiwin.escloud.issueservice.t.model.cases.Cases">
        SELECT  DISTINCT cs.IssueId issueId,cs.CrmId crmId,cs.site,cs.ServiceCode serviceCode,cs.ProductCode productCode,p.ProductCategory productCategory,
         cs.UserId userId,uc.Name username,uc.Phone01 phone,uc.Email email,cs.IssueDescription issueDescription,
         cs.emergency,case when cs.IssueStatus in ('11','22') then cs.UserId else cs.ServiceId end serviceId,cs.SubmitWay submitWay,
         cs.IssueStatus currentStatus,date_format(cs.SubmitTime, '%Y-%m-%d %T') submitTime,
         CASE WHEN ct.another_name IS NOT NULL AND ct.another_name !='' THEN ct.another_name
         WHEN ct.CustomerName is NULL THEN sr.AE002 ELSE ct.CustomerName END custName,
         cs.__version__ updateTime,
         case when cs.IssueStatus in ('11','22') then uc.Name else ud2.name end as serviceName,IFNULL(ud4.name,mu.Username) as submitedName,ud5.name as mainChargeName,cs.main_charge mainCharge,
         cs.CcUser ccUser,cs.submited_id submitedId,cs.question_title questionTitle,cs.live_process liveProcess,
         ifnull(icf.IssueCatelogCode,'') issueCatelogCode,ifnull(icf.IssueCatelogName,'') issueCatelogName, ifnull(ic.IssueClassification,'') issueClassification,ifnull(icf.IssueClassificationDesc,'') issueClassificationDesc,
         ifnull(ic.issueNotDealCode,'') issueNotDealCode,ifnull(indc.issueNotDealDesc,'') issueNotDealDesc,ic.remark,ic.ErpSystemCode erpSystemCode,ic.ProgramCode programCode,
         ic.expected_completion_date expectedCompletionDate,ic.estimated_work_hours estimatedWorkHours,
         ic.totalWorkHours,mcs.pmWorkno pmWorkno,m.isRelated,m.SequenceNum maxSequenceNum,ud6.userName lastHandlerName,m.handlerId maxHandlerId,ic.is_personal_case isPersonalCase,
         turn_to_t100 as turnToT100,cz_revoke as czRevoke, ic.bug_owner as bugOwner, issueLevel,ic.requPlanCompletionDate, cs.serviceRegion,cs.site,cs.environment,cs.ent,cs.contractNo,cs.projectNo,cs.SyncStatus
         FROM issue cs
         LEFT JOIN user_contacts uc ON cs.UserContactId = uc.id
         LEFT JOIN (SELECT IssueId,IssueClassification,issueNotDealCode,ErpSystemCode,ProgramCode,expected_completion_date,turn_to_t100,cz_revoke, estimated_work_hours,total_work_hours totalWorkHours,is_personal_case,bug_owner,issue_level issueLevel,requPlanCompletionDate,remark from issue_casedetail WHERE IssueId=#{issueId} ) ic ON ic.IssueId= cs.IssueId
         LEFT JOIN mars_customer ct ON cs.ServiceCode = ct.CustomerServiceCode
         LEFT JOIN mars_customerservice mcs ON ct.CustomerServiceCode = mcs.CustomerServiceCode and mcs.ProductCode = cs.ProductCode
         LEFT JOIN mars_userpersonalinfo ud2 ON cs.ServiceId=ud2.userid
         left join mars_userpersonalinfo ud4 on cs.submited_id = ud4.userid
         left join mars_user mu  on cs.submited_id = mu.ID
         left join mars_userpersonalinfo ud5 on cs.main_charge = ud5.userid
         LEFT JOIN (SELECT IssueId,SequenceNum,handlerId,Processor,case when handlerId =#{userId} then 'Y' ELSE 'N' END isRelated from issue_progress WHERE IssueId=#{issueId} AND  SequenceNum = ( SELECT MAX(SequenceNum) FROM issue_progress WHERE IssueId=#{issueId} ) limit 1)m ON m.IssueId = cs.IssueId
         LEFT JOIN mars_product p ON p.ProductCode = cs.ProductCode
         LEFT JOIN issueclassification icf ON icf.ProductCode = cs.ProductCode AND icf.IssueClassification = ic.IssueClassification
         LEFT JOIN issuenotdealclassification indc ON indc.productCode = cs.productCode and indc.issueNotDealCode = ic.issueNotDealCode
         LEFT JOIN mars_user ud6 ON m.handlerId = ud6.Id
         LEFT JOIN serae sr ON  cs.ServiceCode = sr.AE001

         WHERE cs.IssueId=#{issueId}
         limit 1
    </select>

    <select id="findCzCaseIdByIssueId" resultType="java.lang.String">
        select t100_case_id from issue_progress where IssueId = #{0}
        and SequenceNum = (
            select a.SequenceNum from (
                select max(cast(SequenceNum as decimal(4,0))) as SequenceNum from issue_progress
                where IssueId = #{0} and t100_case_id != '' and t100_case_id is not null
            ) a
        ) limit 1
    </select>
    <select id="getCurrentCZCaseIdByIssueId" resultType="string">
        SELECT t100_case_id FROM issue_progress
        WHERE issueId = #{issueId}
        AND t100_case_id IS NOT NULL
        AND TRIM(t100_case_id) != ''
        ORDER BY SequenceNum DESC
        LIMIT 1;
    </select>
    <select id="getLastHandlerByStatus" resultType="string">
        SELECT handlerId FROM issue_progress WHERE CurrentStatus=#{status} AND IssueId=#{issueId}
        ORDER BY SequenceNum DESC limit 1
    </select>

    <insert id="saveCzSurvey">
        insert into cz_satisfaction_survey(case_id,cz_case_id,user_id,create_time,score,comment,chosen)
        values(#{caseId},#{czCaseId},#{userId},#{createTime},#{score},#{comment},#{chosen})
    </insert>

    <select id="findByIssueId" resultType="com.digiwin.escloud.issueservice.t.model.cases.CaseHistory">
        SELECT * FROM issue_progress WHERE IssueId = #{issueId}
    </select>

    <select id="getGroupNumber" resultType="java.lang.String">
        select SequenceNum
        from issue_progress
        where IssueId = #{issueId}
        group by SequenceNum
		order by SequenceNum desc
    </select>

    <select id="findHistoryByOrderAndIdGroupByHandler"
            resultType="com.digiwin.escloud.issueservice.t.model.cases.CaseHistory">
        select p.IssueId as issueId,p.CrmId as crmId,p.SequenceNum as sequenceNum,o.operation_name as processType,p.t100_case_id as t100CaseId,
        s.status_name as currentStatus,p.ProcessHours as processHours,p.Description as description,p.ProcessTime as processTime,
        case  when p.ProcessType in(1,9,23,25) then iss.username else i.name end processorName,
        case  when p.CurrentStatus in(11,22) then iss.username else i2.name end handlerName,
		p.t100_case_id as t100CaseId,GROUP_CONCAT(p.Processor) as handler_id
        from issue_progress p
        left join mars_user u
		on p.Processor = u.ID
        left join issue iss
		on p.IssueId = iss.IssueId
        left join mars_userpersonalinfo  i
        on p.Processor = i.userid
        left join mars_userpersonalinfo  i2
        on p.handlerId = i2.userid
        left join issue_operation_info o
        on p.ProcessType = o.operation_id and o.language =#{language}
        left join issue_status_info s
        on p.CurrentStatus = s.status_id and s.language =#{language}
        where p.SequenceNum = #{order}
        and p.IssueId = #{issueId}
        order by p.ProcessTime DESC
    </select>
    <select id="findRemarkByCaseIdAndOrderAndStatus"
            resultType="com.digiwin.escloud.issueservice.t.model.cases.dto.CaseRemarkTimeAxisDTO">
        select crd.remark_title as remarkHeading,crd.remark_detail as remarkDetail,crd.create_time as createTime,ud.name as nickname
        from issue_remark_detail crd
        left join mars_userpersonalinfo ud
        on ud.userid = crd.handler_id
        where IssueId = #{issueId}
        and sequence_num = #{order} order by create_time desc
    </select>
    <select id="findHistoryByOrder" resultType="com.digiwin.escloud.issueservice.t.model.cases.CaseHistory">
        select ch.*,ud.name
        from issue_progress ch
        LEFT JOIN mars_userpersonalinfo ud
        ON ch.Processor = ud.userid
        where SequenceNum = #{maxSequenceNum}
        and ch.IssueId = #{issueId}
        order by ch.ProcessTime DESC
    </select>
    <select id="findHistoryExceptApplicant" resultType="java.lang.String">
        select Processor from issue_progress where IssueId = #{caseId}
        and SequenceNum != '0' and SequenceNum != '1'
        order by SequenceNum asc
    </select>
    <select id="ifHistoryExistsCustomer" parameterType="java.lang.Long" resultType="java.lang.Integer">
        select count(*)
        from issue_progress
        where IssueId =#{issueId}
        and ProcessType = '9'
    </select>

    <select id="findCustomerResponse" parameterType="java.lang.Long" resultType="com.digiwin.escloud.issueservice.t.model.cases.CaseHistory">
        select ch.*,
        case when ch.ProcessType in('1','9','23','25') then uc.Name  else ud.name end handlerName
        from issue_progress ch
        LEFT JOIN issue cs ON cs.IssueId = ch.IssueId
        LEFT JOIN user_contacts uc ON cs.UserContactId = uc.id
        left join mars_userpersonalinfo ud
        on ud.userid = ch.Processor
        LEFT JOIN mars_user mu ON mu.ID = ud.userid
        where ch.IssueId = #{issueId}
        and ch.Description != '' and ch.Description is not null
        and ch.ProcessType = '9'
        order by ch.ProcessTime desc
    </select>
    <select id="findResponseByCaseId" parameterType="java.lang.Long" resultType="com.digiwin.escloud.issueservice.t.model.cases.CaseHistory">
        select ch.*,
        case when ch.ProcessType in('1','9','23','25') then uc.Name  else ud.name end handlerName
        from issue_progress ch
        LEFT JOIN issue cs ON cs.IssueId = ch.IssueId
        LEFT JOIN user_contacts uc ON cs.UserContactId = uc.id
        left join mars_userpersonalinfo ud
        on ud.userid = ch.Processor
        LEFT JOIN mars_user mu ON mu.ID = ud.userid
        where ch.IssueId = #{issueId}
        and ch.Description != '' and ch.Description is not null
        and ch.ProcessType != '9'
        order by ch.ProcessTime desc
    </select>
    <select id="ifExistsHandler" parameterType="java.util.Map" resultType="java.lang.Integer">
        select COUNT(*)
        from issue_progress
        where IssueId = #{issueId}
        and SequenceNum = (SELECT MAX(SequenceNum) FROM  issue_progress WHERE  IssueId = #{issueId})
        AND handlerId = #{handlerId}
    </select>
    <select id="checkIfFillCzSurvey" parameterType="java.lang.String" resultType="java.lang.Integer">
        select count(*)
        from cz_satisfaction_survey
        where cz_case_id = #{0}
    </select>
    <select id="findToCzProcessor" parameterType="java.util.Map" resultType="string">
        select Processor from issue_progress
        where IssueId = #{issueId}
        and CurrentStatus=#{status}
        and ProcessType in (#{openToCz}, #{turnToCz})
        order by ProcessTime desc
        limit 1
    </select>
    <select id="findEndCaseHandlerIdByCaseId" resultType="java.lang.String">
        select Processor from issue_progress
        where IssueId = #{0}
        ORDER BY ProcessTime DESC
        limit 1
    </select>

    <select id="searchTurnToCzPerson" parameterType="java.util.Map" resultType="java.lang.String">
        select Processor
        from issue_progress
        where IssueId = #{issueId}
        and ProcessType = #{processType}
        and SequenceNum <![CDATA[ <= ]]> cast(#{sequenceNum}  as int)
        order by ProcessTime desc
        limit 1
    </select>
    <select id="findMaxOrderHandlerByCaseId" parameterType="java.lang.String" resultType="java.lang.String">
        select handlerId
        from issue_progress
        where IssueId = #{issueId}
        and SequenceNum =
        (SELECT MAX(SequenceNum) FROM  issue_progress WHERE  IssueId = #{issueId})
    </select>
    <!--查询客户对应的最新PM-->
    <select id="getPmByCustId" parameterType="java.util.Map" resultType="java.lang.String">
        select userid
        from mars_customerservice a
        left join mars_userpersonalinfo b on a.pmWorkno = b.workno
        where a.CustomerServiceCode = #{custId} and a.ProductCode=#{prodId}
        order by status desc
    </select>


    <select id="getTempFlag" parameterType="java.lang.String" resultType="com.digiwin.escloud.issueservice.t.model.cases.CasesAnalyse">
        SELECT  ta1.flag,ta1.flag AS 'column',
        type_cnt as 'sum',
        concat(cast(type_cnt /total_cnt *100 AS DECIMAL(18,1)),'%') AS perc,hour,
        concat(cast(hour /total_hour *100 AS DECIMAL(18,1)),'%') AS hourPerc,
        concat(ROUND(C/type_cnt*100, 1),'%') AS 'oneDayReplyRank',
        concat(ROUND(D/type_cnt*100, 1),'%') AS 'threeDayReplyRank',
        concat(ROUND(E/type_cnt*100, 1),'%') AS 'allReplyRank',
        C 'oneDayReplySum',D 'threeDayReplySum',E 'allReplySum'
        FROM (  SELECT COUNT(DISTINCT cqt.issueId) AS type_cnt,
        SUM(ProcessHours) hour,
        flag
        FROM (SELECT  cqt.issueId,CASE WHEN IssueClassificationDesc = '客制需求' AND cs.submitway = 'HD187' THEN estimated_work_hours ELSE  SUM(ProcessHours) END ProcessHours,
        CASE
        WHEN issueStatus in ('Y','P','7','8','10') and 'zh-CN' =#{language}  THEN '已结案'
        WHEN issueStatus in ('N','2','3','4','11','12','22') and 'zh-CN' =#{language}  THEN '未结案'
        WHEN issueStatus in ('Y','P','7','8','10') and 'zh-TW' =#{language}  THEN '已結案'
        WHEN issueStatus in ('N','2','3','4','11','12','22') and 'zh-TW' =#{language}  THEN '未結案'
        ELSE ''
        END flag
        FROM cases_query_tmp cqt
        LEFT JOIN issue_progress p ON cqt.issueId=p.issueId
        LEFT JOIN ( select cs.IssueId,SubmitWay,estimated_work_hours from issue cs left join issue_casedetail d on cs.issueId=d.issueId
        )cs ON cqt.issueId=cs.issueId
        where query_id=#{queryId}
        <choose>
            <when test="flag == '已结案'.toString() or flag == '已結案'.toString()">
                AND cqt.issueStatus in ('Y','P','7','8','10')
            </when>
            <when test="flag == '未结案'.toString() or flag == '未結案'.toString()">
                AND cqt.issueStatus in ('N','2','3','4','11','12','22')
            </when>
        </choose>
        <choose>
            <when test="issueClassificationDesc  == '客制需求(187)'.toString() ">
                and issueClassificationDesc ='客制需求' and (cs.submitway = 'HD187' or cs.submitway like 'EXCEL%')
            </when>
            <when test="issueClassificationDesc  == '客制需求(服务云)'.toString() ">
                and issueClassificationDesc ='客制需求' and cs.SubmitWay = 'INNER'
            </when>
            <when test="issueClassificationDesc != null and issueClassificationDesc != '' and issueClassificationDesc != 'ALL' and issueClassificationDesc != '客制需求(187)'.toString()
                        and issueClassificationDesc != '客制需求(服务云)'.toString() ">
                and issueClassificationDesc = #{issueClassificationDesc}
            </when>
            <when test="issueClassificationDesc  == null ">
                and (issueClassificationDesc is null or issueClassificationDesc = '' )
            </when>
        </choose>
        <choose>
            <when test="customerName != null and customerName != '' and customerName != 'ALL' ">
                and customerName = #{customerName}
            </when>
            <when test="customerName == null ">
                and (customerName is null or customerName = '')
            </when>
        </choose>
        <choose>
            <when test="erpSystemCode != null and erpSystemCode != '' and erpSystemCode != 'ALL' ">
                and cqt.erpSystemCode = #{erpSystemCode}
            </when>
            <when test="erpSystemCode == null  ">
                and (cqt.erpSystemCode  is null or cqt.erpSystemCode  = '')
            </when>
        </choose>
        <choose>
            <when test="productCode != null and productCode != '' and productCode != 'ALL' ">
                and productName = #{productCode}
            </when>
        </choose>
        <choose>
            <when test="emergency == '紧急'.toString() or emergency == '緊急'.toString()">
                and emergency = 1
            </when>
            <when test="emergency == '不紧急'.toString() or emergency == '不緊急'.toString()">
                and (emergency = 0 )
            </when>
        </choose>
        <choose>
            <when test="personalCase== '个案'.toString() or personalCase == '個案'.toString()">
                and isPersonalCase= 'Y'
            </when>
            <when test="personalCase== '案件'.toString() or personalCase == '案件'.toString()">
                and (isPersonalCase= 'N' or isPersonalCase is null or isPersonalCase= ' ')
            </when>
        </choose>
        <choose>
            <when test="username != null and username != '' and username != 'ALL' ">
                and username = #{username}
            </when>
            <when test="username == null ">
                and (username is null or username = '')
            </when>
        </choose>
        <choose>
            <when test="nowHandlerName != null and nowHandlerName != '' and nowHandlerName != 'ALL' ">
                and nowHandlerName= #{nowHandlerName}
            </when>
            <when test="nowHandlerName == null ">
                and (nowHandlerName is null or nowHandlerName='')
            </when>
        </choose>
        <choose>
            <when test="processDay != null and processDay != '' and processDay != 'ALL' ">
                and processDay = #{processDay}
            </when>
        </choose>
        <choose>
            <when test="mainChargeName != null and mainChargeName != '' and mainChargeName != 'ALL' ">
                and mainChargeName = #{mainChargeName}
            </when>
            <when test="mainChargeName == null ">
                and (mainChargeName is null or mainChargeName='')
            </when>
        </choose>
        GROUP BY  cqt.issueId
        )cqt
        GROUP BY flag
        ORDER BY type_cnt DESC) ta1
        LEFT JOIN (  SELECT COUNT(C) C,COUNT(D) D,COUNT(E) E,flag
        FROM (  SELECT DISTINCT cqt.issueId,
        CASE WHEN  (reply_min <![CDATA[ < ]]> 1440 AND reply_min <![CDATA[ > ]]> 0 ) OR (reply_min = 0 AND IssueStatus IN (7,8,10,11) )  THEN 'C' END C,
        CASE WHEN  (reply_min <![CDATA[ < ]]> 4320 AND reply_min <![CDATA[ > ]]> 0 ) OR (reply_min = 0 AND IssueStatus IN (7,8,10,11) ) THEN 'D' END D,
        CASE WHEN  (reply_min <![CDATA[ > ]]> 0  or  IssueStatus IN (7,8,10,11) ) THEN 'E' END E,
        CASE
        WHEN issueStatus in ('Y','P','7','8','10') and 'zh-CN' =#{language}  THEN '已结案'
        WHEN issueStatus in ('N','2','3','4','11','12','22') and 'zh-CN' =#{language}  THEN '未结案'
        WHEN issueStatus in ('Y','P','7','8','10') and 'zh-TW' =#{language}  THEN '已結案'
        WHEN issueStatus in ('N','2','3','4','11','12','22') and 'zh-TW' =#{language}  THEN '未結案'
        ELSE ''
        END flag
        FROM cases_query_tmp cqt
        LEFT JOIN issue_casedetail d ON cqt.issueId = d.issueId
        LEFT JOIN ( select cs.IssueId,SubmitWay from issue cs )cs ON cqt.issueId=cs.issueId
        where query_id=#{queryId}
        <choose>
            <when test="flag == '已结案'.toString() or flag == '已結案'.toString()">
                AND cqt.issueStatus in ('Y','P','7','8','10')
            </when>
            <when test="flag == '未结案'.toString() or flag == '未結案'.toString()">
                AND cqt.issueStatus in ('N','2','3','4','11','12','22')
            </when>
        </choose>
        <choose>
            <when test="issueClassificationDesc  == '客制需求(187)'.toString() ">
                and issueClassificationDesc ='客制需求' and (cs.submitway = 'HD187' or cs.submitway like 'EXCEL%')
            </when>
            <when test="issueClassificationDesc  == '客制需求(服务云)'.toString() ">
                and issueClassificationDesc ='客制需求' and cs.SubmitWay = 'INNER'
            </when>
            <when test="issueClassificationDesc != null and issueClassificationDesc != '' and issueClassificationDesc != 'ALL' and issueClassificationDesc != '客制需求(187)'.toString()
                        and issueClassificationDesc != '客制需求(服务云)'.toString() ">
                and issueClassificationDesc = #{issueClassificationDesc}
            </when>
            <when test="issueClassificationDesc  == null ">
                and (issueClassificationDesc is null or issueClassificationDesc = '' )
            </when>
        </choose>
        <choose>
            <when test="customerName != null and customerName != '' and customerName != 'ALL' ">
                and customerName = #{customerName}
            </when>
            <when test="customerName == null ">
                and (customerName is null or customerName = '')
            </when>
        </choose>
        <choose>
            <when test="erpSystemCode != null and erpSystemCode != '' and erpSystemCode != 'ALL' ">
                and cqt.erpSystemCode = #{erpSystemCode}
            </when>
            <when test="erpSystemCode == null  ">
                and (cqt.erpSystemCode  is null or cqt.erpSystemCode  = '')
            </when>
        </choose>
        <choose>
            <when test="productCode != null and productCode != '' and productCode != 'ALL' ">
                and productName = #{productCode}
            </when>
        </choose>
        <choose>
            <when test="emergency == '紧急'.toString() or emergency == '緊急'.toString()">
                and emergency = 1
            </when>
            <when test="emergency == '不紧急'.toString() or emergency == '不緊急'.toString()">
                and (emergency = 0 )
            </when>
        </choose>
        <choose>
            <when test="personalCase== '个案'.toString() or personalCase == '個案'.toString()">
                and isPersonalCase= 'Y'
            </when>
            <when test="personalCase== '案件'.toString() or personalCase == '案件'.toString()">
                and (isPersonalCase= 'N' or isPersonalCase is null or isPersonalCase= ' ')
            </when>
        </choose>
        <choose>
            <when test="username != null and username != '' and username != 'ALL' ">
                and username = #{username}
            </when>
            <when test="username == null ">
                and (username is null or username = '')
            </when>
        </choose>
        <choose>
            <when test="nowHandlerName != null and nowHandlerName != '' and nowHandlerName != 'ALL' ">
                and nowHandlerName= #{nowHandlerName}
            </when>
            <when test="nowHandlerName == null ">
                and (nowHandlerName is null or nowHandlerName='')
            </when>
        </choose>
        <choose>
            <when test="processDay != null and processDay != '' and processDay != 'ALL' ">
                and processDay = #{processDay}
            </when>
        </choose>
        <choose>
            <when test="mainChargeName != null and mainChargeName != '' and mainChargeName != 'ALL' ">
                and mainChargeName = #{mainChargeName}
            </when>
            <when test="mainChargeName == null ">
                and (mainChargeName is null or mainChargeName='')
            </when>
        </choose>
        GROUP BY cqt.issueId
        )tc1
        GROUP BY flag )td1 ON ta1.flag = td1.flag,
        ( SELECT COUNT(DISTINCT cqt.issueId) AS total_cnt,SUM(ProcessHours) total_hour
        from ( select cqt.issueId,CASE WHEN IssueClassificationDesc = '客制需求' AND cs.submitway = 'HD187' THEN
        estimated_work_hours ELSE SUM(ProcessHours) END ProcessHours
        FROM cases_query_tmp cqt
        LEFT JOIN issue_progress p ON cqt.issueId=p.issueId
        LEFT JOIN ( select cs.IssueId,SubmitWay,estimated_work_hours from issue cs left join issue_casedetail d on
        cs.issueId=d.issueId)cs ON
        cqt.issueId=cs.issueId
        where query_id=#{queryId}
        <choose>
            <when test="flag == '已结案'.toString() or flag == '已結案'.toString()">
                AND cqt.issueStatus in ('Y','P','7','8','10')
            </when>
            <when test="flag == '未结案'.toString() or flag == '未結案'.toString()">
                AND cqt.issueStatus in ('N','2','3','4','11','12','22')
            </when>
        </choose>
        <choose>
            <when test="issueClassificationDesc  == '客制需求(187)'.toString() ">
                and issueClassificationDesc ='客制需求' and (cs.submitway = 'HD187' or cs.submitway like 'EXCEL%')
            </when>
            <when test="issueClassificationDesc  == '客制需求(服务云)'.toString() ">
                and issueClassificationDesc ='客制需求' and cs.SubmitWay = 'INNER'
            </when>
            <when test="issueClassificationDesc != null and issueClassificationDesc != '' and issueClassificationDesc != 'ALL' and issueClassificationDesc != '客制需求(187)'.toString()
                        and issueClassificationDesc != '客制需求(服务云)'.toString()  ">
                and issueClassificationDesc = #{issueClassificationDesc}
            </when>
            <when test="issueClassificationDesc  == null ">
                and (issueClassificationDesc is null or issueClassificationDesc = '' )
            </when>
        </choose>
        <choose>
            <when test="customerName != null and customerName != '' and customerName != 'ALL' ">
                and customerName = #{customerName}
            </when>
            <when test="customerName == null ">
                and (customerName is null or customerName = '')
            </when>
        </choose>
        <choose>
            <when test="erpSystemCode != null and erpSystemCode != '' and erpSystemCode != 'ALL' ">
                and cqt.erpSystemCode = #{erpSystemCode}
            </when>
            <when test="erpSystemCode == null  ">
                and (cqt.erpSystemCode is null or cqt.erpSystemCode = '')
            </when>
        </choose>
        <choose>
            <when test="productCode != null and productCode != '' and productCode != 'ALL' ">
                and productName = #{productCode}
            </when>
        </choose>
        <choose>
            <when test="emergency == '紧急'.toString() or emergency == '緊急'.toString()">
                and emergency = 1
            </when>
            <when test="emergency == '不紧急'.toString() or emergency == '不緊急'.toString()">
                and (emergency = 0 )
            </when>
        </choose>
        <choose>
            <when test="personalCase== '个案'.toString() or personalCase == '個案'.toString()">
                and isPersonalCase= 'Y'
            </when>
            <when test="personalCase== '案件'.toString() or personalCase == '案件'.toString()">
                and (isPersonalCase= 'N' or isPersonalCase is null or isPersonalCase= ' ')
            </when>
        </choose>
        <choose>
            <when test="username != null and username != '' and username != 'ALL' ">
                and username = #{username}
            </when>
            <when test="username == null ">
                and (username is null or username = '')
            </when>
        </choose>
        <choose>
            <when test="nowHandlerName != null and nowHandlerName != '' and nowHandlerName != 'ALL' ">
                and nowHandlerName= #{nowHandlerName}
            </when>
            <when test="nowHandlerName == null ">
                and (nowHandlerName is null or nowHandlerName='')
            </when>
        </choose>
        <choose>
            <when test="processDay != null and processDay != '' and processDay != 'ALL' ">
                and processDay = #{processDay}
            </when>
        </choose>
        <choose>
            <when test="mainChargeName != null and mainChargeName != '' and mainChargeName != 'ALL' ">
                and mainChargeName = #{mainChargeName}
            </when>
            <when test="mainChargeName == null ">
                and (mainChargeName is null or mainChargeName='')
            </when>
        </choose>
        GROUP BY cqt.issueId
        )cqt
        )tb1
        ORDER BY SUM DESC
    </select>
    <select id="getCaseAnalyseList" parameterType="java.lang.String" resultType="com.digiwin.escloud.issueservice.t.model.cases.CasesAnalyse">
        SELECT
        <choose>
            <when test="flag == 'ALL'">
                ta1.flag,ta1.flag AS 'column',
            </when>
        </choose>
        <choose>
            <when test="issueClassificationDesc =='ALL' ">
                CASE WHEN ta1.issueClassificationDesc = '' THEN NULL ELSE ta1.issueClassificationDesc END issueClassificationDesc,
                CASE WHEN ta1.issueClassificationDesc = '' THEN NULL ELSE ta1.issueClassificationDesc END 'column',
            </when>
        </choose>
        <choose>
            <when test="customerName == 'ALL'">
                ta1.customerName,ta1.serviceCode,ta1.customerName AS 'column',
            </when>
        </choose>
        <choose>
            <when test="erpSystemCode == 'ALL' ">
                CASE WHEN ta1.erpSystemCode = '' THEN NULL ELSE ta1.erpSystemCode END erpSystemCode,
                CASE WHEN ta1.erpSystemCode = '' THEN NULL ELSE ta1.erpSystemCode END 'column',
            </when>
        </choose>
        <choose>
            <when test="productCode == 'ALL' ">
                ta1.productName,ta1.productName AS 'column',
            </when>
        </choose>
        <choose>
            <when test="emergency == 'ALL'">
                ta1.emerge as 'emergency',ta1.emerge as 'column',
            </when>
        </choose>
        <choose>
            <when test="personalCase== 'ALL'">
                ta1.isPersonalCase as 'isPersonalCase',ta1.isPersonalCase as 'column',
            </when>
        </choose>
        <choose>
            <when test="username == 'ALL'  ">
                CASE WHEN ta1.username = '' THEN NULL ELSE ta1.username END username,
                CASE WHEN ta1.username = '' THEN NULL ELSE ta1.username END 'column',
            </when>
        </choose>
        <choose>
            <when test="nowHandlerName == 'ALL' ">
                CASE WHEN ta1.nowHandlerName = '' THEN NULL ELSE ta1.nowHandlerName END username,
                CASE WHEN ta1.nowHandlerName = '' THEN NULL ELSE ta1.nowHandlerName END 'column',
            </when>
        </choose>
        <choose>
            <when test="processDay == 'ALL' ">
                ta1.processDay,ta1.processDay as 'column',
            </when>
        </choose>
        <choose>
            <when test="mainChargeName == 'ALL' ">
                CASE WHEN ta1.mainChargeName = '' THEN NULL ELSE ta1.mainChargeName END username,
                CASE WHEN ta1.mainChargeName = '' THEN NULL ELSE ta1.mainChargeName END 'column',
            </when>
        </choose>
        <choose>
            <when test="custLevel == 'ALL' ">
                CASE WHEN ta1.custLevel = '' THEN NULL ELSE ta1.custLevel END custLevel,
                CASE WHEN ta1.custLevel = '' THEN NULL ELSE ta1.custLevel END 'column',
            </when>
        </choose>
        <choose>
            <when test="issueLevel == 'ALL' ">
                CASE WHEN ta1.issueLevel = '' THEN NULL ELSE ta1.issueLevel END issueLevel,
                CASE WHEN ta1.issueLevel = '' THEN NULL ELSE ta1.issueLevel END 'column',
            </when>
        </choose>
        type_cnt as 'sum',custNum,
        concat(cast(type_cnt /total_cnt *100 AS DECIMAL(18,1)),'%') AS perc,hour,
        concat(cast(hour /total_hour *100 AS DECIMAL(18,1)),'%') AS hourPerc,
        concat(ROUND(C/type_cnt*100, 1),'%') AS 'oneDayReplyRank',
        concat(ROUND(D/type_cnt*100, 1),'%') AS 'threeDayReplyRank',
        concat(ROUND(E/type_cnt*100, 1),'%') AS 'allReplyRank',
        C 'oneDayReplySum',D 'threeDayReplySum',E 'allReplySum',F 'refuseNum'
        FROM (  SELECT COUNT(DISTINCT cqt.issueId) AS type_cnt,COUNT(DISTINCT cqt.serviceCode) AS custNum,
                SUM(ProcessHours) hour,

        <choose>
            <when test="flag == 'ALL'">
                flag
                FROM (SELECT  cqt.issueId,cqt.serviceCode,CASE WHEN IssueClassificationDesc = '客制需求' AND cs.submitway = 'HD187' THEN estimated_work_hours ELSE  SUM(ProcessHours) END ProcessHours,
                CASE
                WHEN cqt.issueStatus in ('Y','P','7','8','10') and 'zh-CN' =#{language}  THEN '已结案'
                WHEN cqt.issueStatus in ('C','N','2','3','4','11','12','22') and 'zh-CN' =#{language}  THEN '未结案'
                WHEN cqt.issueStatus in ('Y','P','7','8','10') and 'zh-TW' =#{language}  THEN '已結案'
                WHEN cqt.issueStatus in ('C','N','2','3','4','11','12','22') and 'zh-TW' =#{language}  THEN '未結案'
                ELSE ''
                END flag
            </when>
        </choose>
        <choose>
            <when test="issueClassificationDesc =='ALL' ">
                issueClassificationDesc
                FROM (SELECT cqt.issueId,cqt.serviceCode,
                CASE  WHEN issueclassificationDesc is NULL THEN '' WHEN IssueClassificationDesc = '客制需求' AND (cs.submitway = 'HD187' or cs.submitway like 'EXCEL%') THEN '客制需求(187)'
                WHEN IssueClassificationDesc = '客制需求' AND cs.SubmitWay = 'INNER' THEN '客制需求(服务云)'
                ELSE issueclassificationDesc END issueclassificationDesc,
                CASE WHEN IssueClassificationDesc = '客制需求' AND cs.submitway = 'HD187' THEN estimated_work_hours ELSE  SUM(ProcessHours) END ProcessHours
            </when>
        </choose>
        <choose>
            <when test="customerName == 'ALL'">
                customerName, serviceCode
                FROM (SELECT cqt.issueId,customerName, cqt.serviceCode,CASE WHEN IssueClassificationDesc = '客制需求' AND cs.submitway = 'HD187' THEN estimated_work_hours ELSE  SUM(ProcessHours) END ProcessHours
            </when>
        </choose>
        <choose>
            <when test="erpSystemCode == 'ALL' ">
                erpSystemCode
                FROM (SELECT cqt.issueId,cqt.serviceCode,CASE WHEN IssueClassificationDesc = '客制需求' AND cs.submitway = 'HD187' THEN estimated_work_hours ELSE  SUM(ProcessHours) END ProcessHours,
                CASE  WHEN cqt.erpSystemCode is NULL THEN '' ELSE cqt.erpSystemCode END erpSystemCode
            </when>
        </choose>
        <choose>
            <when test="productCode == 'ALL' ">
                productName
                FROM (SELECT cqt.issueId,cqt.serviceCode,productName,CASE WHEN IssueClassificationDesc = '客制需求' AND cs.submitway = 'HD187' THEN estimated_work_hours ELSE  SUM(ProcessHours) END ProcessHours
            </when>
        </choose>
        <choose>
            <when test="emergency == 'ALL'">
                emerge
                FROM (SELECT cqt.issueId,cqt.serviceCode,CASE WHEN IssueClassificationDesc = '客制需求' AND cs.submitway = 'HD187' THEN estimated_work_hours ELSE  SUM(ProcessHours) END ProcessHours,
                (case
                WHEN ( cqt.emergency = '1' and #{language}='zh-CN' ) THEN '紧急'
                WHEN ( cqt.emergency = '1' and #{language}='zh-TW' ) THEN '緊急'
                WHEN ( cqt.emergency = '0' and #{language}='zh-CN' ) THEN '不紧急'
                WHEN ( cqt.emergency = '0' and #{language}='zh-TW' ) THEN '不緊急'
                ELSE '不紧急'
                END
                ) emerge
            </when>
        </choose>
        <choose>
            <when test="personalCase== 'ALL'">
                isPersonalCase
                FROM (SELECT cqt.issueId,cqt.serviceCode,CASE WHEN IssueClassificationDesc = '客制需求' AND cs.submitway = 'HD187' THEN estimated_work_hours ELSE  SUM(ProcessHours) END ProcessHours,
                (case
                WHEN ( isPersonalCase = 'Y' and #{language}='zh-CN') THEN '个案'
                WHEN ( isPersonalCase = 'Y' and #{language}='zh-TW') THEN '個案'
                ELSE '案件'
                END
                ) isPersonalCase
            </when>
        </choose>
        <choose>
            <when test="username == 'ALL'  ">
                username
                FROM (SELECT cqt.issueId,cqt.serviceCode,CASE WHEN IssueClassificationDesc = '客制需求' AND cs.submitway = 'HD187' THEN estimated_work_hours ELSE  SUM(ProcessHours) END ProcessHours,
                CASE  WHEN cqt.username is NULL THEN '' ELSE cqt.username END username
            </when>
        </choose>
        <choose>
            <when test="nowHandlerName == 'ALL' ">
                nowHandlerName
                FROM (SELECT cqt.issueId,cqt.serviceCode,CASE WHEN IssueClassificationDesc = '客制需求' AND cs.submitway = 'HD187' THEN estimated_work_hours ELSE  SUM(ProcessHours) END ProcessHours,
                CASE  WHEN nowHandlerName is NULL THEN '' ELSE nowHandlerName END nowHandlerName
            </when>
        </choose>
        <choose>
            <when test="issueLevel == 'ALL' ">
                issueLevel
                FROM (SELECT cqt.issueId,cqt.serviceCode,CASE WHEN IssueClassificationDesc = '客制需求' AND cs.submitway = 'HD187' THEN estimated_work_hours ELSE  SUM(ProcessHours) END ProcessHours,
                CASE  WHEN issue_level is NULL THEN '' WHEN issue_level = '' THEN '' ELSE issue_level END issueLevel
            </when>
        </choose>
        <choose>
            <when test="processDay == 'ALL' ">
                processDay
                FROM (SELECT cqt.issueId,cqt.serviceCode,CASE WHEN IssueClassificationDesc = '客制需求' AND cs.submitway = 'HD187' THEN estimated_work_hours ELSE  SUM(ProcessHours) END ProcessHours,
                processDay
            </when>
        </choose>
        <choose>
            <when test="mainChargeName == 'ALL' ">
                mainChargeName
                FROM (SELECT cqt.issueId,cqt.serviceCode,CASE WHEN IssueClassificationDesc = '客制需求' AND cs.submitway = 'HD187' THEN estimated_work_hours ELSE  SUM(ProcessHours) END ProcessHours,
                CASE  WHEN mainChargeName is NULL THEN '' ELSE mainChargeName END mainChargeName
            </when>
        </choose>
        <choose>
            <when test="custLevel == 'ALL' ">
                custLevel
                FROM (SELECT cqt.issueId,cqt.serviceCode,CASE WHEN IssueClassificationDesc = '客制需求' AND cs.submitway = 'HD187' THEN estimated_work_hours ELSE  SUM(ProcessHours) END ProcessHours,
                CASE WHEN cust_level is NULL THEN '' ELSE cust_level END custLevel
            </when>
        </choose>
        FROM cases_query_tmp cqt
        LEFT JOIN issue_progress p ON cqt.issueId=p.issueId
        LEFT JOIN issue cs ON cqt.issueId=cs.issueId
        left join issue_casedetail d on cqt.issueId=d.issueId
        LEFT JOIN mars_customerservice mc ON cqt.serviceCode=mc.CustomerServiceCode AND cs.ProductCode=mc.ProductCode
        where query_id=#{queryId}
        <choose>
            <when test="flag == '已结案'.toString() or flag == '已結案'.toString()">
                AND cqt.issueStatus in ('Y','P','7','8','10')
            </when>
            <when test="flag == '未结案'.toString() or flag == '未結案'.toString()">
                AND cqt.issueStatus in ('C','N','2','3','4','11','12','22')
            </when>
        </choose>
        <choose>
            <when test="issueClassificationDesc  == '客制需求(187)'.toString() ">
                and issueClassificationDesc ='客制需求' and (cs.submitway = 'HD187' or cs.submitway like 'EXCEL%')
            </when>
            <when test="issueClassificationDesc  == '客制需求(服务云)'.toString() ">
                and issueClassificationDesc ='客制需求' and cs.SubmitWay = 'INNER'
            </when>
            <when test="issueClassificationDesc != null and issueClassificationDesc != '' and issueClassificationDesc != 'ALL' and issueClassificationDesc != '客制需求(187)'.toString()
                        and issueClassificationDesc != '客制需求(服务云)'.toString() ">
                and issueClassificationDesc = #{issueClassificationDesc}
            </when>
            <when test="issueClassificationDesc  == null ">
                and (issueClassificationDesc is null or issueClassificationDesc = '' )
            </when>
        </choose>
        <choose>
            <when test="customerName != null and customerName != '' and customerName != 'ALL' ">
                and customerName = #{customerName}
            </when>
            <when test="customerName == null ">
                and (customerName is null or customerName = '')
            </when>
        </choose>
        <choose>
            <when test="erpSystemCode != null and erpSystemCode != '' and erpSystemCode != 'ALL' ">
                and cqt.erpSystemCode = #{erpSystemCode}
            </when>
            <when test="erpSystemCode == null  ">
                and (cqt.erpSystemCode  is null or cqt.erpSystemCode  = '')
            </when>
        </choose>
        <choose>
            <when test="productCode != null and productCode != '' and productCode != 'ALL' ">
                and productName = #{productCode}
            </when>
        </choose>
        <choose>
            <when test="emergency == '紧急'.toString() or emergency == '緊急'.toString()">
                and cqt.emergency = 1
            </when>
            <when test="emergency == '不紧急'.toString() or emergency == '不緊急'.toString()">
                and cqt.emergency = 0
            </when>
        </choose>
        <choose>
            <when test="personalCase== '个案'.toString() or personalCase == '個案'.toString()">
                and isPersonalCase= 'Y'
            </when>
            <when test="personalCase== '案件'.toString() or personalCase == '案件'.toString()">
                and (isPersonalCase= 'N' or isPersonalCase is null or isPersonalCase= ' ')
            </when>
        </choose>
        <choose>
            <when test="username != null and username != '' and username != 'ALL' ">
                and cqt.username = #{username}
            </when>
            <when test="username == null ">
                and (cqt.username is null or cqt.username = '')
            </when>
        </choose>
        <choose>
            <when test="nowHandlerName != null and nowHandlerName != '' and nowHandlerName != 'ALL' ">
                and nowHandlerName= #{nowHandlerName}
            </when>
            <when test="nowHandlerName == null ">
                and (nowHandlerName is null or nowHandlerName='')
            </when>
        </choose>
        <choose>
            <when test="issueLevel != null and issueLevel != '' and issueLevel != 'ALL' ">
                and issue_level= #{issueLevel}
            </when>
            <when test="issueLevel == null ">
                and (issue_level is null or issue_level='')
            </when>
        </choose>
        <choose>
            <when test="processDay != null and processDay != '' and processDay != 'ALL' ">
                and processDay = #{processDay}
            </when>
        </choose>
        <choose>
            <when test="mainChargeName != null and mainChargeName != '' and mainChargeName != 'ALL' ">
                and mainChargeName = #{mainChargeName}
            </when>
            <when test="mainChargeName == null ">
                and (mainChargeName is null or mainChargeName='')
            </when>
        </choose>
        <choose>
            <when test="custLevel != null and custLevel != '' and custLevel != 'ALL' ">
                and cust_level = #{custLevel}
            </when>
            <when test="custLevel == null ">
                and (cust_level is null or cust_level='')
            </when>
        </choose>
        GROUP BY
        <choose>
            <when test="flag == 'ALL'">
                cqt.issueId
                )cqt
                GROUP BY flag
            </when>
        </choose>
        <choose>
            <when test="issueClassificationDesc =='ALL' ">
                cqt.issueId
                )cqt
                GROUP BY issueClassificationDesc
            </when>
        </choose>
        <choose>
            <when test="customerName == 'ALL'">
                cqt.issueId
                )cqt
                GROUP BY serviceCode
            </when>
        </choose>
        <choose>
            <when test="erpSystemCode == 'ALL' ">
                cqt.issueId
                )cqt
                GROUP BY erpSystemCode
            </when>
        </choose>
        <choose>
            <when test="productCode == 'ALL' ">
                cqt.issueId
                )cqt
                GROUP BY productName
            </when>
        </choose>
        <choose>
            <when test="emergency == 'ALL'">
                cqt.issueId
                )cqt
                GROUP BY emerge
            </when>
        </choose>
        <choose>
            <when test="personalCase== 'ALL'">
                cqt.issueId
                )cqt
                GROUP BY isPersonalCase
            </when>
        </choose>
        <choose>
            <when test="username == 'ALL'  ">
                cqt.issueId
                )cqt
                GROUP BY username
            </when>
        </choose>
        <choose>
            <when test="nowHandlerName == 'ALL' ">
                cqt.issueId
                )cqt
                GROUP BY nowHandlerName
            </when>
        </choose>
        <choose>
            <when test="issueLevel == 'ALL' ">
                cqt.issueId
                )cqt
                GROUP BY issueLevel
            </when>
        </choose>
        <choose>
            <when test="processDay == 'ALL' ">
                cqt.issueId
                )cqt
                GROUP BY processDay
            </when>
        </choose>
        <choose>
            <when test="mainChargeName == 'ALL' ">
                cqt.issueId
                )cqt
                GROUP BY mainChargeName
            </when>
        </choose>
        <choose>
            <when test="custLevel == 'ALL' ">
                cqt.issueId
                )cqt
                GROUP BY custLevel
            </when>
        </choose>
        ORDER BY type_cnt DESC) ta1
        LEFT JOIN (  SELECT COUNT(C) C,COUNT(D) D,COUNT(E) E,COUNT(F) F,
        <choose>
            <when test="flag == 'ALL'">
                flag
            </when>
        </choose>
        <choose>
            <when test="issueClassificationDesc =='ALL' ">
                issueClassificationDesc
            </when>
        </choose>
        <choose>
            <when test="customerName == 'ALL'">
                customerName, serviceCode
            </when>
        </choose>
        <choose>
            <when test="erpSystemCode == 'ALL' ">
                erpSystemCode
            </when>
        </choose>
        <choose>
            <when test="productCode == 'ALL' ">
                productName
            </when>
        </choose>
        <choose>
            <when test="emergency == 'ALL'">
                emerge
            </when>
        </choose>
        <choose>
            <when test="personalCase== 'ALL'">
                isPersonalCase
            </when>
        </choose>
        <choose>
            <when test="username == 'ALL'  ">
                username
            </when>
        </choose>
        <choose>
            <when test="nowHandlerName == 'ALL' ">
                nowHandlerName
            </when>
        </choose>
        <choose>
            <when test="issueLevel == 'ALL' ">
                issueLevel
            </when>
        </choose>
        <choose>
            <when test="processDay == 'ALL' ">
                processDay
            </when>
        </choose>
        <choose>
            <when test="mainChargeName == 'ALL' ">
                mainChargeName
            </when>
        </choose>
        <choose>
            <when test="custLevel == 'ALL' ">
                custLevel
            </when>
        </choose>
        FROM (  SELECT DISTINCT cqt.issueId,ta4.issueId F,
        CASE WHEN  (reply_min <![CDATA[ < ]]> 1440 AND reply_min <![CDATA[ > ]]> 0 ) OR (reply_min = 0 AND cqt.IssueStatus IN (7,8,10,11) )  THEN 'C' END C,
        CASE WHEN  (reply_min <![CDATA[ < ]]> 4320 AND reply_min <![CDATA[ > ]]> 0 ) OR (reply_min = 0 AND cqt.IssueStatus IN (7,8,10,11) ) THEN 'D' END D,
        CASE WHEN  (reply_min <![CDATA[ > ]]> 0  or  cqt.IssueStatus IN (7,8,10,11) ) THEN 'E' END E,
        <choose>
            <when test="flag == 'ALL'">
                CASE
                WHEN cqt.issueStatus in ('Y','P','7','8','10') and 'zh-CN' =#{language}  THEN '已结案'
                WHEN cqt.issueStatus in ('C','N','2','3','4','11','12','22') and 'zh-CN' =#{language}  THEN '未结案'
                WHEN cqt.issueStatus in ('Y','P','7','8','10') and 'zh-TW' =#{language}  THEN '已結案'
                WHEN cqt.issueStatus in ('C','N','2','3','4','11','12','22') and 'zh-TW' =#{language}  THEN '未結案'
                ELSE ''
                END flag
            </when>
        </choose>
        <choose>
            <when test="issueClassificationDesc =='ALL' ">
                CASE  WHEN issueclassificationDesc is NULL THEN '' WHEN IssueClassificationDesc = '客制需求' AND (cs.submitway = 'HD187' or cs.submitway like 'EXCEL%') THEN '客制需求(187)'
                WHEN IssueClassificationDesc = '客制需求' AND cs.SubmitWay = 'INNER' THEN '客制需求(服务云)'
                else issueclassificationDesc end issueclassificationDesc
            </when>
        </choose>
        <choose>
            <when test="customerName == 'ALL'">
                customerName, cqt.serviceCode
            </when>
        </choose>
        <choose>
            <when test="erpSystemCode == 'ALL' ">
                CASE  WHEN cqt.erpSystemCode is NULL THEN '' ELSE cqt.erpSystemCode END erpSystemCode
            </when>
        </choose>
        <choose>
            <when test="productCode == 'ALL' ">
                productName
            </when>
        </choose>
        <choose>
            <when test="emergency == 'ALL'">
                (case
                WHEN ( cqt.emergency = '1' and #{language}='zh-CN' ) THEN '紧急'
                WHEN ( cqt.emergency = '1' and #{language}='zh-TW' ) THEN '緊急'
                WHEN ( cqt.emergency = '0' and #{language}='zh-CN' ) THEN '不紧急'
                WHEN ( cqt.emergency = '0' and #{language}='zh-TW' ) THEN '不緊急'
                ELSE '不紧急'
                END
                ) emerge
            </when>
        </choose>
        <choose>
            <when test="personalCase== 'ALL'">
                (case
                WHEN ( isPersonalCase = 'Y' and #{language}='zh-CN') THEN '个案'
                WHEN ( isPersonalCase = 'Y' and #{language}='zh-TW') THEN '個案'
                ELSE '案件'
                END
                ) isPersonalCase
            </when>
        </choose>
        <choose>
            <when test="username == 'ALL'  ">
                CASE  WHEN cqt.username is NULL THEN '' ELSE cqt.username END username
            </when>
        </choose>
        <choose>
            <when test="nowHandlerName == 'ALL' ">
                CASE  WHEN nowHandlerName is NULL THEN '' ELSE nowHandlerName END nowHandlerName
            </when>
        </choose>
        <choose>
            <when test="issueLevel == 'ALL' ">
                CASE  WHEN issue_level is NULL THEN '' WHEN issue_level = '' THEN '' ELSE issue_level END issueLevel
            </when>
        </choose>
        <choose>
            <when test="processDay == 'ALL' ">
                processDay
            </when>
        </choose>
        <choose>
            <when test="mainChargeName == 'ALL' ">
                CASE  WHEN mainChargeName is NULL THEN '' ELSE mainChargeName END mainChargeName
            </when>
        </choose>
        <choose>
            <when test="custLevel == 'ALL' ">
                CASE WHEN cust_level is NULL THEN '' ELSE cust_level END custLevel
            </when>
        </choose>
        FROM cases_query_tmp cqt
        LEFT JOIN issue_casedetail d ON cqt.issueId = d.issueId
        LEFT JOIN issue cs ON cqt.issueId=cs.issueId
        LEFT JOIN ( SELECT DISTINCT IssueId
        FROM issue_progress
        WHERE ProcessType = '9'
        )ta4 ON ta4.IssueId = cqt.IssueId
        LEFT JOIN mars_customerservice mc ON cqt.serviceCode=mc.CustomerServiceCode AND cs.ProductCode=mc.ProductCode
        where query_id=#{queryId}
        <choose>
            <when test="flag == '已结案'.toString() or flag == '已結案'.toString()">
                AND cqt.issueStatus in ('Y','P','7','8','10')
            </when>
            <when test="flag == '未结案'.toString() or flag == '未結案'.toString()">
                AND cqt.issueStatus in ('C','N','2','3','4','11','12','22')
            </when>
        </choose>
        <choose>
            <when test="issueClassificationDesc  == '客制需求(187)'.toString() ">
                and issueClassificationDesc ='客制需求' and (cs.submitway = 'HD187' or cs.submitway like 'EXCEL%')
            </when>
            <when test="issueClassificationDesc  == '客制需求(服务云)'.toString() ">
                and issueClassificationDesc ='客制需求' and cs.SubmitWay = 'INNER'
            </when>
            <when test="issueClassificationDesc != null and issueClassificationDesc != '' and issueClassificationDesc != 'ALL' and issueClassificationDesc != '客制需求(187)'.toString()
                        and issueClassificationDesc != '客制需求(服务云)'.toString() ">
                and issueClassificationDesc = #{issueClassificationDesc}
            </when>
            <when test="issueClassificationDesc  == null ">
                and (issueClassificationDesc is null or issueClassificationDesc = '' )
            </when>
        </choose>
        <choose>
            <when test="customerName != null and customerName != '' and customerName != 'ALL' ">
                and customerName = #{customerName}
            </when>
            <when test="customerName == null ">
                and (customerName is null or customerName = '')
            </when>
        </choose>
        <choose>
            <when test="erpSystemCode != null and erpSystemCode != '' and erpSystemCode != 'ALL' ">
                and cqt.erpSystemCode = #{erpSystemCode}
            </when>
            <when test="erpSystemCode == null  ">
                and (cqt.erpSystemCode  is null or cqt.erpSystemCode  = '')
            </when>
        </choose>
        <choose>
            <when test="productCode != null and productCode != '' and productCode != 'ALL' ">
                and productName = #{productCode}
            </when>
        </choose>
        <choose>
            <when test="emergency == '紧急'.toString() or emergency == '緊急'.toString()">
                and cqt.emergency = 1
            </when>
            <when test="emergency == '不紧急'.toString() or emergency == '不緊急'.toString()">
                and cqt.emergency = 0
            </when>
        </choose>
        <choose>
            <when test="personalCase== '个案'.toString() or personalCase == '個案'.toString()">
                and isPersonalCase= 'Y'
            </when>
            <when test="personalCase== '案件'.toString() or personalCase == '案件'.toString()">
                and (isPersonalCase= 'N' or isPersonalCase is null or isPersonalCase= ' ')
            </when>
        </choose>
        <choose>
            <when test="username != null and username != '' and username != 'ALL' ">
                and cqt.username = #{username}
            </when>
            <when test="username == null ">
                and (cqt.username is null or cqt.username = '')
            </when>
        </choose>
        <choose>
            <when test="nowHandlerName != null and nowHandlerName != '' and nowHandlerName != 'ALL' ">
                and nowHandlerName= #{nowHandlerName}
            </when>
            <when test="nowHandlerName == null ">
                and (nowHandlerName is null or nowHandlerName='')
            </when>
        </choose>
        <choose>
            <when test="issueLevel != null and issueLevel != '' and issueLevel != 'ALL' ">
                and issue_level= #{issueLevel}
            </when>
            <when test="issueLevel == null ">
                and (issue_level is null or issue_level='')
            </when>
        </choose>
        <choose>
            <when test="processDay != null and processDay != '' and processDay != 'ALL' ">
                and processDay = #{processDay}
            </when>
        </choose>
        <choose>
            <when test="mainChargeName != null and mainChargeName != '' and mainChargeName != 'ALL' ">
                and mainChargeName = #{mainChargeName}
            </when>
            <when test="mainChargeName == null ">
                and (mainChargeName is null or mainChargeName='')
            </when>
        </choose>
        <choose>
            <when test="custLevel != null and custLevel != '' and custLevel != 'ALL' ">
                and cust_level = #{custLevel}
            </when>
            <when test="custLevel == null ">
                and (cust_level is null or cust_level='')
            </when>
        </choose>
        GROUP BY cqt.issueId
        )tc1
        GROUP BY
        <choose>
            <when test="flag == 'ALL'">
                flag )td1 ON ta1.flag = td1.flag,
            </when>
        </choose>
        <choose>
            <when test="issueClassificationDesc =='ALL' ">
                issueClassificationDesc )td1 ON ta1.issueClassificationDesc = td1.issueClassificationDesc,
            </when>
        </choose>
        <choose>
            <when test="customerName == 'ALL'">
                 serviceCode )td1 ON ta1.serviceCode = td1.serviceCode,
            </when>
        </choose>
        <choose>
            <when test="erpSystemCode == 'ALL' ">
                erpSystemCode  )td1 ON ta1.erpSystemCode = td1.erpSystemCode,
            </when>
        </choose>
        <choose>
            <when test="productCode == 'ALL' ">
                productName )td1 ON ta1.productName = td1.productName,
            </when>
        </choose>
        <choose>
            <when test="emergency == 'ALL'">
                emerge )td1 ON ta1.emerge = td1.emerge,
            </when>
        </choose>
        <choose>
            <when test="personalCase== 'ALL'">
                isPersonalCase )td1 ON ta1.isPersonalCase = td1.isPersonalCase,
            </when>
        </choose>
        <choose>
            <when test="username == 'ALL'  ">
                username )td1 ON ta1.username = td1.username,
            </when>
        </choose>
        <choose>
            <when test="nowHandlerName == 'ALL' ">
                nowHandlerName )td1 ON ta1.nowHandlerName = td1.nowHandlerName,
            </when>
        </choose>
        <choose>
            <when test="issueLevel == 'ALL' ">
                issueLevel )td1 ON ta1.issueLevel = td1.issueLevel,
            </when>
        </choose>
        <choose>
            <when test="processDay == 'ALL' ">
                processDay )td1 ON ta1.processDay = td1.processDay,
            </when>
        </choose>
        <choose>
            <when test="mainChargeName == 'ALL' ">
                mainChargeName )td1 ON ta1.mainChargeName = td1.mainChargeName,
            </when>
        </choose>
        <choose>
            <when test="custLevel == 'ALL' ">
                custLevel )td1 ON ta1.custLevel = td1.custLevel,
            </when>
        </choose>
        ( SELECT COUNT(DISTINCT cqt.issueId) AS total_cnt,SUM(ProcessHours) total_hour
        from ( select cqt.issueId,CASE WHEN IssueClassificationDesc = '客制需求' AND cs.submitway = 'HD187' THEN
        estimated_work_hours ELSE SUM(ProcessHours) END ProcessHours
        FROM cases_query_tmp cqt
        LEFT JOIN issue_progress p ON cqt.issueId=p.issueId
        LEFT JOIN issue cs ON cqt.issueId=cs.issueId
        left join issue_casedetail d on cs.issueId=d.issueId
        LEFT JOIN mars_customerservice mc ON cqt.serviceCode=mc.CustomerServiceCode AND cs.ProductCode=mc.ProductCode
        where query_id=#{queryId}
        <choose>
            <when test="flag == '已结案'.toString() or flag == '已結案'.toString()">
                AND cqt.issueStatus in ('Y','P','7','8','10')
            </when>
            <when test="flag == '未结案'.toString() or flag == '未結案'.toString()">
                AND cqt.issueStatus in ('C','N','2','3','4','11','12','22')
            </when>
        </choose>
        <choose>
            <when test="issueClassificationDesc  == '客制需求(187)'.toString() ">
                and issueClassificationDesc ='客制需求' and (cs.submitway = 'HD187' or cs.submitway like 'EXCEL%')
            </when>
            <when test="issueClassificationDesc  == '客制需求(服务云)'.toString() ">
                and issueClassificationDesc ='客制需求' and cs.SubmitWay = 'INNER'
            </when>
            <when test="issueClassificationDesc != null and issueClassificationDesc != '' and issueClassificationDesc != 'ALL' and issueClassificationDesc != '客制需求(187)'.toString()
                        and issueClassificationDesc != '客制需求(服务云)'.toString()  ">
                and issueClassificationDesc = #{issueClassificationDesc}
            </when>
            <when test="issueClassificationDesc  == null ">
                and (issueClassificationDesc is null or issueClassificationDesc = '' )
            </when>
        </choose>
        <choose>
            <when test="customerName != null and customerName != '' and customerName != 'ALL' ">
                and customerName = #{customerName}
            </when>
            <when test="customerName == null ">
                and (customerName is null or customerName = '')
            </when>
        </choose>
        <choose>
            <when test="erpSystemCode != null and erpSystemCode != '' and erpSystemCode != 'ALL' ">
                and cqt.erpSystemCode = #{erpSystemCode}
            </when>
            <when test="erpSystemCode == null  ">
                and (cqt.erpSystemCode is null or cqt.erpSystemCode = '')
            </when>
        </choose>
        <choose>
            <when test="productCode != null and productCode != '' and productCode != 'ALL' ">
                and productName = #{productCode}
            </when>
        </choose>
        <choose>
            <when test="emergency == '紧急'.toString() or emergency == '緊急'.toString()">
                and cqt.emergency = 1
            </when>
            <when test="emergency == '不紧急'.toString() or emergency == '不緊急'.toString()">
                and cqt.emergency = 0
            </when>
        </choose>
        <choose>
            <when test="personalCase== '个案'.toString() or personalCase == '個案'.toString()">
                and isPersonalCase= 'Y'
            </when>
            <when test="personalCase== '案件'.toString() or personalCase == '案件'.toString()">
                and (isPersonalCase= 'N' or isPersonalCase is null or isPersonalCase= ' ')
            </when>
        </choose>
        <choose>
            <when test="username != null and username != '' and username != 'ALL' ">
                and cqt.username = #{username}
            </when>
            <when test="username == null ">
                and (cqt.username is null or cqt.username = '')
            </when>
        </choose>
        <choose>
            <when test="nowHandlerName != null and nowHandlerName != '' and nowHandlerName != 'ALL' ">
                and nowHandlerName= #{nowHandlerName}
            </when>
            <when test="nowHandlerName == null ">
                and (nowHandlerName is null or nowHandlerName='')
            </when>
        </choose>
        <choose>
            <when test="issueLevel != null and issueLevel != '' and issueLevel != 'ALL' ">
                and issue_level= #{issueLevel}
            </when>
            <when test="issueLevel == null ">
                and (issue_level is null or issue_level='')
            </when>
        </choose>
        <choose>
            <when test="processDay != null and processDay != '' and processDay != 'ALL' ">
                and processDay = #{processDay}
            </when>
        </choose>
        <choose>
            <when test="mainChargeName != null and mainChargeName != '' and mainChargeName != 'ALL' ">
                and mainChargeName = #{mainChargeName}
            </when>
            <when test="mainChargeName == null ">
                and (mainChargeName is null or mainChargeName='')
            </when>
        </choose>
        <choose>
            <when test="custLevel != null and custLevel != '' and custLevel != 'ALL' ">
                and cust_level = #{custLevel}
            </when>
            <when test="custLevel == null ">
                and (cust_level is null or cust_level='')
            </when>
        </choose>
        GROUP BY cqt.issueId
        )cqt
        )tb1
        ORDER BY SUM DESC
    </select>

    <select id="getTempIssueClassificationDesc" parameterType="java.lang.String" resultType="com.digiwin.escloud.issueservice.t.model.cases.CasesAnalyse">
        SELECT
        CASE WHEN ta1.issueClassificationDesc = '' THEN NULL ELSE ta1.issueClassificationDesc END issueClassificationDesc,
        CASE WHEN ta1.issueClassificationDesc = '' THEN NULL ELSE ta1.issueClassificationDesc END 'column',
        type_cnt as 'sum',
        concat(cast(type_cnt /total_cnt *100 AS DECIMAL(18,1)),'%') AS perc,hour,
        concat(cast(hour /total_hour *100 AS DECIMAL(18,1)),'%') AS hourPerc,
        concat(ROUND(C/type_cnt*100, 1),'%') AS 'oneDayReplyRank',
        concat(ROUND(D/type_cnt*100, 1),'%') AS 'threeDayReplyRank',
        concat(ROUND(E/type_cnt*100, 1),'%') AS 'allReplyRank',
        C 'oneDayReplySum',D 'threeDayReplySum',E 'allReplySum'
        FROM (  SELECT COUNT(DISTINCT cqt.issueId) AS type_cnt,
        SUM(ProcessHours) hour,
        issueClassificationDesc
        FROM (SELECT cqt.issueId,
        CASE  WHEN issueclassificationDesc is NULL THEN '' WHEN IssueClassificationDesc = '客制需求' AND (cs.submitway = 'HD187' or cs.submitway like 'EXCEL%') THEN '客制需求(187)'
        WHEN IssueClassificationDesc = '客制需求' AND cs.SubmitWay = 'INNER' THEN '客制需求(服务云)'
        ELSE issueclassificationDesc END issueclassificationDesc,
        CASE WHEN IssueClassificationDesc = '客制需求' AND cs.submitway = 'HD187' THEN estimated_work_hours ELSE  SUM(ProcessHours) END ProcessHours
        FROM cases_query_tmp cqt
        LEFT JOIN issue_progress p ON cqt.issueId=p.issueId
        LEFT JOIN ( select cs.IssueId,SubmitWay,estimated_work_hours from issue cs left join issue_casedetail d on cs.issueId=d.issueId
        )cs ON cqt.issueId=cs.issueId
        where query_id=#{queryId}
        <choose>
            <when test="flag == '已结案'.toString() or flag == '已結案'.toString()">
                AND cqt.issueStatus in ('Y','P','7','8','10')
            </when>
            <when test="flag == '未结案'.toString() or flag == '未結案'.toString()">
                AND cqt.issueStatus in ('N','2','3','4','11','12','22')
            </when>
        </choose>
        <choose>
            <when test="issueClassificationDesc  == '客制需求(187)'.toString() ">
                and issueClassificationDesc ='客制需求' and (cs.submitway = 'HD187' or cs.submitway like 'EXCEL%')
            </when>
            <when test="issueClassificationDesc  == '客制需求(服务云)'.toString() ">
                and issueClassificationDesc ='客制需求' and cs.SubmitWay = 'INNER'
            </when>
            <when test="issueClassificationDesc != null and issueClassificationDesc != '' and issueClassificationDesc != 'ALL' and issueClassificationDesc != '客制需求(187)'.toString()
                        and issueClassificationDesc != '客制需求(服务云)'.toString() ">
                and issueClassificationDesc = #{issueClassificationDesc}
            </when>
            <when test="issueClassificationDesc  == null ">
                and (issueClassificationDesc is null or issueClassificationDesc = '' )
            </when>
        </choose>
        <choose>
            <when test="customerName != null and customerName != '' and customerName != 'ALL' ">
                and customerName = #{customerName}
            </when>
            <when test="customerName == null ">
                and (customerName is null or customerName = '')
            </when>
        </choose>
        <choose>
            <when test="erpSystemCode != null and erpSystemCode != '' and erpSystemCode != 'ALL' ">
                and cqt.erpSystemCode = #{erpSystemCode}
            </when>
            <when test="erpSystemCode == null  ">
                and (cqt.erpSystemCode  is null or cqt.erpSystemCode  = '')
            </when>
        </choose>
        <choose>
            <when test="productCode != null and productCode != '' and productCode != 'ALL' ">
                and productName = #{productCode}
            </when>
        </choose>
        <choose>
            <when test="emergency == '紧急'.toString() or emergency == '緊急'.toString()">
                and emergency = 1
            </when>
            <when test="emergency == '不紧急'.toString() or emergency == '不緊急'.toString()">
                and (emergency = 0 )
            </when>
        </choose>
        <choose>
            <when test="personalCase== '个案'.toString() or personalCase == '個案'.toString()">
                and isPersonalCase= 'Y'
            </when>
            <when test="personalCase== '案件'.toString() or personalCase == '案件'.toString()">
                and (isPersonalCase= 'N' or isPersonalCase is null or isPersonalCase= ' ')
            </when>
        </choose>
        <choose>
            <when test="username != null and username != '' and username != 'ALL' ">
                and username = #{username}
            </when>
            <when test="username == null ">
                and (username is null or username = '')
            </when>
        </choose>
        <choose>
            <when test="nowHandlerName != null and nowHandlerName != '' and nowHandlerName != 'ALL' ">
                and nowHandlerName= #{nowHandlerName}
            </when>
            <when test="nowHandlerName == null ">
                and (nowHandlerName is null or nowHandlerName='')
            </when>
        </choose>
        <choose>
            <when test="processDay != null and processDay != '' and processDay != 'ALL' ">
                and processDay = #{processDay}
            </when>
        </choose>
        <choose>
            <when test="mainChargeName != null and mainChargeName != '' and mainChargeName != 'ALL' ">
                and mainChargeName = #{mainChargeName}
            </when>
            <when test="mainChargeName == null ">
                and (mainChargeName is null or mainChargeName='')
            </when>
        </choose>
        GROUP BY cqt.issueId
                )cqt
        GROUP BY issueClassificationDesc
        ORDER BY type_cnt DESC) ta1
        LEFT JOIN (  SELECT COUNT(C) C,COUNT(D) D,COUNT(E) E,issueClassificationDesc
        FROM (  SELECT DISTINCT cqt.issueId,
        CASE WHEN  (reply_min <![CDATA[ < ]]> 1440 AND reply_min <![CDATA[ > ]]> 0 ) OR (reply_min = 0 AND IssueStatus IN (7,8,10,11) )  THEN 'C' END C,
        CASE WHEN  (reply_min <![CDATA[ < ]]> 4320 AND reply_min <![CDATA[ > ]]> 0 ) OR (reply_min = 0 AND IssueStatus IN (7,8,10,11) ) THEN 'D' END D,
        CASE WHEN  (reply_min <![CDATA[ > ]]> 0  or  IssueStatus IN (7,8,10,11) ) THEN 'E' END E,
        CASE  WHEN issueclassificationDesc is NULL THEN '' WHEN IssueClassificationDesc = '客制需求' AND (cs.submitway = 'HD187' or cs.submitway like 'EXCEL%') THEN '客制需求(187)'
        WHEN IssueClassificationDesc = '客制需求' AND cs.SubmitWay = 'INNER' THEN '客制需求(服务云)'
        else issueclassificationDesc end issueclassificationDesc
        FROM cases_query_tmp cqt
        LEFT JOIN issue_casedetail d ON cqt.issueId = d.issueId
        LEFT JOIN ( select cs.IssueId,SubmitWay from issue cs)cs ON cqt.issueId=cs.issueId
        where query_id=#{queryId}
        <choose>
            <when test="flag == '已结案'.toString() or flag == '已結案'.toString()">
                AND cqt.issueStatus in ('Y','P','7','8','10')
            </when>
            <when test="flag == '未结案'.toString() or flag == '未結案'.toString()">
                AND cqt.issueStatus in ('N','2','3','4','11','12','22')
            </when>
        </choose>
        <choose>
            <when test="issueClassificationDesc  == '客制需求(187)'.toString() ">
                and issueClassificationDesc ='客制需求' and (cs.submitway = 'HD187' or cs.submitway like 'EXCEL%')
            </when>
            <when test="issueClassificationDesc  == '客制需求(服务云)'.toString() ">
                and issueClassificationDesc ='客制需求' and cs.SubmitWay = 'INNER'
            </when>
            <when test="issueClassificationDesc != null and issueClassificationDesc != '' and issueClassificationDesc != 'ALL' and issueClassificationDesc != '客制需求(187)'.toString()
                        and issueClassificationDesc != '客制需求(服务云)'.toString() ">
                and issueClassificationDesc = #{issueClassificationDesc}
            </when>
            <when test="issueClassificationDesc  == null ">
                and (issueClassificationDesc is null or issueClassificationDesc = '' )
            </when>
        </choose>
        <choose>
            <when test="customerName != null and customerName != '' and customerName != 'ALL' ">
                and customerName = #{customerName}
            </when>
            <when test="customerName == null ">
                and (customerName is null or customerName = '')
            </when>
        </choose>
        <choose>
            <when test="erpSystemCode != null and erpSystemCode != '' and erpSystemCode != 'ALL' ">
                and cqt.erpSystemCode = #{erpSystemCode}
            </when>
            <when test="erpSystemCode == null  ">
                and (cqt.erpSystemCode  is null or cqt.erpSystemCode  = '')
            </when>
        </choose>
        <choose>
            <when test="productCode != null and productCode != '' and productCode != 'ALL' ">
                and productName = #{productCode}
            </when>
        </choose>
        <choose>
            <when test="emergency == '紧急'.toString() or emergency == '緊急'.toString()">
                and emergency = 1
            </when>
            <when test="emergency == '不紧急'.toString() or emergency == '不緊急'.toString()">
                and (emergency = 0 )
            </when>
        </choose>
        <choose>
            <when test="personalCase== '个案'.toString() or personalCase == '個案'.toString()">
                and isPersonalCase= 'Y'
            </when>
            <when test="personalCase== '案件'.toString() or personalCase == '案件'.toString()">
                and (isPersonalCase= 'N' or isPersonalCase is null or isPersonalCase= ' ')
            </when>
        </choose>
        <choose>
            <when test="username != null and username != '' and username != 'ALL' ">
                and username = #{username}
            </when>
            <when test="username == null ">
                and (username is null or username = '')
            </when>
        </choose>
        <choose>
            <when test="nowHandlerName != null and nowHandlerName != '' and nowHandlerName != 'ALL' ">
                and nowHandlerName= #{nowHandlerName}
            </when>
            <when test="nowHandlerName == null ">
                and (nowHandlerName is null or nowHandlerName='')
            </when>
        </choose>
        <choose>
            <when test="processDay != null and processDay != '' and processDay != 'ALL' ">
                and processDay = #{processDay}
            </when>
        </choose>
        <choose>
            <when test="mainChargeName != null and mainChargeName != '' and mainChargeName != 'ALL' ">
                and mainChargeName = #{mainChargeName}
            </when>
            <when test="mainChargeName == null ">
                and (mainChargeName is null or mainChargeName='')
            </when>
        </choose>
        GROUP BY cqt.issueId
        )tc1
        GROUP BY issueClassificationDesc )td1 ON ta1.issueClassificationDesc = td1.issueClassificationDesc,
        ( SELECT COUNT(DISTINCT cqt.issueId) AS total_cnt,SUM(ProcessHours) total_hour
        from ( select cqt.issueId,CASE WHEN IssueClassificationDesc = '客制需求' AND cs.submitway = 'HD187' THEN
        estimated_work_hours ELSE SUM(ProcessHours) END ProcessHours
        FROM cases_query_tmp cqt
        LEFT JOIN issue_progress p ON cqt.issueId=p.issueId
        LEFT JOIN ( select cs.IssueId,SubmitWay,estimated_work_hours from issue cs left join issue_casedetail d on
        cs.issueId=d.issueId)cs ON
        cqt.issueId=cs.issueId
        where query_id=#{queryId}
        <choose>
            <when test="flag == '已结案'.toString() or flag == '已結案'.toString()">
                AND cqt.issueStatus in ('Y','P','7','8','10')
            </when>
            <when test="flag == '未结案'.toString() or flag == '未結案'.toString()">
                AND cqt.issueStatus in ('N','2','3','4','11','12','22')
            </when>
        </choose>
        <choose>
            <when test="issueClassificationDesc  == '客制需求(187)'.toString() ">
                and issueClassificationDesc ='客制需求' and (cs.submitway = 'HD187' or cs.submitway like 'EXCEL%')
            </when>
            <when test="issueClassificationDesc  == '客制需求(服务云)'.toString() ">
                and issueClassificationDesc ='客制需求' and cs.SubmitWay = 'INNER'
            </when>
            <when test="issueClassificationDesc != null and issueClassificationDesc != '' and issueClassificationDesc != 'ALL' and issueClassificationDesc != '客制需求(187)'.toString()
                        and issueClassificationDesc != '客制需求(服务云)'.toString()  ">
                and issueClassificationDesc = #{issueClassificationDesc}
            </when>
            <when test="issueClassificationDesc  == null ">
                and (issueClassificationDesc is null or issueClassificationDesc = '' )
            </when>
        </choose>
        <choose>
            <when test="customerName != null and customerName != '' and customerName != 'ALL' ">
                and customerName = #{customerName}
            </when>
            <when test="customerName == null ">
                and (customerName is null or customerName = '')
            </when>
        </choose>
        <choose>
            <when test="erpSystemCode != null and erpSystemCode != '' and erpSystemCode != 'ALL' ">
                and cqt.erpSystemCode = #{erpSystemCode}
            </when>
            <when test="erpSystemCode == null  ">
                and (cqt.erpSystemCode is null or cqt.erpSystemCode = '')
            </when>
        </choose>
        <choose>
            <when test="productCode != null and productCode != '' and productCode != 'ALL' ">
                and productName = #{productCode}
            </when>
        </choose>
        <choose>
            <when test="emergency == '紧急'.toString() or emergency == '緊急'.toString()">
                and emergency = 1
            </when>
            <when test="emergency == '不紧急'.toString() or emergency == '不緊急'.toString()">
                and (emergency = 0 )
            </when>
        </choose>
        <choose>
            <when test="personalCase== '个案'.toString() or personalCase == '個案'.toString()">
                and isPersonalCase= 'Y'
            </when>
            <when test="personalCase== '案件'.toString() or personalCase == '案件'.toString()">
                and (isPersonalCase= 'N' or isPersonalCase is null or isPersonalCase= ' ')
            </when>
        </choose>
        <choose>
            <when test="username != null and username != '' and username != 'ALL' ">
                and username = #{username}
            </when>
            <when test="username == null ">
                and (username is null or username = '')
            </when>
        </choose>
        <choose>
            <when test="nowHandlerName != null and nowHandlerName != '' and nowHandlerName != 'ALL' ">
                and nowHandlerName= #{nowHandlerName}
            </when>
            <when test="nowHandlerName == null ">
                and (nowHandlerName is null or nowHandlerName='')
            </when>
        </choose>
        <choose>
            <when test="processDay != null and processDay != '' and processDay != 'ALL' ">
                and processDay = #{processDay}
            </when>
        </choose>
        <choose>
            <when test="mainChargeName != null and mainChargeName != '' and mainChargeName != 'ALL' ">
                and mainChargeName = #{mainChargeName}
            </when>
            <when test="mainChargeName == null ">
                and (mainChargeName is null or mainChargeName='')
            </when>
        </choose>
        GROUP BY cqt.issueId
        )cqt
        )tb1
        ORDER BY SUM DESC
    </select>

    <select id="getTempCustomerName" parameterType="java.lang.String" resultType="com.digiwin.escloud.issueservice.t.model.cases.CasesAnalyse">
        SELECT ta1.customerName,ta1.serviceCode,ta1.customerName AS 'column',
        type_cnt as 'sum',
        concat(cast(type_cnt /total_cnt *100 AS DECIMAL(18,1)),'%') AS perc,hour,
        concat(cast(hour /total_hour *100 AS DECIMAL(18,1)),'%') AS hourPerc,
        concat(ROUND(C/type_cnt*100, 1),'%') AS 'oneDayReplyRank',
        concat(ROUND(D/type_cnt*100, 1),'%') AS 'threeDayReplyRank',
        concat(ROUND(E/type_cnt*100, 1),'%') AS 'allReplyRank',
        C 'oneDayReplySum',D 'threeDayReplySum',E 'allReplySum'
        FROM (  SELECT COUNT(DISTINCT cqt.issueId) AS type_cnt,
        SUM(ProcessHours) hour,customerName, serviceCode
        FROM (SELECT cqt.issueId,customerName, serviceCode,CASE WHEN IssueClassificationDesc = '客制需求' AND cs.submitway = 'HD187' THEN estimated_work_hours ELSE  SUM(ProcessHours) END ProcessHours
        FROM cases_query_tmp cqt
        LEFT JOIN issue_progress p ON cqt.issueId=p.issueId
        LEFT JOIN ( select cs.IssueId,SubmitWay,estimated_work_hours from issue cs left join issue_casedetail d on cs.issueId=d.issueId
        )cs ON cqt.issueId=cs.issueId
        where query_id=#{queryId}
        <choose>
            <when test="flag == '已结案'.toString() or flag == '已結案'.toString()">
                AND cqt.issueStatus in ('Y','P','7','8','10')
            </when>
            <when test="flag == '未结案'.toString() or flag == '未結案'.toString()">
                AND cqt.issueStatus in ('N','2','3','4','11','12','22')
            </when>
        </choose>
        <choose>
            <when test="issueClassificationDesc  == '客制需求(187)'.toString() ">
                and issueClassificationDesc ='客制需求' and (cs.submitway = 'HD187' or cs.submitway like 'EXCEL%')
            </when>
            <when test="issueClassificationDesc  == '客制需求(服务云)'.toString() ">
                and issueClassificationDesc ='客制需求' and cs.SubmitWay = 'INNER'
            </when>
            <when test="issueClassificationDesc != null and issueClassificationDesc != '' and issueClassificationDesc != 'ALL' and issueClassificationDesc != '客制需求(187)'.toString()
                        and issueClassificationDesc != '客制需求(服务云)'.toString() ">
                and issueClassificationDesc = #{issueClassificationDesc}
            </when>
            <when test="issueClassificationDesc  == null ">
                and (issueClassificationDesc is null or issueClassificationDesc = '' )
            </when>
        </choose>
        <choose>
            <when test="customerName != null and customerName != '' and customerName != 'ALL' ">
                and customerName = #{customerName}
            </when>
            <when test="customerName == null ">
                and (customerName is null or customerName = '')
            </when>
        </choose>
        <choose>
            <when test="erpSystemCode != null and erpSystemCode != '' and erpSystemCode != 'ALL' ">
                and cqt.erpSystemCode = #{erpSystemCode}
            </when>
            <when test="erpSystemCode == null  ">
                and (cqt.erpSystemCode  is null or cqt.erpSystemCode  = '')
            </when>
        </choose>
        <choose>
            <when test="productCode != null and productCode != '' and productCode != 'ALL' ">
                and productName = #{productCode}
            </when>
        </choose>
        <choose>
            <when test="emergency == '紧急'.toString() or emergency == '緊急'.toString()">
                and emergency = 1
            </when>
            <when test="emergency == '不紧急'.toString() or emergency == '不緊急'.toString()">
                and (emergency = 0 )
            </when>
        </choose>
        <choose>
            <when test="personalCase== '个案'.toString() or personalCase == '個案'.toString()">
                and isPersonalCase= 'Y'
            </when>
            <when test="personalCase== '案件'.toString() or personalCase == '案件'.toString()">
                and (isPersonalCase= 'N' or isPersonalCase is null or isPersonalCase= ' ')
            </when>
        </choose>
        <choose>
            <when test="username != null and username != '' and username != 'ALL' ">
                and username = #{username}
            </when>
            <when test="username == null ">
                and (username is null or username = '')
            </when>
        </choose>
        <choose>
            <when test="nowHandlerName != null and nowHandlerName != '' and nowHandlerName != 'ALL' ">
                and nowHandlerName= #{nowHandlerName}
            </when>
            <when test="nowHandlerName == null ">
                and (nowHandlerName is null or nowHandlerName='')
            </when>
        </choose>
        <choose>
            <when test="processDay != null and processDay != '' and processDay != 'ALL' ">
                and processDay = #{processDay}
            </when>
        </choose>
        <choose>
            <when test="mainChargeName != null and mainChargeName != '' and mainChargeName != 'ALL' ">
                and mainChargeName = #{mainChargeName}
            </when>
            <when test="mainChargeName == null ">
                and (mainChargeName is null or mainChargeName='')
            </when>
        </choose>
        GROUP BY cqt.issueId
        )cqt
        GROUP BY serviceCode
        ORDER BY type_cnt DESC) ta1
        LEFT JOIN (  SELECT COUNT(C) C,COUNT(D) D,COUNT(E) E,customerName, serviceCode
        FROM (  SELECT DISTINCT cqt.issueId,
        CASE WHEN  (reply_min <![CDATA[ < ]]> 1440 AND reply_min <![CDATA[ > ]]> 0 ) OR (reply_min = 0 AND IssueStatus IN (7,8,10,11) )  THEN 'C' END C,
        CASE WHEN  (reply_min <![CDATA[ < ]]> 4320 AND reply_min <![CDATA[ > ]]> 0 ) OR (reply_min = 0 AND IssueStatus IN (7,8,10,11) ) THEN 'D' END D,
        CASE WHEN  (reply_min <![CDATA[ > ]]> 0  or  IssueStatus IN (7,8,10,11) ) THEN 'E' END E,
        customerName, serviceCode
        FROM cases_query_tmp cqt
        LEFT JOIN issue_casedetail d ON cqt.issueId = d.issueId
        LEFT JOIN ( select cs.IssueId,SubmitWay from issue cs )cs ON cqt.issueId=cs.issueId
        where query_id=#{queryId}
        <choose>
            <when test="flag == '已结案'.toString() or flag == '已結案'.toString()">
                AND cqt.issueStatus in ('Y','P','7','8','10')
            </when>
            <when test="flag == '未结案'.toString() or flag == '未結案'.toString()">
                AND cqt.issueStatus in ('N','2','3','4','11','12','22')
            </when>
        </choose>
        <choose>
            <when test="issueClassificationDesc  == '客制需求(187)'.toString() ">
                and issueClassificationDesc ='客制需求' and (cs.submitway = 'HD187' or cs.submitway like 'EXCEL%')
            </when>
            <when test="issueClassificationDesc  == '客制需求(服务云)'.toString() ">
                and issueClassificationDesc ='客制需求' and cs.SubmitWay = 'INNER'
            </when>
            <when test="issueClassificationDesc != null and issueClassificationDesc != '' and issueClassificationDesc != 'ALL' and issueClassificationDesc != '客制需求(187)'.toString()
                        and issueClassificationDesc != '客制需求(服务云)'.toString() ">
                and issueClassificationDesc = #{issueClassificationDesc}
            </when>
            <when test="issueClassificationDesc  == null ">
                and (issueClassificationDesc is null or issueClassificationDesc = '' )
            </when>
        </choose>
        <choose>
            <when test="customerName != null and customerName != '' and customerName != 'ALL' ">
                and customerName = #{customerName}
            </when>
            <when test="customerName == null ">
                and (customerName is null or customerName = '')
            </when>
        </choose>
        <choose>
            <when test="erpSystemCode != null and erpSystemCode != '' and erpSystemCode != 'ALL' ">
                and cqt.erpSystemCode = #{erpSystemCode}
            </when>
            <when test="erpSystemCode == null  ">
                and (cqt.erpSystemCode  is null or cqt.erpSystemCode  = '')
            </when>
        </choose>
        <choose>
            <when test="productCode != null and productCode != '' and productCode != 'ALL' ">
                and productName = #{productCode}
            </when>
        </choose>
        <choose>
            <when test="emergency == '紧急'.toString() or emergency == '緊急'.toString()">
                and emergency = 1
            </when>
            <when test="emergency == '不紧急'.toString() or emergency == '不緊急'.toString()">
                and (emergency = 0 )
            </when>
        </choose>
        <choose>
            <when test="personalCase== '个案'.toString() or personalCase == '個案'.toString()">
                and isPersonalCase= 'Y'
            </when>
            <when test="personalCase== '案件'.toString() or personalCase == '案件'.toString()">
                and (isPersonalCase= 'N' or isPersonalCase is null or isPersonalCase= ' ')
            </when>
        </choose>
        <choose>
            <when test="username != null and username != '' and username != 'ALL' ">
                and username = #{username}
            </when>
            <when test="username == null ">
                and (username is null or username = '')
            </when>
        </choose>
        <choose>
            <when test="nowHandlerName != null and nowHandlerName != '' and nowHandlerName != 'ALL' ">
                and nowHandlerName= #{nowHandlerName}
            </when>
            <when test="nowHandlerName == null ">
                and (nowHandlerName is null or nowHandlerName='')
            </when>
        </choose>
        <choose>
            <when test="processDay != null and processDay != '' and processDay != 'ALL' ">
                and processDay = #{processDay}
            </when>
        </choose>
        <choose>
            <when test="mainChargeName != null and mainChargeName != '' and mainChargeName != 'ALL' ">
                and mainChargeName = #{mainChargeName}
            </when>
            <when test="mainChargeName == null ">
                and (mainChargeName is null or mainChargeName='')
            </when>
        </choose>
        GROUP BY cqt.issueId
        )tc1
        GROUP BY serviceCode )td1 ON ta1.serviceCode = td1.serviceCode,
        ( SELECT COUNT(DISTINCT cqt.issueId) AS total_cnt,SUM(ProcessHours) total_hour
        from ( select cqt.issueId,CASE WHEN IssueClassificationDesc = '客制需求' AND cs.submitway = 'HD187' THEN
        estimated_work_hours ELSE SUM(ProcessHours) END ProcessHours
        FROM cases_query_tmp cqt
        LEFT JOIN issue_progress p ON cqt.issueId=p.issueId
        LEFT JOIN ( select cs.IssueId,SubmitWay,estimated_work_hours from issue cs left join issue_casedetail d on
        cs.issueId=d.issueId )cs ON
        cqt.issueId=cs.issueId
        where query_id=#{queryId}
        <choose>
            <when test="flag == '已结案'.toString() or flag == '已結案'.toString()">
                AND cqt.issueStatus in ('Y','P','7','8','10')
            </when>
            <when test="flag == '未结案'.toString() or flag == '未結案'.toString()">
                AND cqt.issueStatus in ('N','2','3','4','11','12','22')
            </when>
        </choose>
        <choose>
            <when test="issueClassificationDesc  == '客制需求(187)'.toString() ">
                and issueClassificationDesc ='客制需求' and (cs.submitway = 'HD187' or cs.submitway like 'EXCEL%')
            </when>
            <when test="issueClassificationDesc  == '客制需求(服务云)'.toString() ">
                and issueClassificationDesc ='客制需求' and cs.SubmitWay = 'INNER'
            </when>
            <when test="issueClassificationDesc != null and issueClassificationDesc != '' and issueClassificationDesc != 'ALL' and issueClassificationDesc != '客制需求(187)'.toString()
                        and issueClassificationDesc != '客制需求(服务云)'.toString()  ">
                and issueClassificationDesc = #{issueClassificationDesc}
            </when>
            <when test="issueClassificationDesc  == null ">
                and (issueClassificationDesc is null or issueClassificationDesc = '' )
            </when>
        </choose>
        <choose>
            <when test="customerName != null and customerName != '' and customerName != 'ALL' ">
                and customerName = #{customerName}
            </when>
            <when test="customerName == null ">
                and (customerName is null or customerName = '')
            </when>
        </choose>
        <choose>
            <when test="erpSystemCode != null and erpSystemCode != '' and erpSystemCode != 'ALL' ">
                and cqt.erpSystemCode = #{erpSystemCode}
            </when>
            <when test="erpSystemCode == null  ">
                and (cqt.erpSystemCode is null or cqt.erpSystemCode = '')
            </when>
        </choose>
        <choose>
            <when test="productCode != null and productCode != '' and productCode != 'ALL' ">
                and productName = #{productCode}
            </when>
        </choose>
        <choose>
            <when test="emergency == '紧急'.toString() or emergency == '緊急'.toString()">
                and emergency = 1
            </when>
            <when test="emergency == '不紧急'.toString() or emergency == '不緊急'.toString()">
                and (emergency = 0 )
            </when>
        </choose>
        <choose>
            <when test="personalCase== '个案'.toString() or personalCase == '個案'.toString()">
                and isPersonalCase= 'Y'
            </when>
            <when test="personalCase== '案件'.toString() or personalCase == '案件'.toString()">
                and (isPersonalCase= 'N' or isPersonalCase is null or isPersonalCase= ' ')
            </when>
        </choose>
        <choose>
            <when test="username != null and username != '' and username != 'ALL' ">
                and username = #{username}
            </when>
            <when test="username == null ">
                and (username is null or username = '')
            </when>
        </choose>
        <choose>
            <when test="nowHandlerName != null and nowHandlerName != '' and nowHandlerName != 'ALL' ">
                and nowHandlerName= #{nowHandlerName}
            </when>
            <when test="nowHandlerName == null ">
                and (nowHandlerName is null or nowHandlerName='')
            </when>
        </choose>
        <choose>
            <when test="processDay != null and processDay != '' and processDay != 'ALL' ">
                and processDay = #{processDay}
            </when>
        </choose>
        <choose>
            <when test="mainChargeName != null and mainChargeName != '' and mainChargeName != 'ALL' ">
                and mainChargeName = #{mainChargeName}
            </when>
            <when test="mainChargeName == null ">
                and (mainChargeName is null or mainChargeName='')
            </when>
        </choose>
        GROUP BY cqt.issueId
        )cqt
        )tb1
        ORDER BY SUM DESC
    </select>

    <select id="getTempErpSystemCode" parameterType="java.lang.String" resultType="com.digiwin.escloud.issueservice.t.model.cases.CasesAnalyse">
        SELECT
        CASE WHEN ta1.erpSystemCode = '' THEN NULL ELSE ta1.erpSystemCode END erpSystemCode,
        CASE WHEN ta1.erpSystemCode = '' THEN NULL ELSE ta1.erpSystemCode END 'column',
        type_cnt as 'sum',
        concat(cast(type_cnt /total_cnt *100 AS DECIMAL(18,1)),'%') AS perc,hour,
        concat(cast(hour /total_hour *100 AS DECIMAL(18,1)),'%') AS hourPerc,
        concat(ROUND(C/type_cnt*100, 1),'%') AS 'oneDayReplyRank',
        concat(ROUND(D/type_cnt*100, 1),'%') AS 'threeDayReplyRank',
        concat(ROUND(E/type_cnt*100, 1),'%') AS 'allReplyRank',
        C 'oneDayReplySum',D 'threeDayReplySum',E 'allReplySum'
        FROM (  SELECT COUNT(DISTINCT cqt.issueId) AS type_cnt,
        SUM(ProcessHours) hour,erpSystemCode
        FROM (SELECT cqt.issueId,CASE WHEN IssueClassificationDesc = '客制需求' AND cs.submitway = 'HD187' THEN estimated_work_hours ELSE  SUM(ProcessHours) END ProcessHours,
        CASE  WHEN erpSystemCode is NULL THEN '' ELSE erpSystemCode END erpSystemCode
        FROM cases_query_tmp cqt
        LEFT JOIN issue_progress p ON cqt.issueId=p.issueId
        LEFT JOIN ( select cs.IssueId,SubmitWay,estimated_work_hours from issue cs left join issue_casedetail d on cs.issueId=d.issueId
        )cs ON cqt.issueId=cs.issueId
        where query_id=#{queryId}
        <choose>
            <when test="flag == '已结案'.toString() or flag == '已結案'.toString()">
                AND cqt.issueStatus in ('Y','P','7','8','10')
            </when>
            <when test="flag == '未结案'.toString() or flag == '未結案'.toString()">
                AND cqt.issueStatus in ('N','2','3','4','11','12','22')
            </when>
        </choose>
        <choose>
            <when test="issueClassificationDesc  == '客制需求(187)'.toString() ">
                and issueClassificationDesc ='客制需求' and (cs.submitway = 'HD187' or cs.submitway like 'EXCEL%')
            </when>
            <when test="issueClassificationDesc  == '客制需求(服务云)'.toString() ">
                and issueClassificationDesc ='客制需求' and cs.SubmitWay = 'INNER'
            </when>
            <when test="issueClassificationDesc != null and issueClassificationDesc != '' and issueClassificationDesc != 'ALL' and issueClassificationDesc != '客制需求(187)'.toString()
                        and issueClassificationDesc != '客制需求(服务云)'.toString() ">
                and issueClassificationDesc = #{issueClassificationDesc}
            </when>
            <when test="issueClassificationDesc  == null ">
                and (issueClassificationDesc is null or issueClassificationDesc = '' )
            </when>
        </choose>
        <choose>
            <when test="customerName != null and customerName != '' and customerName != 'ALL' ">
                and customerName = #{customerName}
            </when>
            <when test="customerName == null ">
                and (customerName is null or customerName = '')
            </when>
        </choose>
        <choose>
            <when test="erpSystemCode != null and erpSystemCode != '' and erpSystemCode != 'ALL' ">
                and cqt.erpSystemCode = #{erpSystemCode}
            </when>
            <when test="erpSystemCode == null  ">
                and (cqt.erpSystemCode  is null or cqt.erpSystemCode  = '')
            </when>
        </choose>
        <choose>
            <when test="productCode != null and productCode != '' and productCode != 'ALL' ">
                and productName = #{productCode}
            </when>
        </choose>
        <choose>
            <when test="emergency == '紧急'.toString() or emergency == '緊急'.toString()">
                and emergency = 1
            </when>
            <when test="emergency == '不紧急'.toString() or emergency == '不緊急'.toString()">
                and (emergency = 0 )
            </when>
        </choose>
        <choose>
            <when test="personalCase== '个案'.toString() or personalCase == '個案'.toString()">
                and isPersonalCase= 'Y'
            </when>
            <when test="personalCase== '案件'.toString() or personalCase == '案件'.toString()">
                and (isPersonalCase= 'N' or isPersonalCase is null or isPersonalCase= ' ')
            </when>
        </choose>
        <choose>
            <when test="username != null and username != '' and username != 'ALL' ">
                and username = #{username}
            </when>
            <when test="username == null ">
                and (username is null or username = '')
            </when>
        </choose>
        <choose>
            <when test="nowHandlerName != null and nowHandlerName != '' and nowHandlerName != 'ALL' ">
                and nowHandlerName= #{nowHandlerName}
            </when>
            <when test="nowHandlerName == null ">
                and (nowHandlerName is null or nowHandlerName='')
            </when>
        </choose>
        <choose>
            <when test="processDay != null and processDay != '' and processDay != 'ALL' ">
                and processDay = #{processDay}
            </when>
        </choose>
        <choose>
            <when test="mainChargeName != null and mainChargeName != '' and mainChargeName != 'ALL' ">
                and mainChargeName = #{mainChargeName}
            </when>
            <when test="mainChargeName == null ">
                and (mainChargeName is null or mainChargeName='')
            </when>
        </choose>
        GROUP BY cqt.issueId
        )cqt
        GROUP BY erpSystemCode
        ORDER BY type_cnt DESC) ta1
        LEFT JOIN (  SELECT COUNT(C) C,COUNT(D) D,COUNT(E) E,erpSystemCode
        FROM (  SELECT DISTINCT cqt.issueId,
        CASE WHEN  (reply_min <![CDATA[ < ]]> 1440 AND reply_min <![CDATA[ > ]]> 0 ) OR (reply_min = 0 AND IssueStatus IN (7,8,10,11) )  THEN 'C' END C,
        CASE WHEN  (reply_min <![CDATA[ < ]]> 4320 AND reply_min <![CDATA[ > ]]> 0 ) OR (reply_min = 0 AND IssueStatus IN (7,8,10,11) ) THEN 'D' END D,
        CASE WHEN  (reply_min <![CDATA[ > ]]> 0  or  IssueStatus IN (7,8,10,11) ) THEN 'E' END E,
        CASE  WHEN cqt.erpSystemCode is NULL THEN '' ELSE cqt.erpSystemCode END erpSystemCode
        FROM cases_query_tmp cqt
        LEFT JOIN issue_casedetail d ON cqt.issueId = d.issueId
        LEFT JOIN ( select cs.IssueId,SubmitWay from issue cs)cs ON cqt.issueId=cs.issueId
        where query_id=#{queryId}
        <choose>
            <when test="flag == '已结案'.toString() or flag == '已結案'.toString()">
                AND cqt.issueStatus in ('Y','P','7','8','10')
            </when>
            <when test="flag == '未结案'.toString() or flag == '未結案'.toString()">
                AND cqt.issueStatus in ('N','2','3','4','11','12','22')
            </when>
        </choose>
        <choose>
            <when test="issueClassificationDesc  == '客制需求(187)'.toString() ">
                and issueClassificationDesc ='客制需求' and (cs.submitway = 'HD187' or cs.submitway like 'EXCEL%')
            </when>
            <when test="issueClassificationDesc  == '客制需求(服务云)'.toString() ">
                and issueClassificationDesc ='客制需求' and cs.SubmitWay = 'INNER'
            </when>
            <when test="issueClassificationDesc != null and issueClassificationDesc != '' and issueClassificationDesc != 'ALL' and issueClassificationDesc != '客制需求(187)'.toString()
                        and issueClassificationDesc != '客制需求(服务云)'.toString() ">
                and issueClassificationDesc = #{issueClassificationDesc}
            </when>
            <when test="issueClassificationDesc  == null ">
                and (issueClassificationDesc is null or issueClassificationDesc = '' )
            </when>
        </choose>
        <choose>
            <when test="customerName != null and customerName != '' and customerName != 'ALL' ">
                and customerName = #{customerName}
            </when>
            <when test="customerName == null ">
                and (customerName is null or customerName = '')
            </when>
        </choose>
        <choose>
            <when test="erpSystemCode != null and erpSystemCode != '' and erpSystemCode != 'ALL' ">
                and cqt.erpSystemCode = #{erpSystemCode}
            </when>
            <when test="erpSystemCode == null  ">
                and (cqt.erpSystemCode  is null or cqt.erpSystemCode  = '')
            </when>
        </choose>
        <choose>
            <when test="productCode != null and productCode != '' and productCode != 'ALL' ">
                and productName = #{productCode}
            </when>
        </choose>
        <choose>
            <when test="emergency == '紧急'.toString() or emergency == '緊急'.toString()">
                and emergency = 1
            </when>
            <when test="emergency == '不紧急'.toString() or emergency == '不緊急'.toString()">
                and (emergency = 0 )
            </when>
        </choose>
        <choose>
            <when test="personalCase== '个案'.toString() or personalCase == '個案'.toString()">
                and isPersonalCase= 'Y'
            </when>
            <when test="personalCase== '案件'.toString() or personalCase == '案件'.toString()">
                and (isPersonalCase= 'N' or isPersonalCase is null or isPersonalCase= ' ')
            </when>
        </choose>
        <choose>
            <when test="username != null and username != '' and username != 'ALL' ">
                and username = #{username}
            </when>
            <when test="username == null ">
                and (username is null or username = '')
            </when>
        </choose>
        <choose>
            <when test="nowHandlerName != null and nowHandlerName != '' and nowHandlerName != 'ALL' ">
                and nowHandlerName= #{nowHandlerName}
            </when>
            <when test="nowHandlerName == null ">
                and (nowHandlerName is null or nowHandlerName='')
            </when>
        </choose>
        <choose>
            <when test="processDay != null and processDay != '' and processDay != 'ALL' ">
                and processDay = #{processDay}
            </when>
        </choose>
        <choose>
            <when test="mainChargeName != null and mainChargeName != '' and mainChargeName != 'ALL' ">
                and mainChargeName = #{mainChargeName}
            </when>
            <when test="mainChargeName == null ">
                and (mainChargeName is null or mainChargeName='')
            </when>
        </choose>
        GROUP BY cqt.issueId
        )tc1
        GROUP BY erpSystemCode  )td1 ON ta1.erpSystemCode = td1.erpSystemCode,
        ( SELECT COUNT(DISTINCT cqt.issueId) AS total_cnt,SUM(ProcessHours) total_hour
        from ( select cqt.issueId,CASE WHEN IssueClassificationDesc = '客制需求' AND cs.submitway = 'HD187' THEN
        estimated_work_hours ELSE SUM(ProcessHours) END ProcessHours
        FROM cases_query_tmp cqt
        LEFT JOIN issue_progress p ON cqt.issueId=p.issueId
        LEFT JOIN ( select cs.IssueId,SubmitWay,estimated_work_hours from issue cs left join issue_casedetail d on
        cs.issueId=d.issueId)cs ON
        cqt.issueId=cs.issueId
        where query_id=#{queryId}
        <choose>
            <when test="flag == '已结案'.toString() or flag == '已結案'.toString()">
                AND cqt.issueStatus in ('Y','P','7','8','10')
            </when>
            <when test="flag == '未结案'.toString() or flag == '未結案'.toString()">
                AND cqt.issueStatus in ('N','2','3','4','11','12','22')
            </when>
        </choose>
        <choose>
            <when test="issueClassificationDesc  == '客制需求(187)'.toString() ">
                and issueClassificationDesc ='客制需求' and (cs.submitway = 'HD187' or cs.submitway like 'EXCEL%')
            </when>
            <when test="issueClassificationDesc  == '客制需求(服务云)'.toString() ">
                and issueClassificationDesc ='客制需求' and cs.SubmitWay = 'INNER'
            </when>
            <when test="issueClassificationDesc != null and issueClassificationDesc != '' and issueClassificationDesc != 'ALL' and issueClassificationDesc != '客制需求(187)'.toString()
                        and issueClassificationDesc != '客制需求(服务云)'.toString()  ">
                and issueClassificationDesc = #{issueClassificationDesc}
            </when>
            <when test="issueClassificationDesc  == null ">
                and (issueClassificationDesc is null or issueClassificationDesc = '' )
            </when>
        </choose>
        <choose>
            <when test="customerName != null and customerName != '' and customerName != 'ALL' ">
                and customerName = #{customerName}
            </when>
            <when test="customerName == null ">
                and (customerName is null or customerName = '')
            </when>
        </choose>
        <choose>
            <when test="erpSystemCode != null and erpSystemCode != '' and erpSystemCode != 'ALL' ">
                and cqt.erpSystemCode = #{erpSystemCode}
            </when>
            <when test="erpSystemCode == null  ">
                and (cqt.erpSystemCode is null or cqt.erpSystemCode = '')
            </when>
        </choose>
        <choose>
            <when test="productCode != null and productCode != '' and productCode != 'ALL' ">
                and productName = #{productCode}
            </when>
        </choose>
        <choose>
            <when test="emergency == '紧急'.toString() or emergency == '緊急'.toString()">
                and emergency = 1
            </when>
            <when test="emergency == '不紧急'.toString() or emergency == '不緊急'.toString()">
                and (emergency = 0 )
            </when>
        </choose>
        <choose>
            <when test="personalCase== '个案'.toString() or personalCase == '個案'.toString()">
                and isPersonalCase= 'Y'
            </when>
            <when test="personalCase== '案件'.toString() or personalCase == '案件'.toString()">
                and (isPersonalCase= 'N' or isPersonalCase is null or isPersonalCase= ' ')
            </when>
        </choose>
        <choose>
            <when test="username != null and username != '' and username != 'ALL' ">
                and username = #{username}
            </when>
            <when test="username == null ">
                and (username is null or username = '')
            </when>
        </choose>
        <choose>
            <when test="nowHandlerName != null and nowHandlerName != '' and nowHandlerName != 'ALL' ">
                and nowHandlerName= #{nowHandlerName}
            </when>
            <when test="nowHandlerName == null ">
                and (nowHandlerName is null or nowHandlerName='')
            </when>
        </choose>
        <choose>
            <when test="processDay != null and processDay != '' and processDay != 'ALL' ">
                and processDay = #{processDay}
            </when>
        </choose>
        <choose>
            <when test="mainChargeName != null and mainChargeName != '' and mainChargeName != 'ALL' ">
                and mainChargeName = #{mainChargeName}
            </when>
            <when test="mainChargeName == null ">
                and (mainChargeName is null or mainChargeName='')
            </when>
        </choose>
        GROUP BY cqt.issueId
        )cqt
        )tb1
        ORDER BY SUM DESC
    </select>

    <select id="getTempProductCode" parameterType="java.lang.String" resultType="com.digiwin.escloud.issueservice.t.model.cases.CasesAnalyse">
        SELECT ta1.productName,ta1.productName AS 'column',
        type_cnt as 'sum',
        concat(cast(type_cnt /total_cnt *100 AS DECIMAL(18,1)),'%') AS perc,hour,
        concat(cast(hour /total_hour *100 AS DECIMAL(18,1)),'%') AS hourPerc,
        concat(ROUND(C/type_cnt*100, 1),'%') AS 'oneDayReplyRank',
        concat(ROUND(D/type_cnt*100, 1),'%') AS 'threeDayReplyRank',
        concat(ROUND(E/type_cnt*100, 1),'%') AS 'allReplyRank',
        C 'oneDayReplySum',D 'threeDayReplySum',E 'allReplySum'
        FROM (  SELECT COUNT(DISTINCT cqt.issueId) AS type_cnt,
        SUM(ProcessHours) hour,productName
                FROM (SELECT cqt.issueId,productName,CASE WHEN IssueClassificationDesc = '客制需求' AND cs.submitway = 'HD187' THEN estimated_work_hours ELSE  SUM(ProcessHours) END ProcessHours
        FROM cases_query_tmp cqt
        LEFT JOIN issue_progress p ON cqt.issueId=p.issueId
        LEFT JOIN ( select cs.IssueId,SubmitWay,estimated_work_hours from issue cs left join issue_casedetail d on cs.issueId=d.issueId
        )cs ON cqt.issueId=cs.issueId
        where query_id=#{queryId}
        <choose>
            <when test="flag == '已结案'.toString() or flag == '已結案'.toString()">
                AND cqt.issueStatus in ('Y','P','7','8','10')
            </when>
            <when test="flag == '未结案'.toString() or flag == '未結案'.toString()">
                AND cqt.issueStatus in ('N','2','3','4','11','12','22')
            </when>
        </choose>
        <choose>
            <when test="issueClassificationDesc  == '客制需求(187)'.toString() ">
                and issueClassificationDesc ='客制需求' and (cs.submitway = 'HD187' or cs.submitway like 'EXCEL%')
            </when>
            <when test="issueClassificationDesc  == '客制需求(服务云)'.toString() ">
                and issueClassificationDesc ='客制需求' and cs.SubmitWay = 'INNER'
            </when>
            <when test="issueClassificationDesc != null and issueClassificationDesc != '' and issueClassificationDesc != 'ALL' and issueClassificationDesc != '客制需求(187)'.toString()
                        and issueClassificationDesc != '客制需求(服务云)'.toString() ">
                and issueClassificationDesc = #{issueClassificationDesc}
            </when>
            <when test="issueClassificationDesc  == null ">
                and (issueClassificationDesc is null or issueClassificationDesc = '' )
            </when>
        </choose>
        <choose>
            <when test="customerName != null and customerName != '' and customerName != 'ALL' ">
                and customerName = #{customerName}
            </when>
            <when test="customerName == null ">
                and (customerName is null or customerName = '')
            </when>
        </choose>
        <choose>
            <when test="erpSystemCode != null and erpSystemCode != '' and erpSystemCode != 'ALL' ">
                and cqt.erpSystemCode = #{erpSystemCode}
            </when>
            <when test="erpSystemCode == null  ">
                and (cqt.erpSystemCode  is null or cqt.erpSystemCode  = '')
            </when>
        </choose>
        <choose>
            <when test="productCode != null and productCode != '' and productCode != 'ALL' ">
                and productName = #{productCode}
            </when>
        </choose>
        <choose>
            <when test="emergency == '紧急'.toString() or emergency == '緊急'.toString()">
                and emergency = 1
            </when>
            <when test="emergency == '不紧急'.toString() or emergency == '不緊急'.toString()">
                and (emergency = 0 )
            </when>
        </choose>
        <choose>
            <when test="personalCase== '个案'.toString() or personalCase == '個案'.toString()">
                and isPersonalCase= 'Y'
            </when>
            <when test="personalCase== '案件'.toString() or personalCase == '案件'.toString()">
                and (isPersonalCase= 'N' or isPersonalCase is null or isPersonalCase= ' ')
            </when>
        </choose>
        <choose>
            <when test="username != null and username != '' and username != 'ALL' ">
                and username = #{username}
            </when>
            <when test="username == null ">
                and (username is null or username = '')
            </when>
        </choose>
        <choose>
            <when test="nowHandlerName != null and nowHandlerName != '' and nowHandlerName != 'ALL' ">
                and nowHandlerName= #{nowHandlerName}
            </when>
            <when test="nowHandlerName == null ">
                and (nowHandlerName is null or nowHandlerName='')
            </when>
        </choose>
        <choose>
            <when test="processDay != null and processDay != '' and processDay != 'ALL' ">
                and processDay = #{processDay}
            </when>
        </choose>
        <choose>
            <when test="mainChargeName != null and mainChargeName != '' and mainChargeName != 'ALL' ">
                and mainChargeName = #{mainChargeName}
            </when>
            <when test="mainChargeName == null ">
                and (mainChargeName is null or mainChargeName='')
            </when>
        </choose>
        GROUP BY cqt.issueId
        )cqt
        GROUP BY productName
        ORDER BY type_cnt DESC) ta1
        LEFT JOIN (  SELECT COUNT(C) C,COUNT(D) D,COUNT(E) E,productName
        FROM (  SELECT DISTINCT cqt.issueId,
        CASE WHEN  (reply_min <![CDATA[ < ]]> 1440 AND reply_min <![CDATA[ > ]]> 0 ) OR (reply_min = 0 AND IssueStatus IN (7,8,10,11) )  THEN 'C' END C,
        CASE WHEN  (reply_min <![CDATA[ < ]]> 4320 AND reply_min <![CDATA[ > ]]> 0 ) OR (reply_min = 0 AND IssueStatus IN (7,8,10,11) ) THEN 'D' END D,
        CASE WHEN  (reply_min <![CDATA[ > ]]> 0  or  IssueStatus IN (7,8,10,11) ) THEN 'E' END E,productName
        FROM cases_query_tmp cqt
        LEFT JOIN issue_casedetail d ON cqt.issueId = d.issueId
        LEFT JOIN ( select cs.IssueId,SubmitWay from issue cs)cs ON cqt.issueId=cs.issueId
        where query_id=#{queryId}
        <choose>
            <when test="flag == '已结案'.toString() or flag == '已結案'.toString()">
                AND cqt.issueStatus in ('Y','P','7','8','10')
            </when>
            <when test="flag == '未结案'.toString() or flag == '未結案'.toString()">
                AND cqt.issueStatus in ('N','2','3','4','11','12','22')
            </when>
        </choose>
        <choose>
            <when test="issueClassificationDesc  == '客制需求(187)'.toString() ">
                and issueClassificationDesc ='客制需求' and (cs.submitway = 'HD187' or cs.submitway like 'EXCEL%')
            </when>
            <when test="issueClassificationDesc  == '客制需求(服务云)'.toString() ">
                and issueClassificationDesc ='客制需求' and cs.SubmitWay = 'INNER'
            </when>
            <when test="issueClassificationDesc != null and issueClassificationDesc != '' and issueClassificationDesc != 'ALL' and issueClassificationDesc != '客制需求(187)'.toString()
                        and issueClassificationDesc != '客制需求(服务云)'.toString() ">
                and issueClassificationDesc = #{issueClassificationDesc}
            </when>
            <when test="issueClassificationDesc  == null ">
                and (issueClassificationDesc is null or issueClassificationDesc = '' )
            </when>
        </choose>
        <choose>
            <when test="customerName != null and customerName != '' and customerName != 'ALL' ">
                and customerName = #{customerName}
            </when>
            <when test="customerName == null ">
                and (customerName is null or customerName = '')
            </when>
        </choose>
        <choose>
            <when test="erpSystemCode != null and erpSystemCode != '' and erpSystemCode != 'ALL' ">
                and cqt.erpSystemCode = #{erpSystemCode}
            </when>
            <when test="erpSystemCode == null  ">
                and (cqt.erpSystemCode  is null or cqt.erpSystemCode  = '')
            </when>
        </choose>
        <choose>
            <when test="productCode != null and productCode != '' and productCode != 'ALL' ">
                and productName = #{productCode}
            </when>
        </choose>
        <choose>
            <when test="emergency == '紧急'.toString() or emergency == '緊急'.toString()">
                and emergency = 1
            </when>
            <when test="emergency == '不紧急'.toString() or emergency == '不緊急'.toString()">
                and (emergency = 0 )
            </when>
        </choose>
        <choose>
            <when test="personalCase== '个案'.toString() or personalCase == '個案'.toString()">
                and isPersonalCase= 'Y'
            </when>
            <when test="personalCase== '案件'.toString() or personalCase == '案件'.toString()">
                and (isPersonalCase= 'N' or isPersonalCase is null or isPersonalCase= ' ')
            </when>
        </choose>
        <choose>
            <when test="username != null and username != '' and username != 'ALL' ">
                and username = #{username}
            </when>
            <when test="username == null ">
                and (username is null or username = '')
            </when>
        </choose>
        <choose>
            <when test="nowHandlerName != null and nowHandlerName != '' and nowHandlerName != 'ALL' ">
                and nowHandlerName= #{nowHandlerName}
            </when>
            <when test="nowHandlerName == null ">
                and (nowHandlerName is null or nowHandlerName='')
            </when>
        </choose>
        <choose>
            <when test="processDay != null and processDay != '' and processDay != 'ALL' ">
                and processDay = #{processDay}
            </when>
        </choose>
        <choose>
            <when test="mainChargeName != null and mainChargeName != '' and mainChargeName != 'ALL' ">
                and mainChargeName = #{mainChargeName}
            </when>
            <when test="mainChargeName == null ">
                and (mainChargeName is null or mainChargeName='')
            </when>
        </choose>
        GROUP BY cqt.issueId
        )tc1
        GROUP BY productName )td1 ON ta1.productName = td1.productName,
        ( SELECT COUNT(DISTINCT cqt.issueId) AS total_cnt,SUM(ProcessHours) total_hour
        from ( select cqt.issueId,CASE WHEN IssueClassificationDesc = '客制需求' AND cs.submitway = 'HD187' THEN
        estimated_work_hours ELSE SUM(ProcessHours) END ProcessHours
        FROM cases_query_tmp cqt
        LEFT JOIN issue_progress p ON cqt.issueId=p.issueId
        LEFT JOIN ( select cs.IssueId,SubmitWay,estimated_work_hours from issue cs left join issue_casedetail d on
        cs.issueId=d.issueId)cs ON
        cqt.issueId=cs.issueId
        where query_id=#{queryId}
        <choose>
            <when test="flag == '已结案'.toString() or flag == '已結案'.toString()">
                AND cqt.issueStatus in ('Y','P','7','8','10')
            </when>
            <when test="flag == '未结案'.toString() or flag == '未結案'.toString()">
                AND cqt.issueStatus in ('N','2','3','4','11','12','22')
            </when>
        </choose>
        <choose>
            <when test="issueClassificationDesc  == '客制需求(187)'.toString() ">
                and issueClassificationDesc ='客制需求' and (cs.submitway = 'HD187' or cs.submitway like 'EXCEL%')
            </when>
            <when test="issueClassificationDesc  == '客制需求(服务云)'.toString() ">
                and issueClassificationDesc ='客制需求' and cs.SubmitWay = 'INNER'
            </when>
            <when test="issueClassificationDesc != null and issueClassificationDesc != '' and issueClassificationDesc != 'ALL' and issueClassificationDesc != '客制需求(187)'.toString()
                        and issueClassificationDesc != '客制需求(服务云)'.toString()  ">
                and issueClassificationDesc = #{issueClassificationDesc}
            </when>
            <when test="issueClassificationDesc  == null ">
                and (issueClassificationDesc is null or issueClassificationDesc = '' )
            </when>
        </choose>
        <choose>
            <when test="customerName != null and customerName != '' and customerName != 'ALL' ">
                and customerName = #{customerName}
            </when>
            <when test="customerName == null ">
                and (customerName is null or customerName = '')
            </when>
        </choose>
        <choose>
            <when test="erpSystemCode != null and erpSystemCode != '' and erpSystemCode != 'ALL' ">
                and cqt.erpSystemCode = #{erpSystemCode}
            </when>
            <when test="erpSystemCode == null  ">
                and (cqt.erpSystemCode is null or cqt.erpSystemCode = '')
            </when>
        </choose>
        <choose>
            <when test="productCode != null and productCode != '' and productCode != 'ALL' ">
                and productName = #{productCode}
            </when>
        </choose>
        <choose>
            <when test="emergency == '紧急'.toString() or emergency == '緊急'.toString()">
                and emergency = 1
            </when>
            <when test="emergency == '不紧急'.toString() or emergency == '不緊急'.toString()">
                and (emergency = 0 )
            </when>
        </choose>
        <choose>
            <when test="personalCase== '个案'.toString() or personalCase == '個案'.toString()">
                and isPersonalCase= 'Y'
            </when>
            <when test="personalCase== '案件'.toString() or personalCase == '案件'.toString()">
                and (isPersonalCase= 'N' or isPersonalCase is null or isPersonalCase= ' ')
            </when>
        </choose>
        <choose>
            <when test="username != null and username != '' and username != 'ALL' ">
                and username = #{username}
            </when>
            <when test="username == null ">
                and (username is null or username = '')
            </when>
        </choose>
        <choose>
            <when test="nowHandlerName != null and nowHandlerName != '' and nowHandlerName != 'ALL' ">
                and nowHandlerName= #{nowHandlerName}
            </when>
            <when test="nowHandlerName == null ">
                and (nowHandlerName is null or nowHandlerName='')
            </when>
        </choose>
        <choose>
            <when test="processDay != null and processDay != '' and processDay != 'ALL' ">
                and processDay = #{processDay}
            </when>
        </choose>
        <choose>
            <when test="mainChargeName != null and mainChargeName != '' and mainChargeName != 'ALL' ">
                and mainChargeName = #{mainChargeName}
            </when>
            <when test="mainChargeName == null ">
                and (mainChargeName is null or mainChargeName='')
            </when>
        </choose>
        GROUP BY cqt.issueId
        )cqt
        )tb1
        ORDER BY SUM DESC
    </select>

    <select id="getTempEmergency" parameterType="java.lang.String" resultType="com.digiwin.escloud.issueservice.t.model.cases.CasesAnalyse">
        SELECT ta1.emerge as 'emergency',ta1.emerge as 'column',
        type_cnt as 'sum',
        concat(cast(type_cnt /total_cnt *100 AS DECIMAL(18,1)),'%') AS perc,hour,
        concat(cast(hour /total_hour *100 AS DECIMAL(18,1)),'%') AS hourPerc,
        concat(ROUND(C/type_cnt*100, 1),'%') AS 'oneDayReplyRank',
        concat(ROUND(D/type_cnt*100, 1),'%') AS 'threeDayReplyRank',
        concat(ROUND(E/type_cnt*100, 1),'%') AS 'allReplyRank',
        C 'oneDayReplySum',D 'threeDayReplySum',E 'allReplySum'
        FROM (  SELECT COUNT(DISTINCT cqt.issueId) AS type_cnt,
        SUM(ProcessHours) hour,
        emerge
        FROM (SELECT cqt.issueId,CASE WHEN IssueClassificationDesc = '客制需求' AND cs.submitway = 'HD187' THEN estimated_work_hours ELSE  SUM(ProcessHours) END ProcessHours,
        (case
        WHEN ( emergency = '1' and #{language}='zh-CN' ) THEN '紧急'
        WHEN ( emergency = '1' and #{language}='zh-TW' ) THEN '緊急'
        WHEN ( emergency = '0' and #{language}='zh-CN' ) THEN '不紧急'
        WHEN ( emergency = '0' and #{language}='zh-TW' ) THEN '不緊急'
        ELSE '不紧急'
        END
        ) emerge
        FROM cases_query_tmp cqt
        LEFT JOIN issue_progress p ON cqt.issueId=p.issueId
        LEFT JOIN ( select cs.IssueId,SubmitWay,estimated_work_hours from issue cs left join issue_casedetail d on cs.issueId=d.issueId
        )cs ON cqt.issueId=cs.issueId
        where query_id=#{queryId}
        <choose>
            <when test="flag == '已结案'.toString() or flag == '已結案'.toString()">
                AND cqt.issueStatus in ('Y','P','7','8','10')
            </when>
            <when test="flag == '未结案'.toString() or flag == '未結案'.toString()">
                AND cqt.issueStatus in ('N','2','3','4','11','12','22')
            </when>
        </choose>
        <choose>
            <when test="issueClassificationDesc  == '客制需求(187)'.toString() ">
                and issueClassificationDesc ='客制需求' and (cs.submitway = 'HD187' or cs.submitway like 'EXCEL%')
            </when>
            <when test="issueClassificationDesc  == '客制需求(服务云)'.toString() ">
                and issueClassificationDesc ='客制需求' and cs.SubmitWay = 'INNER'
            </when>
            <when test="issueClassificationDesc != null and issueClassificationDesc != '' and issueClassificationDesc != 'ALL' and issueClassificationDesc != '客制需求(187)'.toString()
                        and issueClassificationDesc != '客制需求(服务云)'.toString() ">
                and issueClassificationDesc = #{issueClassificationDesc}
            </when>
            <when test="issueClassificationDesc  == null ">
                and (issueClassificationDesc is null or issueClassificationDesc = '' )
            </when>
        </choose>
        <choose>
            <when test="customerName != null and customerName != '' and customerName != 'ALL' ">
                and customerName = #{customerName}
            </when>
            <when test="customerName == null ">
                and (customerName is null or customerName = '')
            </when>
        </choose>
        <choose>
            <when test="erpSystemCode != null and erpSystemCode != '' and erpSystemCode != 'ALL' ">
                and cqt.erpSystemCode = #{erpSystemCode}
            </when>
            <when test="erpSystemCode == null  ">
                and (cqt.erpSystemCode  is null or cqt.erpSystemCode  = '')
            </when>
        </choose>
        <choose>
            <when test="productCode != null and productCode != '' and productCode != 'ALL' ">
                and productName = #{productCode}
            </when>
        </choose>
        <choose>
            <when test="emergency == '紧急'.toString() or emergency == '緊急'.toString()">
                and emergency = 1
            </when>
            <when test="emergency == '不紧急'.toString() or emergency == '不緊急'.toString()">
                and (emergency = 0 )
            </when>
        </choose>
        <choose>
            <when test="personalCase== '个案'.toString() or personalCase == '個案'.toString()">
                and isPersonalCase= 'Y'
            </when>
            <when test="personalCase== '案件'.toString() or personalCase == '案件'.toString()">
                and (isPersonalCase= 'N' or isPersonalCase is null or isPersonalCase= ' ')
            </when>
        </choose>
        <choose>
            <when test="username != null and username != '' and username != 'ALL' ">
                and username = #{username}
            </when>
            <when test="username == null ">
                and (username is null or username = '')
            </when>
        </choose>
        <choose>
            <when test="nowHandlerName != null and nowHandlerName != '' and nowHandlerName != 'ALL' ">
                and nowHandlerName= #{nowHandlerName}
            </when>
            <when test="nowHandlerName == null ">
                and (nowHandlerName is null or nowHandlerName='')
            </when>
        </choose>
        <choose>
            <when test="processDay != null and processDay != '' and processDay != 'ALL' ">
                and processDay = #{processDay}
            </when>
        </choose>
        <choose>
            <when test="mainChargeName != null and mainChargeName != '' and mainChargeName != 'ALL' ">
                and mainChargeName = #{mainChargeName}
            </when>
            <when test="mainChargeName == null ">
                and (mainChargeName is null or mainChargeName='')
            </when>
        </choose>
        GROUP BY cqt.issueId
        )cqt
        GROUP BY emerge
        ORDER BY type_cnt DESC) ta1
        LEFT JOIN (  SELECT COUNT(C) C,COUNT(D) D,COUNT(E) E,emerge
        FROM (  SELECT DISTINCT cqt.issueId,
        CASE WHEN  (reply_min <![CDATA[ < ]]> 1440 AND reply_min <![CDATA[ > ]]> 0 ) OR (reply_min = 0 AND IssueStatus IN (7,8,10,11) )  THEN 'C' END C,
        CASE WHEN  (reply_min <![CDATA[ < ]]> 4320 AND reply_min <![CDATA[ > ]]> 0 ) OR (reply_min = 0 AND IssueStatus IN (7,8,10,11) ) THEN 'D' END D,
        CASE WHEN  (reply_min <![CDATA[ > ]]> 0  or  IssueStatus IN (7,8,10,11) ) THEN 'E' END E,
        (case
        WHEN ( emergency = '1' and #{language}='zh-CN' ) THEN '紧急'
        WHEN ( emergency = '1' and #{language}='zh-TW' ) THEN '緊急'
        WHEN ( emergency = '0' and #{language}='zh-CN' ) THEN '不紧急'
        WHEN ( emergency = '0' and #{language}='zh-TW' ) THEN '不緊急'
        ELSE '不紧急'
        END
        ) emerge
        FROM cases_query_tmp cqt
        LEFT JOIN issue_casedetail d ON cqt.issueId = d.issueId
        LEFT JOIN ( select cs.IssueId,SubmitWay from issue cs)cs ON cqt.issueId=cs.issueId
        where query_id=#{queryId}
        <choose>
            <when test="flag == '已结案'.toString() or flag == '已結案'.toString()">
                AND cqt.issueStatus in ('Y','P','7','8','10')
            </when>
            <when test="flag == '未结案'.toString() or flag == '未結案'.toString()">
                AND cqt.issueStatus in ('N','2','3','4','11','12','22')
            </when>
        </choose>
        <choose>
            <when test="issueClassificationDesc  == '客制需求(187)'.toString() ">
                and issueClassificationDesc ='客制需求' and (cs.submitway = 'HD187' or cs.submitway like 'EXCEL%')
            </when>
            <when test="issueClassificationDesc  == '客制需求(服务云)'.toString() ">
                and issueClassificationDesc ='客制需求' and cs.SubmitWay = 'INNER'
            </when>
            <when test="issueClassificationDesc != null and issueClassificationDesc != '' and issueClassificationDesc != 'ALL' and issueClassificationDesc != '客制需求(187)'.toString()
                        and issueClassificationDesc != '客制需求(服务云)'.toString() ">
                and issueClassificationDesc = #{issueClassificationDesc}
            </when>
            <when test="issueClassificationDesc  == null ">
                and (issueClassificationDesc is null or issueClassificationDesc = '' )
            </when>
        </choose>
        <choose>
            <when test="customerName != null and customerName != '' and customerName != 'ALL' ">
                and customerName = #{customerName}
            </when>
            <when test="customerName == null ">
                and (customerName is null or customerName = '')
            </when>
        </choose>
        <choose>
            <when test="erpSystemCode != null and erpSystemCode != '' and erpSystemCode != 'ALL' ">
                and cqt.erpSystemCode = #{erpSystemCode}
            </when>
            <when test="erpSystemCode == null  ">
                and (cqt.erpSystemCode  is null or cqt.erpSystemCode  = '')
            </when>
        </choose>
        <choose>
            <when test="productCode != null and productCode != '' and productCode != 'ALL' ">
                and productName = #{productCode}
            </when>
        </choose>
        <choose>
            <when test="emergency == '紧急'.toString() or emergency == '緊急'.toString()">
                and emergency = 1
            </when>
            <when test="emergency == '不紧急'.toString() or emergency == '不緊急'.toString()">
                and (emergency = 0 )
            </when>
        </choose>
        <choose>
            <when test="personalCase== '个案'.toString() or personalCase == '個案'.toString()">
                and isPersonalCase= 'Y'
            </when>
            <when test="personalCase== '案件'.toString() or personalCase == '案件'.toString()">
                and (isPersonalCase= 'N' or isPersonalCase is null or isPersonalCase= ' ')
            </when>
        </choose>
        <choose>
            <when test="username != null and username != '' and username != 'ALL' ">
                and username = #{username}
            </when>
            <when test="username == null ">
                and (username is null or username = '')
            </when>
        </choose>
        <choose>
            <when test="nowHandlerName != null and nowHandlerName != '' and nowHandlerName != 'ALL' ">
                and nowHandlerName= #{nowHandlerName}
            </when>
            <when test="nowHandlerName == null ">
                and (nowHandlerName is null or nowHandlerName='')
            </when>
        </choose>
        <choose>
            <when test="processDay != null and processDay != '' and processDay != 'ALL' ">
                and processDay = #{processDay}
            </when>
        </choose>
        <choose>
            <when test="mainChargeName != null and mainChargeName != '' and mainChargeName != 'ALL' ">
                and mainChargeName = #{mainChargeName}
            </when>
            <when test="mainChargeName == null ">
                and (mainChargeName is null or mainChargeName='')
            </when>
        </choose>
        GROUP BY cqt.issueId
        )tc1
        GROUP BY emerge )td1 ON ta1.emerge = td1.emerge,
        ( SELECT COUNT(DISTINCT cqt.issueId) AS total_cnt,SUM(ProcessHours) total_hour
        from ( select cqt.issueId,CASE WHEN IssueClassificationDesc = '客制需求' AND cs.submitway = 'HD187' THEN
        estimated_work_hours ELSE SUM(ProcessHours) END ProcessHours
        FROM cases_query_tmp cqt
        LEFT JOIN issue_progress p ON cqt.issueId=p.issueId
        LEFT JOIN ( select cs.IssueId,SubmitWay,estimated_work_hours from issue cs left join issue_casedetail d on
        cs.issueId=d.issueId )cs ON
        cqt.issueId=cs.issueId
        where query_id=#{queryId}
        <choose>
            <when test="flag == '已结案'.toString() or flag == '已結案'.toString()">
                AND cqt.issueStatus in ('Y','P','7','8','10')
            </when>
            <when test="flag == '未结案'.toString() or flag == '未結案'.toString()">
                AND cqt.issueStatus in ('N','2','3','4','11','12','22')
            </when>
        </choose>
        <choose>
            <when test="issueClassificationDesc  == '客制需求(187)'.toString() ">
                and issueClassificationDesc ='客制需求' and (cs.submitway = 'HD187' or cs.submitway like 'EXCEL%')
            </when>
            <when test="issueClassificationDesc  == '客制需求(服务云)'.toString() ">
                and issueClassificationDesc ='客制需求' and cs.SubmitWay = 'INNER'
            </when>
            <when test="issueClassificationDesc != null and issueClassificationDesc != '' and issueClassificationDesc != 'ALL' and issueClassificationDesc != '客制需求(187)'.toString()
                        and issueClassificationDesc != '客制需求(服务云)'.toString()  ">
                and issueClassificationDesc = #{issueClassificationDesc}
            </when>
            <when test="issueClassificationDesc  == null ">
                and (issueClassificationDesc is null or issueClassificationDesc = '' )
            </when>
        </choose>
        <choose>
            <when test="customerName != null and customerName != '' and customerName != 'ALL' ">
                and customerName = #{customerName}
            </when>
            <when test="customerName == null ">
                and (customerName is null or customerName = '')
            </when>
        </choose>
        <choose>
            <when test="erpSystemCode != null and erpSystemCode != '' and erpSystemCode != 'ALL' ">
                and cqt.erpSystemCode = #{erpSystemCode}
            </when>
            <when test="erpSystemCode == null  ">
                and (cqt.erpSystemCode is null or cqt.erpSystemCode = '')
            </when>
        </choose>
        <choose>
            <when test="productCode != null and productCode != '' and productCode != 'ALL' ">
                and productName = #{productCode}
            </when>
        </choose>
        <choose>
            <when test="emergency == '紧急'.toString() or emergency == '緊急'.toString()">
                and emergency = 1
            </when>
            <when test="emergency == '不紧急'.toString() or emergency == '不緊急'.toString()">
                and (emergency = 0 )
            </when>
        </choose>
        <choose>
            <when test="personalCase== '个案'.toString() or personalCase == '個案'.toString()">
                and isPersonalCase= 'Y'
            </when>
            <when test="personalCase== '案件'.toString() or personalCase == '案件'.toString()">
                and (isPersonalCase= 'N' or isPersonalCase is null or isPersonalCase= ' ')
            </when>
        </choose>
        <choose>
            <when test="username != null and username != '' and username != 'ALL' ">
                and username = #{username}
            </when>
            <when test="username == null ">
                and (username is null or username = '')
            </when>
        </choose>
        <choose>
            <when test="nowHandlerName != null and nowHandlerName != '' and nowHandlerName != 'ALL' ">
                and nowHandlerName= #{nowHandlerName}
            </when>
            <when test="nowHandlerName == null ">
                and (nowHandlerName is null or nowHandlerName='')
            </when>
        </choose>
        <choose>
            <when test="processDay != null and processDay != '' and processDay != 'ALL' ">
                and processDay = #{processDay}
            </when>
        </choose>
        <choose>
            <when test="mainChargeName != null and mainChargeName != '' and mainChargeName != 'ALL' ">
                and mainChargeName = #{mainChargeName}
            </when>
            <when test="mainChargeName == null ">
                and (mainChargeName is null or mainChargeName='')
            </when>
        </choose>
        GROUP BY cqt.issueId
        )cqt
        )tb1
        ORDER BY SUM DESC
    </select>

    <select id="getTempUsername" parameterType="java.lang.String" resultType="com.digiwin.escloud.issueservice.t.model.cases.CasesAnalyse">
        SELECT
        CASE WHEN ta1.username = '' THEN NULL ELSE ta1.username END username,
        CASE WHEN ta1.username = '' THEN NULL ELSE ta1.username END 'column',
        type_cnt as 'sum',
        concat(cast(type_cnt /total_cnt *100 AS DECIMAL(18,1)),'%') AS perc,hour,
        concat(cast(hour /total_hour *100 AS DECIMAL(18,1)),'%') AS hourPerc,
        concat(ROUND(C/type_cnt*100, 1),'%') AS 'oneDayReplyRank',
        concat(ROUND(D/type_cnt*100, 1),'%') AS 'threeDayReplyRank',
        concat(ROUND(E/type_cnt*100, 1),'%') AS 'allReplyRank',
        C 'oneDayReplySum',D 'threeDayReplySum',E 'allReplySum'
        FROM (  SELECT COUNT(DISTINCT cqt.issueId) AS type_cnt,
        SUM(ProcessHours) hour,username
        FROM (SELECT cqt.issueId,CASE WHEN IssueClassificationDesc = '客制需求' AND cs.submitway = 'HD187' THEN estimated_work_hours ELSE  SUM(ProcessHours) END ProcessHours,
        CASE  WHEN username is NULL THEN '' ELSE username END username
        FROM cases_query_tmp cqt
        LEFT JOIN issue_progress p ON cqt.issueId=p.issueId
        LEFT JOIN ( select cs.IssueId,SubmitWay,estimated_work_hours from issue cs left join issue_casedetail d on cs.issueId=d.issueId
        )cs ON cqt.issueId=cs.issueId
        where query_id=#{queryId}
        <choose>
            <when test="flag == '已结案'.toString() or flag == '已結案'.toString()">
                AND cqt.issueStatus in ('Y','P','7','8','10')
            </when>
            <when test="flag == '未结案'.toString() or flag == '未結案'.toString()">
                AND cqt.issueStatus in ('N','2','3','4','11','12','22')
            </when>
        </choose>
        <choose>
            <when test="issueClassificationDesc  == '客制需求(187)'.toString() ">
                and issueClassificationDesc ='客制需求' and (cs.submitway = 'HD187' or cs.submitway like 'EXCEL%')
            </when>
            <when test="issueClassificationDesc  == '客制需求(服务云)'.toString() ">
                and issueClassificationDesc ='客制需求' and cs.SubmitWay = 'INNER'
            </when>
            <when test="issueClassificationDesc != null and issueClassificationDesc != '' and issueClassificationDesc != 'ALL' and issueClassificationDesc != '客制需求(187)'.toString()
                        and issueClassificationDesc != '客制需求(服务云)'.toString() ">
                and issueClassificationDesc = #{issueClassificationDesc}
            </when>
            <when test="issueClassificationDesc  == null ">
                and (issueClassificationDesc is null or issueClassificationDesc = '' )
            </when>
        </choose>
        <choose>
            <when test="customerName != null and customerName != '' and customerName != 'ALL' ">
                and customerName = #{customerName}
            </when>
            <when test="customerName == null ">
                and (customerName is null or customerName = '')
            </when>
        </choose>
        <choose>
            <when test="erpSystemCode != null and erpSystemCode != '' and erpSystemCode != 'ALL' ">
                and cqt.erpSystemCode = #{erpSystemCode}
            </when>
            <when test="erpSystemCode == null  ">
                and (cqt.erpSystemCode  is null or cqt.erpSystemCode  = '')
            </when>
        </choose>
        <choose>
            <when test="productCode != null and productCode != '' and productCode != 'ALL' ">
                and productName = #{productCode}
            </when>
        </choose>
        <choose>
            <when test="emergency == '紧急'.toString() or emergency == '緊急'.toString()">
                and emergency = 1
            </when>
            <when test="emergency == '不紧急'.toString() or emergency == '不緊急'.toString()">
                and (emergency = 0 )
            </when>
        </choose>
        <choose>
            <when test="personalCase== '个案'.toString() or personalCase == '個案'.toString()">
                and isPersonalCase= 'Y'
            </when>
            <when test="personalCase== '案件'.toString() or personalCase == '案件'.toString()">
                and (isPersonalCase= 'N' or isPersonalCase is null or isPersonalCase= ' ')
            </when>
        </choose>
        <choose>
            <when test="username != null and username != '' and username != 'ALL' ">
                and username = #{username}
            </when>
            <when test="username == null ">
                and (username is null or username = '')
            </when>
        </choose>
        <choose>
            <when test="nowHandlerName != null and nowHandlerName != '' and nowHandlerName != 'ALL' ">
                and nowHandlerName= #{nowHandlerName}
            </when>
            <when test="nowHandlerName == null ">
                and (nowHandlerName is null or nowHandlerName='')
            </when>
        </choose>
        <choose>
            <when test="processDay != null and processDay != '' and processDay != 'ALL' ">
                and processDay = #{processDay}
            </when>
        </choose>
        <choose>
            <when test="mainChargeName != null and mainChargeName != '' and mainChargeName != 'ALL' ">
                and mainChargeName = #{mainChargeName}
            </when>
            <when test="mainChargeName == null ">
                and (mainChargeName is null or mainChargeName='')
            </when>
        </choose>
        GROUP BY cqt.issueId
        )cqt
        GROUP BY username
        ORDER BY type_cnt DESC) ta1
        LEFT JOIN (  SELECT COUNT(C) C,COUNT(D) D,COUNT(E) E,username
        FROM (  SELECT DISTINCT cqt.issueId,
        CASE WHEN  (reply_min <![CDATA[ < ]]> 1440 AND reply_min <![CDATA[ > ]]> 0 ) OR (reply_min = 0 AND IssueStatus IN (7,8,10,11) )  THEN 'C' END C,
        CASE WHEN  (reply_min <![CDATA[ < ]]> 4320 AND reply_min <![CDATA[ > ]]> 0 ) OR (reply_min = 0 AND IssueStatus IN (7,8,10,11) ) THEN 'D' END D,
        CASE WHEN  (reply_min <![CDATA[ > ]]> 0  or  IssueStatus IN (7,8,10,11) ) THEN 'E' END E,
        CASE  WHEN username is NULL THEN '' ELSE username END username
        FROM cases_query_tmp cqt
        LEFT JOIN issue_casedetail d ON cqt.issueId = d.issueId
        LEFT JOIN ( select cs.IssueId,SubmitWay from issue cs)cs ON cqt.issueId=cs.issueId
        where query_id=#{queryId}
        <choose>
            <when test="flag == '已结案'.toString() or flag == '已結案'.toString()">
                AND cqt.issueStatus in ('Y','P','7','8','10')
            </when>
            <when test="flag == '未结案'.toString() or flag == '未結案'.toString()">
                AND cqt.issueStatus in ('N','2','3','4','11','12','22')
            </when>
        </choose>
        <choose>
            <when test="issueClassificationDesc  == '客制需求(187)'.toString() ">
                and issueClassificationDesc ='客制需求' and (cs.submitway = 'HD187' or cs.submitway like 'EXCEL%')
            </when>
            <when test="issueClassificationDesc  == '客制需求(服务云)'.toString() ">
                and issueClassificationDesc ='客制需求' and cs.SubmitWay = 'INNER'
            </when>
            <when test="issueClassificationDesc != null and issueClassificationDesc != '' and issueClassificationDesc != 'ALL' and issueClassificationDesc != '客制需求(187)'.toString()
                        and issueClassificationDesc != '客制需求(服务云)'.toString() ">
                and issueClassificationDesc = #{issueClassificationDesc}
            </when>
            <when test="issueClassificationDesc  == null ">
                and (issueClassificationDesc is null or issueClassificationDesc = '' )
            </when>
        </choose>
        <choose>
            <when test="customerName != null and customerName != '' and customerName != 'ALL' ">
                and customerName = #{customerName}
            </when>
            <when test="customerName == null ">
                and (customerName is null or customerName = '')
            </when>
        </choose>
        <choose>
            <when test="erpSystemCode != null and erpSystemCode != '' and erpSystemCode != 'ALL' ">
                and cqt.erpSystemCode = #{erpSystemCode}
            </when>
            <when test="erpSystemCode == null  ">
                and (cqt.erpSystemCode  is null or cqt.erpSystemCode  = '')
            </when>
        </choose>
        <choose>
            <when test="productCode != null and productCode != '' and productCode != 'ALL' ">
                and productName = #{productCode}
            </when>
        </choose>
        <choose>
            <when test="emergency == '紧急'.toString() or emergency == '緊急'.toString()">
                and emergency = 1
            </when>
            <when test="emergency == '不紧急'.toString() or emergency == '不緊急'.toString()">
                and (emergency = 0 )
            </when>
        </choose>
        <choose>
            <when test="personalCase== '个案'.toString() or personalCase == '個案'.toString()">
                and isPersonalCase= 'Y'
            </when>
            <when test="personalCase== '案件'.toString() or personalCase == '案件'.toString()">
                and (isPersonalCase= 'N' or isPersonalCase is null or isPersonalCase= ' ')
            </when>
        </choose>
        <choose>
            <when test="username != null and username != '' and username != 'ALL' ">
                and username = #{username}
            </when>
            <when test="username == null ">
                and (username is null or username = '')
            </when>
        </choose>
        <choose>
            <when test="nowHandlerName != null and nowHandlerName != '' and nowHandlerName != 'ALL' ">
                and nowHandlerName= #{nowHandlerName}
            </when>
            <when test="nowHandlerName == null ">
                and (nowHandlerName is null or nowHandlerName='')
            </when>
        </choose>
        <choose>
            <when test="processDay != null and processDay != '' and processDay != 'ALL' ">
                and processDay = #{processDay}
            </when>
        </choose>
        <choose>
            <when test="mainChargeName != null and mainChargeName != '' and mainChargeName != 'ALL' ">
                and mainChargeName = #{mainChargeName}
            </when>
            <when test="mainChargeName == null ">
                and (mainChargeName is null or mainChargeName='')
            </when>
        </choose>
        GROUP BY cqt.issueId
        )tc1
        GROUP BY username )td1 ON ta1.username = td1.username,
        ( SELECT COUNT(DISTINCT cqt.issueId) AS total_cnt,SUM(ProcessHours) total_hour
        from ( select cqt.issueId,CASE WHEN IssueClassificationDesc = '客制需求' AND cs.submitway = 'HD187' THEN
        estimated_work_hours ELSE SUM(ProcessHours) END ProcessHours
        FROM cases_query_tmp cqt
        LEFT JOIN issue_progress p ON cqt.issueId=p.issueId
        LEFT JOIN ( select cs.IssueId,SubmitWay,estimated_work_hours from issue cs left join issue_casedetail d on
        cs.issueId=d.issueId)cs ON
        cqt.issueId=cs.issueId
        where query_id=#{queryId}
        <choose>
            <when test="flag == '已结案'.toString() or flag == '已結案'.toString()">
                AND cqt.issueStatus in ('Y','P','7','8','10')
            </when>
            <when test="flag == '未结案'.toString() or flag == '未結案'.toString()">
                AND cqt.issueStatus in ('N','2','3','4','11','12','22')
            </when>
        </choose>
        <choose>
            <when test="issueClassificationDesc  == '客制需求(187)'.toString() ">
                and issueClassificationDesc ='客制需求' and (cs.submitway = 'HD187' or cs.submitway like 'EXCEL%')
            </when>
            <when test="issueClassificationDesc  == '客制需求(服务云)'.toString() ">
                and issueClassificationDesc ='客制需求' and cs.SubmitWay = 'INNER'
            </when>
            <when test="issueClassificationDesc != null and issueClassificationDesc != '' and issueClassificationDesc != 'ALL' and issueClassificationDesc != '客制需求(187)'.toString()
                        and issueClassificationDesc != '客制需求(服务云)'.toString()  ">
                and issueClassificationDesc = #{issueClassificationDesc}
            </when>
            <when test="issueClassificationDesc  == null ">
                and (issueClassificationDesc is null or issueClassificationDesc = '' )
            </when>
        </choose>
        <choose>
            <when test="customerName != null and customerName != '' and customerName != 'ALL' ">
                and customerName = #{customerName}
            </when>
            <when test="customerName == null ">
                and (customerName is null or customerName = '')
            </when>
        </choose>
        <choose>
            <when test="erpSystemCode != null and erpSystemCode != '' and erpSystemCode != 'ALL' ">
                and cqt.erpSystemCode = #{erpSystemCode}
            </when>
            <when test="erpSystemCode == null  ">
                and (cqt.erpSystemCode is null or cqt.erpSystemCode = '')
            </when>
        </choose>
        <choose>
            <when test="productCode != null and productCode != '' and productCode != 'ALL' ">
                and productName = #{productCode}
            </when>
        </choose>
        <choose>
            <when test="emergency == '紧急'.toString() or emergency == '緊急'.toString()">
                and emergency = 1
            </when>
            <when test="emergency == '不紧急'.toString() or emergency == '不緊急'.toString()">
                and (emergency = 0 )
            </when>
        </choose>
        <choose>
            <when test="personalCase== '个案'.toString() or personalCase == '個案'.toString()">
                and isPersonalCase= 'Y'
            </when>
            <when test="personalCase== '案件'.toString() or personalCase == '案件'.toString()">
                and (isPersonalCase= 'N' or isPersonalCase is null or isPersonalCase= ' ')
            </when>
        </choose>
        <choose>
            <when test="username != null and username != '' and username != 'ALL' ">
                and username = #{username}
            </when>
            <when test="username == null ">
                and (username is null or username = '')
            </when>
        </choose>
        <choose>
            <when test="nowHandlerName != null and nowHandlerName != '' and nowHandlerName != 'ALL' ">
                and nowHandlerName= #{nowHandlerName}
            </when>
            <when test="nowHandlerName == null ">
                and (nowHandlerName is null or nowHandlerName='')
            </when>
        </choose>
        <choose>
            <when test="processDay != null and processDay != '' and processDay != 'ALL' ">
                and processDay = #{processDay}
            </when>
        </choose>
        <choose>
            <when test="mainChargeName != null and mainChargeName != '' and mainChargeName != 'ALL' ">
                and mainChargeName = #{mainChargeName}
            </when>
            <when test="mainChargeName == null ">
                and (mainChargeName is null or mainChargeName='')
            </when>
        </choose>
        GROUP BY cqt.issueId
        )cqt
        )tb1
        ORDER BY SUM DESC
    </select>

    <select id="getTempNowHandlerName" parameterType="java.lang.String" resultType="com.digiwin.escloud.issueservice.t.model.cases.CasesAnalyse">
        SELECT
        CASE WHEN ta1.nowHandlerName = '' THEN NULL ELSE ta1.nowHandlerName END username,
        CASE WHEN ta1.nowHandlerName = '' THEN NULL ELSE ta1.nowHandlerName END 'column',
        type_cnt as 'sum',
        concat(cast(type_cnt /total_cnt *100 AS DECIMAL(18,1)),'%') AS perc,hour,
        concat(cast(hour /total_hour *100 AS DECIMAL(18,1)),'%') AS hourPerc,
        concat(ROUND(C/type_cnt*100, 1),'%') AS 'oneDayReplyRank',
        concat(ROUND(D/type_cnt*100, 1),'%') AS 'threeDayReplyRank',
        concat(ROUND(E/type_cnt*100, 1),'%') AS 'allReplyRank',
        C 'oneDayReplySum',D 'threeDayReplySum',E 'allReplySum'
        FROM (  SELECT COUNT(DISTINCT cqt.issueId) AS type_cnt,
        SUM(ProcessHours) hour,
        nowHandlerName
        FROM (SELECT cqt.issueId,CASE WHEN IssueClassificationDesc = '客制需求' AND cs.submitway = 'HD187' THEN estimated_work_hours ELSE  SUM(ProcessHours) END ProcessHours,
        CASE  WHEN nowHandlerName is NULL THEN '' ELSE nowHandlerName END nowHandlerName
        FROM cases_query_tmp cqt
        LEFT JOIN issue_progress p ON cqt.issueId=p.issueId
        LEFT JOIN ( select cs.IssueId,SubmitWay,estimated_work_hours from issue cs left join issue_casedetail d on cs.issueId=d.issueId
        )cs ON cqt.issueId=cs.issueId
        where query_id=#{queryId}
        <choose>
            <when test="flag == '已结案'.toString() or flag == '已結案'.toString()">
                AND cqt.issueStatus in ('Y','P','7','8','10')
            </when>
            <when test="flag == '未结案'.toString() or flag == '未結案'.toString()">
                AND cqt.issueStatus in ('N','2','3','4','11','12','22')
            </when>
        </choose>
        <choose>
            <when test="issueClassificationDesc  == '客制需求(187)'.toString() ">
                and issueClassificationDesc ='客制需求' and (cs.submitway = 'HD187' or cs.submitway like 'EXCEL%')
            </when>
            <when test="issueClassificationDesc  == '客制需求(服务云)'.toString() ">
                and issueClassificationDesc ='客制需求' and cs.SubmitWay = 'INNER'
            </when>
            <when test="issueClassificationDesc != null and issueClassificationDesc != '' and issueClassificationDesc != 'ALL' and issueClassificationDesc != '客制需求(187)'.toString()
                        and issueClassificationDesc != '客制需求(服务云)'.toString() ">
                and issueClassificationDesc = #{issueClassificationDesc}
            </when>
            <when test="issueClassificationDesc  == null ">
                and (issueClassificationDesc is null or issueClassificationDesc = '' )
            </when>
        </choose>
        <choose>
            <when test="customerName != null and customerName != '' and customerName != 'ALL' ">
                and customerName = #{customerName}
            </when>
            <when test="customerName == null ">
                and (customerName is null or customerName = '')
            </when>
        </choose>
        <choose>
            <when test="erpSystemCode != null and erpSystemCode != '' and erpSystemCode != 'ALL' ">
                and cqt.erpSystemCode = #{erpSystemCode}
            </when>
            <when test="erpSystemCode == null  ">
                and (cqt.erpSystemCode  is null or cqt.erpSystemCode  = '')
            </when>
        </choose>
        <choose>
            <when test="productCode != null and productCode != '' and productCode != 'ALL' ">
                and productName = #{productCode}
            </when>
        </choose>
        <choose>
            <when test="emergency == '紧急'.toString() or emergency == '緊急'.toString()">
                and emergency = 1
            </when>
            <when test="emergency == '不紧急'.toString() or emergency == '不緊急'.toString()">
                and (emergency = 0 )
            </when>
        </choose>
        <choose>
            <when test="personalCase== '个案'.toString() or personalCase == '個案'.toString()">
                and isPersonalCase= 'Y'
            </when>
            <when test="personalCase== '案件'.toString() or personalCase == '案件'.toString()">
                and (isPersonalCase= 'N' or isPersonalCase is null or isPersonalCase= ' ')
            </when>
        </choose>
        <choose>
            <when test="username != null and username != '' and username != 'ALL' ">
                and username = #{username}
            </when>
            <when test="username == null ">
                and (username is null or username = '')
            </when>
        </choose>
        <choose>
            <when test="nowHandlerName != null and nowHandlerName != '' and nowHandlerName != 'ALL' ">
                and nowHandlerName= #{nowHandlerName}
            </when>
            <when test="nowHandlerName == null ">
                and (nowHandlerName is null or nowHandlerName='')
            </when>
        </choose>
        <choose>
            <when test="processDay != null and processDay != '' and processDay != 'ALL' ">
                and processDay = #{processDay}
            </when>
        </choose>
        <choose>
            <when test="mainChargeName != null and mainChargeName != '' and mainChargeName != 'ALL' ">
                and mainChargeName = #{mainChargeName}
            </when>
            <when test="mainChargeName == null ">
                and (mainChargeName is null or mainChargeName='')
            </when>
        </choose>
        GROUP BY cqt.issueId
        )cqt
        GROUP BY nowHandlerName
        ORDER BY type_cnt DESC) ta1
        LEFT JOIN (  SELECT COUNT(C) C,COUNT(D) D,COUNT(E) E,nowHandlerName
        FROM (  SELECT DISTINCT cqt.issueId,
        CASE WHEN  (reply_min <![CDATA[ < ]]> 1440 AND reply_min <![CDATA[ > ]]> 0 ) OR (reply_min = 0 AND IssueStatus IN (7,8,10,11) )  THEN 'C' END C,
        CASE WHEN  (reply_min <![CDATA[ < ]]> 4320 AND reply_min <![CDATA[ > ]]> 0 ) OR (reply_min = 0 AND IssueStatus IN (7,8,10,11) ) THEN 'D' END D,
        CASE WHEN  (reply_min <![CDATA[ > ]]> 0  or  IssueStatus IN (7,8,10,11) ) THEN 'E' END E,
        CASE  WHEN nowHandlerName is NULL THEN '' ELSE nowHandlerName END nowHandlerName
        FROM cases_query_tmp cqt
        LEFT JOIN issue_casedetail d ON cqt.issueId = d.issueId
        LEFT JOIN ( select cs.IssueId,SubmitWay from issue cs)cs ON cqt.issueId=cs.issueId
        where query_id=#{queryId}
        <choose>
            <when test="flag == '已结案'.toString() or flag == '已結案'.toString()">
                AND cqt.issueStatus in ('Y','P','7','8','10')
            </when>
            <when test="flag == '未结案'.toString() or flag == '未結案'.toString()">
                AND cqt.issueStatus in ('N','2','3','4','11','12','22')
            </when>
        </choose>
        <choose>
            <when test="issueClassificationDesc  == '客制需求(187)'.toString() ">
                and issueClassificationDesc ='客制需求' and (cs.submitway = 'HD187' or cs.submitway like 'EXCEL%')
            </when>
            <when test="issueClassificationDesc  == '客制需求(服务云)'.toString() ">
                and issueClassificationDesc ='客制需求' and cs.SubmitWay = 'INNER'
            </when>
            <when test="issueClassificationDesc != null and issueClassificationDesc != '' and issueClassificationDesc != 'ALL' and issueClassificationDesc != '客制需求(187)'.toString()
                        and issueClassificationDesc != '客制需求(服务云)'.toString() ">
                and issueClassificationDesc = #{issueClassificationDesc}
            </when>
            <when test="issueClassificationDesc  == null ">
                and (issueClassificationDesc is null or issueClassificationDesc = '' )
            </when>
        </choose>
        <choose>
            <when test="customerName != null and customerName != '' and customerName != 'ALL' ">
                and customerName = #{customerName}
            </when>
            <when test="customerName == null ">
                and (customerName is null or customerName = '')
            </when>
        </choose>
        <choose>
            <when test="erpSystemCode != null and erpSystemCode != '' and erpSystemCode != 'ALL' ">
                and cqt.erpSystemCode = #{erpSystemCode}
            </when>
            <when test="erpSystemCode == null  ">
                and (cqt.erpSystemCode  is null or cqt.erpSystemCode  = '')
            </when>
        </choose>
        <choose>
            <when test="productCode != null and productCode != '' and productCode != 'ALL' ">
                and productName = #{productCode}
            </when>
        </choose>
        <choose>
            <when test="emergency == '紧急'.toString() or emergency == '緊急'.toString()">
                and emergency = 1
            </when>
            <when test="emergency == '不紧急'.toString() or emergency == '不緊急'.toString()">
                and (emergency = 0 )
            </when>
        </choose>
        <choose>
            <when test="personalCase== '个案'.toString() or personalCase == '個案'.toString()">
                and isPersonalCase= 'Y'
            </when>
            <when test="personalCase== '案件'.toString() or personalCase == '案件'.toString()">
                and (isPersonalCase= 'N' or isPersonalCase is null or isPersonalCase= ' ')
            </when>
        </choose>
        <choose>
            <when test="username != null and username != '' and username != 'ALL' ">
                and username = #{username}
            </when>
            <when test="username == null ">
                and (username is null or username = '')
            </when>
        </choose>
        <choose>
            <when test="nowHandlerName != null and nowHandlerName != '' and nowHandlerName != 'ALL' ">
                and nowHandlerName= #{nowHandlerName}
            </when>
            <when test="nowHandlerName == null ">
                and (nowHandlerName is null or nowHandlerName='')
            </when>
        </choose>
        <choose>
            <when test="processDay != null and processDay != '' and processDay != 'ALL' ">
                and processDay = #{processDay}
            </when>
        </choose>
        <choose>
            <when test="mainChargeName != null and mainChargeName != '' and mainChargeName != 'ALL' ">
                and mainChargeName = #{mainChargeName}
            </when>
            <when test="mainChargeName == null ">
                and (mainChargeName is null or mainChargeName='')
            </when>
        </choose>
        GROUP BY cqt.issueId
        )tc1
        GROUP BY nowHandlerName )td1 ON ta1.nowHandlerName = td1.nowHandlerName,
        ( SELECT COUNT(DISTINCT cqt.issueId) AS total_cnt,SUM(ProcessHours) total_hour
        from ( select cqt.issueId,CASE WHEN IssueClassificationDesc = '客制需求' AND cs.submitway = 'HD187' THEN
        estimated_work_hours ELSE SUM(ProcessHours) END ProcessHours
        FROM cases_query_tmp cqt
        LEFT JOIN issue_progress p ON cqt.issueId=p.issueId
        LEFT JOIN ( select cs.IssueId,SubmitWay,estimated_work_hours from issue cs left join issue_casedetail d on
        cs.issueId=d.issueId)cs ON
        cqt.issueId=cs.issueId
        where query_id=#{queryId}
        <choose>
            <when test="flag == '已结案'.toString() or flag == '已結案'.toString()">
                AND cqt.issueStatus in ('Y','P','7','8','10')
            </when>
            <when test="flag == '未结案'.toString() or flag == '未結案'.toString()">
                AND cqt.issueStatus in ('N','2','3','4','11','12','22')
            </when>
        </choose>
        <choose>
            <when test="issueClassificationDesc  == '客制需求(187)'.toString() ">
                and issueClassificationDesc ='客制需求' and (cs.submitway = 'HD187' or cs.submitway like 'EXCEL%')
            </when>
            <when test="issueClassificationDesc  == '客制需求(服务云)'.toString() ">
                and issueClassificationDesc ='客制需求' and cs.SubmitWay = 'INNER'
            </when>
            <when test="issueClassificationDesc != null and issueClassificationDesc != '' and issueClassificationDesc != 'ALL' and issueClassificationDesc != '客制需求(187)'.toString()
                        and issueClassificationDesc != '客制需求(服务云)'.toString()  ">
                and issueClassificationDesc = #{issueClassificationDesc}
            </when>
            <when test="issueClassificationDesc  == null ">
                and (issueClassificationDesc is null or issueClassificationDesc = '' )
            </when>
        </choose>
        <choose>
            <when test="customerName != null and customerName != '' and customerName != 'ALL' ">
                and customerName = #{customerName}
            </when>
            <when test="customerName == null ">
                and (customerName is null or customerName = '')
            </when>
        </choose>
        <choose>
            <when test="erpSystemCode != null and erpSystemCode != '' and erpSystemCode != 'ALL' ">
                and cqt.erpSystemCode = #{erpSystemCode}
            </when>
            <when test="erpSystemCode == null  ">
                and (cqt.erpSystemCode is null or cqt.erpSystemCode = '')
            </when>
        </choose>
        <choose>
            <when test="productCode != null and productCode != '' and productCode != 'ALL' ">
                and productName = #{productCode}
            </when>
        </choose>
        <choose>
            <when test="emergency == '紧急'.toString() or emergency == '緊急'.toString()">
                and emergency = 1
            </when>
            <when test="emergency == '不紧急'.toString() or emergency == '不緊急'.toString()">
                and (emergency = 0 )
            </when>
        </choose>
        <choose>
            <when test="personalCase== '个案'.toString() or personalCase == '個案'.toString()">
                and isPersonalCase= 'Y'
            </when>
            <when test="personalCase== '案件'.toString() or personalCase == '案件'.toString()">
                and (isPersonalCase= 'N' or isPersonalCase is null or isPersonalCase= ' ')
            </when>
        </choose>
        <choose>
            <when test="username != null and username != '' and username != 'ALL' ">
                and username = #{username}
            </when>
            <when test="username == null ">
                and (username is null or username = '')
            </when>
        </choose>
        <choose>
            <when test="nowHandlerName != null and nowHandlerName != '' and nowHandlerName != 'ALL' ">
                and nowHandlerName= #{nowHandlerName}
            </when>
            <when test="nowHandlerName == null ">
                and (nowHandlerName is null or nowHandlerName='')
            </when>
        </choose>
        <choose>
            <when test="processDay != null and processDay != '' and processDay != 'ALL' ">
                and processDay = #{processDay}
            </when>
        </choose>
        <choose>
            <when test="mainChargeName != null and mainChargeName != '' and mainChargeName != 'ALL' ">
                and mainChargeName = #{mainChargeName}
            </when>
            <when test="mainChargeName == null ">
                and (mainChargeName is null or mainChargeName='')
            </when>
        </choose>
        GROUP BY cqt.issueId
        )cqt
        )tb1
        ORDER BY SUM DESC
    </select>

    <select id="getTempMainChargeName" parameterType="java.lang.String" resultType="com.digiwin.escloud.issueservice.t.model.cases.CasesAnalyse">
        SELECT
        CASE WHEN ta1.mainChargeName = '' THEN NULL ELSE ta1.mainChargeName END username,
        CASE WHEN ta1.mainChargeName = '' THEN NULL ELSE ta1.mainChargeName END 'column',
        type_cnt as 'sum',
        concat(cast(type_cnt /total_cnt *100 AS DECIMAL(18,1)),'%') AS perc,hour,
        concat(cast(hour /total_hour *100 AS DECIMAL(18,1)),'%') AS hourPerc,
        concat(ROUND(C/type_cnt*100, 1),'%') AS 'oneDayReplyRank',
        concat(ROUND(D/type_cnt*100, 1),'%') AS 'threeDayReplyRank',
        concat(ROUND(E/type_cnt*100, 1),'%') AS 'allReplyRank',
        C 'oneDayReplySum',D 'threeDayReplySum',E 'allReplySum'
        FROM (  SELECT COUNT(DISTINCT cqt.issueId) AS type_cnt,
        SUM(ProcessHours) hour,mainChargeName
        FROM (SELECT cqt.issueId,CASE WHEN IssueClassificationDesc = '客制需求' AND cs.submitway = 'HD187' THEN estimated_work_hours ELSE  SUM(ProcessHours) END ProcessHours,
        CASE  WHEN mainChargeName is NULL THEN '' ELSE mainChargeName END mainChargeName
        FROM cases_query_tmp cqt
        LEFT JOIN issue_progress p ON cqt.issueId=p.issueId
        LEFT JOIN ( select cs.IssueId,SubmitWay,estimated_work_hours from issue cs left join issue_casedetail d on cs.issueId=d.issueId
        )cs ON cqt.issueId=cs.issueId
        where query_id=#{queryId}
        <choose>
            <when test="flag == '已结案'.toString() or flag == '已結案'.toString()">
                AND cqt.issueStatus in ('Y','P','7','8','10')
            </when>
            <when test="flag == '未结案'.toString() or flag == '未結案'.toString()">
                AND cqt.issueStatus in ('N','2','3','4','11','12','22')
            </when>
        </choose>
        <choose>
            <when test="issueClassificationDesc  == '客制需求(187)'.toString() ">
                and issueClassificationDesc ='客制需求' and (cs.submitway = 'HD187' or cs.submitway like 'EXCEL%')
            </when>
            <when test="issueClassificationDesc  == '客制需求(服务云)'.toString() ">
                and issueClassificationDesc ='客制需求' and cs.SubmitWay = 'INNER'
            </when>
            <when test="issueClassificationDesc != null and issueClassificationDesc != '' and issueClassificationDesc != 'ALL' and issueClassificationDesc != '客制需求(187)'.toString()
                        and issueClassificationDesc != '客制需求(服务云)'.toString() ">
                and issueClassificationDesc = #{issueClassificationDesc}
            </when>
            <when test="issueClassificationDesc  == null ">
                and (issueClassificationDesc is null or issueClassificationDesc = '' )
            </when>
        </choose>
        <choose>
            <when test="customerName != null and customerName != '' and customerName != 'ALL' ">
                and customerName = #{customerName}
            </when>
            <when test="customerName == null ">
                and (customerName is null or customerName = '')
            </when>
        </choose>
        <choose>
            <when test="erpSystemCode != null and erpSystemCode != '' and erpSystemCode != 'ALL' ">
                and cqt.erpSystemCode = #{erpSystemCode}
            </when>
            <when test="erpSystemCode == null  ">
                and (cqt.erpSystemCode  is null or cqt.erpSystemCode  = '')
            </when>
        </choose>
        <choose>
            <when test="productCode != null and productCode != '' and productCode != 'ALL' ">
                and productName = #{productCode}
            </when>
        </choose>
        <choose>
            <when test="emergency == '紧急'.toString() or emergency == '緊急'.toString()">
                and emergency = 1
            </when>
            <when test="emergency == '不紧急'.toString() or emergency == '不緊急'.toString()">
                and (emergency = 0 )
            </when>
        </choose>
        <choose>
            <when test="personalCase== '个案'.toString() or personalCase == '個案'.toString()">
                and isPersonalCase= 'Y'
            </when>
            <when test="personalCase== '案件'.toString() or personalCase == '案件'.toString()">
                and (isPersonalCase= 'N' or isPersonalCase is null or isPersonalCase= ' ')
            </when>
        </choose>
        <choose>
            <when test="username != null and username != '' and username != 'ALL' ">
                and username = #{username}
            </when>
            <when test="username == null ">
                and (username is null or username = '')
            </when>
        </choose>
        <choose>
            <when test="nowHandlerName != null and nowHandlerName != '' and nowHandlerName != 'ALL' ">
                and nowHandlerName= #{nowHandlerName}
            </when>
            <when test="nowHandlerName == null ">
                and (nowHandlerName is null or nowHandlerName='')
            </when>
        </choose>
        <choose>
            <when test="processDay != null and processDay != '' and processDay != 'ALL' ">
                and processDay = #{processDay}
            </when>
        </choose>
        <choose>
            <when test="mainChargeName != null and mainChargeName != '' and mainChargeName != 'ALL' ">
                and mainChargeName = #{mainChargeName}
            </when>
            <when test="mainChargeName == null ">
                and (mainChargeName is null or mainChargeName='')
            </when>
        </choose>
        GROUP BY cqt.issueId
        )cqt
        GROUP BY mainChargeName
        ORDER BY type_cnt DESC) ta1
        LEFT JOIN (  SELECT COUNT(C) C,COUNT(D) D,COUNT(E) E,mainChargeName
        FROM (  SELECT DISTINCT cqt.issueId,
        CASE WHEN  (reply_min <![CDATA[ < ]]> 1440 AND reply_min <![CDATA[ > ]]> 0 ) OR (reply_min = 0 AND IssueStatus IN (7,8,10,11) )  THEN 'C' END C,
        CASE WHEN  (reply_min <![CDATA[ < ]]> 4320 AND reply_min <![CDATA[ > ]]> 0 ) OR (reply_min = 0 AND IssueStatus IN (7,8,10,11) ) THEN 'D' END D,
        CASE WHEN  (reply_min <![CDATA[ > ]]> 0  or  IssueStatus IN (7,8,10,11) ) THEN 'E' END E,
        CASE  WHEN mainChargeName is NULL THEN '' ELSE mainChargeName END mainChargeName
        FROM cases_query_tmp cqt
        LEFT JOIN issue_casedetail d ON cqt.issueId = d.issueId
        LEFT JOIN ( select cs.IssueId,SubmitWay from issue cs )cs ON cqt.issueId=cs.issueId
        where query_id=#{queryId}
        <choose>
            <when test="flag == '已结案'.toString() or flag == '已結案'.toString()">
                AND cqt.issueStatus in ('Y','P','7','8','10')
            </when>
            <when test="flag == '未结案'.toString() or flag == '未結案'.toString()">
                AND cqt.issueStatus in ('N','2','3','4','11','12','22')
            </when>
        </choose>
        <choose>
            <when test="issueClassificationDesc  == '客制需求(187)'.toString() ">
                and issueClassificationDesc ='客制需求' and (cs.submitway = 'HD187' or cs.submitway like 'EXCEL%')
            </when>
            <when test="issueClassificationDesc  == '客制需求(服务云)'.toString() ">
                and issueClassificationDesc ='客制需求' and cs.SubmitWay = 'INNER'
            </when>
            <when test="issueClassificationDesc != null and issueClassificationDesc != '' and issueClassificationDesc != 'ALL' and issueClassificationDesc != '客制需求(187)'.toString()
                        and issueClassificationDesc != '客制需求(服务云)'.toString() ">
                and issueClassificationDesc = #{issueClassificationDesc}
            </when>
            <when test="issueClassificationDesc  == null ">
                and (issueClassificationDesc is null or issueClassificationDesc = '' )
            </when>
        </choose>
        <choose>
            <when test="customerName != null and customerName != '' and customerName != 'ALL' ">
                and customerName = #{customerName}
            </when>
            <when test="customerName == null ">
                and (customerName is null or customerName = '')
            </when>
        </choose>
        <choose>
            <when test="erpSystemCode != null and erpSystemCode != '' and erpSystemCode != 'ALL' ">
                and cqt.erpSystemCode = #{erpSystemCode}
            </when>
            <when test="erpSystemCode == null  ">
                and (cqt.erpSystemCode  is null or cqt.erpSystemCode  = '')
            </when>
        </choose>
        <choose>
            <when test="productCode != null and productCode != '' and productCode != 'ALL' ">
                and productName = #{productCode}
            </when>
        </choose>
        <choose>
            <when test="emergency == '紧急'.toString() or emergency == '緊急'.toString()">
                and emergency = 1
            </when>
            <when test="emergency == '不紧急'.toString() or emergency == '不緊急'.toString()">
                and (emergency = 0 )
            </when>
        </choose>
        <choose>
            <when test="personalCase== '个案'.toString() or personalCase == '個案'.toString()">
                and isPersonalCase= 'Y'
            </when>
            <when test="personalCase== '案件'.toString() or personalCase == '案件'.toString()">
                and (isPersonalCase= 'N' or isPersonalCase is null or isPersonalCase= ' ')
            </when>
        </choose>
        <choose>
            <when test="username != null and username != '' and username != 'ALL' ">
                and username = #{username}
            </when>
            <when test="username == null ">
                and (username is null or username = '')
            </when>
        </choose>
        <choose>
            <when test="nowHandlerName != null and nowHandlerName != '' and nowHandlerName != 'ALL' ">
                and nowHandlerName= #{nowHandlerName}
            </when>
            <when test="nowHandlerName == null ">
                and (nowHandlerName is null or nowHandlerName='')
            </when>
        </choose>
        <choose>
            <when test="processDay != null and processDay != '' and processDay != 'ALL' ">
                and processDay = #{processDay}
            </when>
        </choose>
        <choose>
            <when test="mainChargeName != null and mainChargeName != '' and mainChargeName != 'ALL' ">
                and mainChargeName = #{mainChargeName}
            </when>
            <when test="mainChargeName == null ">
                and (mainChargeName is null or mainChargeName='')
            </when>
        </choose>
        GROUP BY cqt.issueId
        )tc1
        GROUP BY mainChargeName )td1 ON ta1.mainChargeName = td1.mainChargeName,
        ( SELECT COUNT(DISTINCT cqt.issueId) AS total_cnt,SUM(ProcessHours) total_hour
        from ( select cqt.issueId,CASE WHEN IssueClassificationDesc = '客制需求' AND cs.submitway = 'HD187' THEN
        estimated_work_hours ELSE SUM(ProcessHours) END ProcessHours
        FROM cases_query_tmp cqt
        LEFT JOIN issue_progress p ON cqt.issueId=p.issueId
        LEFT JOIN ( select cs.IssueId,SubmitWay,estimated_work_hours from issue cs left join issue_casedetail d on
        cs.issueId=d.issueId )cs ON
        cqt.issueId=cs.issueId
        where query_id=#{queryId}
        <choose>
            <when test="flag == '已结案'.toString() or flag == '已結案'.toString()">
                AND cqt.issueStatus in ('Y','P','7','8','10')
            </when>
            <when test="flag == '未结案'.toString() or flag == '未結案'.toString()">
                AND cqt.issueStatus in ('N','2','3','4','11','12','22')
            </when>
        </choose>
        <choose>
            <when test="issueClassificationDesc  == '客制需求(187)'.toString() ">
                and issueClassificationDesc ='客制需求' and (cs.submitway = 'HD187' or cs.submitway like 'EXCEL%')
            </when>
            <when test="issueClassificationDesc  == '客制需求(服务云)'.toString() ">
                and issueClassificationDesc ='客制需求' and cs.SubmitWay = 'INNER'
            </when>
            <when test="issueClassificationDesc != null and issueClassificationDesc != '' and issueClassificationDesc != 'ALL' and issueClassificationDesc != '客制需求(187)'.toString()
                        and issueClassificationDesc != '客制需求(服务云)'.toString()  ">
                and issueClassificationDesc = #{issueClassificationDesc}
            </when>
            <when test="issueClassificationDesc  == null ">
                and (issueClassificationDesc is null or issueClassificationDesc = '' )
            </when>
        </choose>
        <choose>
            <when test="customerName != null and customerName != '' and customerName != 'ALL' ">
                and customerName = #{customerName}
            </when>
            <when test="customerName == null ">
                and (customerName is null or customerName = '')
            </when>
        </choose>
        <choose>
            <when test="erpSystemCode != null and erpSystemCode != '' and erpSystemCode != 'ALL' ">
                and cqt.erpSystemCode = #{erpSystemCode}
            </when>
            <when test="erpSystemCode == null  ">
                and (cqt.erpSystemCode is null or cqt.erpSystemCode = '')
            </when>
        </choose>
        <choose>
            <when test="productCode != null and productCode != '' and productCode != 'ALL' ">
                and productName = #{productCode}
            </when>
        </choose>
        <choose>
            <when test="emergency == '紧急'.toString() or emergency == '緊急'.toString()">
                and emergency = 1
            </when>
            <when test="emergency == '不紧急'.toString() or emergency == '不緊急'.toString()">
                and (emergency = 0 )
            </when>
        </choose>
        <choose>
            <when test="personalCase== '个案'.toString() or personalCase == '個案'.toString()">
                and isPersonalCase= 'Y'
            </when>
            <when test="personalCase== '案件'.toString() or personalCase == '案件'.toString()">
                and (isPersonalCase= 'N' or isPersonalCase is null or isPersonalCase= ' ')
            </when>
        </choose>
        <choose>
            <when test="username != null and username != '' and username != 'ALL' ">
                and username = #{username}
            </when>
            <when test="username == null ">
                and (username is null or username = '')
            </when>
        </choose>
        <choose>
            <when test="nowHandlerName != null and nowHandlerName != '' and nowHandlerName != 'ALL' ">
                and nowHandlerName= #{nowHandlerName}
            </when>
            <when test="nowHandlerName == null ">
                and (nowHandlerName is null or nowHandlerName='')
            </when>
        </choose>
        <choose>
            <when test="processDay != null and processDay != '' and processDay != 'ALL' ">
                and processDay = #{processDay}
            </when>
        </choose>
        <choose>
            <when test="mainChargeName != null and mainChargeName != '' and mainChargeName != 'ALL' ">
                and mainChargeName = #{mainChargeName}
            </when>
            <when test="mainChargeName == null ">
                and (mainChargeName is null or mainChargeName='')
            </when>
        </choose>
        GROUP BY cqt.issueId
        )cqt
        )tb1
        ORDER BY SUM DESC
    </select>

    <select id="getTempProcessDay" parameterType="java.lang.String" resultType="com.digiwin.escloud.issueservice.t.model.cases.CasesAnalyse">
        SELECT ta1.processDay,ta1.processDay as 'column',
        type_cnt as 'sum',
        concat(cast(type_cnt /total_cnt *100 AS DECIMAL(18,1)),'%') AS perc,hour,
        concat(cast(hour /total_hour *100 AS DECIMAL(18,1)),'%') AS hourPerc,
        concat(ROUND(C/type_cnt*100, 1),'%') AS 'oneDayReplyRank',
        concat(ROUND(D/type_cnt*100, 1),'%') AS 'threeDayReplyRank',
        concat(ROUND(E/type_cnt*100, 1),'%') AS 'allReplyRank',
        C 'oneDayReplySum',D 'threeDayReplySum',E 'allReplySum'
        FROM (  SELECT COUNT(DISTINCT cqt.issueId) AS type_cnt,
        SUM(ProcessHours) hour,processDay
        FROM (SELECT cqt.issueId,CASE WHEN IssueClassificationDesc = '客制需求' AND cs.submitway = 'HD187' THEN estimated_work_hours ELSE  SUM(ProcessHours) END ProcessHours,
        processDay
        FROM cases_query_tmp cqt
        LEFT JOIN issue_progress p ON cqt.issueId=p.issueId
        LEFT JOIN ( select cs.IssueId,SubmitWay,estimated_work_hours from issue cs left join issue_casedetail d on cs.issueId=d.issueId
        )cs ON cqt.issueId=cs.issueId
        where query_id=#{queryId}
        <choose>
            <when test="flag == '已结案'.toString() or flag == '已結案'.toString()">
                AND cqt.issueStatus in ('Y','P','7','8','10')
            </when>
            <when test="flag == '未结案'.toString() or flag == '未結案'.toString()">
                AND cqt.issueStatus in ('N','2','3','4','11','12','22')
            </when>
        </choose>
        <choose>
            <when test="issueClassificationDesc  == '客制需求(187)'.toString() ">
                and issueClassificationDesc ='客制需求' and (cs.submitway = 'HD187' or cs.submitway like 'EXCEL%')
            </when>
            <when test="issueClassificationDesc  == '客制需求(服务云)'.toString() ">
                and issueClassificationDesc ='客制需求' and cs.SubmitWay = 'INNER'
            </when>
            <when test="issueClassificationDesc != null and issueClassificationDesc != '' and issueClassificationDesc != 'ALL' and issueClassificationDesc != '客制需求(187)'.toString()
                        and issueClassificationDesc != '客制需求(服务云)'.toString() ">
                and issueClassificationDesc = #{issueClassificationDesc}
            </when>
            <when test="issueClassificationDesc  == null ">
                and (issueClassificationDesc is null or issueClassificationDesc = '' )
            </when>
        </choose>
        <choose>
            <when test="customerName != null and customerName != '' and customerName != 'ALL' ">
                and customerName = #{customerName}
            </when>
            <when test="customerName == null ">
                and (customerName is null or customerName = '')
            </when>
        </choose>
        <choose>
            <when test="erpSystemCode != null and erpSystemCode != '' and erpSystemCode != 'ALL' ">
                and cqt.erpSystemCode = #{erpSystemCode}
            </when>
            <when test="erpSystemCode == null  ">
                and (cqt.erpSystemCode  is null or cqt.erpSystemCode  = '')
            </when>
        </choose>
        <choose>
            <when test="productCode != null and productCode != '' and productCode != 'ALL' ">
                and productName = #{productCode}
            </when>
        </choose>
        <choose>
            <when test="emergency == '紧急'.toString() or emergency == '緊急'.toString()">
                and emergency = 1
            </when>
            <when test="emergency == '不紧急'.toString() or emergency == '不緊急'.toString()">
                and (emergency = 0 )
            </when>
        </choose>
        <choose>
            <when test="personalCase== '个案'.toString() or personalCase == '個案'.toString()">
                and isPersonalCase= 'Y'
            </when>
            <when test="personalCase== '案件'.toString() or personalCase == '案件'.toString()">
                and (isPersonalCase= 'N' or isPersonalCase is null or isPersonalCase= ' ')
            </when>
        </choose>
        <choose>
            <when test="username != null and username != '' and username != 'ALL' ">
                and username = #{username}
            </when>
            <when test="username == null ">
                and (username is null or username = '')
            </when>
        </choose>
        <choose>
            <when test="nowHandlerName != null and nowHandlerName != '' and nowHandlerName != 'ALL' ">
                and nowHandlerName= #{nowHandlerName}
            </when>
            <when test="nowHandlerName == null ">
                and (nowHandlerName is null or nowHandlerName='')
            </when>
        </choose>
        <choose>
            <when test="processDay != null and processDay != '' and processDay != 'ALL' ">
                and processDay = #{processDay}
            </when>
        </choose>
        <choose>
            <when test="mainChargeName != null and mainChargeName != '' and mainChargeName != 'ALL' ">
                and mainChargeName = #{mainChargeName}
            </when>
            <when test="mainChargeName == null ">
                and (mainChargeName is null or mainChargeName='')
            </when>
        </choose>
        GROUP BY cqt.issueId
        )cqt
        GROUP BY processDay
        ORDER BY type_cnt DESC) ta1
        LEFT JOIN (  SELECT COUNT(C) C,COUNT(D) D,COUNT(E) E,processDay
        FROM (  SELECT DISTINCT cqt.issueId,
        CASE WHEN  (reply_min <![CDATA[ < ]]> 1440 AND reply_min <![CDATA[ > ]]> 0 ) OR (reply_min = 0 AND IssueStatus IN (7,8,10,11) )  THEN 'C' END C,
        CASE WHEN  (reply_min <![CDATA[ < ]]> 4320 AND reply_min <![CDATA[ > ]]> 0 ) OR (reply_min = 0 AND IssueStatus IN (7,8,10,11) ) THEN 'D' END D,
        CASE WHEN  (reply_min <![CDATA[ > ]]> 0  or  IssueStatus IN (7,8,10,11) ) THEN 'E' END E,processDay
        FROM cases_query_tmp cqt
        LEFT JOIN issue_casedetail d ON cqt.issueId = d.issueId
        LEFT JOIN ( select cs.IssueId,SubmitWay from issue cs)cs ON cqt.issueId=cs.issueId
        where query_id=#{queryId}
        <choose>
            <when test="flag == '已结案'.toString() or flag == '已結案'.toString()">
                AND cqt.issueStatus in ('Y','P','7','8','10')
            </when>
            <when test="flag == '未结案'.toString() or flag == '未結案'.toString()">
                AND cqt.issueStatus in ('N','2','3','4','11','12','22')
            </when>
        </choose>
        <choose>
            <when test="issueClassificationDesc  == '客制需求(187)'.toString() ">
                and issueClassificationDesc ='客制需求' and (cs.submitway = 'HD187' or cs.submitway like 'EXCEL%')
            </when>
            <when test="issueClassificationDesc  == '客制需求(服务云)'.toString() ">
                and issueClassificationDesc ='客制需求' and cs.SubmitWay = 'INNER'
            </when>
            <when test="issueClassificationDesc != null and issueClassificationDesc != '' and issueClassificationDesc != 'ALL' and issueClassificationDesc != '客制需求(187)'.toString()
                        and issueClassificationDesc != '客制需求(服务云)'.toString() ">
                and issueClassificationDesc = #{issueClassificationDesc}
            </when>
            <when test="issueClassificationDesc  == null ">
                and (issueClassificationDesc is null or issueClassificationDesc = '' )
            </when>
        </choose>
        <choose>
            <when test="customerName != null and customerName != '' and customerName != 'ALL' ">
                and customerName = #{customerName}
            </when>
            <when test="customerName == null ">
                and (customerName is null or customerName = '')
            </when>
        </choose>
        <choose>
            <when test="erpSystemCode != null and erpSystemCode != '' and erpSystemCode != 'ALL' ">
                and cqt.erpSystemCode = #{erpSystemCode}
            </when>
            <when test="erpSystemCode == null  ">
                and (cqt.erpSystemCode  is null or cqt.erpSystemCode  = '')
            </when>
        </choose>
        <choose>
            <when test="productCode != null and productCode != '' and productCode != 'ALL' ">
                and productName = #{productCode}
            </when>
        </choose>
        <choose>
            <when test="emergency == '紧急'.toString() or emergency == '緊急'.toString()">
                and emergency = 1
            </when>
            <when test="emergency == '不紧急'.toString() or emergency == '不緊急'.toString()">
                and (emergency = 0 )
            </when>
        </choose>
        <choose>
            <when test="personalCase== '个案'.toString() or personalCase == '個案'.toString()">
                and isPersonalCase= 'Y'
            </when>
            <when test="personalCase== '案件'.toString() or personalCase == '案件'.toString()">
                and (isPersonalCase= 'N' or isPersonalCase is null or isPersonalCase= ' ')
            </when>
        </choose>
        <choose>
            <when test="username != null and username != '' and username != 'ALL' ">
                and username = #{username}
            </when>
            <when test="username == null ">
                and (username is null or username = '')
            </when>
        </choose>
        <choose>
            <when test="nowHandlerName != null and nowHandlerName != '' and nowHandlerName != 'ALL' ">
                and nowHandlerName= #{nowHandlerName}
            </when>
            <when test="nowHandlerName == null ">
                and (nowHandlerName is null or nowHandlerName='')
            </when>
        </choose>
        <choose>
            <when test="processDay != null and processDay != '' and processDay != 'ALL' ">
                and processDay = #{processDay}
            </when>
        </choose>
        <choose>
            <when test="mainChargeName != null and mainChargeName != '' and mainChargeName != 'ALL' ">
                and mainChargeName = #{mainChargeName}
            </when>
            <when test="mainChargeName == null ">
                and (mainChargeName is null or mainChargeName='')
            </when>
        </choose>
        GROUP BY cqt.issueId
        )tc1
        GROUP BY processDay )td1 ON ta1.processDay = td1.processDay,
        ( SELECT COUNT(DISTINCT cqt.issueId) AS total_cnt,SUM(ProcessHours) total_hour
        from ( select cqt.issueId,CASE WHEN IssueClassificationDesc = '客制需求' AND cs.submitway = 'HD187' THEN
        estimated_work_hours ELSE SUM(ProcessHours) END ProcessHours
        FROM cases_query_tmp cqt
        LEFT JOIN issue_progress p ON cqt.issueId=p.issueId
        LEFT JOIN ( select cs.IssueId,SubmitWay,estimated_work_hours from issue cs left join issue_casedetail d on
        cs.issueId=d.issueId)cs ON
        cqt.issueId=cs.issueId
        where query_id=#{queryId}
        <choose>
            <when test="flag == '已结案'.toString() or flag == '已結案'.toString()">
                AND cqt.issueStatus in ('Y','P','7','8','10')
            </when>
            <when test="flag == '未结案'.toString() or flag == '未結案'.toString()">
                AND cqt.issueStatus in ('N','2','3','4','11','12','22')
            </when>
        </choose>
        <choose>
            <when test="issueClassificationDesc  == '客制需求(187)'.toString() ">
                and issueClassificationDesc ='客制需求' and (cs.submitway = 'HD187' or cs.submitway like 'EXCEL%')
            </when>
            <when test="issueClassificationDesc  == '客制需求(服务云)'.toString() ">
                and issueClassificationDesc ='客制需求' and cs.SubmitWay = 'INNER'
            </when>
            <when test="issueClassificationDesc != null and issueClassificationDesc != '' and issueClassificationDesc != 'ALL' and issueClassificationDesc != '客制需求(187)'.toString()
                        and issueClassificationDesc != '客制需求(服务云)'.toString()  ">
                and issueClassificationDesc = #{issueClassificationDesc}
            </when>
            <when test="issueClassificationDesc  == null ">
                and (issueClassificationDesc is null or issueClassificationDesc = '' )
            </when>
        </choose>
        <choose>
            <when test="customerName != null and customerName != '' and customerName != 'ALL' ">
                and customerName = #{customerName}
            </when>
            <when test="customerName == null ">
                and (customerName is null or customerName = '')
            </when>
        </choose>
        <choose>
            <when test="erpSystemCode != null and erpSystemCode != '' and erpSystemCode != 'ALL' ">
                and cqt.erpSystemCode = #{erpSystemCode}
            </when>
            <when test="erpSystemCode == null  ">
                and (cqt.erpSystemCode is null or cqt.erpSystemCode = '')
            </when>
        </choose>
        <choose>
            <when test="productCode != null and productCode != '' and productCode != 'ALL' ">
                and productName = #{productCode}
            </when>
        </choose>
        <choose>
            <when test="emergency == '紧急'.toString() or emergency == '緊急'.toString()">
                and emergency = 1
            </when>
            <when test="emergency == '不紧急'.toString() or emergency == '不緊急'.toString()">
                and (emergency = 0 )
            </when>
        </choose>
        <choose>
            <when test="personalCase== '个案'.toString() or personalCase == '個案'.toString()">
                and isPersonalCase= 'Y'
            </when>
            <when test="personalCase== '案件'.toString() or personalCase == '案件'.toString()">
                and (isPersonalCase= 'N' or isPersonalCase is null or isPersonalCase= ' ')
            </when>
        </choose>
        <choose>
            <when test="username != null and username != '' and username != 'ALL' ">
                and username = #{username}
            </when>
            <when test="username == null ">
                and (username is null or username = '')
            </when>
        </choose>
        <choose>
            <when test="nowHandlerName != null and nowHandlerName != '' and nowHandlerName != 'ALL' ">
                and nowHandlerName= #{nowHandlerName}
            </when>
            <when test="nowHandlerName == null ">
                and (nowHandlerName is null or nowHandlerName='')
            </when>
        </choose>
        <choose>
            <when test="processDay != null and processDay != '' and processDay != 'ALL' ">
                and processDay = #{processDay}
            </when>
        </choose>
        <choose>
            <when test="mainChargeName != null and mainChargeName != '' and mainChargeName != 'ALL' ">
                and mainChargeName = #{mainChargeName}
            </when>
            <when test="mainChargeName == null ">
                and (mainChargeName is null or mainChargeName='')
            </when>
        </choose>
        GROUP BY cqt.issueId
        )cqt
        )tb1
        ORDER BY SUM DESC
    </select>

    <select id="getTempPersonalCase" parameterType="java.lang.String" resultType="com.digiwin.escloud.issueservice.t.model.cases.CasesAnalyse">
        SELECT ta1.isPersonalCase as 'isPersonalCase',ta1.isPersonalCase as 'column',
        type_cnt as 'sum',
        concat(cast(type_cnt /total_cnt *100 AS DECIMAL(18,1)),'%') AS perc,hour,
        concat(cast(hour /total_hour *100 AS DECIMAL(18,1)),'%') AS hourPerc,
        concat(ROUND(C/type_cnt*100, 1),'%') AS 'oneDayReplyRank',
        concat(ROUND(D/type_cnt*100, 1),'%') AS 'threeDayReplyRank',
        concat(ROUND(E/type_cnt*100, 1),'%') AS 'allReplyRank',
        C 'oneDayReplySum',D 'threeDayReplySum',E 'allReplySum'
        FROM (  SELECT COUNT(DISTINCT cqt.issueId) AS type_cnt,
        SUM(ProcessHours) hour,isPersonalCase
        FROM (SELECT cqt.issueId,CASE WHEN IssueClassificationDesc = '客制需求' AND cs.submitway = 'HD187' THEN estimated_work_hours ELSE  SUM(ProcessHours) END ProcessHours,
        (case
        WHEN ( isPersonalCase = 'Y' and #{language}='zh-CN') THEN '个案'
        WHEN ( isPersonalCase = 'Y' and #{language}='zh-TW') THEN '個案'
        ELSE '案件'
        END
        ) isPersonalCase
        FROM cases_query_tmp cqt
        LEFT JOIN issue_progress p ON cqt.issueId=p.issueId
        LEFT JOIN ( select cs.IssueId,SubmitWay,estimated_work_hours from issue cs left join issue_casedetail d on cs.issueId=d.issueId
        )cs ON cqt.issueId=cs.issueId
        where query_id=#{queryId}
        <choose>
            <when test="flag == '已结案'.toString() or flag == '已結案'.toString()">
                AND cqt.issueStatus in ('Y','P','7','8','10')
            </when>
            <when test="flag == '未结案'.toString() or flag == '未結案'.toString()">
                AND cqt.issueStatus in ('N','2','3','4','11','12','22')
            </when>
        </choose>
        <choose>
            <when test="issueClassificationDesc  == '客制需求(187)'.toString() ">
                and issueClassificationDesc ='客制需求' and (cs.submitway = 'HD187' or cs.submitway like 'EXCEL%')
            </when>
            <when test="issueClassificationDesc  == '客制需求(服务云)'.toString() ">
                and issueClassificationDesc ='客制需求' and cs.SubmitWay = 'INNER'
            </when>
            <when test="issueClassificationDesc != null and issueClassificationDesc != '' and issueClassificationDesc != 'ALL' and issueClassificationDesc != '客制需求(187)'.toString()
                        and issueClassificationDesc != '客制需求(服务云)'.toString() ">
                and issueClassificationDesc = #{issueClassificationDesc}
            </when>
            <when test="issueClassificationDesc  == null ">
                and (issueClassificationDesc is null or issueClassificationDesc = '' )
            </when>
        </choose>
        <choose>
            <when test="customerName != null and customerName != '' and customerName != 'ALL' ">
                and customerName = #{customerName}
            </when>
            <when test="customerName == null ">
                and (customerName is null or customerName = '')
            </when>
        </choose>
        <choose>
            <when test="erpSystemCode != null and erpSystemCode != '' and erpSystemCode != 'ALL' ">
                and cqt.erpSystemCode = #{erpSystemCode}
            </when>
            <when test="erpSystemCode == null  ">
                and (cqt.erpSystemCode  is null or cqt.erpSystemCode  = '')
            </when>
        </choose>
        <choose>
            <when test="productCode != null and productCode != '' and productCode != 'ALL' ">
                and productName = #{productCode}
            </when>
        </choose>
        <choose>
            <when test="emergency == '紧急'.toString() or emergency == '緊急'.toString()">
                and emergency = 1
            </when>
            <when test="emergency == '不紧急'.toString() or emergency == '不緊急'.toString()">
                and (emergency = 0 )
            </when>
        </choose>
        <choose>
            <when test="personalCase== '个案'.toString() or personalCase == '個案'.toString()">
                and isPersonalCase= 'Y'
            </when>
            <when test="personalCase== '案件'.toString() or personalCase == '案件'.toString()">
                and (isPersonalCase= 'N' or isPersonalCase is null or isPersonalCase= ' ')
            </when>
        </choose>
        <choose>
            <when test="username != null and username != '' and username != 'ALL' ">
                and username = #{username}
            </when>
            <when test="username == null ">
                and (username is null or username = '')
            </when>
        </choose>
        <choose>
            <when test="nowHandlerName != null and nowHandlerName != '' and nowHandlerName != 'ALL' ">
                and nowHandlerName= #{nowHandlerName}
            </when>
            <when test="nowHandlerName == null ">
                and (nowHandlerName is null or nowHandlerName='')
            </when>
        </choose>
        <choose>
            <when test="processDay != null and processDay != '' and processDay != 'ALL' ">
                and processDay = #{processDay}
            </when>
        </choose>
        <choose>
            <when test="mainChargeName != null and mainChargeName != '' and mainChargeName != 'ALL' ">
                and mainChargeName = #{mainChargeName}
            </when>
            <when test="mainChargeName == null ">
                and (mainChargeName is null or mainChargeName='')
            </when>
        </choose>
        GROUP BY cqt.issueId
        )cqt
        GROUP BY isPersonalCase
        ORDER BY type_cnt DESC) ta1
        LEFT JOIN (  SELECT COUNT(C) C,COUNT(D) D,COUNT(E) E,isPersonalCase
        FROM (  SELECT DISTINCT cqt.issueId,
        CASE WHEN  (reply_min <![CDATA[ < ]]> 1440 AND reply_min <![CDATA[ > ]]> 0 ) OR (reply_min = 0 AND IssueStatus IN (7,8,10,11) )  THEN 'C' END C,
        CASE WHEN  (reply_min <![CDATA[ < ]]> 4320 AND reply_min <![CDATA[ > ]]> 0 ) OR (reply_min = 0 AND IssueStatus IN (7,8,10,11) ) THEN 'D' END D,
        CASE WHEN  (reply_min <![CDATA[ > ]]> 0  or  IssueStatus IN (7,8,10,11) ) THEN 'E' END E,
        (case
        WHEN ( isPersonalCase = 'Y' and #{language}='zh-CN') THEN '个案'
        WHEN ( isPersonalCase = 'Y' and #{language}='zh-TW') THEN '個案'
        ELSE '案件'
        END
        ) isPersonalCase
        FROM cases_query_tmp cqt
        LEFT JOIN issue_casedetail d ON cqt.issueId = d.issueId
        LEFT JOIN ( select cs.IssueId,SubmitWay from issue cs)cs ON cqt.issueId=cs.issueId
        where query_id=#{queryId}
        <choose>
            <when test="flag == '已结案'.toString() or flag == '已結案'.toString()">
                AND cqt.issueStatus in ('Y','P','7','8','10')
            </when>
            <when test="flag == '未结案'.toString() or flag == '未結案'.toString()">
                AND cqt.issueStatus in ('N','2','3','4','11','12','22')
            </when>
        </choose>
        <choose>
            <when test="issueClassificationDesc  == '客制需求(187)'.toString() ">
                and issueClassificationDesc ='客制需求' and (cs.submitway = 'HD187' or cs.submitway like 'EXCEL%')
            </when>
            <when test="issueClassificationDesc  == '客制需求(服务云)'.toString() ">
                and issueClassificationDesc ='客制需求' and cs.SubmitWay = 'INNER'
            </when>
            <when test="issueClassificationDesc != null and issueClassificationDesc != '' and issueClassificationDesc != 'ALL' and issueClassificationDesc != '客制需求(187)'.toString()
                        and issueClassificationDesc != '客制需求(服务云)'.toString() ">
                and issueClassificationDesc = #{issueClassificationDesc}
            </when>
            <when test="issueClassificationDesc  == null ">
                and (issueClassificationDesc is null or issueClassificationDesc = '' )
            </when>
        </choose>
        <choose>
            <when test="customerName != null and customerName != '' and customerName != 'ALL' ">
                and customerName = #{customerName}
            </when>
            <when test="customerName == null ">
                and (customerName is null or customerName = '')
            </when>
        </choose>
        <choose>
            <when test="erpSystemCode != null and erpSystemCode != '' and erpSystemCode != 'ALL' ">
                and cqt.erpSystemCode = #{erpSystemCode}
            </when>
            <when test="erpSystemCode == null  ">
                and (cqt.erpSystemCode  is null or cqt.erpSystemCode  = '')
            </when>
        </choose>
        <choose>
            <when test="productCode != null and productCode != '' and productCode != 'ALL' ">
                and productName = #{productCode}
            </when>
        </choose>
        <choose>
            <when test="emergency == '紧急'.toString() or emergency == '緊急'.toString()">
                and emergency = 1
            </when>
            <when test="emergency == '不紧急'.toString() or emergency == '不緊急'.toString()">
                and (emergency = 0 )
            </when>
        </choose>
        <choose>
            <when test="personalCase== '个案'.toString() or personalCase == '個案'.toString()">
                and isPersonalCase= 'Y'
            </when>
            <when test="personalCase== '案件'.toString() or personalCase == '案件'.toString()">
                and (isPersonalCase= 'N' or isPersonalCase is null or isPersonalCase= ' ')
            </when>
        </choose>
        <choose>
            <when test="username != null and username != '' and username != 'ALL' ">
                and username = #{username}
            </when>
            <when test="username == null ">
                and (username is null or username = '')
            </when>
        </choose>
        <choose>
            <when test="nowHandlerName != null and nowHandlerName != '' and nowHandlerName != 'ALL' ">
                and nowHandlerName= #{nowHandlerName}
            </when>
            <when test="nowHandlerName == null ">
                and (nowHandlerName is null or nowHandlerName='')
            </when>
        </choose>
        <choose>
            <when test="processDay != null and processDay != '' and processDay != 'ALL' ">
                and processDay = #{processDay}
            </when>
        </choose>
        <choose>
            <when test="mainChargeName != null and mainChargeName != '' and mainChargeName != 'ALL' ">
                and mainChargeName = #{mainChargeName}
            </when>
            <when test="mainChargeName == null ">
                and (mainChargeName is null or mainChargeName='')
            </when>
        </choose>
        GROUP BY cqt.issueId
        )tc1
        GROUP BY isPersonalCase )td1 ON ta1.isPersonalCase = td1.isPersonalCase,
        ( SELECT COUNT(DISTINCT cqt.issueId) AS total_cnt,SUM(ProcessHours) total_hour
        from ( select cqt.issueId,CASE WHEN IssueClassificationDesc = '客制需求' AND cs.submitway = 'HD187' THEN
        estimated_work_hours ELSE SUM(ProcessHours) END ProcessHours
        FROM cases_query_tmp cqt
        LEFT JOIN issue_progress p ON cqt.issueId=p.issueId
        LEFT JOIN ( select cs.IssueId,SubmitWay,estimated_work_hours from issue cs left join issue_casedetail d on
        cs.issueId=d.issueId)cs ON
        cqt.issueId=cs.issueId
        where query_id=#{queryId}
        <choose>
            <when test="flag == '已结案'.toString() or flag == '已結案'.toString()">
                AND cqt.issueStatus in ('Y','P','7','8','10')
            </when>
            <when test="flag == '未结案'.toString() or flag == '未結案'.toString()">
                AND cqt.issueStatus in ('N','2','3','4','11','12','22')
            </when>
        </choose>
        <choose>
            <when test="issueClassificationDesc  == '客制需求(187)'.toString() ">
                and issueClassificationDesc ='客制需求' and (cs.submitway = 'HD187' or cs.submitway like 'EXCEL%')
            </when>
            <when test="issueClassificationDesc  == '客制需求(服务云)'.toString() ">
                and issueClassificationDesc ='客制需求' and cs.SubmitWay = 'INNER'
            </when>
            <when test="issueClassificationDesc != null and issueClassificationDesc != '' and issueClassificationDesc != 'ALL' and issueClassificationDesc != '客制需求(187)'.toString()
                        and issueClassificationDesc != '客制需求(服务云)'.toString()  ">
                and issueClassificationDesc = #{issueClassificationDesc}
            </when>
            <when test="issueClassificationDesc  == null ">
                and (issueClassificationDesc is null or issueClassificationDesc = '' )
            </when>
        </choose>
        <choose>
            <when test="customerName != null and customerName != '' and customerName != 'ALL' ">
                and customerName = #{customerName}
            </when>
            <when test="customerName == null ">
                and (customerName is null or customerName = '')
            </when>
        </choose>
        <choose>
            <when test="erpSystemCode != null and erpSystemCode != '' and erpSystemCode != 'ALL' ">
                and cqt.erpSystemCode = #{erpSystemCode}
            </when>
            <when test="erpSystemCode == null  ">
                and (cqt.erpSystemCode is null or cqt.erpSystemCode = '')
            </when>
        </choose>
        <choose>
            <when test="productCode != null and productCode != '' and productCode != 'ALL' ">
                and productName = #{productCode}
            </when>
        </choose>
        <choose>
            <when test="emergency == '紧急'.toString() or emergency == '緊急'.toString()">
                and emergency = 1
            </when>
            <when test="emergency == '不紧急'.toString() or emergency == '不緊急'.toString()">
                and (emergency = 0 )
            </when>
        </choose>
        <choose>
            <when test="personalCase== '个案'.toString() or personalCase == '個案'.toString()">
                and isPersonalCase= 'Y'
            </when>
            <when test="personalCase== '案件'.toString() or personalCase == '案件'.toString()">
                and (isPersonalCase= 'N' or isPersonalCase is null or isPersonalCase= ' ')
            </when>
        </choose>
        <choose>
            <when test="username != null and username != '' and username != 'ALL' ">
                and username = #{username}
            </when>
            <when test="username == null ">
                and (username is null or username = '')
            </when>
        </choose>
        <choose>
            <when test="nowHandlerName != null and nowHandlerName != '' and nowHandlerName != 'ALL' ">
                and nowHandlerName= #{nowHandlerName}
            </when>
            <when test="nowHandlerName == null ">
                and (nowHandlerName is null or nowHandlerName='')
            </when>
        </choose>
        <choose>
            <when test="processDay != null and processDay != '' and processDay != 'ALL' ">
                and processDay = #{processDay}
            </when>
        </choose>
        <choose>
            <when test="mainChargeName != null and mainChargeName != '' and mainChargeName != 'ALL' ">
                and mainChargeName = #{mainChargeName}
            </when>
            <when test="mainChargeName == null ">
                and (mainChargeName is null or mainChargeName='')
            </when>
        </choose>
        GROUP BY cqt.issueId
        )cqt
        )tb1
        ORDER BY SUM DESC
    </select>

    <select id="getNullCustName" parameterType="java.lang.String" resultType="java.lang.Integer">
        SELECT COUNT(*) FROM
        (
        SELECT * from
        (
        SELECT COUNT(*) AS sum,customerName ,serviceCode
        FROM `cases_query_tmp` cqt
        where query_id=#{queryId}
        <choose>
            <when test="flag == '已结案'.toString() or flag == '已結案'.toString()">
                AND cqt.issueStatus <![CDATA[ >= ]]> 7 and cqt.issueStatus <![CDATA[ <= ]]> 10
            </when>
            <when test="flag == '未结案'.toString() or flag == '未結案'.toString()">
                AND (cqt.issueStatus <![CDATA[<]]> 7 or cqt.issueStatus = '12' or cqt.issueStatus = '11' or cqt.issueStatus = '22')
            </when>
        </choose>
        <choose>
            <when test="issueClassificationDesc != null and issueClassificationDesc != '' and issueClassificationDesc != '空'.toString()">
                and issueClassificationDesc = #{issueClassificationDesc}
            </when>
            <when test="issueClassificationDesc  == '空'.toString() ">
                and (issueClassificationDesc is null or issueClassificationDesc = '' )
            </when>
            <when test="issueClassificationDesc == '其它'.toString()">
                and ( issueClassificationDesc not in
                (
                SELECT issueClassificationDesc FROM
                (
                SELECT issueClassificationDesc,sum
                FROM(
                SELECT COUNT(*) AS sum,issueClassificationDesc
                FROM `cases_query_tmp` cqt
                where query_id=#{queryId}
                <choose>
                    <when test="flag == '已结案'.toString() or flag == '已結案'.toString()">
                        AND cqt.issueStatus <![CDATA[ >= ]]> 7 and cqt.issueStatus <![CDATA[ <= ]]> 10
                    </when>
                    <when test="flag == '未结案'.toString() or flag == '未結案'.toString()">
                        AND (cqt.issueStatus <![CDATA[<]]> 7 or cqt.issueStatus = '12' or cqt.issueStatus = '11' or cqt.issueStatus = '22')
                    </when>
                </choose>
                <choose>
                    <when test="customerName != null and customerName != '' and customerName != '空'.toString()">
                        and customerName = #{customerName}
                    </when>
                    <when test="customerName == '空'.toString() ">
                        and customerName is null
                    </when>
                </choose>

                <choose>
                    <when test="erpSystemCode != null and erpSystemCode != '' and erpSystemCode != '空'.toString() ">
                        and erpSystemCode = #{moduleCode}
                    </when>
                    <!--<when test="erpSystemCode == '空'.toString() ">
                        and (erpSystemCode is null or erpSystemCode = '')
                    </when>-->
                </choose>

                <choose>
                    <when test="productCode != null and productCode != '' and productCode != '空'.toString()">
                        and productName = #{productCode}
                    </when>
                    <!--<when test="productCode == '空'.toString() ">
                        and productName is null
                    </when>-->
                </choose>

                <choose>
                    <when test="emergency == '紧急'.toString() or emergency == '緊急'.toString() ">
                        and emergency = 1
                    </when>
                    <when test="emergency == '不紧急'.toString() or emergency == '不緊急'.toString()  ">
                        and (emergency = 0 )
                    </when>
                </choose>
                <choose>
                    <when test="personalCase== '个案'.toString() or personalCase== '個案'.toString()">
                        and isPersonalCase= 'Y'
                    </when>
                    <when test="personalCase== '案件'.toString() ">
                        and (isPersonalCase= 'N' or isPersonalCase is null or isPersonalCase= ' ')
                    </when>
                </choose>

                <choose>
                    <when test="username != null and username != '' and username != '空'.toString() ">
                        and username = #{username}
                    </when>
                    <!--<when test="username == '空'.toString() ">
                        and username is null
                    </when>-->
                </choose>
                <choose>
                    <when test="nowHandlerName != null and nowHandlerName != '' and nowHandlerName != '空'.toString() ">
                        and nowHandlerName= #{nowHandlerName}
                    </when>
                    <!-- <when test="nowHandlerName == '空'.toString() ">
                         and nowHandlerName is null
                     </when>-->
                </choose>
                <choose>
                    <when test="processDay != null and processDay != '' and processDay!= '空'.toString() ">
                        and processDay = #{processDay}
                    </when>
                </choose>

                <choose>
                    <when test="mainChargeName != null and mainChargeName != '' and mainChargeName != '空'.toString() and mainChargeName != '其它'.toString()">
                        and mainChargeName = #{mainChargeName}
                    </when>
                    <when test="mainChargeName == '空'.toString() ">
                        and mainChargeName is null
                    </when>
                    <when test="mainChargeName == '其它'.toString()">
                        and ( mainChargeName not in
                        (
                        SELECT mainChargeName FROM
                        (
                        SELECT mainChargeName,sum
                        FROM(
                        SELECT COUNT(*) AS sum,mainChargeName
                        FROM `cases_query_tmp` cqt
                        where query_id=#{queryId}
                        <choose>
                            <when test="flag == '已结案'.toString() or flag == '已結案'.toString()">AND cqt.issueStatus <![CDATA[ >= ]]> 7 and cqt.issueStatus <![CDATA[ <= ]]> 10</when>

                            <when test="flag == '未结案'.toString() or flag == '未結案'.toString()">
                                AND (cqt.issueStatus <![CDATA[<]]> 7 or cqt.issueStatus = '12' or cqt.issueStatus = '11' or cqt.issueStatus = '22')
                            </when>
                        </choose>

                        <choose>
                            <when test="issueClassificationDesc != null and issueClassificationDesc != '' and issueClassificationDesc != '空'.toString()">
                                and issueClassificationDesc = #{issueClassificationDesc}
                            </when>
                            <when test="issueClassificationDesc  == '空'.toString() ">
                                and (issueClassificationDesc is null or issueClassificationDesc = '' )
                            </when>
                            <when test="issueClassificationDesc == '其它'.toString()">
                                and ( issueClassificationDesc not in
                                (
                                SELECT issueClassificationDesc FROM
                                (
                                SELECT issueClassificationDesc,sum
                                FROM(
                                SELECT COUNT(*) AS sum,issueClassificationDesc
                                FROM `cases_query_tmp` cqt
                                where query_id=#{queryId}
                                <choose>
                                    <when test="flag == '已结案'.toString() or flag == '已結案'.toString()">
                                        AND cqt.issueStatus <![CDATA[ >= ]]> 7 and cqt.issueStatus <![CDATA[ <= ]]> 10
                                    </when>
                                    <when test="flag == '未结案'.toString() or flag == '未結案'.toString()">
                                        AND (cqt.issueStatus <![CDATA[<]]> 7 or cqt.issueStatus = '12' or cqt.issueStatus = '11' or cqt.issueStatus = '22')
                                    </when>
                                </choose>
                                <choose>
                                    <when test="customerName != null and customerName != '' and customerName != '空'.toString()">
                                        and customerName = #{customerName}
                                    </when>
                                    <when test="customerName == '空'.toString() ">
                                        and customerName is null
                                    </when>
                                </choose>

                                <choose>
                                    <when test="erpSystemCode != null and erpSystemCode != '' and erpSystemCode != '空'.toString() ">
                                        and erpSystemCode = #{moduleCode}
                                    </when>
                                    <!--<when test="erpSystemCode == '空'.toString() ">
                                        and (erpSystemCode is null or erpSystemCode = '')
                                    </when>-->
                                </choose>

                                <choose>
                                    <when test="productCode != null and productCode != '' and productCode != '空'.toString()">
                                        and productName = #{productCode}
                                    </when>
                                    <!--<when test="productCode == '空'.toString() ">
                                        and productName is null
                                    </when>-->
                                </choose>

                                <choose>
                                    <when test="emergency == '紧急'.toString() or emergency == '緊急'.toString() ">
                                        and emergency = 1
                                    </when>
                                    <when test="emergency == '不紧急'.toString() or emergency == '不緊急'.toString()  ">
                                        and (emergency = 0 )
                                    </when>
                                </choose>
                                <choose>
                                    <when test="personalCase== '个案'.toString() or personalCase== '個案'.toString()">
                                        and isPersonalCase= 'Y'
                                    </when>
                                    <when test="personalCase== '案件'.toString() ">
                                        and (isPersonalCase= 'N' or isPersonalCase is null or isPersonalCase= ' ')
                                    </when>
                                </choose>

                                <choose>
                                    <when test="username != null and username != '' and username != '空'.toString() ">
                                        and username = #{username}
                                    </when>
                                    <!--<when test="username == '空'.toString() ">
                                        and username is null
                                    </when>-->
                                </choose>
                                <choose>
                                    <when test="nowHandlerName != null and nowHandlerName != '' and nowHandlerName != '空'.toString() ">
                                        and nowHandlerName= #{nowHandlerName}
                                    </when>
                                    <!-- <when test="nowHandlerName == '空'.toString() ">
                                         and nowHandlerName is null
                                     </when>-->
                                </choose>
                                <choose>
                                    <when test="processDay != null and processDay != '' and processDay!= '空'.toString() ">
                                        and processDay = #{processDay}
                                    </when>
                                </choose>

                                <choose>
                                    <when test="mainChargeName != null and mainChargeName != '' and mainChargeName != '空'.toString() ">
                                        and mainChargeName= #{mainChargeName}
                                    </when>
                                    <!--<when test="mainChargeName == '空'.toString() ">
                                        and mainChargeName is null
                                    </when>-->
                                </choose>


                                <choose>
                                    <when test="count != '0'.toString() ">
                                        AND issueClassificationDesc IS NOT null
                                    </when>
                                </choose>
                                GROUP BY issueClassificationDesc
                                ORDER BY sum DESC,issueClassificationDesc asc
                                ) tb1
                                LIMIT
                                <choose>
                                    <when test="count != '0'.toString() ">
                                        8
                                    </when>
                                </choose>
                                <choose>
                                    <when test="count == '0'.toString() ">
                                        9
                                    </when>
                                </choose>

                                ) tb4)
                                )


                            </when>
                        </choose>

                        <choose>
                            <when test="customerName != null and customerName != '' and customerName != '空'.toString()">
                                and customerName = #{customerName}
                            </when>
                            <when test="customerName == '空'.toString() ">
                                and customerName is null
                            </when>
                        </choose>

                        <choose>
                            <when test="erpSystemCode != null and erpSystemCode != '' and erpSystemCode != '空'.toString() ">
                                and erpSystemCode = #{erpSystemCode}
                            </when>
                            <!-- <when test="erpSystemCode == '空'.toString() ">
                                 and (erpSystemCode is null or erpSystemCode = '')
                             </when>-->
                        </choose>

                        <choose>
                            <when test="productCode != null and productCode != '' and productCode != '空'.toString()">
                                and productName = #{productCode}
                            </when>
                            <!-- <when test="productCode == '空'.toString() ">
                                 and productName is null
                             </when>-->
                        </choose>

                        <choose>
                            <when test="emergency == '紧急'.toString() or emergency == '緊急'.toString() ">
                                and emergency = 1
                            </when>
                            <when test="emergency == '不紧急'.toString() or emergency == '不緊急'.toString()  ">
                                and (emergency = 0 )
                            </when>
                        </choose>
                        <choose>
                            <when test="personalCase== '个案'.toString() or personalCase== '個案'.toString()">
                                and isPersonalCase= 'Y'
                            </when>
                            <when test="personalCase== '案件'.toString() ">
                                and (isPersonalCase= 'N' or isPersonalCase is null or isPersonalCase= ' ')
                            </when>
                        </choose>

                        <choose>
                            <when test="username != null and username != '' and username != '空'.toString() ">
                                and username = #{username}
                            </when>
                            <!--<when test="username == '空'.toString() ">
                                and username is null
                            </when>-->
                        </choose>
                        <choose>
                            <when test="processDay != null and processDay != '' and processDay != '空'">
                                and processDay = #{processDay}
                            </when>
                        </choose>
                        <choose>
                            <when test="nowHandlerName != null and nowHandlerName != '' and nowHandlerName != '空'.toString() ">
                                and nowHandlerName= #{nowHandlerName}
                            </when>
                            <!--  <when test="nowHandlerName == '空'.toString() ">
                                  and nowHandlerName is null
                              </when>-->
                        </choose>


                        <choose>
                            <when test="count != '0'.toString() ">
                                AND mainChargeName IS NOT null
                            </when>
                        </choose>
                        GROUP BY mainChargeName
                        ORDER BY sum DESC,mainChargeName asc
                        ) tb1
                        LIMIT
                        <choose>
                            <when test="count != '0'.toString() ">
                                19
                            </when>
                        </choose>
                        <choose>
                            <when test="count == '0'.toString() ">
                                20
                            </when>
                        </choose>

                        ) tb4)
                        )


                    </when>
                </choose>

                <choose>
                    <when test="count != '0'.toString() ">
                        AND issueClassificationDesc IS NOT null
                    </when>
                </choose>
                GROUP BY issueClassificationDesc
                ORDER BY sum DESC,issueClassificationDesc asc
                ) tb1
                LIMIT
                <choose>
                    <when test="count != '0'.toString() ">
                        8
                    </when>
                </choose>
                <choose>
                    <when test="count == '0'.toString() ">
                        9
                    </when>
                </choose>

                ) tb4)
                )


            </when>
        </choose>
        <choose>
            <when test="erpSystemCode != null and erpSystemCode != '' and erpSystemCode != '空'.toString() ">
                and erpSystemCode = #{erpSystemCode}
            </when>
            <when test="erpSystemCode == '空'.toString() ">
                and (erpSystemCode is null or erpSystemCode = '')
            </when>
        </choose>
        <choose>
            <when test="productCode != null and productCode != '' and productCode != '空'.toString()">
                and productName = #{productCode}
            </when>
            <when test="productCode == '空'.toString() ">
                and productName is null
            </when>
        </choose>
        <choose>
            <when test="emergency == '紧急'.toString() or emergency == '緊急'.toString() ">
                and emergency = 1
            </when>
            <when test="emergency == '不紧急'.toString() or emergency == '不緊急'.toString()  ">
                and (emergency = 0 )
            </when>
        </choose>
        <choose>
            <when test="personalCase== '个案'.toString() or personalCase== '個案'.toString()">
                and isPersonalCase= 'Y'
            </when>
            <when test="personalCase== '案件'.toString() ">
                and (isPersonalCase= 'N' or isPersonalCase is null or isPersonalCase= ' ')
            </when>
        </choose>
        <choose>
            <when test="username != null and username != '' and username != '空'.toString() ">
                and username = #{username}
            </when>
            <when test="username == '空'.toString() ">
                and username is null
            </when>
        </choose>
        <choose>
            <when test="nowHandlerName != null and nowHandlerName != '' and nowHandlerName != '空'.toString() ">
                and nowHandlerName= #{nowHandlerName}
            </when>
            <when test="nowHandlerName == '空'.toString() ">
                and nowHandlerName is null
            </when>
        </choose>
        <choose>
            <when test="processDay != null and processDay != '' and processDay != '空'.toString()">
                and processDay = #{processDay}
            </when>
        </choose>

        <choose>
            <when test="mainChargeName != null and mainChargeName != '' and mainChargeName != '空'.toString() and mainChargeName != '其它'.toString()">
                and mainChargeName = #{mainChargeName}
            </when>
            <when test="mainChargeName == == '空'.toString() ">
                and mainChargeName is null
            </when>
            <when test="mainChargeName == '其它'.toString()">
                and ( mainChargeName not in
                (
                SELECT mainChargeName FROM
                (
                SELECT mainChargeName,sum
                FROM(
                SELECT COUNT(*) AS sum,mainChargeName
                FROM `cases_query_tmp` cqt
                where query_id=#{queryId}
                <choose>
                    <when test="flag == '已结案'.toString() or flag == '已結案'.toString()">AND cqt.issueStatus <![CDATA[ >= ]]> 7 and cqt.issueStatus <![CDATA[ <= ]]> 10</when>

                    <when test="flag == '未结案'.toString() or flag == '未結案'.toString()">
                        AND (cqt.issueStatus <![CDATA[<]]> 7 or cqt.issueStatus = '12' or cqt.issueStatus = '11' or cqt.issueStatus = '22')
                    </when>
                </choose>

                <choose>
                    <when test="issueClassificationDesc != null and issueClassificationDesc != '' and issueClassificationDesc != '空'.toString()">
                        and issueClassificationDesc = #{issueClassificationDesc}
                    </when>
                    <when test="issueClassificationDesc  == '空'.toString() ">
                        and (issueClassificationDesc is null or issueClassificationDesc = '' )
                    </when>
                    <when test="issueClassificationDesc == '其它'.toString()">
                        and ( issueClassificationDesc not in
                        (
                        SELECT issueClassificationDesc FROM
                        (
                        SELECT issueClassificationDesc,sum
                        FROM(
                        SELECT COUNT(*) AS sum,issueClassificationDesc
                        FROM `cases_query_tmp` cqt
                        where query_id=#{queryId}
                        <choose>
                            <when test="flag == '已结案'.toString() or flag == '已結案'.toString()">
                                AND cqt.issueStatus <![CDATA[ >= ]]> 7 and cqt.issueStatus <![CDATA[ <= ]]> 10
                            </when>
                            <when test="flag == '未结案'.toString() or flag == '未結案'.toString()">
                                AND (cqt.issueStatus <![CDATA[<]]> 7 or cqt.issueStatus = '12' or cqt.issueStatus = '11' or cqt.issueStatus = '22')
                            </when>
                        </choose>
                        <choose>
                            <when test="customerName != null and customerName != '' and customerName != '空'.toString()">
                                and customerName = #{customerName}
                            </when>
                            <when test="customerName == null ">
                                and customerName is null
                            </when>
                        </choose>

                        <choose>
                            <when test="erpSystemCode != null and erpSystemCode != '' and erpSystemCode != '空'.toString() ">
                                and erpSystemCode = #{moduleCode}
                            </when>
                            <!--<when test="erpSystemCode == '空'.toString() ">
                                and (erpSystemCode is null or erpSystemCode = '')
                            </when>-->
                        </choose>

                        <choose>
                            <when test="productCode != null and productCode != '' and productCode != '空'.toString()">
                                and productName = #{productCode}
                            </when>
                            <!--<when test="productCode == '空'.toString() ">
                                and productName is null
                            </when>-->
                        </choose>

                        <choose>
                            <when test="emergency == '紧急'.toString() or emergency == '緊急'.toString() ">
                                and emergency = 1
                            </when>
                            <when test="emergency == '不紧急'.toString() or emergency == '不緊急'.toString()  ">
                                and (emergency = 0 )
                            </when>
                        </choose>
                        <choose>
                            <when test="personalCase== '个案'.toString() or personalCase== '個案'.toString()">
                                and isPersonalCase= 'Y'
                            </when>
                            <when test="personalCase== '案件'.toString() ">
                                and (isPersonalCase= 'N' or isPersonalCase is null or isPersonalCase= ' ')
                            </when>
                        </choose>

                        <choose>
                            <when test="username != null and username != '' and username != '空'.toString() ">
                                and username = #{username}
                            </when>
                            <!--<when test="username == '空'.toString() ">
                                and username is null
                            </when>-->
                        </choose>
                        <choose>
                            <when test="nowHandlerName != null and nowHandlerName != '' and nowHandlerName != '空'.toString() ">
                                and nowHandlerName= #{nowHandlerName}
                            </when>
                            <!-- <when test="nowHandlerName == '空'.toString() ">
                                 and nowHandlerName is null
                             </when>-->
                        </choose>
                        <choose>
                            <when test="processDay != null and processDay != '' and processDay!= '空'.toString() ">
                                and processDay = #{processDay}
                            </when>
                        </choose>

                        <choose>
                            <when test="mainChargeName != null and mainChargeName != '' and mainChargeName != '空'.toString() ">
                                and mainChargeName= #{mainChargeName}
                            </when>
                            <!--<when test="mainChargeName == '空'.toString() ">
                                and mainChargeName is null
                            </when>-->
                        </choose>


                        <choose>
                            <when test="count != '0'.toString() ">
                                AND issueClassificationDesc IS NOT null
                            </when>
                        </choose>
                        GROUP BY issueClassificationDesc
                        ORDER BY sum DESC,issueClassificationDesc asc
                        ) tb1
                        LIMIT
                        <choose>
                            <when test="count != '0'.toString() ">
                                8
                            </when>
                        </choose>
                        <choose>
                            <when test="count == '0'.toString() ">
                                9
                            </when>
                        </choose>

                        ) tb4)
                        )


                    </when>
                </choose>

                <choose>
                    <when test="customerName != null and customerName != '' and customerName != '空'.toString()">
                        and customerName = #{customerName}
                    </when>
                    <when test="customerName == '空'.toString() ">
                        and customerName is null
                    </when>
                </choose>

                <choose>
                    <when test="erpSystemCode != null and erpSystemCode != '' and erpSystemCode != '空'.toString() ">
                        and erpSystemCode = #{erpSystemCode}
                    </when>
                    <!-- <when test="erpSystemCode == '空'.toString() ">
                         and (erpSystemCode is null or erpSystemCode = '')
                     </when>-->
                </choose>

                <choose>
                    <when test="productCode != null and productCode != '' and productCode != '空'.toString()">
                        and productName = #{productCode}
                    </when>
                    <!-- <when test="productCode == '空'.toString() ">
                         and productName is null
                     </when>-->
                </choose>

                <choose>
                    <when test="emergency == '紧急'.toString() or emergency == '緊急'.toString() ">
                        and emergency = 1
                    </when>
                    <when test="emergency == '不紧急'.toString() or emergency == '不緊急'.toString()  ">
                        and (emergency = 0 )
                    </when>
                </choose>
                <choose>
                    <when test="personalCase== '个案'.toString() or personalCase== '個案'.toString()">
                        and isPersonalCase= 'Y'
                    </when>
                    <when test="personalCase== '案件'.toString() ">
                        and (isPersonalCase= 'N' or isPersonalCase is null or isPersonalCase= ' ')
                    </when>
                </choose>

                <choose>
                    <when test="username != null and username != '' and username != '空'.toString() ">
                        and username = #{username}
                    </when>
                    <!--<when test="username == '空'.toString() ">
                        and username is null
                    </when>-->
                </choose>
                <choose>
                    <when test="processDay != null and processDay != '' and processDay != '空'">
                        and processDay = #{processDay}
                    </when>
                </choose>
                <choose>
                    <when test="nowHandlerName != null and nowHandlerName != '' and nowHandlerName != '空'.toString() ">
                        and nowHandlerName= #{nowHandlerName}
                    </when>
                    <!--  <when test="nowHandlerName == '空'.toString() ">
                          and nowHandlerName is null
                      </when>-->
                </choose>


                <choose>
                    <when test="count != '0'.toString() ">
                        AND mainChargeName IS NOT null
                    </when>
                </choose>
                GROUP BY mainChargeName
                ORDER BY sum DESC,mainChargeName asc
                ) tb1
                LIMIT
                <choose>
                    <when test="count != '0'.toString() ">
                        19
                    </when>
                </choose>
                <choose>
                    <when test="count == '0'.toString() ">
                        20
                    </when>
                </choose>

                ) tb4)
                )


            </when>
        </choose>
        GROUP BY customerName
        ORDER BY sum DESC,customerName asc
        ) tb1
        LIMIT 30) tb4
        WHERE tb4.customerName is null
    </select>

    <select id="getNullIssueClassificationDesc" parameterType="java.lang.String" resultType="java.lang.Integer">
        SELECT COUNT(*) FROM
        (
        SELECT * from
        (
        SELECT COUNT(*) AS sum,issueClassificationDesc
        FROM `cases_query_tmp` cqt
        where query_id=#{queryId}
        <choose>
            <when test="flag == '已结案'.toString() or flag == '已結案'.toString()">
                AND cqt.issueStatus <![CDATA[ >= ]]> 7 and cqt.issueStatus <![CDATA[ <= ]]> 10
            </when>
            <when test="flag == '未结案'.toString() or flag == '未結案'.toString()">
                AND (cqt.issueStatus <![CDATA[<]]> 7 or cqt.issueStatus = '12' or cqt.issueStatus = '11' or cqt.issueStatus = '22')
            </when>
        </choose>
        <choose>
            <when test="customerName != null and customerName != '' and customerName != '空'.toString()">
                and customerName = #{custName}
            </when>
            <when test="customerName == '空'.toString() ">
                and customerName is null
            </when>
        </choose>
        <choose>
            <when test="erpSystemCode != null and erpSystemCode != '' and erpSystemCode != '空'.toString() ">
                and erpSystemCode = #{erpSystemCode}
            </when>
            <when test="erpSystemCode == '空'.toString() ">
                and (erpSystemCode is null or erpSystemCode = '')
            </when>
        </choose>
        <choose>
            <when test="productCode != null and productCode != '' and productCode != '空'.toString()">
                and productName = #{productCode}
            </when>
            <when test="productCode == '空'.toString() ">
                and productName is null
            </when>
        </choose>
        <choose>
            <when test="emergency == '紧急'.toString() or emergency == '緊急'.toString() ">
                and emergency = 1
            </when>
            <when test="emergency == '不紧急'.toString() or emergency == '不緊急'.toString()  ">
                and (emergency = 0 )
            </when>
        </choose>
        <choose>
            <when test="personalCase== '个案'.toString() or personalCase== '個案'.toString()">
                and isPersonalCase= 'Y'
            </when>
            <when test="personalCase== '案件'.toString() ">
                and (isPersonalCase= 'N' or isPersonalCase is null or isPersonalCase= ' ')
            </when>
        </choose>
        <choose>
            <when test="username != null and username != '' and username != '空'.toString() ">
                and username = #{username}
            </when>
            <when test="username == '空'.toString() ">
                and username is null
            </when>
        </choose>
        <choose>
            <when test="nowHandlerName != null and nowHandlerName != '' and nowHandlerName != '空'.toString() ">
                and nowHandlerName= #{nowHandlerName}
            </when>
            <when test="nowHandlerName == '空'.toString() ">
                and nowHandlerName is null
            </when>
        </choose>
        <choose>
            <when test="processDay != null and processDay != '' and processDay != '空'.toString()">
                and processDay = #{processDay}
            </when>
        </choose>

        <choose>
            <when test="mainChargeName != null and mainChargeName != '' and mainChargeName != '空'.toString() and mainChargeName != '其它'.toString()">
                and mainChargeName = #{mainChargeName}
            </when>
            <when test="mainChargeName == '空'.toString() ">
                and mainChargeName is null
            </when>
            <when test="mainChargeName == '其它'.toString()">
                and ( mainChargeName not in
                (
                SELECT mainChargeName FROM
                (
                SELECT mainChargeName,sum
                FROM(
                SELECT COUNT(*) AS sum,mainChargeName
                FROM `cases_query_tmp` cqt
                where query_id=#{queryId}
                <choose>
                    <when test="flag == '已结案'.toString() or flag == '已結案'.toString()">AND cqt.issueStatus <![CDATA[ >= ]]> 7 and cqt.issueStatus <![CDATA[ <= ]]> 10</when>

                    <when test="flag == '未结案'.toString() or flag == '未結案'.toString()">
                        AND (cqt.issueStatus <![CDATA[<]]> 7 or cqt.issueStatus = '12' or cqt.issueStatus = '11' or cqt.issueStatus = '22')
                    </when>
                </choose>

                <choose>
                    <when test="issueClassificationDesc != null and issueClassificationDesc != '' and issueClassificationDesc != '空'.toString()">
                        and issueClassificationDesc = #{issueClassificationDesc}
                    </when>
                    <when test="issueClassificationDesc  == '空'.toString() ">
                        and (issueClassificationDesc is null or issueClassificationDesc = '' )
                    </when>
                    <when test="issueClassificationDesc == '其它'.toString()">
                        and ( issueClassificationDesc not in
                        (
                        SELECT issueClassificationDesc FROM
                        (
                        SELECT issueClassificationDesc,sum
                        FROM(
                        SELECT COUNT(*) AS sum,issueClassificationDesc
                        FROM `cases_query_tmp` cqt
                        where query_id=#{queryId}
                        <choose>
                            <when test="flag == '已结案'.toString() or flag == '已結案'.toString()">
                                AND cqt.issueStatus <![CDATA[ >= ]]> 7 and cqt.issueStatus <![CDATA[ <= ]]> 10
                            </when>
                            <when test="flag == '未结案'.toString() or flag == '未結案'.toString()">
                                AND (cqt.issueStatus <![CDATA[<]]> 7 or cqt.issueStatus = '12' or cqt.issueStatus = '11' or cqt.issueStatus = '22')
                            </when>
                        </choose>
                        <choose>
                            <when test="customerName != null and customerName != '' and customerName != '空'.toString()">
                                and customerName = #{customerName}
                            </when>
                            <when test="customerName == null ">
                                and customerName is null
                            </when>
                        </choose>

                        <choose>
                            <when test="erpSystemCode != null and erpSystemCode != '' and erpSystemCode != '空'.toString() ">
                                and erpSystemCode = #{moduleCode}
                            </when>
                            <!--<when test="erpSystemCode == '空'.toString() ">
                                and (erpSystemCode is null or erpSystemCode = '')
                            </when>-->
                        </choose>

                        <choose>
                            <when test="productCode != null and productCode != '' and productCode != '空'.toString()">
                                and productName = #{productCode}
                            </when>
                            <!--<when test="productCode == '空'.toString() ">
                                and productName is null
                            </when>-->
                        </choose>

                        <choose>
                            <when test="emergency == '紧急'.toString() or emergency == '緊急'.toString() ">
                                and emergency = 1
                            </when>
                            <when test="emergency == '不紧急'.toString() or emergency == '不緊急'.toString()  ">
                                and (emergency = 0 )
                            </when>
                        </choose>
                        <choose>
                            <when test="personalCase== '个案'.toString() or personalCase== '個案'.toString()">
                                and isPersonalCase= 'Y'
                            </when>
                            <when test="personalCase== '案件'.toString() ">
                                and (isPersonalCase= 'N' or isPersonalCase is null or isPersonalCase= ' ')
                            </when>
                        </choose>

                        <choose>
                            <when test="username != null and username != '' and username != '空'.toString() ">
                                and username = #{username}
                            </when>
                            <!--<when test="username == '空'.toString() ">
                                and username is null
                            </when>-->
                        </choose>
                        <choose>
                            <when test="nowHandlerName != null and nowHandlerName != '' and nowHandlerName != '空'.toString() ">
                                and nowHandlerName= #{nowHandlerName}
                            </when>
                            <!-- <when test="nowHandlerName == '空'.toString() ">
                                 and nowHandlerName is null
                             </when>-->
                        </choose>
                        <choose>
                            <when test="processDay != null and processDay != '' and processDay!= '空'.toString() ">
                                and processDay = #{processDay}
                            </when>
                        </choose>

                        <choose>
                            <when test="mainChargeName != null and mainChargeName != '' and mainChargeName != '空'.toString() ">
                                and mainChargeName= #{mainChargeName}
                            </when>
                            <!--<when test="mainChargeName == '空'.toString() ">
                                and mainChargeName is null
                            </when>-->
                        </choose>


                        <choose>
                            <when test="count != '0'.toString() ">
                                AND issueClassificationDesc IS NOT null
                            </when>
                        </choose>
                        GROUP BY issueClassificationDesc
                        ORDER BY sum DESC,issueClassificationDesc asc
                        ) tb1
                        LIMIT
                        <choose>
                            <when test="count != '0'.toString() ">
                                8
                            </when>
                        </choose>
                        <choose>
                            <when test="count == '0'.toString() ">
                                9
                            </when>
                        </choose>

                        ) tb4)
                        )


                    </when>
                </choose>

                <choose>
                    <when test="customerName != null and customerName != '' and customerName != '空'.toString()">
                        and customerName = #{customerName}
                    </when>
                    <when test="customerName == '空'.toString() ">
                        and customerName is null
                    </when>
                </choose>

                <choose>
                    <when test="erpSystemCode != null and erpSystemCode != '' and erpSystemCode != '空'.toString() ">
                        and erpSystemCode = #{erpSystemCode}
                    </when>
                    <!-- <when test="erpSystemCode == '空'.toString() ">
                         and (erpSystemCode is null or erpSystemCode = '')
                     </when>-->
                </choose>

                <choose>
                    <when test="productCode != null and productCode != '' and productCode != '空'.toString()">
                        and productName = #{productCode}
                    </when>
                    <!-- <when test="productCode == '空'.toString() ">
                         and productName is null
                     </when>-->
                </choose>

                <choose>
                    <when test="emergency == '紧急'.toString() or emergency == '緊急'.toString() ">
                        and emergency = 1
                    </when>
                    <when test="emergency == '不紧急'.toString() or emergency == '不緊急'.toString()  ">
                        and (emergency = 0 )
                    </when>
                </choose>
                <choose>
                    <when test="personalCase== '个案'.toString() or personalCase== '個案'.toString()">
                        and isPersonalCase= 'Y'
                    </when>
                    <when test="personalCase== '案件'.toString() ">
                        and (isPersonalCase= 'N' or isPersonalCase is null or isPersonalCase= ' ')
                    </when>
                </choose>

                <choose>
                    <when test="username != null and username != '' and username != '空'.toString() ">
                        and username = #{username}
                    </when>
                    <!--<when test="username == '空'.toString() ">
                        and username is null
                    </when>-->
                </choose>
                <choose>
                    <when test="processDay != null and processDay != '' and processDay != '空'">
                        and processDay = #{processDay}
                    </when>
                </choose>
                <choose>
                    <when test="nowHandlerName != null and nowHandlerName != '' and nowHandlerName != '空'.toString() ">
                        and nowHandlerName= #{nowHandlerName}
                    </when>
                    <!--  <when test="nowHandlerName == '空'.toString() ">
                          and nowHandlerName is null
                      </when>-->
                </choose>


                <choose>
                    <when test="count != '0'.toString() ">
                        AND mainChargeName IS NOT null
                    </when>
                </choose>
                GROUP BY mainChargeName
                ORDER BY sum DESC,mainChargeName asc
                ) tb1
                LIMIT
                <choose>
                    <when test="count != '0'.toString() ">
                        19
                    </when>
                </choose>
                <choose>
                    <when test="count == '0'.toString() ">
                        20
                    </when>
                </choose>

                ) tb4)
                )


            </when>
        </choose>
        GROUP BY issueClassificationDesc
        ORDER BY sum DESC,issueClassificationDesc asc
        ) tb3
        LIMIT 9
        ) tb4 WHERE tb4.issueClassificationDesc is null
    </select>

    <select id="getNullErpSystemCode" parameterType="java.lang.String" resultType="java.lang.Integer">
        SELECT COUNT(*) FROM
        (
        SELECT * from
        (
        SELECT COUNT(*) AS sum,CASE WHEN erpSystemCode = '' OR erpSystemCode is NULL THEN NULL ELSE erpSystemCode END as erpSystemCode
        FROM `cases_query_tmp` cqt
        where query_id=#{queryId}
        <choose>
            <when test="flag == '已结案'.toString() or flag == '已結案'.toString()">
                AND cqt.issueStatus <![CDATA[ >= ]]> 7 and cqt.issueStatus <![CDATA[ <= ]]> 10
            </when>
            <when test="flag == '未结案'.toString() or flag == '未結案'.toString()">
                AND (cqt.issueStatus <![CDATA[<]]> 7 or cqt.issueStatus = '12' or cqt.issueStatus = '11' or cqt.issueStatus = '22')
            </when>
        </choose>
        <choose>
            <when test="customerName != null and customerName != '' and customerName != '空'.toString()">
                and customerName = #{customerName}
            </when>
            <when test="customerName == '空'.toString() ">
                and customerName is null
            </when>
        </choose>
        <choose>
            <when test="issueClassificationDesc != null and issueClassificationDesc != '' and issueClassificationDesc != '空'.toString()">
                and issueClassificationDesc = #{issueClassificationDesc}
            </when>
            <when test="issueClassificationDesc  == '空'.toString() ">
                and (issueClassificationDesc is null or issueClassificationDesc = '' )
            </when>
            <when test="issueClassificationDesc == '其它'.toString()">
                and ( issueClassificationDesc not in
                (
                SELECT issueClassificationDesc FROM
                (
                SELECT issueClassificationDesc,sum
                FROM(
                SELECT COUNT(*) AS sum,issueClassificationDesc
                FROM `cases_query_tmp` cqt
                where query_id=#{queryId}
                <choose>
                    <when test="flag == '已结案'.toString() or flag == '已結案'.toString()">
                        AND cqt.issueStatus <![CDATA[ >= ]]> 7 and cqt.issueStatus <![CDATA[ <= ]]> 10
                    </when>
                    <when test="flag == '未结案'.toString() or flag == '未結案'.toString()">
                        AND (cqt.issueStatus <![CDATA[<]]> 7 or cqt.issueStatus = '12' or cqt.issueStatus = '11' or cqt.issueStatus = '22')
                    </when>
                </choose>
                <choose>
                    <when test="customerName != null and customerName != '' and customerName != '空'.toString()">
                        and customerName = #{customerName}
                    </when>
                    <when test="customerName == '空'.toString() ">
                        and customerName is null
                    </when>
                </choose>

                <choose>
                    <when test="erpSystemCode != null and erpSystemCode != '' and erpSystemCode != '空'.toString() ">
                        and erpSystemCode = #{moduleCode}
                    </when>
                    <!--<when test="erpSystemCode == '空'.toString() ">
                        and (erpSystemCode is null or erpSystemCode = '')
                    </when>-->
                </choose>

                <choose>
                    <when test="productCode != null and productCode != '' and productCode != '空'.toString()">
                        and productName = #{productCode}
                    </when>
                    <!--<when test="productCode == '空'.toString() ">
                        and productName is null
                    </when>-->
                </choose>

                <choose>
                    <when test="emergency == '紧急'.toString() or emergency == '緊急'.toString() ">
                        and emergency = 1
                    </when>
                    <when test="emergency == '不紧急'.toString() or emergency == '不緊急'.toString()  ">
                        and (emergency = 0 )
                    </when>
                </choose>
                <choose>
                    <when test="personalCase== '个案'.toString() or personalCase== '個案'.toString()">
                        and isPersonalCase= 'Y'
                    </when>
                    <when test="personalCase== '案件'.toString() ">
                        and (isPersonalCase= 'N' or isPersonalCase is null or isPersonalCase= ' ')
                    </when>
                </choose>

                <choose>
                    <when test="username != null and username != '' and username != '空'.toString() ">
                        and username = #{username}
                    </when>
                    <!--<when test="username == '空'.toString() ">
                        and username is null
                    </when>-->
                </choose>
                <choose>
                    <when test="nowHandlerName != null and nowHandlerName != '' and nowHandlerName != '空'.toString() ">
                        and nowHandlerName= #{nowHandlerName}
                    </when>
                    <!-- <when test="nowHandlerName == '空'.toString() ">
                         and nowHandlerName is null
                     </when>-->
                </choose>
                <choose>
                    <when test="processDay != null and processDay != '' and processDay!= '空'.toString() ">
                        and processDay = #{processDay}
                    </when>
                </choose>

                <choose>
                    <when test="mainChargeName != null and mainChargeName != '' and mainChargeName != '空'.toString() and mainChargeName != '其它'.toString()">
                        and mainChargeName = #{mainChargeName}
                    </when>
                    <when test="mainChargeName == '空'.toString() ">
                        and mainChargeName is null
                    </when>
                    <when test="mainChargeName == '其它'.toString()">
                        and ( mainChargeName not in
                        (
                        SELECT mainChargeName FROM
                        (
                        SELECT mainChargeName,sum
                        FROM(
                        SELECT COUNT(*) AS sum,mainChargeName
                        FROM `cases_query_tmp` cqt
                        where query_id=#{queryId}
                        <choose>
                            <when test="flag == '已结案'.toString() or flag == '已結案'.toString()">AND cqt.issueStatus <![CDATA[ >= ]]> 7 and cqt.issueStatus <![CDATA[ <= ]]> 10</when>

                            <when test="flag == '未结案'.toString() or flag == '未結案'.toString()">
                                AND (cqt.issueStatus <![CDATA[<]]> 7 or cqt.issueStatus = '12' or cqt.issueStatus = '11' or cqt.issueStatus = '22')
                            </when>
                        </choose>

                        <choose>
                            <when test="issueClassificationDesc != null and issueClassificationDesc != '' and issueClassificationDesc != '空'.toString()">
                                and issueClassificationDesc = #{issueClassificationDesc}
                            </when>
                            <when test="issueClassificationDesc  == '空'.toString() ">
                                and (issueClassificationDesc is null or issueClassificationDesc = '' )
                            </when>
                            <when test="issueClassificationDesc == '其它'.toString()">
                                and ( issueClassificationDesc not in
                                (
                                SELECT issueClassificationDesc FROM
                                (
                                SELECT issueClassificationDesc,sum
                                FROM(
                                SELECT COUNT(*) AS sum,issueClassificationDesc
                                FROM `cases_query_tmp` cqt
                                where query_id=#{queryId}
                                <choose>
                                    <when test="flag == '已结案'.toString() or flag == '已結案'.toString()">
                                        AND cqt.issueStatus <![CDATA[ >= ]]> 7 and cqt.issueStatus <![CDATA[ <= ]]> 10
                                    </when>
                                    <when test="flag == '未结案'.toString() or flag == '未結案'.toString()">
                                        AND (cqt.issueStatus <![CDATA[<]]> 7 or cqt.issueStatus = '12' or cqt.issueStatus = '11' or cqt.issueStatus = '22')
                                    </when>
                                </choose>
                                <choose>
                                    <when test="customerName != null and customerName != '' and customerName != '空'.toString()">
                                        and customerName = #{customerName}
                                    </when>
                                    <when test="customerName == null ">
                                        and customerName is null
                                    </when>
                                </choose>

                                <choose>
                                    <when test="erpSystemCode != null and erpSystemCode != '' and erpSystemCode != '空'.toString() ">
                                        and erpSystemCode = #{moduleCode}
                                    </when>
                                    <!--<when test="erpSystemCode == '空'.toString() ">
                                        and (erpSystemCode is null or erpSystemCode = '')
                                    </when>-->
                                </choose>

                                <choose>
                                    <when test="productCode != null and productCode != '' and productCode != '空'.toString()">
                                        and productName = #{productCode}
                                    </when>
                                    <!--<when test="productCode == '空'.toString() ">
                                        and productName is null
                                    </when>-->
                                </choose>

                                <choose>
                                    <when test="emergency == '紧急'.toString() or emergency == '緊急'.toString() ">
                                        and emergency = 1
                                    </when>
                                    <when test="emergency == '不紧急'.toString() or emergency == '不緊急'.toString()  ">
                                        and (emergency = 0 )
                                    </when>
                                </choose>
                                <choose>
                                    <when test="personalCase== '个案'.toString() or personalCase== '個案'.toString()">
                                        and isPersonalCase= 'Y'
                                    </when>
                                    <when test="personalCase== '案件'.toString() ">
                                        and (isPersonalCase= 'N' or isPersonalCase is null or isPersonalCase= ' ')
                                    </when>
                                </choose>

                                <choose>
                                    <when test="username != null and username != '' and username != '空'.toString() ">
                                        and username = #{username}
                                    </when>
                                    <!--<when test="username == '空'.toString() ">
                                        and username is null
                                    </when>-->
                                </choose>
                                <choose>
                                    <when test="nowHandlerName != null and nowHandlerName != '' and nowHandlerName != '空'.toString() ">
                                        and nowHandlerName= #{nowHandlerName}
                                    </when>
                                    <!-- <when test="nowHandlerName == '空'.toString() ">
                                         and nowHandlerName is null
                                     </when>-->
                                </choose>
                                <choose>
                                    <when test="processDay != null and processDay != '' and processDay!= '空'.toString() ">
                                        and processDay = #{processDay}
                                    </when>
                                </choose>

                                <choose>
                                    <when test="mainChargeName != null and mainChargeName != '' and mainChargeName != '空'.toString() ">
                                        and mainChargeName= #{mainChargeName}
                                    </when>
                                    <!--<when test="mainChargeName == '空'.toString() ">
                                        and mainChargeName is null
                                    </when>-->
                                </choose>


                                <choose>
                                    <when test="count != '0'.toString() ">
                                        AND issueClassificationDesc IS NOT null
                                    </when>
                                </choose>
                                GROUP BY issueClassificationDesc
                                ORDER BY sum DESC,issueClassificationDesc asc
                                ) tb1
                                LIMIT
                                <choose>
                                    <when test="count != '0'.toString() ">
                                        8
                                    </when>
                                </choose>
                                <choose>
                                    <when test="count == '0'.toString() ">
                                        9
                                    </when>
                                </choose>

                                ) tb4)
                                )


                            </when>
                        </choose>

                        <choose>
                            <when test="customerName != null and customerName != '' and customerName != '空'.toString()">
                                and customerName = #{customerName}
                            </when>
                            <when test="customerName == '空'.toString() ">
                                and customerName is null
                            </when>
                        </choose>

                        <choose>
                            <when test="erpSystemCode != null and erpSystemCode != '' and erpSystemCode != '空'.toString() ">
                                and erpSystemCode = #{erpSystemCode}
                            </when>
                            <!-- <when test="erpSystemCode == '空'.toString() ">
                                 and (erpSystemCode is null or erpSystemCode = '')
                             </when>-->
                        </choose>

                        <choose>
                            <when test="productCode != null and productCode != '' and productCode != '空'.toString()">
                                and productName = #{productCode}
                            </when>
                            <!-- <when test="productCode == '空'.toString() ">
                                 and productName is null
                             </when>-->
                        </choose>

                        <choose>
                            <when test="emergency == '紧急'.toString() or emergency == '緊急'.toString() ">
                                and emergency = 1
                            </when>
                            <when test="emergency == '不紧急'.toString() or emergency == '不緊急'.toString()  ">
                                and (emergency = 0 )
                            </when>
                        </choose>
                        <choose>
                            <when test="personalCase== '个案'.toString() or personalCase== '個案'.toString()">
                                and isPersonalCase= 'Y'
                            </when>
                            <when test="personalCase== '案件'.toString() ">
                                and (isPersonalCase= 'N' or isPersonalCase is null or isPersonalCase= ' ')
                            </when>
                        </choose>

                        <choose>
                            <when test="username != null and username != '' and username != '空'.toString() ">
                                and username = #{username}
                            </when>
                            <!--<when test="username == '空'.toString() ">
                                and username is null
                            </when>-->
                        </choose>
                        <choose>
                            <when test="processDay != null and processDay != '' and processDay != '空'">
                                and processDay = #{processDay}
                            </when>
                        </choose>
                        <choose>
                            <when test="nowHandlerName != null and nowHandlerName != '' and nowHandlerName != '空'.toString() ">
                                and nowHandlerName= #{nowHandlerName}
                            </when>
                            <!--  <when test="nowHandlerName == '空'.toString() ">
                                  and nowHandlerName is null
                              </when>-->
                        </choose>


                        <choose>
                            <when test="count != '0'.toString() ">
                                AND mainChargeName IS NOT null
                            </when>
                        </choose>
                        GROUP BY mainChargeName
                        ORDER BY sum DESC,mainChargeName asc
                        ) tb1
                        LIMIT
                        <choose>
                            <when test="count != '0'.toString() ">
                                19
                            </when>
                        </choose>
                        <choose>
                            <when test="count == '0'.toString() ">
                                20
                            </when>
                        </choose>

                        ) tb4)
                        )


                    </when>
                </choose>


                <choose>
                    <when test="count != '0'.toString() ">
                        AND issueClassificationDesc IS NOT null
                    </when>
                </choose>
                GROUP BY issueClassificationDesc
                ORDER BY sum DESC,issueClassificationDesc asc
                ) tb1
                LIMIT
                <choose>
                    <when test="count != '0'.toString() ">
                        8
                    </when>
                </choose>
                <choose>
                    <when test="count == '0'.toString() ">
                        9
                    </when>
                </choose>

                ) tb4)
                )


            </when>
        </choose>
        <choose>
            <when test="productCode != null and productCode != '' and productCode != '空'.toString()">
                and productName = #{productCode}
            </when>
            <when test="productCode == '空'.toString() ">
                and productName is null
            </when>
        </choose>
        <choose>
            <when test="emergency == '紧急'.toString() or emergency == '緊急'.toString() ">
                and emergency = 1
            </when>
            <when test="emergency == '不紧急'.toString() or emergency == '不緊急'.toString()  ">
                and (emergency = 0 )
            </when>
        </choose>
        <choose>
            <when test="personalCase== '个案'.toString() or personalCase== '個案'.toString()">
                and isPersonalCase= 'Y'
            </when>
            <when test="personalCase== '案件'.toString() ">
                and (isPersonalCase= 'N' or isPersonalCase is null or isPersonalCase= ' ')
            </when>
        </choose>
        <choose>
            <when test="username != null and username != '' and username != '空'.toString() ">
                and username = #{username}
            </when>
            <when test="username == '空'.toString() ">
                and username is null
            </when>
        </choose>
        <choose>
            <when test="nowHandlerName != null and nowHandlerName != '' and nowHandlerName != '空'.toString() ">
                and nowHandlerName= #{nowHandlerName}
            </when>
            <when test="nowHandlerName == '空'.toString() ">
                and nowHandlerName is null
            </when>
        </choose>
        <choose>
            <when test="processDay != null and processDay != '' and processDay!= '空'.toString() ">
                and processDay = #{processDay}
            </when>
        </choose>

        <choose>
            <when test="mainChargeName != null and mainChargeName != '' and mainChargeName != '空'.toString() and mainChargeName != '其它'.toString()">
                and mainChargeName = #{mainChargeName}
            </when>
            <when test="mainChargeName == == '空'.toString() ">
                and mainChargeName is null
            </when>
            <when test="mainChargeName == '其它'.toString()">
                and ( mainChargeName not in
                (
                SELECT mainChargeName FROM
                (
                SELECT mainChargeName,sum
                FROM(
                SELECT COUNT(*) AS sum,mainChargeName
                FROM `cases_query_tmp` cqt
                where query_id=#{queryId}
                <choose>
                    <when test="flag == '已结案'.toString() or flag == '已結案'.toString()">AND cqt.issueStatus <![CDATA[ >= ]]> 7 and cqt.issueStatus <![CDATA[ <= ]]> 10</when>

                    <when test="flag == '未结案'.toString() or flag == '未結案'.toString()">
                        AND (cqt.issueStatus <![CDATA[<]]> 7 or cqt.issueStatus = '12' or cqt.issueStatus = '11' or cqt.issueStatus = '22')
                    </when>
                </choose>

                <choose>
                    <when test="issueClassificationDesc != null and issueClassificationDesc != '' and issueClassificationDesc != '空'.toString()">
                        and issueClassificationDesc = #{issueClassificationDesc}
                    </when>
                    <when test="issueClassificationDesc  == null ">
                        and (issueClassificationDesc is null or issueClassificationDesc = '' )
                    </when>
                    <when test="issueClassificationDesc == '其它'.toString()">
                        and ( issueClassificationDesc not in
                        (
                        SELECT issueClassificationDesc FROM
                        (
                        SELECT issueClassificationDesc,sum
                        FROM(
                        SELECT COUNT(*) AS sum,issueClassificationDesc
                        FROM `cases_query_tmp` cqt
                        where query_id=#{queryId}
                        <choose>
                            <when test="flag == '已结案'.toString() or flag == '已結案'.toString()">
                                AND cqt.issueStatus <![CDATA[ >= ]]> 7 and cqt.issueStatus <![CDATA[ <= ]]> 10
                            </when>
                            <when test="flag == '未结案'.toString() or flag == '未結案'.toString()">
                                AND (cqt.issueStatus <![CDATA[<]]> 7 or cqt.issueStatus = '12' or cqt.issueStatus = '11' or cqt.issueStatus = '22')
                            </when>
                        </choose>
                        <choose>
                            <when test="customerName != null and customerName != '' and customerName != '空'.toString()">
                                and customerName = #{customerName}
                            </when>
                            <when test="customerName == '空'.toString() ">
                                and customerName is null
                            </when>
                        </choose>

                        <choose>
                            <when test="erpSystemCode != null and erpSystemCode != '' and erpSystemCode != '空'.toString() ">
                                and erpSystemCode = #{moduleCode}
                            </when>
                            <!--<when test="erpSystemCode == '空'.toString() ">
                                and (erpSystemCode is null or erpSystemCode = '')
                            </when>-->
                        </choose>

                        <choose>
                            <when test="productCode != null and productCode != '' and productCode != '空'.toString()">
                                and productName = #{productCode}
                            </when>
                            <!--<when test="productCode == '空'.toString() ">
                                and productName is null
                            </when>-->
                        </choose>

                        <choose>
                            <when test="emergency == '紧急'.toString() or emergency == '緊急'.toString() ">
                                and emergency = 1
                            </when>
                            <when test="emergency == '不紧急'.toString() or emergency == '不緊急'.toString()  ">
                                and (emergency = 0 )
                            </when>
                        </choose>
                        <choose>
                            <when test="personalCase== '个案'.toString() or personalCase== '個案'.toString()">
                                and isPersonalCase= 'Y'
                            </when>
                            <when test="personalCase== '案件'.toString() ">
                                and (isPersonalCase= 'N' or isPersonalCase is null or isPersonalCase= ' ')
                            </when>
                        </choose>

                        <choose>
                            <when test="username != null and username != '' and username != '空'.toString() ">
                                and username = #{username}
                            </when>
                            <!--<when test="username == '空'.toString() ">
                                and username is null
                            </when>-->
                        </choose>
                        <choose>
                            <when test="nowHandlerName != null and nowHandlerName != '' and nowHandlerName != '空'.toString() ">
                                and nowHandlerName= #{nowHandlerName}
                            </when>
                            <!-- <when test="nowHandlerName == '空'.toString() ">
                                 and nowHandlerName is null
                             </when>-->
                        </choose>
                        <choose>
                            <when test="processDay != null and processDay != '' and processDay!= '空'.toString() ">
                                and processDay = #{processDay}
                            </when>
                        </choose>

                        <choose>
                            <when test="mainChargeName != null and mainChargeName != '' and mainChargeName != '空'.toString() ">
                                and mainChargeName= #{mainChargeName}
                            </when>
                            <!--<when test="mainChargeName == '空'.toString() ">
                                and mainChargeName is null
                            </when>-->
                        </choose>


                        <choose>
                            <when test="count != '0'.toString() ">
                                AND issueClassificationDesc IS NOT null
                            </when>
                        </choose>
                        GROUP BY issueClassificationDesc
                        ORDER BY sum DESC,issueClassificationDesc asc
                        ) tb1
                        LIMIT
                        <choose>
                            <when test="count != '0'.toString() ">
                                8
                            </when>
                        </choose>
                        <choose>
                            <when test="count == '0'.toString() ">
                                9
                            </when>
                        </choose>

                        ) tb4)
                        )


                    </when>
                </choose>

                <choose>
                    <when test="customerName != null and customerName != '' and customerName != '空'.toString()">
                        and customerName = #{customerName}
                    </when>
                    <when test="customerName == '空'.toString() ">
                        and customerName is null
                    </when>
                </choose>

                <choose>
                    <when test="erpSystemCode != null and erpSystemCode != '' and erpSystemCode != '空'.toString() ">
                        and erpSystemCode = #{erpSystemCode}
                    </when>
                    <!-- <when test="erpSystemCode == '空'.toString() ">
                         and (erpSystemCode is null or erpSystemCode = '')
                     </when>-->
                </choose>

                <choose>
                    <when test="productCode != null and productCode != '' and productCode != '空'.toString()">
                        and productName = #{productCode}
                    </when>
                    <!-- <when test="productCode == '空'.toString() ">
                         and productName is null
                     </when>-->
                </choose>

                <choose>
                    <when test="emergency == '紧急'.toString() or emergency == '緊急'.toString() ">
                        and emergency = 1
                    </when>
                    <when test="emergency == '不紧急'.toString() or emergency == '不緊急'.toString()  ">
                        and (emergency = 0 )
                    </when>
                </choose>
                <choose>
                    <when test="personalCase== '个案'.toString() or personalCase== '個案'.toString()">
                        and isPersonalCase= 'Y'
                    </when>
                    <when test="personalCase== '案件'.toString() ">
                        and (isPersonalCase= 'N' or isPersonalCase is null or isPersonalCase= ' ')
                    </when>
                </choose>

                <choose>
                    <when test="username != null and username != '' and username != '空'.toString() ">
                        and username = #{username}
                    </when>
                    <!--<when test="username == '空'.toString() ">
                        and username is null
                    </when>-->
                </choose>
                <choose>
                    <when test="processDay != null and processDay != '' and processDay != '空'">
                        and processDay = #{processDay}
                    </when>
                </choose>
                <choose>
                    <when test="nowHandlerName != null and nowHandlerName != '' and nowHandlerName != '空'.toString() ">
                        and nowHandlerName= #{nowHandlerName}
                    </when>
                    <!--  <when test="nowHandlerName == '空'.toString() ">
                          and nowHandlerName is null
                      </when>-->
                </choose>


                <choose>
                    <when test="count != '0'.toString() ">
                        AND mainChargeName IS NOT null
                    </when>
                </choose>
                GROUP BY mainChargeName
                ORDER BY sum DESC,mainChargeName asc
                ) tb1
                LIMIT
                <choose>
                    <when test="count != '0'.toString() ">
                        19
                    </when>
                </choose>
                <choose>
                    <when test="count == '0'.toString() ">
                        20
                    </when>
                </choose>

                ) tb4)
                )


            </when>
        </choose>
        GROUP BY cqt.erpSystemCode
        ORDER BY sum DESC,cqt.erpSystemCode asc
        ) tb1
        LIMIT 9)tb4 WHERE tb4.erpSystemCode is null
    </select>

    <select id="getNullUsername" parameterType="java.lang.String" resultType="java.lang.Integer">
        SELECT COUNT(*) FROM
        (
        SELECT * from
        (
        SELECT COUNT(*) AS sum,username
        FROM `cases_query_tmp` cqt
        where query_id=#{queryId}
        <choose>
            <when test="flag == '已结案'.toString() or flag == '已結案'.toString()">
                AND cqt.issueStatus <![CDATA[ >= ]]> 7 and cqt.issueStatus <![CDATA[ <= ]]> 10
            </when>
            <when test="flag == '未结案'.toString() or flag == '未結案'.toString()">
                AND (cqt.issueStatus <![CDATA[<]]> 7 or cqt.issueStatus = '12' or cqt.issueStatus = '11' or cqt.issueStatus = '22')
            </when>
        </choose>
        <choose>
            <when test="customerName != null and customerName != '' and customerName != '空'.toString()">
                and customerName = #{customerName}
            </when>
            <when test="customerName == '空'.toString() ">
                and customerName is null
            </when>
        </choose>
        <choose>
            <when test="issueClassificationDesc != null and issueClassificationDesc != '' and issueClassificationDesc != '空'.toString()">
                and issueClassificationDesc = #{issueClassificationDesc}
            </when>
            <when test="issueClassificationDesc  == '空'.toString() ">
                and (issueClassificationDesc is null or issueClassificationDesc = '' )
            </when>
            <when test="issueClassificationDesc == '其它'.toString()">
                and ( issueClassificationDesc not in
                (
                SELECT issueClassificationDesc FROM
                (
                SELECT issueClassificationDesc,sum
                FROM(
                SELECT COUNT(*) AS sum,issueClassificationDesc
                FROM `cases_query_tmp` cqt
                where query_id=#{queryId}
                <choose>
                    <when test="flag == '已结案'.toString() or flag == '已結案'.toString()">
                        AND cqt.issueStatus <![CDATA[ >= ]]> 7 and cqt.issueStatus <![CDATA[ <= ]]> 10
                    </when>
                    <when test="flag == '未结案'.toString() or flag == '未結案'.toString()">
                        AND (cqt.issueStatus <![CDATA[<]]> 7 or cqt.issueStatus = '12' or cqt.issueStatus = '11' or cqt.issueStatus = '22')
                    </when>
                </choose>
                <choose>
                    <when test="customerName != null and customerName != '' and customerName != '空'.toString()">
                        and customerName = #{customerName}
                    </when>
                    <when test="customerName == '空'.toString() ">
                        and customerName is null
                    </when>
                </choose>

                <choose>
                    <when test="erpSystemCode != null and erpSystemCode != '' and erpSystemCode != '空'.toString() ">
                        and erpSystemCode = #{moduleCode}
                    </when>
                    <!--<when test="erpSystemCode == '空'.toString() ">
                        and (erpSystemCode is null or erpSystemCode = '')
                    </when>-->
                </choose>

                <choose>
                    <when test="productCode != null and productCode != '' and productCode != '空'.toString()">
                        and productName = #{productCode}
                    </when>
                    <!--<when test="productCode == '空'.toString() ">
                        and productName is null
                    </when>-->
                </choose>

                <choose>
                    <when test="emergency == '紧急'.toString() or emergency == '緊急'.toString() ">
                        and emergency = 1
                    </when>
                    <when test="emergency == '不紧急'.toString() or emergency == '不緊急'.toString()  ">
                        and (emergency = 0 )
                    </when>
                </choose>
                <choose>
                    <when test="personalCase== '个案'.toString() or personalCase== '個案'.toString()">
                        and isPersonalCase= 'Y'
                    </when>
                    <when test="personalCase== '案件'.toString() ">
                        and (isPersonalCase= 'N' or isPersonalCase is null or isPersonalCase= ' ')
                    </when>
                </choose>

                <choose>
                    <when test="username != null and username != '' and username != '空'.toString() ">
                        and username = #{username}
                    </when>
                    <!--<when test="username == '空'.toString() ">
                        and username is null
                    </when>-->
                </choose>
                <choose>
                    <when test="nowHandlerName != null and nowHandlerName != '' and nowHandlerName != '空'.toString() ">
                        and nowHandlerName= #{nowHandlerName}
                    </when>
                    <!-- <when test="nowHandlerName == '空'.toString() ">
                         and nowHandlerName is null
                     </when>-->
                </choose>
                <choose>
                    <when test="processDay != null and processDay != '' and processDay!= '空'.toString() ">
                        and processDay = #{processDay}
                    </when>
                </choose>

                <choose>
                    <when test="mainChargeName != null and mainChargeName != '' and mainChargeName != '空'.toString() and mainChargeName != '其它'.toString()">
                        and mainChargeName = #{mainChargeName}
                    </when>
                    <when test="mainChargeName == '空'.toString() ">
                        and mainChargeName is null
                    </when>
                    <when test="mainChargeName == '其它'.toString()">
                        and ( mainChargeName not in
                        (
                        SELECT mainChargeName FROM
                        (
                        SELECT mainChargeName,sum
                        FROM(
                        SELECT COUNT(*) AS sum,mainChargeName
                        FROM `cases_query_tmp` cqt
                        where query_id=#{queryId}
                        <choose>
                            <when test="flag == '已结案'.toString() or flag == '已結案'.toString()">AND cqt.issueStatus <![CDATA[ >= ]]> 7 and cqt.issueStatus <![CDATA[ <= ]]> 10</when>

                            <when test="flag == '未结案'.toString() or flag == '未結案'.toString()">
                                AND (cqt.issueStatus <![CDATA[<]]> 7 or cqt.issueStatus = '12' or cqt.issueStatus = '11' or cqt.issueStatus = '22')
                            </when>
                        </choose>

                        <choose>
                            <when test="issueClassificationDesc != null and issueClassificationDesc != '' and issueClassificationDesc != '空'.toString()">
                                and issueClassificationDesc = #{issueClassificationDesc}
                            </when>
                            <when test="issueClassificationDesc  == null ">
                                and (issueClassificationDesc is null or issueClassificationDesc = '' )
                            </when>
                            <when test="issueClassificationDesc == '其它'.toString()">
                                and ( issueClassificationDesc not in
                                (
                                SELECT issueClassificationDesc FROM
                                (
                                SELECT issueClassificationDesc,sum
                                FROM(
                                SELECT COUNT(*) AS sum,issueClassificationDesc
                                FROM `cases_query_tmp` cqt
                                where query_id=#{queryId}
                                <choose>
                                    <when test="flag == '已结案'.toString() or flag == '已結案'.toString()">
                                        AND cqt.issueStatus <![CDATA[ >= ]]> 7 and cqt.issueStatus <![CDATA[ <= ]]> 10
                                    </when>
                                    <when test="flag == '未结案'.toString() or flag == '未結案'.toString()">
                                        AND (cqt.issueStatus <![CDATA[<]]> 7 or cqt.issueStatus = '12' or cqt.issueStatus = '11' or cqt.issueStatus = '22')
                                    </when>
                                </choose>
                                <choose>
                                    <when test="customerName != null and customerName != '' and customerName != '空'.toString()">
                                        and customerName = #{customerName}
                                    </when>
                                    <when test="customerName == '空'.toString() ">
                                        and customerName is null
                                    </when>
                                </choose>

                                <choose>
                                    <when test="erpSystemCode != null and erpSystemCode != '' and erpSystemCode != '空'.toString() ">
                                        and erpSystemCode = #{moduleCode}
                                    </when>
                                    <!--<when test="erpSystemCode == '空'.toString() ">
                                        and (erpSystemCode is null or erpSystemCode = '')
                                    </when>-->
                                </choose>

                                <choose>
                                    <when test="productCode != null and productCode != '' and productCode != '空'.toString()">
                                        and productName = #{productCode}
                                    </when>
                                    <!--<when test="productCode == '空'.toString() ">
                                        and productName is null
                                    </when>-->
                                </choose>

                                <choose>
                                    <when test="emergency == '紧急'.toString() or emergency == '緊急'.toString() ">
                                        and emergency = 1
                                    </when>
                                    <when test="emergency == '不紧急'.toString() or emergency == '不緊急'.toString()  ">
                                        and (emergency = 0 )
                                    </when>
                                </choose>
                                <choose>
                                    <when test="personalCase== '个案'.toString() or personalCase== '個案'.toString()">
                                        and isPersonalCase= 'Y'
                                    </when>
                                    <when test="personalCase== '案件'.toString() ">
                                        and (isPersonalCase= 'N' or isPersonalCase is null or isPersonalCase= ' ')
                                    </when>
                                </choose>

                                <choose>
                                    <when test="username != null and username != '' and username != '空'.toString() ">
                                        and username = #{username}
                                    </when>
                                    <!--<when test="username == '空'.toString() ">
                                        and username is null
                                    </when>-->
                                </choose>
                                <choose>
                                    <when test="nowHandlerName != null and nowHandlerName != '' and nowHandlerName != '空'.toString() ">
                                        and nowHandlerName= #{nowHandlerName}
                                    </when>
                                    <!-- <when test="nowHandlerName == '空'.toString() ">
                                         and nowHandlerName is null
                                     </when>-->
                                </choose>
                                <choose>
                                    <when test="processDay != null and processDay != '' and processDay!= '空'.toString() ">
                                        and processDay = #{processDay}
                                    </when>
                                </choose>

                                <choose>
                                    <when test="mainChargeName != null and mainChargeName != '' and mainChargeName != '空'.toString() ">
                                        and mainChargeName= #{mainChargeName}
                                    </when>
                                    <!--<when test="mainChargeName == '空'.toString() ">
                                        and mainChargeName is null
                                    </when>-->
                                </choose>


                                <choose>
                                    <when test="count != '0'.toString() ">
                                        AND issueClassificationDesc IS NOT null
                                    </when>
                                </choose>
                                GROUP BY issueClassificationDesc
                                ORDER BY sum DESC,issueClassificationDesc asc
                                ) tb1
                                LIMIT
                                <choose>
                                    <when test="count != '0'.toString() ">
                                        8
                                    </when>
                                </choose>
                                <choose>
                                    <when test="count == '0'.toString() ">
                                        9
                                    </when>
                                </choose>

                                ) tb4)
                                )


                            </when>
                        </choose>

                        <choose>
                            <when test="customerName != null and customerName != '' and customerName != '空'.toString()">
                                and customerName = #{customerName}
                            </when>
                            <when test="customerName == '空'.toString() ">
                                and customerName is null
                            </when>
                        </choose>

                        <choose>
                            <when test="erpSystemCode != null and erpSystemCode != '' and erpSystemCode != '空'.toString() ">
                                and erpSystemCode = #{erpSystemCode}
                            </when>
                            <!-- <when test="erpSystemCode == '空'.toString() ">
                                 and (erpSystemCode is null or erpSystemCode = '')
                             </when>-->
                        </choose>

                        <choose>
                            <when test="productCode != null and productCode != '' and productCode != '空'.toString()">
                                and productName = #{productCode}
                            </when>
                            <!-- <when test="productCode == '空'.toString() ">
                                 and productName is null
                             </when>-->
                        </choose>

                        <choose>
                            <when test="emergency == '紧急'.toString() or emergency == '緊急'.toString() ">
                                and emergency = 1
                            </when>
                            <when test="emergency == '不紧急'.toString() or emergency == '不緊急'.toString()  ">
                                and (emergency = 0 )
                            </when>
                        </choose>
                        <choose>
                            <when test="personalCase== '个案'.toString() or personalCase== '個案'.toString()">
                                and isPersonalCase= 'Y'
                            </when>
                            <when test="personalCase== '案件'.toString() ">
                                and (isPersonalCase= 'N' or isPersonalCase is null or isPersonalCase= ' ')
                            </when>
                        </choose>

                        <choose>
                            <when test="username != null and username != '' and username != '空'.toString() ">
                                and username = #{username}
                            </when>
                            <!--<when test="username == '空'.toString() ">
                                and username is null
                            </when>-->
                        </choose>
                        <choose>
                            <when test="processDay != null and processDay != '' and processDay != '空'">
                                and processDay = #{processDay}
                            </when>
                        </choose>
                        <choose>
                            <when test="nowHandlerName != null and nowHandlerName != '' and nowHandlerName != '空'.toString() ">
                                and nowHandlerName= #{nowHandlerName}
                            </when>
                            <!--  <when test="nowHandlerName == '空'.toString() ">
                                  and nowHandlerName is null
                              </when>-->
                        </choose>


                        <choose>
                            <when test="count != '0'.toString() ">
                                AND mainChargeName IS NOT null
                            </when>
                        </choose>
                        GROUP BY mainChargeName
                        ORDER BY sum DESC,mainChargeName asc
                        ) tb1
                        LIMIT
                        <choose>
                            <when test="count != '0'.toString() ">
                                19
                            </when>
                        </choose>
                        <choose>
                            <when test="count == '0'.toString() ">
                                20
                            </when>
                        </choose>

                        ) tb4)
                        )


                    </when>
                </choose>

                <choose>
                    <when test="count != '0'.toString() ">
                        AND issueClassificationDesc IS NOT null
                    </when>
                </choose>
                GROUP BY issueClassificationDesc
                ORDER BY sum DESC,issueClassificationDesc asc
                ) tb1
                LIMIT
                <choose>
                    <when test="count != '0'.toString() ">
                        8
                    </when>
                </choose>
                <choose>
                    <when test="count == '0'.toString() ">
                        9
                    </when>
                </choose>

                ) tb4)
                )


            </when>
        </choose>
        <choose>
            <when test="emergency == '紧急'.toString() or emergency == '緊急'.toString() ">
                and emergency = 1
            </when>
            <when test="emergency == '不紧急'.toString() or emergency == '不緊急'.toString()  ">
                and (emergency = 0 )
            </when>
        </choose>
        <choose>
            <when test="personalCase== '个案'.toString() or personalCase== '個案'.toString()">
                and isPersonalCase= 'Y'
            </when>
            <when test="personalCase== '案件'.toString() ">
                and (isPersonalCase= 'N' or isPersonalCase is null or isPersonalCase= ' ')
            </when>
        </choose>
        <choose>
            <when test="erpSystemCode != null and erpSystemCode != '' and erpSystemCode != '空'.toString() ">
                and erpSystemCode = #{erpSystemCode}
            </when>
            <when test="erpSystemCode == '空'.toString() ">
                and (erpSystemCode is null or erpSystemCode = '')
            </when>
        </choose>

        <choose>
            <when test="productCode != null and productCode != '' and productCode != '空'.toString()">
                and productName = #{productCode}
            </when>
            <when test="productCode == '空'.toString() ">
                and productName is null
            </when>
        </choose>
        <choose>
            <when test="nowHandlerName != null and nowHandlerName != '' and nowHandlerName != '空'.toString() ">
                and nowHandlerName= #{nowHandlerName}
            </when>
            <when test="nowHandlerName == '空'.toString() ">
                and nowHandlerName is null
            </when>
        </choose>
        <choose>
            <when test="processDay != null and processDay != '' and processDay!= '空'.toString() ">
                and processDay = #{processDay}
            </when>
        </choose>

        <choose>
            <when test="mainChargeName != null and mainChargeName != '' and mainChargeName != '空'.toString() and mainChargeName != '其它'.toString()">
                and mainChargeName = #{mainChargeName}
            </when>
            <when test="mainChargeName == '空'.toString() ">
                and mainChargeName is null
            </when>
            <when test="mainChargeName == '其它'.toString()">
                and ( mainChargeName not in
                (
                SELECT mainChargeName FROM
                (
                SELECT mainChargeName,sum
                FROM(
                SELECT COUNT(*) AS sum,mainChargeName
                FROM `cases_query_tmp` cqt
                where query_id=#{queryId}
                <choose>
                    <when test="flag == '已结案'.toString() or flag == '已結案'.toString()">AND cqt.issueStatus <![CDATA[ >= ]]> 7 and cqt.issueStatus <![CDATA[ <= ]]> 10</when>

                    <when test="flag == '未结案'.toString() or flag == '未結案'.toString()">
                        AND (cqt.issueStatus <![CDATA[<]]> 7 or cqt.issueStatus = '12' or cqt.issueStatus = '11' or cqt.issueStatus = '22')
                    </when>
                </choose>

                <choose>
                    <when test="issueClassificationDesc != null and issueClassificationDesc != '' and issueClassificationDesc != '空'.toString()">
                        and issueClassificationDesc = #{issueClassificationDesc}
                    </when>
                    <when test="issueClassificationDesc  == null ">
                        and (issueClassificationDesc is null or issueClassificationDesc = '' )
                    </when>
                    <when test="issueClassificationDesc == '其它'.toString()">
                        and ( issueClassificationDesc not in
                        (
                        SELECT issueClassificationDesc FROM
                        (
                        SELECT issueClassificationDesc,sum
                        FROM(
                        SELECT COUNT(*) AS sum,issueClassificationDesc
                        FROM `cases_query_tmp` cqt
                        where query_id=#{queryId}
                        <choose>
                            <when test="flag == '已结案'.toString() or flag == '已結案'.toString()">
                                AND cqt.issueStatus <![CDATA[ >= ]]> 7 and cqt.issueStatus <![CDATA[ <= ]]> 10
                            </when>
                            <when test="flag == '未结案'.toString() or flag == '未結案'.toString()">
                                AND (cqt.issueStatus <![CDATA[<]]> 7 or cqt.issueStatus = '12' or cqt.issueStatus = '11' or cqt.issueStatus = '22')
                            </when>
                        </choose>
                        <choose>
                            <when test="customerName != null and customerName != '' and customerName != '空'.toString()">
                                and customerName = #{customerName}
                            </when>
                            <when test="customerName == '空'.toString() ">
                                and customerName is null
                            </when>
                        </choose>

                        <choose>
                            <when test="erpSystemCode != null and erpSystemCode != '' and erpSystemCode != '空'.toString() ">
                                and erpSystemCode = #{moduleCode}
                            </when>
                            <!--<when test="erpSystemCode == '空'.toString() ">
                                and (erpSystemCode is null or erpSystemCode = '')
                            </when>-->
                        </choose>

                        <choose>
                            <when test="productCode != null and productCode != '' and productCode != '空'.toString()">
                                and productName = #{productCode}
                            </when>
                            <!--<when test="productCode == '空'.toString() ">
                                and productName is null
                            </when>-->
                        </choose>

                        <choose>
                            <when test="emergency == '紧急'.toString() or emergency == '緊急'.toString() ">
                                and emergency = 1
                            </when>
                            <when test="emergency == '不紧急'.toString() or emergency == '不緊急'.toString()  ">
                                and (emergency = 0 )
                            </when>
                        </choose>
                        <choose>
                            <when test="personalCase== '个案'.toString() or personalCase== '個案'.toString()">
                                and isPersonalCase= 'Y'
                            </when>
                            <when test="personalCase== '案件'.toString() ">
                                and (isPersonalCase= 'N' or isPersonalCase is null or isPersonalCase= ' ')
                            </when>
                        </choose>

                        <choose>
                            <when test="username != null and username != '' and username != '空'.toString() ">
                                and username = #{username}
                            </when>
                            <!--<when test="username == '空'.toString() ">
                                and username is null
                            </when>-->
                        </choose>
                        <choose>
                            <when test="nowHandlerName != null and nowHandlerName != '' and nowHandlerName != '空'.toString() ">
                                and nowHandlerName= #{nowHandlerName}
                            </when>
                            <!-- <when test="nowHandlerName == '空'.toString() ">
                                 and nowHandlerName is null
                             </when>-->
                        </choose>
                        <choose>
                            <when test="processDay != null and processDay != '' and processDay!= '空'.toString() ">
                                and processDay = #{processDay}
                            </when>
                        </choose>

                        <choose>
                            <when test="mainChargeName != null and mainChargeName != '' and mainChargeName != '空'.toString() ">
                                and mainChargeName= #{mainChargeName}
                            </when>
                            <!--<when test="mainChargeName == '空'.toString() ">
                                and mainChargeName is null
                            </when>-->
                        </choose>


                        <choose>
                            <when test="count != '0'.toString() ">
                                AND issueClassificationDesc IS NOT null
                            </when>
                        </choose>
                        GROUP BY issueClassificationDesc
                        ORDER BY sum DESC,issueClassificationDesc asc
                        ) tb1
                        LIMIT
                        <choose>
                            <when test="count != '0'.toString() ">
                                8
                            </when>
                        </choose>
                        <choose>
                            <when test="count == '0'.toString() ">
                                9
                            </when>
                        </choose>

                        ) tb4)
                        )


                    </when>
                </choose>

                <choose>
                    <when test="customerName != null and customerName != '' and customerName != '空'.toString()">
                        and customerName = #{customerName}
                    </when>
                    <when test="customerName == '空'.toString() ">
                        and customerName is null
                    </when>
                </choose>

                <choose>
                    <when test="erpSystemCode != null and erpSystemCode != '' and erpSystemCode != '空'.toString() ">
                        and erpSystemCode = #{erpSystemCode}
                    </when>
                    <!-- <when test="erpSystemCode == '空'.toString() ">
                         and (erpSystemCode is null or erpSystemCode = '')
                     </when>-->
                </choose>

                <choose>
                    <when test="productCode != null and productCode != '' and productCode != '空'.toString()">
                        and productName = #{productCode}
                    </when>
                    <!-- <when test="productCode == '空'.toString() ">
                         and productName is null
                     </when>-->
                </choose>

                <choose>
                    <when test="emergency == '紧急'.toString() or emergency == '緊急'.toString() ">
                        and emergency = 1
                    </when>
                    <when test="emergency == '不紧急'.toString() or emergency == '不緊急'.toString()  ">
                        and (emergency = 0 )
                    </when>
                </choose>
                <choose>
                    <when test="personalCase== '个案'.toString() or personalCase== '個案'.toString()">
                        and isPersonalCase= 'Y'
                    </when>
                    <when test="personalCase== '案件'.toString() ">
                        and (isPersonalCase= 'N' or isPersonalCase is null or isPersonalCase= ' ')
                    </when>
                </choose>

                <choose>
                    <when test="username != null and username != '' and username != '空'.toString() ">
                        and username = #{username}
                    </when>
                    <!--<when test="username == '空'.toString() ">
                        and username is null
                    </when>-->
                </choose>
                <choose>
                    <when test="processDay != null and processDay != '' and processDay != '空'">
                        and processDay = #{processDay}
                    </when>
                </choose>
                <choose>
                    <when test="nowHandlerName != null and nowHandlerName != '' and nowHandlerName != '空'.toString() ">
                        and nowHandlerName= #{nowHandlerName}
                    </when>
                    <!--  <when test="nowHandlerName == '空'.toString() ">
                          and nowHandlerName is null
                      </when>-->
                </choose>


                <choose>
                    <when test="count != '0'.toString() ">
                        AND mainChargeName IS NOT null
                    </when>
                </choose>
                GROUP BY mainChargeName
                ORDER BY sum DESC,mainChargeName asc
                ) tb1
                LIMIT
                <choose>
                    <when test="count != '0'.toString() ">
                        19
                    </when>
                </choose>
                <choose>
                    <when test="count == '0'.toString() ">
                        20
                    </when>
                </choose>

                ) tb4)
                )


            </when>
        </choose>
        GROUP BY username
        ORDER BY sum DESC,username asc
        ) tb1
        LIMIT 9)tb4 WHERE tb4.username is null
    </select>
    <select id="getNullNowHandlerName" parameterType="java.lang.String" resultType="java.lang.Integer">
        SELECT COUNT(*) FROM
        (
        SELECT * from
        (
        SELECT COUNT(*) AS sum, nowHandlerName
        FROM `cases_query_tmp` cqt
        where query_id=#{queryId}
        <choose>
            <when test="flag == '已结案'.toString() or flag == '已結案'.toString()">
                AND cqt.issueStatus <![CDATA[ >= ]]> 7 and cqt.issueStatus <![CDATA[ <= ]]> 10
            </when>
            <when test="flag == '未结案'.toString() or flag == '未結案'.toString()">
                AND (cqt.issueStatus <![CDATA[<]]> 7 or cqt.issueStatus = '12' or cqt.issueStatus = '11' or cqt.issueStatus = '22')
            </when>
        </choose>
        <choose>
            <when test="customerName != null and customerName != '' and customerName != '空'.toString()">
                and customerName = #{customerName}
            </when>
            <when test="customerName == '空'.toString() ">
                and customerName is null
            </when>
        </choose>
        <choose>
            <when test="issueClassificationDesc != null and issueClassificationDesc != '' and issueClassificationDesc != '空'.toString()">
                and issueClassificationDesc = #{issueClassificationDesc}
            </when>
            <when test="issueClassificationDesc  == null ">
                and (issueClassificationDesc is null or issueClassificationDesc = '' )
            </when>
            <when test="issueClassificationDesc == '其它'.toString()">
                and ( issueClassificationDesc not in
                (
                SELECT issueClassificationDesc FROM
                (
                SELECT issueClassificationDesc,sum
                FROM(
                SELECT COUNT(*) AS sum,issueClassificationDesc
                FROM `cases_query_tmp` cqt
                where query_id=#{queryId}
                <choose>
                    <when test="flag == '已结案'.toString() or flag == '已結案'.toString()">
                        AND cqt.issueStatus <![CDATA[ >= ]]> 7 and cqt.issueStatus <![CDATA[ <= ]]> 10
                    </when>
                    <when test="flag == '未结案'.toString() or flag == '未結案'.toString()">
                        AND (cqt.issueStatus <![CDATA[<]]> 7 or cqt.issueStatus = '12' or cqt.issueStatus = '11' or cqt.issueStatus = '22')
                    </when>
                </choose>
                <choose>
                    <when test="customerName != null and customerName != '' and customerName != '空'.toString()">
                        and customerName = #{customerName}
                    </when>
                    <when test="customerName == null ">
                        and customerName is null
                    </when>
                </choose>

                <choose>
                    <when test="erpSystemCode != null and erpSystemCode != '' and erpSystemCode != '空'.toString() ">
                        and erpSystemCode = #{moduleCode}
                    </when>
                    <!--<when test="erpSystemCode == '空'.toString() ">
                        and (erpSystemCode is null or erpSystemCode = '')
                    </when>-->
                </choose>

                <choose>
                    <when test="productCode != null and productCode != '' and productCode != '空'.toString()">
                        and productName = #{productCode}
                    </when>
                    <!--<when test="productCode == '空'.toString() ">
                        and productName is null
                    </when>-->
                </choose>

                <choose>
                    <when test="emergency == '紧急'.toString() or emergency == '緊急'.toString() ">
                        and emergency = 1
                    </when>
                    <when test="emergency == '不紧急'.toString() or emergency == '不緊急'.toString()  ">
                        and (emergency = 0 )
                    </when>
                </choose>
                <choose>
                    <when test="personalCase== '个案'.toString() or personalCase== '個案'.toString()">
                        and isPersonalCase= 'Y'
                    </when>
                    <when test="personalCase== '案件'.toString() ">
                        and (isPersonalCase= 'N' or isPersonalCase is null or isPersonalCase= ' ')
                    </when>
                </choose>

                <choose>
                    <when test="username != null and username != '' and username != '空'.toString() ">
                        and username = #{username}
                    </when>
                    <!--<when test="username == '空'.toString() ">
                        and username is null
                    </when>-->
                </choose>
                <choose>
                    <when test="nowHandlerName != null and nowHandlerName != '' and nowHandlerName != '空'.toString() ">
                        and nowHandlerName= #{nowHandlerName}
                    </when>
                    <!-- <when test="nowHandlerName == '空'.toString() ">
                         and nowHandlerName is null
                     </when>-->
                </choose>
                <choose>
                    <when test="processDay != null and processDay != '' and processDay!= '空'.toString() ">
                        and processDay = #{processDay}
                    </when>
                </choose>

                <choose>
                    <when test="mainChargeName != null and mainChargeName != '' and mainChargeName != '空'.toString() and mainChargeName != '其它'.toString()">
                        and mainChargeName = #{mainChargeName}
                    </when>
                    <when test="mainChargeName == null ">
                        and mainChargeName is null
                    </when>
                    <when test="mainChargeName == '其它'.toString()">
                        and ( mainChargeName not in
                        (
                        SELECT mainChargeName FROM
                        (
                        SELECT mainChargeName,sum
                        FROM(
                        SELECT COUNT(*) AS sum,mainChargeName
                        FROM `cases_query_tmp` cqt
                        where query_id=#{queryId}
                        <choose>
                            <when test="flag == '已结案'.toString() or flag == '已結案'.toString()">AND cqt.issueStatus <![CDATA[ >= ]]> 7 and cqt.issueStatus <![CDATA[ <= ]]> 10</when>

                            <when test="flag == '未结案'.toString() or flag == '未結案'.toString()">
                                AND (cqt.issueStatus <![CDATA[<]]> 7 or cqt.issueStatus = '12' or cqt.issueStatus = '11' or cqt.issueStatus = '22')
                            </when>
                        </choose>

                        <choose>
                            <when test="issueClassificationDesc != null and issueClassificationDesc != '' and issueClassificationDesc != '空'.toString()">
                                and issueClassificationDesc = #{issueClassificationDesc}
                            </when>
                            <when test="issueClassificationDesc  == null ">
                                and (issueClassificationDesc is null or issueClassificationDesc = '' )
                            </when>
                            <when test="issueClassificationDesc == '其它'.toString()">
                                and ( issueClassificationDesc not in
                                (
                                SELECT issueClassificationDesc FROM
                                (
                                SELECT issueClassificationDesc,sum
                                FROM(
                                SELECT COUNT(*) AS sum,issueClassificationDesc
                                FROM `cases_query_tmp` cqt
                                where query_id=#{queryId}
                                <choose>
                                    <when test="flag == '已结案'.toString() or flag == '已結案'.toString()">
                                        AND cqt.issueStatus <![CDATA[ >= ]]> 7 and cqt.issueStatus <![CDATA[ <= ]]> 10
                                    </when>
                                    <when test="flag == '未结案'.toString() or flag == '未結案'.toString()">
                                        AND (cqt.issueStatus <![CDATA[<]]> 7 or cqt.issueStatus = '12' or cqt.issueStatus = '11' or cqt.issueStatus = '22')
                                    </when>
                                </choose>
                                <choose>
                                    <when test="customerName != null and customerName != '' and customerName != '空'.toString()">
                                        and customerName = #{customerName}
                                    </when>
                                    <when test="customerName == null ">
                                        and customerName is null
                                    </when>
                                </choose>

                                <choose>
                                    <when test="erpSystemCode != null and erpSystemCode != '' and erpSystemCode != '空'.toString() ">
                                        and erpSystemCode = #{moduleCode}
                                    </when>
                                    <!--<when test="erpSystemCode == '空'.toString() ">
                                        and (erpSystemCode is null or erpSystemCode = '')
                                    </when>-->
                                </choose>

                                <choose>
                                    <when test="productCode != null and productCode != '' and productCode != '空'.toString()">
                                        and productName = #{productCode}
                                    </when>
                                    <!--<when test="productCode == '空'.toString() ">
                                        and productName is null
                                    </when>-->
                                </choose>

                                <choose>
                                    <when test="emergency == '紧急'.toString() or emergency == '緊急'.toString() ">
                                        and emergency = 1
                                    </when>
                                    <when test="emergency == '不紧急'.toString() or emergency == '不緊急'.toString()  ">
                                        and (emergency = 0 )
                                    </when>
                                </choose>
                                <choose>
                                    <when test="personalCase== '个案'.toString() or personalCase== '個案'.toString()">
                                        and isPersonalCase= 'Y'
                                    </when>
                                    <when test="personalCase== '案件'.toString() ">
                                        and (isPersonalCase= 'N' or isPersonalCase is null or isPersonalCase= ' ')
                                    </when>
                                </choose>

                                <choose>
                                    <when test="username != null and username != '' and username != '空'.toString() ">
                                        and username = #{username}
                                    </when>
                                    <!--<when test="username == '空'.toString() ">
                                        and username is null
                                    </when>-->
                                </choose>
                                <choose>
                                    <when test="nowHandlerName != null and nowHandlerName != '' and nowHandlerName != '空'.toString() ">
                                        and nowHandlerName= #{nowHandlerName}
                                    </when>
                                    <!-- <when test="nowHandlerName == '空'.toString() ">
                                         and nowHandlerName is null
                                     </when>-->
                                </choose>
                                <choose>
                                    <when test="processDay != null and processDay != '' and processDay!= '空'.toString() ">
                                        and processDay = #{processDay}
                                    </when>
                                </choose>

                                <choose>
                                    <when test="mainChargeName != null and mainChargeName != '' and mainChargeName != '空'.toString() ">
                                        and mainChargeName= #{mainChargeName}
                                    </when>
                                    <!--<when test="mainChargeName == '空'.toString() ">
                                        and mainChargeName is null
                                    </when>-->
                                </choose>


                                <choose>
                                    <when test="count != '0'.toString() ">
                                        AND issueClassificationDesc IS NOT null
                                    </when>
                                </choose>
                                GROUP BY issueClassificationDesc
                                ORDER BY sum DESC,issueClassificationDesc asc
                                ) tb1
                                LIMIT
                                <choose>
                                    <when test="count != '0'.toString() ">
                                        8
                                    </when>
                                </choose>
                                <choose>
                                    <when test="count == '0'.toString() ">
                                        9
                                    </when>
                                </choose>

                                ) tb4)
                                )


                            </when>
                        </choose>

                        <choose>
                            <when test="customerName != null and customerName != '' and customerName != '空'.toString()">
                                and customerName = #{customerName}
                            </when>
                            <when test="customerName == null ">
                                and customerName is null
                            </when>
                        </choose>

                        <choose>
                            <when test="erpSystemCode != null and erpSystemCode != '' and erpSystemCode != '空'.toString() ">
                                and erpSystemCode = #{erpSystemCode}
                            </when>
                            <!-- <when test="erpSystemCode == '空'.toString() ">
                                 and (erpSystemCode is null or erpSystemCode = '')
                             </when>-->
                        </choose>

                        <choose>
                            <when test="productCode != null and productCode != '' and productCode != '空'.toString()">
                                and productName = #{productCode}
                            </when>
                            <!-- <when test="productCode == '空'.toString() ">
                                 and productName is null
                             </when>-->
                        </choose>

                        <choose>
                            <when test="emergency == '紧急'.toString() or emergency == '緊急'.toString() ">
                                and emergency = 1
                            </when>
                            <when test="emergency == '不紧急'.toString() or emergency == '不緊急'.toString()  ">
                                and (emergency = 0 )
                            </when>
                        </choose>
                        <choose>
                            <when test="personalCase== '个案'.toString() or personalCase== '個案'.toString()">
                                and isPersonalCase= 'Y'
                            </when>
                            <when test="personalCase== '案件'.toString() ">
                                and (isPersonalCase= 'N' or isPersonalCase is null or isPersonalCase= ' ')
                            </when>
                        </choose>

                        <choose>
                            <when test="username != null and username != '' and username != '空'.toString() ">
                                and username = #{username}
                            </when>
                            <!--<when test="username == '空'.toString() ">
                                and username is null
                            </when>-->
                        </choose>
                        <choose>
                            <when test="processDay != null and processDay != '' and processDay != '空'">
                                and processDay = #{processDay}
                            </when>
                        </choose>
                        <choose>
                            <when test="nowHandlerName != null and nowHandlerName != '' and nowHandlerName != '空'.toString() ">
                                and nowHandlerName= #{nowHandlerName}
                            </when>
                            <!--  <when test="nowHandlerName == '空'.toString() ">
                                  and nowHandlerName is null
                              </when>-->
                        </choose>


                        <choose>
                            <when test="count != '0'.toString() ">
                                AND mainChargeName IS NOT null
                            </when>
                        </choose>
                        GROUP BY mainChargeName
                        ORDER BY sum DESC,mainChargeName asc
                        ) tb1
                        LIMIT
                        <choose>
                            <when test="count != '0'.toString() ">
                                19
                            </when>
                        </choose>
                        <choose>
                            <when test="count == '0'.toString() ">
                                20
                            </when>
                        </choose>

                        ) tb4)
                        )


                    </when>
                </choose>


                <choose>
                    <when test="count != '0'.toString() ">
                        AND issueClassificationDesc IS NOT null
                    </when>
                </choose>
                GROUP BY issueClassificationDesc
                ORDER BY sum DESC,issueClassificationDesc asc
                ) tb1
                LIMIT
                <choose>
                    <when test="count != '0'.toString() ">
                        8
                    </when>
                </choose>
                <choose>
                    <when test="count == '0'.toString() ">
                        9
                    </when>
                </choose>

                ) tb4)
                )


            </when>
        </choose>
        <choose>
            <when test="erpSystemCode != null and erpSystemCode != '' and erpSystemCode != '空'.toString() ">
                and erpSystemCode = #{erpSystemCode}
            </when>
            <when test="erpSystemCode == '空'.toString() ">
                and (erpSystemCode is null or erpSystemCode = '')
            </when>
        </choose>
        <choose>
            <when test="emergency == '紧急'.toString() or emergency == '緊急'.toString() ">
                and emergency = 1
            </when>
            <when test="emergency == '不紧急'.toString() or emergency == '不緊急'.toString()  ">
                and (emergency = 0 )
            </when>
        </choose>
        <choose>
            <when test="personalCase== '个案'.toString() or personalCase== '個案'.toString()">
                and isPersonalCase= 'Y'
            </when>
            <when test="personalCase== '案件'.toString() ">
                and (isPersonalCase= 'N' or isPersonalCase is null or isPersonalCase= ' ')
            </when>
        </choose>
        <choose>
            <when test="productCode != null and productCode != '' and productCode != '空'.toString()">
                and productName = #{productCode}
            </when>
            <when test="productCode == '空'.toString() ">
                and productName is null
            </when>
        </choose>
        <choose>
            <when test="username != null and username != '' and username != '空'.toString() ">
                and username = #{username}
            </when>
            <when test="username == '空'.toString() ">
                and username is null
            </when>
        </choose>
        <choose>
            <when test="processDay != null and processDay != '' and processDay!= '空'.toString() ">
                and processDay = #{processDay}
            </when>
        </choose>

        <choose>
            <when test="mainChargeName != null and mainChargeName != '' and mainChargeName != '空'.toString() and mainChargeName != '其它'.toString()">
                and mainChargeName = #{mainChargeName}
            </when>
            <when test="mainChargeName == '空'.toString() ">
                and mainChargeName is null
            </when>
            <when test="mainChargeName == '其它'.toString()">
                and ( mainChargeName not in
                (
                SELECT mainChargeName FROM
                (
                SELECT mainChargeName,sum
                FROM(
                SELECT COUNT(*) AS sum,mainChargeName
                FROM `cases_query_tmp` cqt
                where query_id=#{queryId}
                <choose>
                    <when test="flag == '已结案'.toString() or flag == '已結案'.toString()">AND cqt.issueStatus <![CDATA[ >= ]]> 7 and cqt.issueStatus <![CDATA[ <= ]]> 10</when>

                    <when test="flag == '未结案'.toString() or flag == '未結案'.toString()">
                        AND (cqt.issueStatus <![CDATA[<]]> 7 or cqt.issueStatus = '12' or cqt.issueStatus = '11' or cqt.issueStatus = '22')
                    </when>
                </choose>

                <choose>
                    <when test="issueClassificationDesc != null and issueClassificationDesc != '' and issueClassificationDesc != '空'.toString()">
                        and issueClassificationDesc = #{issueClassificationDesc}
                    </when>
                    <when test="issueClassificationDesc  == null ">
                        and (issueClassificationDesc is null or issueClassificationDesc = '' )
                    </when>
                    <when test="issueClassificationDesc == '其它'.toString()">
                        and ( issueClassificationDesc not in
                        (
                        SELECT issueClassificationDesc FROM
                        (
                        SELECT issueClassificationDesc,sum
                        FROM(
                        SELECT COUNT(*) AS sum,issueClassificationDesc
                        FROM `cases_query_tmp` cqt
                        where query_id=#{queryId}
                        <choose>
                            <when test="flag == '已结案'.toString() or flag == '已結案'.toString()">
                                AND cqt.issueStatus <![CDATA[ >= ]]> 7 and cqt.issueStatus <![CDATA[ <= ]]> 10
                            </when>
                            <when test="flag == '未结案'.toString() or flag == '未結案'.toString()">
                                AND (cqt.issueStatus <![CDATA[<]]> 7 or cqt.issueStatus = '12' or cqt.issueStatus = '11' or cqt.issueStatus = '22')
                            </when>
                        </choose>
                        <choose>
                            <when test="customerName != null and customerName != '' and customerName != '空'.toString()">
                                and customerName = #{customerName}
                            </when>
                            <when test="customerName == '空'.toString() ">
                                and customerName is null
                            </when>
                        </choose>

                        <choose>
                            <when test="erpSystemCode != null and erpSystemCode != '' and erpSystemCode != '空'.toString() ">
                                and erpSystemCode = #{moduleCode}
                            </when>
                            <!--<when test="erpSystemCode == '空'.toString() ">
                                and (erpSystemCode is null or erpSystemCode = '')
                            </when>-->
                        </choose>

                        <choose>
                            <when test="productCode != null and productCode != '' and productCode != '空'.toString()">
                                and productName = #{productCode}
                            </when>
                            <!--<when test="productCode == '空'.toString() ">
                                and productName is null
                            </when>-->
                        </choose>

                        <choose>
                            <when test="emergency == '紧急'.toString() or emergency == '緊急'.toString() ">
                                and emergency = 1
                            </when>
                            <when test="emergency == '不紧急'.toString() or emergency == '不緊急'.toString()  ">
                                and (emergency = 0 )
                            </when>
                        </choose>
                        <choose>
                            <when test="personalCase== '个案'.toString() or personalCase== '個案'.toString()">
                                and isPersonalCase= 'Y'
                            </when>
                            <when test="personalCase== '案件'.toString() ">
                                and (isPersonalCase= 'N' or isPersonalCase is null or isPersonalCase= ' ')
                            </when>
                        </choose>

                        <choose>
                            <when test="username != null and username != '' and username != '空'.toString() ">
                                and username = #{username}
                            </when>
                            <!--<when test="username == '空'.toString() ">
                                and username is null
                            </when>-->
                        </choose>
                        <choose>
                            <when test="nowHandlerName != null and nowHandlerName != '' and nowHandlerName != '空'.toString() ">
                                and nowHandlerName= #{nowHandlerName}
                            </when>
                            <!-- <when test="nowHandlerName == '空'.toString() ">
                                 and nowHandlerName is null
                             </when>-->
                        </choose>
                        <choose>
                            <when test="processDay != null and processDay != '' and processDay!= '空'.toString() ">
                                and processDay = #{processDay}
                            </when>
                        </choose>

                        <choose>
                            <when test="mainChargeName != null and mainChargeName != '' and mainChargeName != '空'.toString() ">
                                and mainChargeName= #{mainChargeName}
                            </when>
                            <!--<when test="mainChargeName == '空'.toString() ">
                                and mainChargeName is null
                            </when>-->
                        </choose>


                        <choose>
                            <when test="count != '0'.toString() ">
                                AND issueClassificationDesc IS NOT null
                            </when>
                        </choose>
                        GROUP BY issueClassificationDesc
                        ORDER BY sum DESC,issueClassificationDesc asc
                        ) tb1
                        LIMIT
                        <choose>
                            <when test="count != '0'.toString() ">
                                8
                            </when>
                        </choose>
                        <choose>
                            <when test="count == '0'.toString() ">
                                9
                            </when>
                        </choose>

                        ) tb4)
                        )


                    </when>
                </choose>

                <choose>
                    <when test="customerName != null and customerName != '' and customerName != '空'.toString()">
                        and customerName = #{customerName}
                    </when>
                    <when test="customerName == '空'.toString() ">
                        and customerName is null
                    </when>
                </choose>

                <choose>
                    <when test="erpSystemCode != null and erpSystemCode != '' and erpSystemCode != '空'.toString() ">
                        and erpSystemCode = #{erpSystemCode}
                    </when>
                    <!-- <when test="erpSystemCode == '空'.toString() ">
                         and (erpSystemCode is null or erpSystemCode = '')
                     </when>-->
                </choose>

                <choose>
                    <when test="productCode != null and productCode != '' and productCode != '空'.toString()">
                        and productName = #{productCode}
                    </when>
                    <!-- <when test="productCode == '空'.toString() ">
                         and productName is null
                     </when>-->
                </choose>

                <choose>
                    <when test="emergency == '紧急'.toString() or emergency == '緊急'.toString() ">
                        and emergency = 1
                    </when>
                    <when test="emergency == '不紧急'.toString() or emergency == '不緊急'.toString()  ">
                        and (emergency = 0 )
                    </when>
                </choose>
                <choose>
                    <when test="personalCase== '个案'.toString() or personalCase== '個案'.toString()">
                        and isPersonalCase= 'Y'
                    </when>
                    <when test="personalCase== '案件'.toString() ">
                        and (isPersonalCase= 'N' or isPersonalCase is null or isPersonalCase= ' ')
                    </when>
                </choose>

                <choose>
                    <when test="username != null and username != '' and username != '空'.toString() ">
                        and username = #{username}
                    </when>
                    <!--<when test="username == '空'.toString() ">
                        and username is null
                    </when>-->
                </choose>
                <choose>
                    <when test="processDay != null and processDay != '' and processDay != '空'">
                        and processDay = #{processDay}
                    </when>
                </choose>
                <choose>
                    <when test="nowHandlerName != null and nowHandlerName != '' and nowHandlerName != '空'.toString() ">
                        and nowHandlerName= #{nowHandlerName}
                    </when>
                    <!--  <when test="nowHandlerName == '空'.toString() ">
                          and nowHandlerName is null
                      </when>-->
                </choose>


                <choose>
                    <when test="count != '0'.toString() ">
                        AND mainChargeName IS NOT null
                    </when>
                </choose>
                GROUP BY mainChargeName
                ORDER BY sum DESC,mainChargeName asc
                ) tb1
                LIMIT
                <choose>
                    <when test="count != '0'.toString() ">
                        19
                    </when>
                </choose>
                <choose>
                    <when test="count == '0'.toString() ">
                        20
                    </when>
                </choose>

                ) tb4)
                )


            </when>
        </choose>
        GROUP BY nowHandlerName
        ORDER BY sum DESC,nowHandlerName asc
        ) tb1
        LIMIT 20 )tb4 WHERE tb4.nowHandlerName is null
    </select>

    <select id="getNullMainChargeName" parameterType="java.lang.String" resultType="java.lang.Integer">
        SELECT COUNT(*) FROM
        (
        SELECT * from
        (
        SELECT COUNT(*) AS sum, mainChargeName
        FROM `cases_query_tmp` cqt
        where query_id=#{queryId}
        <choose>
            <when test="flag == '已结案'.toString() or flag == '已結案'.toString()">
                AND cqt.issueStatus <![CDATA[ >= ]]> 7 and cqt.issueStatus <![CDATA[ <= ]]> 10
            </when>
            <when test="flag == '未结案'.toString() or flag == '未結案'.toString()">
                AND (cqt.issueStatus <![CDATA[<]]> 7 or cqt.issueStatus = '12' or cqt.issueStatus = '11' or cqt.issueStatus = '22')
            </when>
        </choose>
        <choose>
            <when test="customerName != null and customerName != '' and customerName != '空'.toString()">
                and customerName = #{customerName}
            </when>
            <when test="customerName == '空'.toString() ">
                and customerName is null
            </when>
        </choose>
        <choose>
            <when test="issueClassificationDesc != null and issueClassificationDesc != '' and issueClassificationDesc != '空'.toString()">
                and issueClassificationDesc = #{issueClassificationDesc}
            </when>
            <when test="issueClassificationDesc  == '空'.toString() ">
                and (issueClassificationDesc is null or issueClassificationDesc = '' )
            </when>
            <when test="issueClassificationDesc == '其它'.toString()">
                and ( issueClassificationDesc not in
                (
                SELECT issueClassificationDesc FROM
                (
                SELECT issueClassificationDesc,sum
                FROM(
                SELECT COUNT(*) AS sum,issueClassificationDesc
                FROM `cases_query_tmp` cqt
                where query_id=#{queryId}
                <choose>
                    <when test="flag == '已结案'.toString() or flag == '已結案'.toString()">
                        AND cqt.issueStatus <![CDATA[ >= ]]> 7 and cqt.issueStatus <![CDATA[ <= ]]> 10
                    </when>
                    <when test="flag == '未结案'.toString() or flag == '未結案'.toString()">
                        AND (cqt.issueStatus <![CDATA[<]]> 7 or cqt.issueStatus = '12' or cqt.issueStatus = '11' or cqt.issueStatus = '22')
                    </when>
                </choose>
                <choose>
                    <when test="customerName != null and customerName != '' and customerName != '空'.toString()">
                        and customerName = #{customerName}
                    </when>
                    <when test="customerName == null ">
                        and customerName is null
                    </when>
                </choose>

                <choose>
                    <when test="erpSystemCode != null and erpSystemCode != '' and erpSystemCode != '空'.toString() ">
                        and erpSystemCode = #{moduleCode}
                    </when>
                    <!--<when test="erpSystemCode == '空'.toString() ">
                        and (erpSystemCode is null or erpSystemCode = '')
                    </when>-->
                </choose>

                <choose>
                    <when test="productCode != null and productCode != '' and productCode != '空'.toString()">
                        and productName = #{productCode}
                    </when>
                    <!--<when test="productCode == '空'.toString() ">
                        and productName is null
                    </when>-->
                </choose>

                <choose>
                    <when test="emergency == '紧急'.toString() or emergency == '緊急'.toString() ">
                        and emergency = 1
                    </when>
                    <when test="emergency == '不紧急'.toString() or emergency == '不緊急'.toString()  ">
                        and (emergency = 0 )
                    </when>
                </choose>
                <choose>
                    <when test="personalCase== '个案'.toString() or personalCase== '個案'.toString()">
                        and isPersonalCase= 'Y'
                    </when>
                    <when test="personalCase== '案件'.toString() ">
                        and (isPersonalCase= 'N' or isPersonalCase is null or isPersonalCase= ' ')
                    </when>
                </choose>

                <choose>
                    <when test="username != null and username != '' and username != '空'.toString() ">
                        and username = #{username}
                    </when>
                    <!--<when test="username == '空'.toString() ">
                        and username is null
                    </when>-->
                </choose>
                <choose>
                    <when test="nowHandlerName != null and nowHandlerName != '' and nowHandlerName != '空'.toString() ">
                        and nowHandlerName= #{nowHandlerName}
                    </when>
                    <!-- <when test="nowHandlerName == '空'.toString() ">
                         and nowHandlerName is null
                     </when>-->
                </choose>
                <choose>
                    <when test="processDay != null and processDay != '' and processDay!= '空'.toString() ">
                        and processDay = #{processDay}
                    </when>
                </choose>

                <choose>
                    <when test="mainChargeName != null and mainChargeName != '' and mainChargeName != '空'.toString() and mainChargeName != '其它'.toString()">
                        and mainChargeName = #{mainChargeName}
                    </when>
                    <when test="mainChargeName == null ">
                        and mainChargeName is null
                    </when>
                    <when test="mainChargeName == '其它'.toString()">
                        and ( mainChargeName not in
                        (
                        SELECT mainChargeName FROM
                        (
                        SELECT mainChargeName,sum
                        FROM(
                        SELECT COUNT(*) AS sum,mainChargeName
                        FROM `cases_query_tmp` cqt
                        where query_id=#{queryId}
                        <choose>
                            <when test="flag == '已结案'.toString() or flag == '已結案'.toString()">AND cqt.issueStatus <![CDATA[ >= ]]> 7 and cqt.issueStatus <![CDATA[ <= ]]> 10</when>

                            <when test="flag == '未结案'.toString() or flag == '未結案'.toString()">
                                AND (cqt.issueStatus <![CDATA[<]]> 7 or cqt.issueStatus = '12' or cqt.issueStatus = '11' or cqt.issueStatus = '22')
                            </when>
                        </choose>

                        <choose>
                            <when test="issueClassificationDesc != null and issueClassificationDesc != '' and issueClassificationDesc != '空'.toString()">
                                and issueClassificationDesc = #{issueClassificationDesc}
                            </when>
                            <when test="issueClassificationDesc  == null ">
                                and (issueClassificationDesc is null or issueClassificationDesc = '' )
                            </when>
                            <when test="issueClassificationDesc == '其它'.toString()">
                                and ( issueClassificationDesc not in
                                (
                                SELECT issueClassificationDesc FROM
                                (
                                SELECT issueClassificationDesc,sum
                                FROM(
                                SELECT COUNT(*) AS sum,issueClassificationDesc
                                FROM `cases_query_tmp` cqt
                                where query_id=#{queryId}
                                <choose>
                                    <when test="flag == '已结案'.toString() or flag == '已結案'.toString()">
                                        AND cqt.issueStatus <![CDATA[ >= ]]> 7 and cqt.issueStatus <![CDATA[ <= ]]> 10
                                    </when>
                                    <when test="flag == '未结案'.toString() or flag == '未結案'.toString()">
                                        AND (cqt.issueStatus <![CDATA[<]]> 7 or cqt.issueStatus = '12' or cqt.issueStatus = '11' or cqt.issueStatus = '22')
                                    </when>
                                </choose>
                                <choose>
                                    <when test="customerName != null and customerName != '' and customerName != '空'.toString()">
                                        and customerName = #{customerName}
                                    </when>
                                    <when test="customerName == null ">
                                        and customerName is null
                                    </when>
                                </choose>

                                <choose>
                                    <when test="erpSystemCode != null and erpSystemCode != '' and erpSystemCode != '空'.toString() ">
                                        and erpSystemCode = #{moduleCode}
                                    </when>
                                    <!--<when test="erpSystemCode == '空'.toString() ">
                                        and (erpSystemCode is null or erpSystemCode = '')
                                    </when>-->
                                </choose>

                                <choose>
                                    <when test="productCode != null and productCode != '' and productCode != '空'.toString()">
                                        and productName = #{productCode}
                                    </when>
                                    <!--<when test="productCode == '空'.toString() ">
                                        and productName is null
                                    </when>-->
                                </choose>

                                <choose>
                                    <when test="emergency == '紧急'.toString() or emergency == '緊急'.toString() ">
                                        and emergency = 1
                                    </when>
                                    <when test="emergency == '不紧急'.toString() or emergency == '不緊急'.toString()  ">
                                        and (emergency = 0 )
                                    </when>
                                </choose>
                                <choose>
                                    <when test="personalCase== '个案'.toString() or personalCase== '個案'.toString()">
                                        and isPersonalCase= 'Y'
                                    </when>
                                    <when test="personalCase== '案件'.toString() ">
                                        and (isPersonalCase= 'N' or isPersonalCase is null or isPersonalCase= ' ')
                                    </when>
                                </choose>

                                <choose>
                                    <when test="username != null and username != '' and username != '空'.toString() ">
                                        and username = #{username}
                                    </when>
                                    <!--<when test="username == '空'.toString() ">
                                        and username is null
                                    </when>-->
                                </choose>
                                <choose>
                                    <when test="nowHandlerName != null and nowHandlerName != '' and nowHandlerName != '空'.toString() ">
                                        and nowHandlerName= #{nowHandlerName}
                                    </when>
                                    <!-- <when test="nowHandlerName == '空'.toString() ">
                                         and nowHandlerName is null
                                     </when>-->
                                </choose>
                                <choose>
                                    <when test="processDay != null and processDay != '' and processDay!= '空'.toString() ">
                                        and processDay = #{processDay}
                                    </when>
                                </choose>

                                <choose>
                                    <when test="mainChargeName != null and mainChargeName != '' and mainChargeName != '空'.toString() ">
                                        and mainChargeName= #{mainChargeName}
                                    </when>
                                    <!--<when test="mainChargeName == '空'.toString() ">
                                        and mainChargeName is null
                                    </when>-->
                                </choose>


                                <choose>
                                    <when test="count != '0'.toString() ">
                                        AND issueClassificationDesc IS NOT null
                                    </when>
                                </choose>
                                GROUP BY issueClassificationDesc
                                ORDER BY sum DESC,issueClassificationDesc asc
                                ) tb1
                                LIMIT
                                <choose>
                                    <when test="count != '0'.toString() ">
                                        8
                                    </when>
                                </choose>
                                <choose>
                                    <when test="count == '0'.toString() ">
                                        9
                                    </when>
                                </choose>

                                ) tb4)
                                )


                            </when>
                        </choose>

                        <choose>
                            <when test="customerName != null and customerName != '' and customerName != '空'.toString()">
                                and customerName = #{customerName}
                            </when>
                            <when test="customerName == null ">
                                and customerName is null
                            </when>
                        </choose>

                        <choose>
                            <when test="erpSystemCode != null and erpSystemCode != '' and erpSystemCode != '空'.toString() ">
                                and erpSystemCode = #{erpSystemCode}
                            </when>
                            <!-- <when test="erpSystemCode == '空'.toString() ">
                                 and (erpSystemCode is null or erpSystemCode = '')
                             </when>-->
                        </choose>

                        <choose>
                            <when test="productCode != null and productCode != '' and productCode != '空'.toString()">
                                and productName = #{productCode}
                            </when>
                            <!-- <when test="productCode == '空'.toString() ">
                                 and productName is null
                             </when>-->
                        </choose>

                        <choose>
                            <when test="emergency == '紧急'.toString() or emergency == '緊急'.toString() ">
                                and emergency = 1
                            </when>
                            <when test="emergency == '不紧急'.toString() or emergency == '不緊急'.toString()  ">
                                and (emergency = 0 )
                            </when>
                        </choose>
                        <choose>
                            <when test="personalCase== '个案'.toString() or personalCase== '個案'.toString()">
                                and isPersonalCase= 'Y'
                            </when>
                            <when test="personalCase== '案件'.toString() ">
                                and (isPersonalCase= 'N' or isPersonalCase is null or isPersonalCase= ' ')
                            </when>
                        </choose>

                        <choose>
                            <when test="username != null and username != '' and username != '空'.toString() ">
                                and username = #{username}
                            </when>
                            <!--<when test="username == '空'.toString() ">
                                and username is null
                            </when>-->
                        </choose>
                        <choose>
                            <when test="processDay != null and processDay != '' and processDay != '空'">
                                and processDay = #{processDay}
                            </when>
                        </choose>
                        <choose>
                            <when test="nowHandlerName != null and nowHandlerName != '' and nowHandlerName != '空'.toString() ">
                                and nowHandlerName= #{nowHandlerName}
                            </when>
                            <!--  <when test="nowHandlerName == '空'.toString() ">
                                  and nowHandlerName is null
                              </when>-->
                        </choose>


                        <choose>
                            <when test="count != '0'.toString() ">
                                AND mainChargeName IS NOT null
                            </when>
                        </choose>
                        GROUP BY mainChargeName
                        ORDER BY sum DESC,mainChargeName asc
                        ) tb1
                        LIMIT
                        <choose>
                            <when test="count != '0'.toString() ">
                                19
                            </when>
                        </choose>
                        <choose>
                            <when test="count == '0'.toString() ">
                                20
                            </when>
                        </choose>

                        ) tb4)
                        )


                    </when>
                </choose>


                <choose>
                    <when test="count != '0'.toString() ">
                        AND issueClassificationDesc IS NOT null
                    </when>
                </choose>
                GROUP BY issueClassificationDesc
                ORDER BY sum DESC,issueClassificationDesc asc
                ) tb1
                LIMIT
                <choose>
                    <when test="count != '0'.toString() ">
                        8
                    </when>
                </choose>
                <choose>
                    <when test="count == '0'.toString() ">
                        9
                    </when>
                </choose>

                ) tb4)
                )


            </when>
        </choose>
        <choose>
            <when test="erpSystemCode != null and erpSystemCode != '' and erpSystemCode != '空'.toString() ">
                and erpSystemCode = #{erpSystemCode}
            </when>
            <when test="erpSystemCode == '空'.toString() ">
                and (erpSystemCode is null or erpSystemCode = '')
            </when>
        </choose>
        <choose>
            <when test="emergency == '紧急'.toString() or emergency == '緊急'.toString() ">
                and emergency = 1
            </when>
            <when test="emergency == '不紧急'.toString() or emergency == '不緊急'.toString()  ">
                and (emergency = 0 )
            </when>
        </choose>
        <choose>
            <when test="personalCase== '个案'.toString() or personalCase== '個案'.toString()">
                and isPersonalCase= 'Y'
            </when>
            <when test="personalCase== '案件'.toString() ">
                and (isPersonalCase= 'N' or isPersonalCase is null or isPersonalCase= ' ')
            </when>
        </choose>
        <choose>
            <when test="productCode != null and productCode != '' and productCode != '空'.toString()">
                and productName = #{productCode}
            </when>
            <when test="productCode == '空'.toString() ">
                and productName is null
            </when>
        </choose>
        <choose>
            <when test="username != null and username != '' and username != '空'.toString() ">
                and username = #{username}
            </when>
            <when test="username == '空'.toString() ">
                and username is null
            </when>
        </choose>
        <choose>
            <when test="processDay != null and processDay != '' and processDay!= '空'.toString() ">
                and processDay = #{processDay}
            </when>
        </choose>
        <choose>
            <when test="nowHandlerName != null and nowHandlerName != '' and nowHandlerName != '空'.toString() ">
                and nowHandlerName= #{nowHandlerName}
            </when>
            <when test="nowHandlerName == '空'.toString() ">
                and nowHandlerName is null
            </when>
        </choose>
        GROUP BY mainChargeName
        ORDER BY sum DESC,mainChargeName asc
        ) tb1
        LIMIT 20 )tb4 WHERE tb4.mainChargeName is null
    </select>
    <!--获取案件明细-->
    <select id="getCaseAnalyseDetails" parameterType="com.digiwin.escloud.issueservice.t.model.cases.dto.MyCasesQueryParamDTO" resultType="com.digiwin.escloud.issueservice.t.model.cases.CasesAnalyseDetailsDTO">
        SELECT  cqt.issueId,cs.crmId,cqt.customerName ,cqt.erpSystemCode,cqt.programCode,cqt.issueStatus,cqt.nowHandlerName,cqt.username,cqt.emergency,SUBSTR(cs.submitTime FROM 1 FOR 16) submitTime,
        concat(floor(cqt.processTime/1440),'天',floor(cqt.processTime%1440/60),'小时',cqt.processTime%1440%60,'分' ) processTime,cqt.processDay, cs.question_title questionTitle,
        CASE   WHEN  issueClassificationDesc = '客制需求' AND (cs.submitway = 'HD187' or cs.submitway like 'EXCEL%') THEN '客制需求(187)'
        WHEN  issueClassificationDesc = '客制需求' AND cs.SubmitWay = 'INNER' THEN '客制需求(服务云)'
        ELSE issueclassificationDesc END issueclassificationDesc,
        cust_level custLevel
        FROM cases_query_tmp cqt
        left join issue cs on cqt.issueId=cs.IssueId
        left join issue_casedetail icd on icd.issueId = cs.issueId
        LEFT JOIN mars_customerservice mc ON cqt.serviceCode=mc.CustomerServiceCode AND cs.ProductCode=mc.ProductCode
        where query_id=#{queryId}
        <choose>
            <when test="issueClassificationDesc  == '客制需求(187)'.toString() ">
                and issueClassificationDesc ='客制需求' and (cs.submitway = 'HD187' or cs.submitway like 'EXCEL%')
            </when>
            <when test="issueClassificationDesc  == '客制需求(服务云)'.toString() ">
                and issueClassificationDesc ='客制需求' and cs.SubmitWay = 'INNER'
            </when>
            <when test="issueClassificationDesc != null and issueClassificationDesc != '' and issueClassificationDesc != 'ALL' and issueClassificationDesc != '客制需求(187)'.toString()
                and issueClassificationDesc != '客制需求(服务云)'.toString() and issueClassificationDesc != '空'.toString() ">
                and issueClassificationDesc = #{issueClassificationDesc}
            </when>
            <when test="issueClassificationDesc  == '空'.toString()">
                and (issueClassificationDesc is null or issueClassificationDesc = '' )
            </when>
        </choose>
        <choose>
            <when test="issueLevel != null and issueLevel != '' and issueLevel != '空'.toString()">
                and icd.issue_level = #{issueLevel}
            </when>
            <when test="issueLevel == '空'.toString() ">
                and ( icd.issue_level is null or icd.issue_level = '' )
            </when>
        </choose>
        <choose>
            <when test="customerName != null and customerName != '' and customerName != '其它'.toString() and customerName != '空'.toString()">
                and cqt.customerName = #{customerName}
            </when>
            <when test="customerName == '空'.toString() ">
                and ( cqt.customerName is null or customerName = '' )
            </when>
        </choose>
        <choose>
            <when test="flag == '已结案'.toString() or flag == '已結案'.toString()">
                AND cqt.issueStatus in ('Y','P','7','8','10')
            </when>
            <when test="flag == '未结案'.toString() or flag == '未結案'.toString()">
                AND cqt.issueStatus in ('C','N','2','3','4','11','12','22')
            </when>
        </choose>
        <choose>
            <when test="erpSystemCode != null and erpSystemCode != '' and erpSystemCode != '空'.toString() and erpSystemCode != '其它'.toString() ">
                and cqt.erpSystemCode = #{erpSystemCode}
            </when>
            <when test="erpSystemCode == '空'.toString() ">
                and (cqt.erpSystemCode is null or cqt.erpSystemCode = '')
            </when>
        </choose>
        <choose>
            <when test="productCode != null and productCode != '' and productCode != '空'.toString()">
                and cqt.productName = #{productCode}
            </when>
            <when test="productCode == '空'.toString() ">
                and ( cqt.productName is null or cqt.productName = '' )
            </when>
        </choose>
        <choose>
            <when test="emergency == '紧急'.toString() or emergency == '緊急'.toString() ">
                and cqt.emergency = 1
            </when>
            <when test="emergency == '不紧急'.toString() or emergency == '不緊急'.toString()  ">
                and (cqt.emergency = 0 )
            </when>
        </choose>

        <choose>
            <when test="username != null and username != '' and username != '空'.toString() and username != '其它'.toString()">
                and cqt.username = #{username}
            </when>
            <when test="username == '空'.toString() ">
                and ( cqt.username is null or cqt.username = '' )
            </when>
        </choose>
        <choose>
            <when test="nowHandlerName != null and nowHandlerName != '' and nowHandlerName != '空'.toString() and nowHandlerName != '其它'.toString()">
                and cqt.nowHandlerName = #{nowHandlerName}
            </when>
            <when test="nowHandlerName == '空'.toString() ">
                and cqt.nowHandlerName is null
            </when>
        </choose>
        <choose>
            <when test="mainChargeName != null and mainChargeName != '' and mainChargeName != '空'.toString() and mainChargeName != '其它'.toString()">
                and cqt.mainChargeName = #{mainChargeName}
            </when>
            <when test="mainChargeName == '空'.toString()  ">
                and cqt.mainChargeName is null
            </when>
        </choose>
        <choose>
            <when test="processDay != null and processDay != '' and processDay != '空'.toString()">
                and cqt.processDay = #{processDay}
            </when>
        </choose>
        <choose>
            <when test="personalCase== '个案'.toString() or personalCase== '個案'.toString()">
                and cqt.isPersonalCase= 'Y'
            </when>
            <when test="personalCase== '案件'.toString() ">
                and (cqt.isPersonalCase= 'N' or isPersonalCase is null or isPersonalCase= ' ')
            </when>
        </choose>
        <choose>
            <when test="custLevel != null and custLevel != '' and custLevel != '空'.toString() ">
                and cust_level = #{custLevel}
            </when>
            <when test="custLevel == '空'.toString() ">
                and (cust_level is null or cust_level='')
            </when>
        </choose>
        ORDER BY cs.SubmitTime ASC
    </select>
    <select id="getCaseByCrmId" parameterType="string" resultType="com.digiwin.escloud.issueservice.t.model.cases.Cases">
        SELECT  cs.IssueId issueId,cs.CrmId crmId,cs.site,cs.ServiceCode serviceCode,cs.ProductCode productCode,
        cs.UserId userId,uc.Name username,uc.Phone01 phone,uc.Email email,cs.IssueDescription issueDescription,
        cs.emergency,case when cs.IssueStatus in ('11','22') then cs.UserId else cs.ServiceId end serviceId,cs.SubmitWay submitWay,
        cs.IssueStatus currentStatus,cs.SubmitTime submitTime,cs.__version__ updateTime,cs.main_charge mainCharge,
        cs.CcUser ccUser,cs.submited_id submitedId,cs.question_title questionTitle,cs.live_process liveProcess,
        ic.IssueClassification issueClassification,ic.ErpSystemCode erpSystemCode,ic.ProgramCode programCode,
        ic.expected_completion_date expectedCompletionDate,ic.estimated_work_hours estimatedWorkHours,
        ic.total_work_hours total_work_hours, ic.issue_level issueLevel, cs.serviceRegion,
        (select max(SequenceNum) from issue_progress where CrmId=#{crmId}) as maxSequenceNum, ic.bug_owner as bugOwner
        FROM issue cs
        LEFT JOIN user_contacts uc ON uc.id = cs.UserContactId
        LEFT JOIN issue_casedetail ic ON ic.IssueId = cs.IssueId
        WHERE cs.CrmId=#{crmId}
    </select>
    <select id="getIssueIdByCrmId" parameterType="string" resultType="java.lang.Long">
        SELECT  IssueId as issueId FROM issue WHERE CrmId=#{crmId}
    </select>
    <select id="getCaseByIssueId" parameterType="long" resultType="com.digiwin.escloud.issueservice.t.model.cases.Cases">
        SELECT  cs.IssueId issueId,cs.CrmId crmId,cs.site,cs.ServiceCode serviceCode,cs.ProductCode productCode, p.ProductCategory productCategory,
        cs.UserId userId,uc.Name username,uc.Phone01 phone,uc.Email email,cs.IssueDescription issueDescription,
        cs.emergency,case when cs.IssueStatus in ('11','22') then cs.UserId else cs.ServiceId end serviceId,cs.SubmitWay submitWay,
        cs.IssueStatus currentStatus,date_format(cs.SubmitTime, '%Y-%m-%d %T') submitTime,cs.__version__ updateTime,cs.main_charge mainCharge,
        cs.CcUser ccUser,cs.submited_id submitedId,cs.question_title questionTitle,cs.live_process liveProcess,
        icf.IssueCatelogCode,ic.IssueClassification issueClassification,ic.ErpSystemCode erpSystemCode,ic.ProgramCode programCode,
        ic.expected_completion_date expectedCompletionDate,ic.estimated_work_hours estimatedWorkHours,
        ic.total_work_hours totalWorkHours, issue_level issueLevel, cs.serviceRegion,
        (select max(SequenceNum) from issue_progress where IssueId=#{issueId}) as maxSequenceNum, ic.bug_owner as bugOwner,tag.tagName,cs.contractNo,cs.projectNo
        FROM issue cs
        LEFT JOIN user_contacts uc ON uc.id = cs.UserContactId
        LEFT JOIN issue_casedetail ic ON ic.IssueId = cs.IssueId
        LEFT JOIN issueclassification icf ON icf.ProductCode = cs.ProductCode AND icf.IssueClassification = ic.IssueClassification
        LEFT JOIN mars_product p ON p.ProductCode = cs.ProductCode
        LEFT JOIN (SELECT GROUP_CONCAT(tt.tagName) tagName,tm.sourceId FROM tag_mapping tm
		             left join tag_type tt ON tt.id = tm.tagId WHERE tm.sourceId=#{issueId} AND tm.sourceType='issue') tag ON tag.sourceId = cs.issueId
        WHERE cs.IssueId=#{issueId}
    </select>
    <select id="getProcessorByProcessType" resultType="string">
        select Processor from issue_progress where IssueId=#{issueId} and ProcessType=#{processType} order by SequenceNum desc limit 1
    </select>
    <select id="getCustomerCcEmailsString" resultType="string">
        select customer_ccmail from issue_casedetail where IssueId=#{issueId}
    </select>
    <select id="queryOperationSyncStatus" resultType="com.digiwin.escloud.issueservice.t.model.cases.dto.CaseOperationSyncStatus">
        select a.IssueId issueId, a.SequenceNum sequenceNum, a.is_sync187 success, b.exception
        from issue_progress a left join fuguan_post_record b on a.IssueId=b.issue_id and a.SequenceNum = b.sequence_num
        where a.IssueId=#{issueId} and (b.status is not null or a.is_sync187 = true)
        order by a.SequenceNum desc
    </select>
    <select id="querySubCrmIds" resultType="string">
        select subCrmId from crmid_relationship where crmId=#{crmId}
    </select>

    <select id="queryRelateIssue" resultType="com.digiwin.escloud.issueservice.model.RelateIssue">
        SELECT b.issueId,a.subCrmId crmId from crmid_relationship a
        INNER JOIN issue b ON b.CrmId = a.subCrmId
        WHERE a.crmId = #{crmId}
        UNION
        SELECT b.issueId,a.CrmId crmId from crmid_relationship a
        INNER JOIN issue b ON b.CrmId = a.CrmId
        WHERE a.subCrmId = #{crmId}
    </select>
    <select id="queryParentCrmId" resultType="string">
        select crmId from crmid_relationship where subCrmId = #{subCrmId}
    </select>
    <select id="queryProcessRecordForCustomer" resultType="com.digiwin.escloud.issueservice.t.model.cases.CaseHistory">
        select i.IssueId issueId, i.SequenceNum sequenceNum, i.ProcessType processType, i.Processor processor,
        mu.name processorName, i.ProcessTime processTime, i.Description description, i.replyType
        from issue_progress i left join mars_userpersonalinfo mu on i.Processor=mu.userid
        where i.issueId=#{issueId}
        and i.ProcessType in
        <foreach collection="processTypeList" item="item" index="index" separator="," open="(" close=")">
            #{item}
        </foreach>
        order by i.id desc
    </select>
    <select id="queryReplyMessageToCustomer" resultType="com.digiwin.escloud.issueservice.t.model.message.ServiceMessage">
        SELECT id, issueId, sequenceNum, replyUserId, mu.name replyUserName, date_format(replyTime, '%Y-%m-%d %T') replyTime, message
        FROM service_message s left join mars_userpersonalinfo mu on s.replyUserId=mu.userid
        WHERE issueId=#{issueId} order by id desc
    </select>

    <update id="updateServiceInfoOnIssue" parameterType="com.digiwin.escloud.issueservice.t.model.cases.Cases">
        UPDATE issue SET ServiceId=#{serviceId}, ServiceDepartment=#{serviceDepartment} WHERE issueId = #{issueId}
    </update>
    <update id="setZeroForReplyMinEndMin">
        update issue_casedetail set reply_min = 0, end_min = 0
        where IssueId = #{issueId}
    </update>


    <delete id="deletecasedetailtemp">
        truncate table cases_detail_temp
    </delete>
    <delete id="deletecaseerrortemp">
        truncate table cases_error_temp
    </delete>
    <delete id="deletecustomersexporttemp">
        truncate table customers_export_temp
    </delete>
    <delete id="deletecasequerytmp">
        truncate table cases_query_tmp
    </delete>
    <delete id="deleteRequTmp">
        truncate table customized_requ_temp
    </delete>
    <insert id="doSaveIssueProgress" parameterType="com.digiwin.escloud.issueservice.t.model.cases.CaseHistory">
        INSERT INTO issue_progress ( IssueId,CrmId,SequenceNum,ProcessType,CurrentStatus,Processor,workno,Description,
        ProcessTime,ReplyType,ProcessHours,SyncStatus,handlerId, t100_case_id, is_sync187)
        VALUES(
        #{issueId},#{crmId},#{sequenceNum},#{processType},#{currentStatus},#{processor},#{workno},#{description},
        #{processTime},#{replyType},#{processHours},#{syncStatus},#{handlerId}, #{t100CaseId}, false
        )
    </insert>

    <select id="getTipTopProductCode" parameterType="java.lang.String" resultType="java.lang.Integer">
        select ifnull(count(*), 0) from qmank
        where 1=1
          and NK001=#{productCode}
          and NK003 like '%F%'
          and NK002 = (
            SELECT ifnull(ContractState, '99') from mars_customerservice where CustomerServiceCode = #{customerServiceCode} and ProductCode = #{productCode}
        )
    </select>
    <update id="turnToGuWenCRM">
        insert into issue_to_guwencrm(issueId, status)
        values(#{issueId},#{status})
        ON DUPLICATE KEY UPDATE status = #{status}
    </update>
    <select id="IsTurnToGuWenCRM" resultType="java.lang.String">
        select a.status
        from issue_to_guwencrm a
        where a.issueId = #{issueId}
    </select>
    <select id="selectIssueStatus" resultType="java.lang.String">
        select case WHEN a.IssueStatus in ('11','7') then 'Y' else  a.IssueStatus end IssueStatus
        from issue a
        where a.issueId = #{issueId}
    </select>

    <update id="updateIssueRequ">
        update issue_casedetail a
        set a.requNo = #{requNo},a.requSeq= #{requSeq}
        where a.IssueId = #{issueId}
    </update>
    <select id="getIssueSummary" resultType="com.digiwin.escloud.issueservice.model.IssueSummary">
        select * from issue_summary
        where issueId = #{issueId}
    </select>
    <insert id="insertIssueSummary">
        insert into issue_summary(IssueId,AcceptTime,AcceptHours,AcceptUserId)
        values (#{issueId},#{processTime},round(count_process_time((select issue.SubmitTime from Issue where IssueId=#{issueId}),#{processTime})/60,2),#{userId})
    </insert>

    <update id="updateIssueSummary">
        UPDATE issue_summary
        SET AcceptTime=#{processTime},AcceptHours= round(count_process_time((select issue.SubmitTime from Issue where IssueId=#{issueId}),#{processTime})/60,2),AcceptUserId = #{userId}
        where IssueId = #{issueId} and AcceptTime is null
    </update>
    <insert id="insertIssueProgress" parameterType="com.digiwin.escloud.issueservice.model.IssueProgress" useGeneratedKeys="true"
            keyProperty="id" keyColumn="Id">
        insert into issue_progress( IssueId,CrmId,SequenceNum,ProcessType,CurrentStatus,Processor,workno,Description,
               ProcessTime,ReplyType,SyncStatus)
        VALUES(#{issueId}, #{crmId}, #{sequeceNum}, #{processType}, #{currentStatus}, #{processor}, #{workno}, #{description},
               now(), #{replyType}, #{syncStatus})
    </insert>
    <update id="updateIssuePlanDate">
        update issue_casedetail a set a.expected_completion_date = #{planDate} where a.issueId = #{issueId}
    </update>
    <select id="getCustomerInfo" resultType="java.util.Map">
        SELECT a.CustomerCode,a.CustomerName
        FROM mars_customer a
        WHERE a.CustomerServiceCode = #{serviceCode} LIMIT 1
    </select>
    <select id="getDefaultProcess" resultType="java.lang.String">
        select i.workNo
        from issue_defaultprocess idp
        inner join mars_staffaccount s on idp.itcode = s.itcode
        inner join mars_userpersonalinfo i on s.userid = i.userid
        where idp.productCode = #{productCode} limit 1
    </select>
    <select id="query187ErrorIssues" resultType="java.lang.Long">
        select DISTINCT a.IssueId
        from issue_progress a
        INNER JOIN issue c ON c.issueId = a.IssueId
		INNER join fuguan_post_record b on a.IssueId=b.issue_id and a.SequenceNum = b.sequence_num
        WHERE c.productCode IN ('100','06','164') AND c.issueStatus NOT IN ('Y','7','10','P') and a.is_sync187 = 0
    </select>
    <select id="getCheckInvalidKnowledgeNo" resultType="java.lang.Boolean">
        Select icc.checkInvalidKnowledgeNo
        From issue_chatfile_config icc
        where 1=1
        <if test="productCode != null and productCode != '' ">
            and  icc.productCode=#{productCode}
        </if>
        <if test="serviceRegion != null and serviceRegion != '' ">
            and  icc.serviceRegion=#{serviceRegion}
        </if>
        limit 1
    </select>
    <select id="queryTIssues" resultType="java.lang.Long">
        SELECT DISTINCT c.issueId FROM
        (
            SELECT a.IssueId
            FROM issue a
            WHERE a.ProductCode IN ('100','06','999','164','147') AND a.SubmitTime > '2024-01-01'
            UNION
            SELECT a.IssueId
            FROM issue a
            WHERE a.ProductCode IN ('100','06','999','164','147') AND a.IssueStatus NOT IN ('Y','7','10','P')
        )c
    </select>

    <select id="getFinalIssueProgressInfo" resultType="java.util.Map">
        select main.CrmId, main.IssueStatus, main.description, main.summitTime, main.processTime
        , main.case_manager_name, main.ServiceCode
        , mc.ServiceStaff as productContactPerson, mc.ServiceStaffContact as productContactPersonMail
        , main.IssueDescription
        from (
        select productCode, CustomerServiceCode, ServiceStaff, ServiceStaffContact
        from mars_customerservice
        where  1=1
        and CustomerServiceCode = #{serviceCode}
        and productcode=#{productCode}
        ) mc left join (
        select ip.CrmId, i.IssueStatus, ip.workno, ip_max_reply.description description, ip_min.summitTime
        , ifnull(ip_max_reply.processTime,ip.processTime) processTime
        , case when i.case_manager_name is null then '' else i.case_manager_name end case_manager_name
        , case when i.case_manager_email  is null then '' else i.case_manager_email end case_manager_email
        , case when i.ServiceCode is null then #{serviceCode} else i.ServiceCode end ServiceCode
        , i.IssueDescription
        from issue_progress ip
        inner join (
        select ip.crmId, max(processTime) processTime, max(SequenceNum) SequenceNum
        from issue_progress ip
        where 1=1
          and ip.CrmId in (
               select CrmId from issue i
               where 1=1
               <foreach collection="issueStatus" item="status" index="index" open="and (" separator=" or " close=")">
                   i.IssueStatus = #{status}
               </foreach>
               <if test="(isRelateWaringIssue != null and isRelateWaringIssue == false)">
                   and ip.CrmId in (select CrmId from issue where ServiceCode = #{serviceCode} and productcode = #{productCode})
                   <foreach collection="crmIds" item="crmId" index="index" open="and (" separator=" or " close=")">
                       crmId = #{crmId}
                   </foreach>
               </if>
               <if test="(isRelateWaringIssue != null and isRelateWaringIssue == true)">
                   <if test="crmIds.isEmpty()">
                       and 1=0
                   </if>
                   <foreach collection="crmIds" item="crmId" index="index" open="and (" separator=" or " close=")">
                       crmId = #{crmId}
                   </foreach>
               </if>
          )
        group by ip.crmId
        ) ip_max on ip.crmId = ip_max.crmId and ip.processTime = ip_max.processTime and ip.SequenceNum = ip_max.SequenceNum
        inner join (
           select ip.crmid, min(processTime) as summitTime
           from issue_progress ip
           where 1=1
           and processType = 'Submit'
           and ip.CrmId in (
               select CrmId from issue i
               where 1=1
<!--                <foreach collection="issueStatus" item="status" index="index" open="and (" separator=" or " close=")">-->
<!--                    i.IssueStatus = #{status}-->
<!--                </foreach>-->
                <if test="(isRelateWaringIssue != null and isRelateWaringIssue == false)">
                    and ServiceCode = #{serviceCode} and productcode = #{productCode}
                    <foreach collection="crmIds" item="crmId" index="index" open="and (" separator=" or " close=")">
                        crmId = #{crmId}
                    </foreach>
                </if>
                <if test="(isRelateWaringIssue != null and isRelateWaringIssue == true)">
                    <if test="crmIds.isEmpty()">
                        and 1=0
                    </if>
                    <foreach collection="crmIds" item="crmId" index="index" open="and (" separator=" or " close=")">
                        crmId = #{crmId}
                    </foreach>
                </if>
           )
        group by processTime
        ) ip_min on ip_min.crmid = ip_max.crmid
        left join (
        select i.ServiceCode, i.IssueStatus, crmId, mup.name as case_manager_name, mup.email as case_manager_email,i.IssueDescription
        from issue i
        left join mars_userpersonalinfo mup on mup.userid = i.serviceId
        where 1=1
        <foreach collection="issueStatus" item="status" index="index" open="and (" separator=" or " close=")">
            i.IssueStatus = #{status}
        </foreach>
        <if test="(isRelateWaringIssue != null and isRelateWaringIssue == false)">
            and ServiceCode = #{serviceCode} and productcode=#{productCode}
            <foreach collection="crmIds" item="crmId" index="index" open="and (" separator=" or " close=")">
                i.crmId = #{crmId}
            </foreach>
        </if>
        <if test="(isRelateWaringIssue != null and isRelateWaringIssue == true)">
            <if test="crmIds.isEmpty()">
                and 1=0
            </if>
            <foreach collection="crmIds" item="crmId" index="index" open="and (" separator=" or " close=")">
                crmId = #{crmId}
            </foreach>
        </if>
        ) i on i.crmId = ip.CrmId
        left join (
        select ip.crmid, ip.description, ip.processTime
        from issue_progress ip
        where 1=1
        and ReplyType = 'A'
        and ip.CrmId in (
        select CrmId from issue i
        where 1=1
        <if test="(isRelateWaringIssue != null and isRelateWaringIssue == false)">
            and ServiceCode = #{serviceCode} and productcode = #{productCode}
            <foreach collection="crmIds" item="crmId" index="index" open="and (" separator=" or " close=")">
                crmId = #{crmId}
            </foreach>
        </if>
        <if test="(isRelateWaringIssue != null and isRelateWaringIssue == true)">
            <if test="crmIds.isEmpty()">
                and 1=0
            </if>
            <foreach collection="crmIds" item="crmId" index="index" open="and (" separator=" or " close=")">
                crmId = #{crmId}
            </foreach>
        </if>
        )
        order by processTime desc
        limit 1
        ) ip_max_reply on ip_max_reply.crmid = ip.CrmId
        where 1=1
        <if test="(queryStartTime != null and queryStartTime != '') and (queryEndTime != null and queryStartTime != '')">
            and ip_min.summitTime between #{queryStartTime} and #{queryEndTime}
        </if>
        ) main on mc.CustomerServiceCode = main.ServiceCode
        order by main.processTime DESC
        <if test="size != 0">
            LIMIT #{start} , #{size}
        </if>
    </select>

    <select id="selectFinalCrmIdAndIssueStatus" resultType="com.digiwin.escloud.aioissue.model.Issue">

        select distinct i.CrmId crmId,i.IssueStatus issueStatus
        from issue i
        inner join (select CrmId, MAX(__version__) __version__
        from issue where 1=1
        <foreach collection="crmIdList" item="crmId"  open="and CrmId in (" separator=" , " close=")">
            #{crmId}
        </foreach>
        group by CrmId ) i2
        on i.CrmId = i2.CrmId and i.__version__ = i2.__version__
        where 1=1
        <foreach collection="crmIdList" item="crmId"  open="and i.CrmId in (" separator=" , " close=")">
            #{crmId}
        </foreach>


    </select>

</mapper>
