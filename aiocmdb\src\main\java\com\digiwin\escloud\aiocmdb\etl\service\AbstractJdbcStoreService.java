package com.digiwin.escloud.aiocmdb.etl.service;

import com.alibaba.fastjson.JSONArray;
import com.digiwin.escloud.common.constant.BigDataConstant;
import com.digiwin.escloud.common.response.BaseResponse;
import com.digiwin.escloud.aiocmdb.assetmaintenance.bigdata.BigDataUtil;
import com.digiwin.escloud.aiocmdb.etl.dto.EtlEngineSaveReqDTO;
import com.digiwin.escloud.aiocmdb.etl.dto.EtlEngineTableRespDTO;
import com.digiwin.escloud.etl.model.EtlEngine;
import com.digiwin.escloud.etl.model.SinkType;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * @Date 2024/11/25 13:13
 * @Created yanggld
 * @Description
 */
@Slf4j
public abstract class AbstractJdbcStoreService implements IEtlStoreService {

    public String INT = "INT";
    public String BIGINT = "BIGINT";
    public String BIT = "BIT";
    public String CHAR = "CHAR";
    public String VARCHAR = "VARCHAR";
    public String TEXT = "TEXT";
    public String DECIMAL = "DECIMAL";
    public String BOOLEAN = "BOOLEAN";
    public String DATE = "DATE";
    public String TIME = "TIME";
    public String DATETIME = "DATETIME";
    public String ENUM = "ENUM";
    public String RESOURCE = "RESOURCE";

    public abstract BigDataUtil getBigDataUtil();
    public abstract String schemaSql(String schemaName, String schemaExtend);

    public abstract String tableSql(EtlEngine etl);

    @Override
    public boolean initStore(EtlEngine t) {
        return createSchema(t) && createTable(t);
    }

    @Override
    public String getSchemaDdl(EtlEngine etl) {
        String schemaName = etl.getSchemaName();
        if (BigDataConstant.DEFAULT_SCHEMA.equalsIgnoreCase(schemaName) && etl.getSinkType().equalsIgnoreCase(SinkType.STARROCKS.getName())) {
            schemaName = BigDataConstant.DEFAULT_SR_DB;
        }
        if (!schemaName.equalsIgnoreCase(BigDataConstant.DEFAULT_SCHEMA)) {
            //目前没有schemaExtend字段，暂时不处理schemaExtend逻辑
            String sql = schemaSql(schemaName, "");
            if (log.isDebugEnabled()) {
                log.debug(sql);
            }
            return sql;
        }
        return null;
    }


    @Override
    public String getSinkDdl(EtlEngine etl) {
        return tableSql(etl);
    }


    @Override
    public List<String> getAlterDdl(EtlEngineTableRespDTO etlEngineTableRespDTO) {
        List<String> result = new ArrayList<>();
        String changeOp = etlEngineTableRespDTO.getChangeOp();
        if (StringUtils.isBlank(changeOp)) {
            return result;
        }
        List<EtlEngineSaveReqDTO.EtlSinkOp> changeOpList = JSONArray.parseArray(changeOp).toJavaList(EtlEngineSaveReqDTO.EtlSinkOp.class);
        for (EtlEngineSaveReqDTO.EtlSinkOp sinkOp : changeOpList) {
            result.add(alterDdlSql(etlEngineTableRespDTO.getSchemaName(),etlEngineTableRespDTO.getSinkName(),sinkOp));
        }
        return result;
    }
    public List<String> getAlterDdl(String changeOp, String schemaName, String sinkName) {
        List<String> result = new ArrayList<>();
        if (StringUtils.isBlank(changeOp)) {
            return result;
        }
        List<EtlEngineSaveReqDTO.EtlSinkOp> changeOpList = JSONArray.parseArray(changeOp).toJavaList(EtlEngineSaveReqDTO.EtlSinkOp.class);
        return getAlterDdl(changeOpList,schemaName,sinkName);
    }
    public List<String> getAlterDdl( List<EtlEngineSaveReqDTO.EtlSinkOp> changeOpList, String schemaName, String sinkName) {
        List<String> result = new ArrayList<>();
        for (EtlEngineSaveReqDTO.EtlSinkOp sinkOp : changeOpList) {
            result.add(alterDdlSql(schemaName,sinkName,sinkOp));
        }
        return result;
    }

    protected abstract String alterDdlSql(String schemaName, String sinkName, EtlEngineSaveReqDTO.EtlSinkOp sinkOp);

    private boolean createSchema(EtlEngine etl) {
        String schemaDdl = getSchemaDdl(etl);
        if (StringUtils.isBlank(schemaDdl)) {
            return true;
        } else {
            return createSchema(schemaDdl);
        }
    }

    private boolean createSchema(String sql) {
        BaseResponse resp = getBigDataUtil().srExecuteSql(sql);
        if (resp.checkIsSuccess()) {
            return true;
        } else {
            throw new RuntimeException(resp.getData().toString());
        }
    }


    private boolean createTable(EtlEngine etl) {
        String sql = "";
        try {
            sql = tableSql(etl);
            return createTable(sql);
        } catch (Exception ex) {
            return false;
        }
    }

    private boolean createTable(String sql) {
        BaseResponse resp = getBigDataUtil().srExecuteSql(sql);
        if (resp.checkIsSuccess()) {
            return true;
        } else {
            throw new RuntimeException(resp.getData().toString());
        }
    }
}
