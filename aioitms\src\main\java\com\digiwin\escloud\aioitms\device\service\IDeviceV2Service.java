package com.digiwin.escloud.aioitms.device.service;

import com.digiwin.escloud.aioitms.bigdata.ImpalaQuery;
import com.digiwin.escloud.aioitms.collectapp.model.CollectAppMapping;
import com.digiwin.escloud.aioitms.collectwarning.model.CollectWarning;
import com.digiwin.escloud.aioitms.device.model.*;
import com.digiwin.escloud.aioitms.instance.model.AiopsItemContext;
import com.digiwin.escloud.aioitms.instance.model.WarningMergeStatisticsDetailInfo;
import com.digiwin.escloud.aioitms.model.authorize.AiopsAuthStatus;
import com.digiwin.escloud.aioitms.model.device.AiopsKitDevice;
import com.digiwin.escloud.aioitms.model.device.TenantDeviceCntReq;
import com.digiwin.escloud.aioitms.model.instance.AdimRelateContext;
import com.digiwin.escloud.aioitms.modulecollect.model.ModuleCollectLayered;
import com.digiwin.escloud.common.response.BaseResponse;
import com.digiwin.escloud.common.response.PageInfo;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.function.Consumer;

/**
 * 设备服务v2
 */
public interface IDeviceV2Service {
    /**
     * 保存设备相关信息
     * @param deviceCompleteInfo 设备完整信息(包括基础、用户协议、数据库等信息)
     * @return 回覆对象
     */
    BaseResponse saveDeviceByCompleteInfo(AiopsKitDeviceCompleteInfo deviceCompleteInfo);

    /**
     * 保存设备信息
     * @param device 设备基础信息
     * @return 回覆对象
     */
    BaseResponse saveDeviceInfo(AiopsKitDevice device);

    /**
     * 保存设备的协议信息
     * @param deviceAgreement 设备协议
     * @return 回覆对象
     */
    BaseResponse saveDeviceAgreement(AiopsKitDeviceAgreement deviceAgreement);

    /**
     * 保存设备的协议信息
     * @param deviceId 设备Id
     * @param eid 租户Sid
     * @return 回覆对象
     */
    BaseResponse saveDeviceCustomerInfo(String deviceId, Long eid);

    /**
     * 保存设备的数据源
     //     * @param adId 设备主键Id  //huly: 修复漏洞/bug 去掉入参adId
     * @param deviceId 设备Id
     * @param aiopsKitDataSourceList 数据源列表
     * @return 回覆对象
     */
    BaseResponse saveDeviceDataSource(String deviceId,
                                      List<AiopsKitDeviceDataSource> aiopsKitDataSourceList);

    /**
     * 保存设备特定数据库设定
     * @param deviceId 设备Id
     * @param tryOpenAuth 是否尝试开启授权
     * @param aiopsKitDataSource 数据源
     * @param addCollItem 采集项目标志位
     * @param aicConsumer 运维项目上下文加工消费器
     * @return 回覆对象
     */
    BaseResponse savePartDeviceDataSource(String deviceId, Boolean tryOpenAuth,
                                          AiopsKitDeviceDataSource aiopsKitDataSource, Boolean addCollItem,
                                          Consumer<AiopsItemContext> aicConsumer);

    /**
     * 删除设备特定数据库设定
     * @param deviceId 设备Id
     * @param dbId 数据库Id
     * @return 回覆对象
     */
    BaseResponse deletePartDeviceDataSource(String deviceId, String dbId);

    /**
     * 获取设备基础信息
     * @param deviceId 设备Id
     * @return 回覆对象
     */
    BaseResponse getDeviceBasicInfo(String deviceId);

    /**
     * 获取设备授权状态
     * @param deviceId 设备Id
     * @return 回覆对象
     */
    BaseResponse getDeviceAuthStatus(String deviceId);

    /**
     * 获取设备是否是未定义
     * @param deviceId 设备Id
     * @return 回覆对象
     */
    BaseResponse getDeviceIsUndefined(String deviceId);

    /**
     * 依据租户Id与设备Id获取设备信息
     * @param eid 租户Id
     * @param deviceId 设备Id
     * @return 设备信息
     */
    AiopsKitDevice getDeviceInfoByEidAndDeviceId(Long eid, String deviceId);

    /**
     * 依据设备Id获取设备信息
     * @param deviceId 设备Id
     * @return 设备信息
     */
    AiopsKitDevice getDeviceByDeviceId(String deviceId);

    /**
     * 依据设备Id获取设备主键Id
     * @param deviceId 设备Id
     * @return 设备主键Id
     */
    Long getAdIdByDeviceId(String deviceId);

    /**
     * 实例真实信息保存(从数采异动同步)
     * @param aiTrustInfoMap 实例真实信息字典
     * @return 回覆对象
     */
    BaseResponse aiTrustInfoSave(Map<String, Object> aiTrustInfoMap);

    /**
     * 实例真实信息移除(从数采异动同步)
     * @param aiTrustInfoMap 实例真实信息字典
     * @return 回覆对象
     */
    BaseResponse aiTrustInfoRemove(Map<String, Object> aiTrustInfoMap);

    /**
     * 获取设备列表
     * @param deviceRequest 设备请求条件
     * @return 分页后的设备列表
     */
    BaseResponse getDeviceList(DeviceRequest deviceRequest);

    /**
     * 获取设备信息
     * @param map 条件字典
     * @return 设备信息
     */
    DeviceInfo getDeviceDetail(Map<String, Object> map);

    /**
     * 依据类型获取设备详情
     *
     * @param id       设备主键Id
     * @param type     类型
     * @param pageNum  页次
     * @param pageSize 页笔数
     * @return
     */
    BaseResponse<PageInfo> getDeviceDetailByType(long id, String type, String search, int pageNum, int pageSize);

    /**
     * 获取设备收集项预警项设定列表
     * @param adId 设备主键Id
     * @param pageNum 页次
     * @param pageSize 页笔数
     * @return 回覆对象
     */
    BaseResponse getDeviceCollectWarningSetList(Long adId, Integer pageNum, Integer pageSize);

    /**
     * 依据运维实例Id获取预警设定列表
     * @param aiId 运维实例Id
     * @param needPaging 是否分页
     * @param pageNum 页次
     * @param pageSize 页笔数
     * @return 回覆对象
     */
    BaseResponse getWarningListByAiId(Long aiId, Boolean needPaging, Integer pageNum, Integer pageSize);

    /**
     * 添加设备产品应用/服务
     * @param spId 产品Id
     * @param adId 设备主键Id
     * @param deviceId 设备Id
     * @param deviceProductMappingList 设备产品应设列表
     * @return 回覆对象
     */
    BaseResponse addProductAppForDevice(
            long spId, long adId, String deviceId, List<DeviceProductMapping> deviceProductMappingList);

    /**
     * 新增设备收集项
     * @param adId 设备主键Id
     * @param deviceId 设备Id
     * @param autoAddCollect 是否自动添加收集项
     * @param deviceProductMappingList 设备产品映射列表
     */
    void addAppCollectForDevice(Long adId, String deviceId, boolean autoAddCollect, List<DeviceProductMapping> deviceProductMappingList);

    /**
     * @param spId 产品Id
     * @param adId 设备主键Id
     * @return
     */
    List<DeviceProductMapping> getProductAppForDevice(long spId, long adId, String modelCode);

    /**
     * 更新设备产品应用/服务
     * @param spId 产品Id
     * @param adId 设备主键
     * @param deviceId 设备Id
     * @param deviceProductMappingList 设备产品应设列表
     * @return 回覆对象
     */
    BaseResponse updateProductAppForDevice(
            long spId, long adId, String deviceId, String aiopsItem, List<DeviceProductMapping> deviceProductMappingList);

    /**
     * 更新设备产品应用/服务
     * @param sid 运维商Id
     * @param spId 产品Id
     * @param adId 设备主键
     * @return 回覆对象
     */
    BaseResponse deleteProductAppForDevice(long sid,long spId, long adId);

    /**
     * 获取默认收集项(未关联该设备)
     * @param sid 运维商Id
     * @param deviceId 设备Id
     * @param scopeId 作用域Id
     * @param collectItem 收集项名称(模糊查询用)
     * @return
     */
    BaseResponse getCollectConfigsNotInDevice(long sid, String deviceId, String scopeId, String collectItem);

    /**
     * 设备新增收集项
     * @param sid 运维商Id
     * @param adcdId 设备收集项明细Id
     * @param adId 设备主键Id
     * @param collectName 收集项名称
     * @param accId 收集项Id
     * @return 回覆对象
     */
    BaseResponse addCollectDetailForDevice(long sid, long adcdId, long adId, String deviceId, String collectName,
                                           long accId, String scopeId);

    /**
     * 更新设备收集项详情
     * @param adcdId 设备收集项明细Id
     * @param collectName 收集项名称
     * @return 回覆对象
     */
    BaseResponse updateCollectDetailForDevice(long adcdId, String collectName, String deviceId);

    /**
     * 删除设备收集项详情
     * @param adcdId 设备收集项明细Id
     * @return 回覆对象
     */
    BaseResponse deleteCollectDetailForDevice(long adcdId, long accId, String deviceId, String scopeId,
                                              List<CollectWarning> collectWarningList);

    /**
     * 设定设备执行参数
     * @Param eid 租户Id
     * @param changeInterval 是否异动执行周期
     * @param deviceCollectDetail 设备收集项信息
     * @return 回覆对象
     */
    BaseResponse execParamsSetForDevice(Long eid, boolean changeInterval, long adId, String deviceId,
                                        DeviceCollectDetail deviceCollectDetail);

    /**
     * 自定义收集项
     * @param accMap 自定义收集项参数
     * @param adcdId 设备收集项明细Id
     * @param deviceId 设备Id
     * @return 回覆对象
     */
    BaseResponse customizeAcc(Map<String, Object> accMap, Long adcdId, String deviceId);

    /**
     * 异动设备收集项详情启用状态
     * @param adcdId 设备收集项明细Id
     * @param isEnable 启用状态
     * @param deviceId 设备Id
     * @return 是否执行成功
     */
    BaseResponse modifyCollectDetailEnableForDevice(long adcdId, boolean isEnable, String deviceId);

    /**
     * 批量异动收集项配置预警启用状态
     * @param adcdId 设备收集项明细Id
     * @param isWarningEnable 预警启用状态
     * @param collectWarningList 收集项预警列表
     * @return 回覆对象
     */
    BaseResponse modifyCollectConfigWarningEnableForDevice(long adcdId, boolean isWarningEnable, List<CollectWarning> collectWarningList);

    /**
     * 异动收集项配置预警启用状态
     * @param adcdId 设备收集项明细Id
     * @param acwId 收集项预警项Id
     * @param isWarningEnable 预警启用状态
     * @return 回覆对象
     */
    BaseResponse modifyWarningEnableForDeviceWarning(long adcdId, long acwId, boolean isWarningEnable);

    /**
     * 批量开关预警项
     *
     * @param acwId
     * @param adcwIdList
     * @return
     */
    BaseResponse modifyWarningEnableForDeviceWarningBatch(Long acwId, boolean isWarningEnable, Long eid, List<Long> adcwIdList);

    /**
     * 异动设备预警设定
     * @param adcdId 设备收集项明细Id
     * @param acwId 收集项预警项Id
     * @param deviceId 设备Id
     * @param collectWarning 收集项预警项
     * @return 回覆对象
     */
    BaseResponse modifyWarningSetForDevice(long adcdId, long acwId, String deviceId, CollectWarning collectWarning);

    /**
     * 获取设备预警列表
     * @param deviceId 设备Id
     * @param accId 收集项Id
     * @param adcdId 设备收集项明细Id
     * @return 回覆对象
     */
    BaseResponse getWarningListForDeviceCache(String deviceId, long accId, Long adcdId);
    BaseResponse removeWarningListForDeviceCache(String deviceId, long accId, Long adcdId);

    /**
     * 获取设备数据源列表
     * @param adId 设备主键Id
     * @param dbType 数据库类型
     * @param dbIdCollection 数据库Id集合
     * @return 设备数据源列表
     */
    List<AiopsKitDeviceDataSource> getDeviceDataSourceList(Long adId, String dbType, Collection<String> dbIdCollection);

    /**
     * 获取设备应用/服务Id列表
     * @param adId 设备主键Id
     * @return 设备应用/服务Id列表
     */
    List<DeviceAppInfo> getDeviceAppIdList(Long adId);

    /**
     * 依据产品服务/应用映射列表新增设备收集项
     * @param adId 设备主键Id
     * @param deviceId 设备Id
     * @param collectAppMappingList 收集项产品服务/应用映射列表
     * @return 回覆对象
     */
    BaseResponse addDeviceCollectDetailByAppMappingList(Long adId, String deviceId, List<CollectAppMapping> collectAppMappingList);

    /**
     * 获取设备执行参数设置列表
     * @param adcId 设备收集项版本Id
     * @param collectAppMappingList 收集项产品服务/应用列表
     * @return 回覆对象
     */
    BaseResponse getDeviceCollectDetailExecParams(Long adcId, List<CollectAppMapping> collectAppMappingList);

    /**
     * 获取设备收集项预警列表(只有收集项预警项，没有预警设定相关内容)
     * @param adcId 设备收集项版本Id
     * @param collectAppMappingList 收集项产品服务/应用列表
     * @return 回覆对象
     */
    BaseResponse getDeviceCollectWarningOneLevelList(Long adcId, List<CollectAppMapping> collectAppMappingList);

    /**
     * 获取设备收集项预警
     * @param adcwId 设备收集项预警Id
     * @param adcdId 设备收集项列表Id
     * @param collectAppMappingList 收集项产品服务/应用列表
     * @return 回覆对象
     */
    BaseResponse getDeviceCollectWarning(Long adcwId, Long adcdId, List<CollectAppMapping> collectAppMappingList);

    /**
     * 获取预警通知映射
     * @param adcwId 设备收集项预警Id
     * @param containContinueStopSetting 是否包含停发通知设定
     * @return 回覆对象
     */
    BaseResponse getWarningNotifyMapping(Long adcwId, Boolean containContinueStopSetting);

    /**
     * 检查是否存在通知映射
     * @param sourceId 来源Id(租户群组通知人Id或租户通知群组Id)
     * @param notifyCategoryString 通知类别字串
     * @return 回覆对象
     */
    BaseResponse checkNotifyMappingExist(Long sourceId, String notifyCategoryString);

    /**
     * 保存设备预警通知
     * @param adcwId 设备收集项预警Id
     * @param deviceWarningNotify 设备预警通知
     * @return 回覆对象
     */
    BaseResponse saveDeviceWarningNotify(Long adcwId, DeviceWarningNotify deviceWarningNotify);

    /**
     * 删除设备预警通知
     * @param adcwId 设备收集项预警Id
     * @param adwnmId 设备预警通知映射Id
     * @param notifyCategoryString 通知类别字串
     * @return 回覆对象
     */
    BaseResponse removeDeviceWarningNotify(Long adcwId, Long adwnmId, String notifyCategoryString);

    /**
     * 异动设备预警通知默认带入项
     * @param oriSourceId 原始的来源Id(租户群组通知人Id或租户通知群组Id)
     * @param sourceId 来源Id(租户群组通知人Id或租户通知群组Id)
     * @param notifyCategoryString 通知类别字串
     * @return 回覆对象
     */
    BaseResponse changeDeviceWarningNotifyDefault(Long oriSourceId, Long sourceId, String notifyCategoryString);

    /**
     * 修正设备收集项详情存在，但是设备收集项预警不存在
     * @param accId 收集项Id
     * @param acwId 收集项预警项Id
     * @param isWarningEnable 是否启用预警
     * @return 回覆对象
     */
    BaseResponse fixDeviceCollectWarning(Long accId, Long acwId, Boolean isWarningEnable);

    /**
     * 异步修正设备收集项详情存在，但是设备收集项预警不存在
     * @param accId 收集项Id
     * @param acwId 收集项预警项Id
     * @param isWarningEnable 是否启用预警
     * @return 回覆对象
     */
    void fixDeviceCollectWarningAsync(Long accId, Long acwId, Boolean isWarningEnable);

    /**
     * 清除设备收集项预警
     * @param accId 收集项Id
     * @param acwId 收集项预警项Id
     * @return 回覆对象
     */
    BaseResponse clearDeviceCollectWarning(Long accId, Long acwId);

    /**
     * 异步清除设备收集项预警
     * @param accId 收集项Id
     * @param acwId 收集项预警项Id
     */
    void asyncClearDeviceCollectWarning(Long accId, Long acwId);

    /**
     * 地端获取设备类型列表
     * @return 回覆对象(设备类型列表)
     */
    BaseResponse getDeviceTypeList();


    /**
     * 获取设备收集项预警映射
     * @param adcwId 设备收集项预警Id
     * @param notifyCategory 通知类型
     * @return 设备收集项预警映射列表
     */
    List<DeviceWarningNotifyMapping> getDeviceWarningNotifyMapping(Long adcwId, String notifyCategory);

    /**
     * 获取设备类型在线统计
     * @param eid 租户Id
     * @return 回覆对象(设备类型在线统计列表)
     */
    BaseResponse getDeviceTypeOnlineStatistic(Long eid);

    /**
     * 获取离线设备数
     * @param eid 租户Id
     * @return 回覆对象
     */
    BaseResponse getDeviceOfflineTotal(Long eid);

    /**
     * 获取设备云管家用户映射列表
     * @param eid 租户Id
     * @param serviceCode 客服代号
     * @param needPaging 是否需要分页
     * @param pageNum 页码
     * @param pageSize 页笔数
     * @param searchText 查找文字(设备名、放置点、备注等)
     * @return 回覆对象
     */
    BaseResponse getDeviceScpUserMappingList(Long eid, String serviceCode, Boolean needPaging,
                                             Integer pageNum, Integer pageSize, String searchText);

    /**
     * 获取设备收集项串联用列表
     * @param adId 设备主键Id
     * @return 回覆对象
     */
    BaseResponse getAdcdSeriesList(Long adId);

    /**
     * 异动未定义设备作废状态
     * @param deviceId 设备Id
     * @param isDeleted 作废状态
     * @return 回覆对象
     */
    BaseResponse modifyUndefineDeviceIsDeletedStatus(String deviceId, Boolean isDeleted);

    /**
     * 依据运维项目上下文列表批量保存设备运维实例映射
     * @param aicList 运维项目上下文列表
     * @return 回覆对象
     */
    BaseResponse batchSaveDeviceInstanceMappingByAicList(List<AiopsItemContext> aicList);

    /**
     * 处理设备收集项明细只与实例Id映射的内容
     * @param aicList 运维项目上下文列表
     * @return 回覆对象
     */
    BaseResponse processAdcdOnlyMappingAiId(List<AiopsItemContext> aicList);

    /**
     * 批量保存设备运维实例映射
     * @param deviceInstanceMappingList 设备运维实例映射列表
     * @return 回覆对象
     */
    BaseResponse batchSaveAiopsDeviceInstanceMapping(List<DeviceInstanceMapping> deviceInstanceMappingList);

    /**
     * 批量修正设备收集项明细执行参数内容
     * @param aicList 运维项目上下文列表
     * @return 回覆对象
     */
    BaseResponse batchFixAdcdExecParamsContent(List<AiopsItemContext> aicList);

    /**
     * 依据运维项目上下文列表查询设备运维实例映射
     * @param aicList 运维项目上下文列表
     * @return 回覆对象
     */
    BaseResponse getDeviceInstanceMappingByAicList(List<AiopsItemContext> aicList);

    /**
     * 为设备添加模组收集项
     * @param aiopsItemContext 运维项目实例
     */
    void addModuleCollectForDevice(AiopsItemContext aiopsItemContext);

    /**
     * 依据运维项目上下文列表异动设备作废状态
     * @param aicList 运维项目上下文列表
     * @param authStatus 运维授权状态
     * @return 回覆对象
     */
    BaseResponse modifyDeviceDeletedStatusByAicList(List<AiopsItemContext> aicList,
                                                    AiopsAuthStatus authStatus);

    /**
     * 依据运维项目Id获取设备收集项明细
     * @param deviceId 设备Id
     * @param aiopsItemId 运维项目Id
     * @return 回覆对象
     */
    BaseResponse getDeviceCollectDetailByAiopsItemId(String deviceId, String aiopsItemId);

    /**
     * 依据运维项目类型获取设备运维实例列表
     * @param deviceId 设备Id
     * @param aiopsItemGroup 运维项目群组
     * @param aiopsItemType 运维项目类型
     * @return 回覆对象
     */
    BaseResponse getDeviceAiopsInstanceByAiopsItemType(String deviceId, String aiopsItemGroup, String aiopsItemType);

    /**
     * 获取设备运维项目设定列表
     * @param deviceId 设备Id
     * @param aiopsItemGroup 运维项目群组
     * @param aiopsItemType 运维项目类型
     * @param needPaging 是否分页
     * @param pageNum 页次
     * @param pageSize 页笔数
     * @return 回覆对象
     */
    BaseResponse getDeviceAiopsItemSetting(String deviceId, String aiopsItemGroup, String aiopsItemType,
                                           Boolean needPaging, Integer pageNum, Integer pageSize);

    /**
     * 依据运维项目Id获取运维实例收集项分层列表
     * @param deviceId 设备Id
     * @param aiopsItemId 运维项目Id
     * @return 回覆对象
     */
    BaseResponse getDeviceAiopsInstanceCollectByAiopsItemId(String deviceId, String aiopsItemId);

    /**
     * 依据模组收集项分层列表添加设备收集项列表
     * @param deviceId 设备Id
     * @param aiopsItemId 运维项目Id
     * @param mclList 模组收集项分层列表
     * @return 回覆对象
     */
    BaseResponse addDeviceCollectDetailByModuleCollectLayered(String deviceId, String aiopsItemId,
                                                              List<ModuleCollectLayered> mclList);

    /**
     * 获取设备映射的运维实例项目类型
     * @param deviceId 设备Id
     * @return 回覆对象
     */
    BaseResponse getDeviceMappingAiopsInstanceItemType(String deviceId);

    /**
     * 获取设备映射的运维实例项目群组
     * @param deviceId 设备Id
     * @return 回覆对象
     */
    BaseResponse getDeviceMappingAiopsInstanceItemGroup(String deviceId);

    /**
     * 添加设备运维实例映射
     * @param aiopsItemContext 运维项目上下文
     * @param clearDeviceCollectConfigCache 是否清理设备收集项缓存
     * @return 回覆对象
     */
    BaseResponse addDeviceAiopsInstanceMapping(AiopsItemContext aiopsItemContext, boolean clearDeviceCollectConfigCache);


    /**
     * 依据运维设备实例映射关系上下文添加设备运维实例映射
     * @param arc 运维设备实例映射关系上下文
     * @return 回覆对象
     */
    BaseResponse addDeviceAiopsInstanceMappingByArc(AdimRelateContext arc);

    /**
     * 移除设备运维实例映射
     * @param deviceId 设备Id
     * @param aiopsItemIdList 运维实例Id列表
     * @return 回覆对象
     */
    BaseResponse removeDeviceAiopsInstanceMapping(String deviceId, List<String> aiopsItemIdList);

    /**
     * 依据运维设备实例映射关系上下文移除设备运维实例映射
     * @param arc 运维设备实例映射关系上下文
     * @return 回覆对象
     */
    BaseResponse removeDeviceAiopsInstanceMappingByArc(AdimRelateContext arc);

    /**
     * 获取特定设备类型设备列表
     * @param eid 租户Id
     * @param deviceType 设备类型
     * @param selfDeviceId 自己的设备Id
     * @return 回覆对象
     */
    BaseResponse getCurrentDeviceTypeDeviceList(Long eid, String deviceType, String selfDeviceId);

    /**
     * 依据租户Id获取设备数据库列表
     * @param eid 租户Id
     * @return 回覆对象
     */
    BaseResponse getDeviceDataSourceByEid(Long eid);

    /**
     * 依据运维项目Id列表异动来源设备运维实例映射到目标
     * @param sourceDeviceId 来源设备Id
     * @param targetAdId 目标设备主键Id
     * @param targetDeviceId 目标设备Id
     * @param aiopsItemIdList 运维项目Id列表
     * @return 回覆对象
     */
    BaseResponse modifySourceAdimToTargetByAiopsItemIdList(String sourceDeviceId, Long targetAdId,
                                                           String targetDeviceId, List<String> aiopsItemIdList);

    /**
     * 依据运维项目Id列表异动来源设备收集项明细到目标
     * @param sourceDeviceId 来源设备Id
     * @param targetAdId 目标设备主键Id
     * @param targetDeviceId 目标设备Id
     * @param aiopsItemIdList 运维项目Id列表
     * @return 回覆对象
     */
    BaseResponse modifySourceAdcdToTargetByAiopsItemIdList(String sourceDeviceId, Long targetAdId,
                                                           String targetDeviceId, List<String> aiopsItemIdList);

    /**
     * 依据数据库Id集合获取设备符合的数据库Id列表
     * @param deviceId 设备Id
     * @param dbIdCollection 数据库Id集合
     * @return 回覆对象
     */
    BaseResponse getDeviceDatasourceMatchDbId(String deviceId, Collection<String> dbIdCollection);

    /**
     * 依据运维项目Id列表移除设备收集项明细
     * @param deviceId 设备Id
     * @param aiopsItemIdList 运维项目Id列表
     * @return 回覆对象
     */
    BaseResponse removeAdcdByAiopsItemIdList(String deviceId, List<String> aiopsItemIdList);

    /**
     * 获取没有运维实例的设备信息列表
     * @param map 条件字典
     * @return 设备信息列表
     */
    List<DeviceInfo> getNotAiopsInstanceDeviceList(Map<String, Object> map);

    /**
     * 依据设备收集项明细Id列表异动运维实例Id及设备实例映射Id
     * @param aiId 运维实例Id
     * @param adimId 设备实例映射Id
     * @param adcdIdList 设备收集项明细Id列表
     * @return 回覆对象
     */
    BaseResponse modifyDeviceCollectDetailAiIdAdimIdByAdcdIdList(Long aiId, Long adimId, List<Long> adcdIdList);

    /**
     * 依据条件字典获取设备数据库列表
     * @param map 条件字典
     * @return 设备数据库列表
     */
    List<AiopsKitDeviceDataSource> getDeviceDataSourceByMap(Map<String, Object> map);

    /**
     * 依据字典获取没有运维实例及运维设备映射的设备收集项明细
     * @param map 条件字典
     * @return 设备收集项明细
     */
    List<DeviceCollectDetail> getNotAiIdAndAdimIdAdcdByMap(Map<String, Object> map);

    /**
     * 批量依据字典列表异动设备实例映射运维实例Id
     * @param mapList 字典列表
     * @return 影响笔数
     */
    Integer batchModifyDeviceInstanceMappingAiIdByMapList(List<Map<String, Object>> mapList);

    /**
     * 批量依据字典列表异动设备收集项明细运维实例Id
     * @param mapList 字典列表
     * @return 影响笔数
     */
    Integer batchModifyDeviceCollectDetailAiIdByMapList(List<Map<String, Object>> mapList);


    /**
     * 依据条件字典列表获取设备应用/服务映射列表
     * @param mapList 条件字典列表
     * @return 设备应用/服务映射列表
     */
    List<DeviceProductMapping> getAdpmListByMapList(List<Map<String, Object>> mapList);

    /**
     * 依据设备应用/服务映射Id列表移除设备应用/服务映射
     * @param adpmIdList 设备应用/服务映射Id列表
     * @return 影响笔数
     */
    Integer removeAdpmByAdpmIdList(List<Long> adpmIdList);

    /**
     * 依据设备应用/服务映射Id列表异动设备应用/服务映射的设备主键Id
     * @param adId 设备主键Id
     * @param deviceId 设备Id
     * @param adpmIdList 设备应用/服务映射Id列表
     * @return 影响笔数
     */
    Integer modifyAdpmAdIdByAdpmIdList(Long adId, String deviceId, List<Long> adpmIdList);

    /**
     * 依据设备Id获取租户Id
     * @param deviceId 设备Id
     * @return 租户Id
     */
    Long getEidByDeviceId(String deviceId);

    /**
     * 获取分组设备收集项明细执行参数
     * @param deviceId 设备Id
     * @param aiopsItemIdList 运维项目Id列表
     * @return 回覆对象
     */
    BaseResponse getGroupAdcdExecParam(String deviceId, List<String> aiopsItemIdList);

    /**
     * 保存分组设备收集项明细执行参数
     * @param deviceId 设备Id
     * @param adId 设备主键Id
     * @param eid 租户Sid
     * @param groupAdcdExecParamList 分组设备收集项明细执行参数列表
     * @return 回覆对象
     */
    BaseResponse saveGroupAdcdExecParam(String deviceId, Long adId, Long eid,
                                        List<GroupAdcdExecParam> groupAdcdExecParamList);

    /**
     * 获取运维实例
     *
     * @param deviceId
     * @param aiopsItemId
     * @return
     */
    BaseResponse queryAiopsItemInstance(String deviceId, String aiopsItemId);

    /**
     * 通过租户获取设备看板
     *
     * @param eid
     */
    BaseResponse getDeviceDashboard(Long eid);

    /**
     * 依据条件字典获取具有真实启用状态的设备收集项明细列表
     * @param map 条件字典
     * @return 具有真实启用状态的设备收集项明细列表
     */
    List<Map<String, Object>> getAdcdTrueEnableByMap(Map<String, Object> map);

    /**
     * 获取设备看板上部分信息
     *
     * @param eid
     * @param aiopsItemList
     * @return
     */
    BaseResponse getDashboardUpperData(Long eid,List<String> aiopsItemList);

    /**
     * 获取it运维列表
     * @param eid
     * @param q
     */
    BaseResponse queryServerList(long eid, ImpalaQuery q);

    /**
     * 获取租户下某个运维项目的预警项
     *
     * @param eid
     * @param aiopsItem
     * @param pageNum
     * @param pageSize
     * @param warningCode
     * @param warningName
     * @param warningLevelString
     * @param customize
     * @return
     */
    BaseResponse getAwcByEidAndAiopsItem(Long eid, String aiopsItem, Integer pageNum, Integer pageSize, String warningCode,
                                         String warningName, String warningLevelString, Boolean customize);

    /**
     * 获取租户下某个运维项目的预警项
     *
     * @param eid
     * @param aiopsItem
     * @param acwId
     * @return
     */
    BaseResponse<List<WarningMergeStatisticsDetailInfo>> getAwcByEidAndAiopsItemAndAcwId(Long eid, String aiopsItem, Long acwId);

    BaseResponse batchModifyWarningSetForDevice(BatchModifyWarningSetForDeviceRequest batchModifyWarningSetRequest);

    BaseResponse batchModifyWarningSet(BatchModifyWarningSetRequest batchModifyWarningSetRequest);

    BaseResponse getTenantDeviceCnt(TenantDeviceCntReq tenantDeviceCntReq);


    BaseResponse updateTenantWarningEnable(AiopsTenantWarning tenantWarning);

    Map<Long, Boolean> getTenantWarningEnable(List<Long> acwIdList, Long eid);

    BaseResponse fixSourceAcwIdNull( Long eid);

    void deleteWarning(String deviceId);

    BaseResponse getAcwIsEnable(Long acwId, Long eid);

    void batchAddDefaultDeviceWarningNotifyMapping(List<DeviceCollectWarning> deviceCollectWarningList,
                                                   Long eid, String deviceId);

    BaseResponse getAwcByEidAndAiopsItemAndAsset(Long eid, String aiopsItem, Boolean related, String deviceNameOrId, int pageNum, int pageSize, Long aiopsInstance);

}
