package com.digiwin.escloud.issueservice.v3.service.impl;


import com.alibaba.fastjson.JSON;
import com.digiwin.escloud.common.util.CollectionUtil;
import com.digiwin.escloud.issueservice.t.model.issuekb.IssueKbExecuteRecordStatus;
import com.digiwin.escloud.issueservice.t.model.issuekb.IssueKbOverview;
import com.digiwin.escloud.issueservice.v3.dao.IIssuekbDaoV3;
import com.digiwin.escloud.issueservice.v3.service.IIndepthAIService;
import com.digiwin.escloud.issueservice.v3.service.IIssuekbServiceV3;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.codec.ServerSentEvent;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

@Slf4j
@Service
public class IssuekbServiceV3 implements IIssuekbServiceV3 {
    @Autowired
    private IIssuekbDaoV3 dao;
    @Autowired
    IIndepthAIService aiService;
    @Override
    public void reBuildIssueKb(long id){
        log.info("获取当前生成失败的案件知识");


        Runnable runnable = () -> updateIssueKbOverviews(id);
        ExecutorService executorService = Executors.newSingleThreadExecutor();
        try {
            executorService.execute(runnable);
        } catch (Exception ex) {
            log.error("asyncRun", ex);
        } finally {
            executorService.shutdown();
            log.info("获取昨天结案的案件，生成知识 结束");
        }
    }


    //生成知识，并记录案件知识一览表中
    private void updateIssueKbOverviews(long id) {
        List<IssueKbOverview> issueKbOverviews = dao.getFailIssuesKbOverviews(id);
        if(CollectionUtil.isEmpty(issueKbOverviews)){
            return;
        }
        //重新生成时，初始化状态为生成中
        dao.updateIssueKbExecuteRecordStatus(id,IssueKbExecuteRecordStatus.INIT.getIndex());
        int size = issueKbOverviews.size();
        for(int i=0 ; i<size;i++){
            IssueKbOverview issueKbOverview = issueKbOverviews.get(i);
            //循环每个案件，调用智能体，将访问的A1标题、A1解答、A1关键字取出来，入库
            Map<String,Object> question = new HashMap<>();
            question.put("title",issueKbOverview.getIssueDespToAI());
            question.put("language",issueKbOverview.getLang());
            question.put("description",issueKbOverview.getProcessDespToAI());

            issueKbOverview.setIndex(i);
            issueKbOverview.setSize(size);
            //生成知识，并记录案件知识一览表中
            try{
                issueToAI(JSON.toJSONString(question),issueKbOverview).blockLast();
            }catch (Exception e){
                log.error("重新生成知识失败，ikerId：{},issueKbOverview:{}",id,issueKbOverview);
            }
        }
    }

    public Flux<ServerSentEvent<Object>> issueToAI(String askMessage, IssueKbOverview issueKbOverview){
        return aiService.generateTextStream(askMessage, "", issueKbOverview);

    }
}
