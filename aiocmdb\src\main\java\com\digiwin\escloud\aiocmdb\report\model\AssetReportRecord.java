package com.digiwin.escloud.aiocmdb.report.model;

import lombok.Data;
import java.util.Date;

@Data
public class AssetReportRecord {
    private Long id;
    private Long sid;
    private String eid;
    private String serviceCode;
    private String customerName;
    private String customerFullName;
    private String reportDate;
    private String reportType;
    private String reportStatus;
    private String assetCategory;
    private String confidentialityLevel;
    private Integer assetCount;
    private String assetObject;
    private String reportGenerateTime;
    private String reportVersion;
    private String documentNumber;
    private String recordNumber;
    private Integer retentionThreshold;
    private String retentionUnit;
    private String userId;
    private String userName;
    private String reportName;
}
