package com.digiwin.escloud.aiouser.service;

import com.digiwin.escloud.aiouser.model.tenant.TenantInfo;
import com.digiwin.escloud.common.model.App;

import java.util.List;

/**
 * <AUTHOR>
 * @Date: 2022-04-27 10:18
 * @Description
 */
public interface IAioAppService {
    List<App> getAioApps(Long tenantSid);
    List<App> getAioApps(Long tenantSid, Long sid);
    List<TenantInfo> getSubscribedTenantByAppCode(String appCode);
}
