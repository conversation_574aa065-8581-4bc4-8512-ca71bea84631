package com.digiwin.escloud.aiocmdb.asset.controller;

import com.digiwin.escloud.aiocmdb.asset.model.*;
import com.digiwin.escloud.aiocmdb.asset.service.IAssetCategoryService;
import com.digiwin.escloud.common.model.ResponseBase;
import com.digiwin.escloud.common.model.ResponseCode;
import com.digiwin.escloud.common.response.BaseResponse;
import com.digiwin.escloud.common.util.RequestUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <p>
 * 资产类别分类表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-01
 */
@Api(value = "资产类别管理", tags = {"资产类别管理"})
@RestController
@RequestMapping("/asset/assetCategory")
@Slf4j
public class AssetCategoryController {

    @Autowired
    private IAssetCategoryService assetCategoryService;

    // ==================== AssetCategoryClassification 相关接口 ====================

    @ApiOperation("资产类别分类新增")
    @PostMapping("/classification/save")
    public BaseResponse saveAssetCategoryClassification(@RequestBody AssetCategoryClassification classification) {
        return assetCategoryService.saveAssetCategoryClassification(classification);
    }

    @ApiOperation("资产类别分类查询")
    @GetMapping("/classification/list")
    public BaseResponse getAssetCategoryClassificationList(String categoryType) {
        return assetCategoryService.getAssetCategoryClassificationList(categoryType);
    }

    @ApiOperation("资产类别分类编辑")
    @PutMapping("/classification/update")
    public BaseResponse updateAssetCategoryClassification(@RequestBody AssetCategoryClassification classification) {
        return assetCategoryService.updateAssetCategoryClassification(classification);
    }

    @ApiOperation("资产类别分类删除")
    @DeleteMapping("/classification/delete/{id}")
    public ResponseBase deleteAssetCategoryClassification(@PathVariable Long id) {
        return assetCategoryService.deleteAssetCategoryClassification(id);
    }

    // ==================== AssetCategory 相关接口 ====================

    @ApiOperation("资产类别新增")
    @PostMapping("/save")
    public BaseResponse saveAssetCategory(@RequestBody AssetCategory category) {
        return assetCategoryService.saveAssetCategory(category);
    }

    @ApiOperation("资产类别编辑")
    @PutMapping("/update")
    public BaseResponse updateAssetCategory(@RequestBody AssetCategory category) {
        return assetCategoryService.updateAssetCategory(category);
    }

    @ApiOperation("资产类别状态编辑")
    @PutMapping("/updateStatus")
    public ResponseBase updateAssetCategoryStatus(@RequestParam Long id, @RequestParam String status) {
        return assetCategoryService.updateAssetCategoryStatus(id, status);
    }

    @ApiOperation("资产类别删除")
    @DeleteMapping("/delete/{id}")
    public ResponseBase deleteAssetCategory(@PathVariable Long id) {
        return assetCategoryService.deleteAssetCategory(id);
    }

    @ApiOperation("资产类别查询")
    @PostMapping("/list")
    public BaseResponse getAssetCategoryList(@RequestBody AssetCategoryQueryParam queryParam) {
        return assetCategoryService.getAssetCategoryList(queryParam);
    }

    // ==================== CmdbModelDataFieldRelationMapping 相关接口 ====================

    @ApiOperation("模型数据字段关系映射新增")
    @PostMapping("/fieldMapping/save")
    public BaseResponse saveCmdbModelDataFieldRelationMapping(@RequestBody List<CmdbModelDataFieldRelationMapping> mappingList) {
        return assetCategoryService.saveCmdbModelDataFieldRelationMapping(mappingList);
    }

    @ApiOperation("模型数据字段关系映射查询")
    @GetMapping("/fieldMapping/list")
    public ResponseBase getCmdbModelDataFieldRelationMappingList(@RequestParam String targetModelCode) {
        return assetCategoryService.getCmdbModelDataFieldRelationMappingList(targetModelCode);
    }

    // ==================== AssetCategoryCodingRule 相关接口 ====================

    @ApiOperation("资产类别编码规则查询")
    @GetMapping("/codingRule/list")
    public ResponseBase getAllAssetCategoryCodingRule() {
        return assetCategoryService.getAllAssetCategoryCodingRule();
    }

    @ApiOperation("资产类别编码规则查询")
    @PostMapping("/ds/process")
    public ResponseBase dsProcess(@RequestBody AiopsProcessParam param) {

        return assetCategoryService.dsProcess(param);
    }

    @PostMapping("/batchUpsertAiopsCollectSink")
    public ResponseBase batchUpsertAiopsCollectSink(@RequestBody List<AiopsCollectSink> sinkList) {
        try {
            return assetCategoryService.batchUpsertAiopsCollectSink(sinkList);
        } catch (Exception e) {
            return ResponseBase.error("1","Error processing batch: " + e.getMessage());
        }
    }

    @GetMapping(value = "/getAssetModel")
    public ResponseBase getAllModel(@RequestParam String modelGroupCode,@RequestParam(required = false) String modelCode) {
        ResponseBase res = new ResponseBase();
        try {
            return assetCategoryService.getAllModel(modelGroupCode,modelCode);
        } catch (Exception ex) {
            log.error(ex.getMessage());
            res.setCode(ResponseCode.INTERNAL_ERROR.toString());
            res.setErrMsg(ex.getMessage());
        }
        return res;
    }

    // ==================== 模型显示字段配置相关接口 ====================

    @ApiOperation("根据模型编码填充模型显示字段配置")
    @PostMapping("/populateModelShowFields")
    public ResponseBase populateModelShowFields(@RequestParam String modelCode) {
        long headerSid = RequestUtil.getHeaderSid();
        return assetCategoryService.populateModelShowFields(modelCode,headerSid);
    }

    @ApiOperation("根据用户ID删除用户字段配置")
    @DeleteMapping("/deleteUserFieldConfig")
    public ResponseBase deleteCmdbModelShowFieldUserByUserId(@RequestParam String userId,@RequestParam String modelCode) {
        return assetCategoryService.deleteCmdbModelShowFieldUser(userId, modelCode);
    }

}
