package com.digiwin.escloud.aioitms.device.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.digiwin.escloud.aiocmdb.model.ModelRelateInstance;
import com.digiwin.escloud.aiocmdb.model.ModelRelateTypeEnum;
import com.digiwin.escloud.aioitms.authorize.service.IAiopsAuthorizeV2Service;
import com.digiwin.escloud.aioitms.bigdata.BigDataUtil;
import com.digiwin.escloud.aioitms.bigdata.ImpalaQuery;
import com.digiwin.escloud.aioitms.cache.WarningRuleCache;
import com.digiwin.escloud.aioitms.collectapp.dao.CollectAppMapper;
import com.digiwin.escloud.aioitms.collectapp.model.CollectAppMapping;
import com.digiwin.escloud.aioitms.collectconfig.dao.CollectConfigMapper;
import com.digiwin.escloud.aioitms.collectconfig.model.CollectConfig;
import com.digiwin.escloud.aioitms.collectconfig.model.CollectConfigSavedResponse;
import com.digiwin.escloud.aioitms.collectconfig.model.CollectType;
import com.digiwin.escloud.aioitms.collectconfig.service.ICollectConfigService;
import com.digiwin.escloud.aioitms.collectwarning.dao.CollectWarningMapper;
import com.digiwin.escloud.aioitms.collectwarning.model.*;
import com.digiwin.escloud.aioitms.collectwarning.service.ICollectWarningService;
import com.digiwin.escloud.aioitms.common.Constants;
import com.digiwin.escloud.aioitms.common.DbHelp;
import com.digiwin.escloud.aioitms.constants.MqConstant;
import com.digiwin.escloud.aioitms.device.dao.DeviceV2Mapper;
import com.digiwin.escloud.aioitms.device.model.DeviceWarningNotifyMapping;
import com.digiwin.escloud.aioitms.device.model.*;
import com.digiwin.escloud.aioitms.exam.dao.AiopsExamMapper;
import com.digiwin.escloud.aioitms.exam.model.AiopsExamItemInstanceScore;
import com.digiwin.escloud.aioitms.exam.model.AiopsExamItemScore;
import com.digiwin.escloud.aioitms.exam.model.AiopsExamRecord;
import com.digiwin.escloud.aioitms.exam.model.ScoreResult;
import com.digiwin.escloud.aioitms.exam.service.AiopsExamScoreQueryService;
import com.digiwin.escloud.aioitms.exam.service.AiopsExamService;
import com.digiwin.escloud.aioitms.exam.service.factory.AiopsExamScoreQueryFactory;
import com.digiwin.escloud.aioitms.execParams.common.ExecParamsHelp;
import com.digiwin.escloud.aioitms.execParams.service.ExecParamsFactoryService;
import com.digiwin.escloud.aioitms.instance.dao.InstanceMapper;
import com.digiwin.escloud.aioitms.instance.factory.TrustInstanceFactoryService;
import com.digiwin.escloud.aioitms.instance.model.*;
import com.digiwin.escloud.aioitms.instance.service.IInstanceService;
import com.digiwin.escloud.aioitms.model.authorize.AiopsAuthStatus;
import com.digiwin.escloud.aioitms.model.device.*;
import com.digiwin.escloud.aioitms.model.instance.AdimRelateContext;
import com.digiwin.escloud.aioitms.model.instance.AiopsItemGroup;
import com.digiwin.escloud.aioitms.modulecollect.dao.ModuleCollectMapper;
import com.digiwin.escloud.aioitms.modulecollect.model.ModuleCollectLayered;
import com.digiwin.escloud.aioitms.modulecollect.model.ModuleCollectMapping;
import com.digiwin.escloud.aioitms.prediction.service.PredictionOfflineDataService;
import com.digiwin.escloud.aioitms.ruleengine.dao.RuleEngineDao;
import com.digiwin.escloud.aioitms.ruleengine.model.RuleSetting;
import com.digiwin.escloud.aioitms.ruleengine.service.IRuleEngineService;
import com.digiwin.escloud.aioitms.upgrade.enums.DeviceUpgradeEnum;
import com.digiwin.escloud.aioitms.upgrade.enums.InstanceUpgradeEnum;
import com.digiwin.escloud.aioitms.upgrade.enums.UpgradeMode;
import com.digiwin.escloud.aioitms.upgrade.enums.UpgradeType;
import com.digiwin.escloud.aioitms.upgrade.model.AiopsUpgradeDcdpAccRequest;
import com.digiwin.escloud.aioitms.upgrade.model.AiopsUpgradeDcdpDetail;
import com.digiwin.escloud.aioitms.upgrade.model.AiopsUpgradeDcdpRequest;
import com.digiwin.escloud.aioitms.upgrade.service.UpgradeService;
import com.digiwin.escloud.aioitms.util.RestItemsUtil;
import com.digiwin.escloud.aioitms.util.RestUtil;
import com.digiwin.escloud.aiouser.model.supplier.SupplierAiopsModuleCollectLayered;
import com.digiwin.escloud.aiouser.model.tenant.AuthUsedRequest;
import com.digiwin.escloud.aiouser.model.tenant.TenantModuleContractDetail;
import com.digiwin.escloud.aiouser.model.tenantNotice.TenantNotifyGroupRespDTO;
import com.digiwin.escloud.aiouser.model.tenantNotice.UserNotifyContact;
import com.digiwin.escloud.aiouser.model.user.UserAiopsDeviceMapping;
import com.digiwin.escloud.common.controller.ParamCheckHelp;
import com.digiwin.escloud.common.feign.AioUserFeignClient;
import com.digiwin.escloud.common.feign.UserV2FeignClient;
import com.digiwin.escloud.common.model.ResponseBase;
import com.digiwin.escloud.common.model.ResponseCode;
import com.digiwin.escloud.common.response.BaseResponse;
import com.digiwin.escloud.common.response.PageInfo;
import com.digiwin.escloud.common.util.*;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.io.PrintWriter;
import java.io.StringWriter;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.BiConsumer;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.digiwin.escloud.aioitms.common.Constants.AiopsItemInstance.*;
import static com.digiwin.escloud.aioitms.common.Constants.CollectConfig.DEFAULT_SCOPE_ID;
import static com.digiwin.escloud.aioitms.common.Constants.Device.*;
import static com.digiwin.escloud.aioitms.common.Constants.ExecParam.ADPM_ID;
import static com.digiwin.escloud.aioitms.common.Constants.ExecParam.AIOPS_ITEM_ID;
import static com.digiwin.escloud.aioitms.device.model.DeviceDashboardRet.DeviceDashboardCount.CountType.*;
import static com.digiwin.escloud.aioitms.device.model.NotifyCategory.BY_GROUP;
import static com.digiwin.escloud.common.constant.AioConstant.EID;

@Slf4j
@Service
@RefreshScope
public class DeviceV2Service implements IDeviceV2Service, ParamCheckHelp, DbHelp, ExecParamsHelp {

    private final static SnowFlake SNOWFLAKE = SnowFlake.getInstance();


    @Value("${com.digwin.aioitms.defaultSid:241199971893824}")
    Long defaultSid;
    @Value("${aio.service.area}")
    String serviceArea;
    @Value("${get.valid.samcd.id.retry.count:3}")
    int getValidSamcdIdRetryCount;
    @Value("${add.module.collect.log.mode:0}")
    Integer addModuleCollectLogMode;

    @Value("${inner.runner.accId.set:102990000000000,102990000000001,102990000000002,102990000000003,102990000000004,102990000000005}")
    private Set<Long> innerRunnerAccIdSet;

    @Value("${acc.module.collect.collectorModelCode:HTTPListenReader,TailxV2Reader,ReadAtOnceV2Reader,MqMsSQLReader,HTTPListenReader,WindowsEventLogReader,MQTTSubReader,DapMqSubReader}")
    private List<String> modelCodeList;

    @Autowired
    DeviceV2Mapper deviceMapper;
    @Autowired
    InstanceMapper instanceMapper;
    @Autowired
    CollectConfigMapper collectConfigMapper;
    @Autowired
    CollectAppMapper collectAppMapper;
    @Autowired
    CollectWarningMapper collectWarningMapper;
    @Autowired
    ModuleCollectMapper moduleCollectMapper;

    @Autowired
    WarningRuleCache warningRuleCache;

    @Autowired
    @Lazy
    ICollectWarningService collectWarningService;
    @Autowired
    @Lazy
    ICollectConfigService collectConfigService;
    @Autowired
    IAiopsAuthorizeV2Service authorizeService;
    @Autowired
    IInstanceService instanceService;
    @Autowired
    IRuleEngineService ruleEngineService;

    @Autowired
    AioUserFeignClient aioUserFeignClient;
    @Autowired
    PredictionOfflineDataService predictionOfflineDataService;
    @Resource
    private RestUtil restUtil;
    @Resource
    private RestItemsUtil restItemsUtil;
    @Autowired
    UserV2FeignClient userV2FeignClient;
    @Autowired
    AioUserFeignClient userFeignClient;

    @Autowired
    ExecParamsFactoryService execParamsFactoryService;
    @Autowired
    UpgradeService upgradeService;

    @Autowired
    RabbitTemplate rabbitTemplate;
    @Autowired
    PlatformTransactionManager platformTransactionManager;

    @Autowired
    BigDataUtil bigDataUtil;

    @Resource
    private AiopsExamService aiopsExamService;
    @Resource
    private AiopsExamMapper aiopsExamMapper;
    @Autowired
    private DeviceV2Mapper deviceV2Mapper;
    @Autowired
    private IDeviceV2Service deviceService;

    @Resource
    private RuleEngineDao ruleEngineDao;

    @Resource
    private AiopsExamScoreQueryFactory<AiopsExamItemScore> examScoreQueryFactory;
    @Autowired
    TrustInstanceFactoryService trustInstanceFactoryService;

    @Autowired
    private Map<String, IAiopsDeviceCollectedDetailService> servicesMap;

    @Resource
    private TransactionTemplate transactionTemplate;

    //设备在线数
    private Map<String, Map<String, Integer>> deviceOnlineMap = new HashMap<>();

    @Override
    public BaseResponse saveDeviceByCompleteInfo(AiopsKitDeviceCompleteInfo deviceCompleteInfo) {
        //region 参数检查

        Optional<BaseResponse> optResponse = checkParamIsEmpty(deviceCompleteInfo, "deviceCompleteInfo");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        //endregion

        AiopsKitDevice device = deviceCompleteInfo.getDevice();
        BaseResponse response = this.saveDeviceInfo(device);
        if (!response.checkIsSuccess()) {
            return response;
        }
        Long adId = 0L;
        String deviceId = "";
        if (device != null) {
            adId = device.getId();
            deviceId = device.getDeviceId();
        }
        AiopsKitDeviceAgreement deviceAgreement = deviceCompleteInfo.getDeviceAgreement();
        if (deviceAgreement != null) {
            deviceAgreement.setAdId(adId);
            deviceAgreement.setDeviceId(deviceId);
            response = this.saveDeviceAgreement(deviceAgreement);
            if (!response.checkIsSuccess()) {
                return response;
            }
        }
        return this.saveDeviceDataSource(deviceId, deviceCompleteInfo.getDeviceDataSource());
    }

    @Override
    public BaseResponse saveDeviceInfo(AiopsKitDevice device) {
        //region 参数检查

        Optional<BaseResponse> optResponse = checkParamIsEmpty(device, "device");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        String deviceId = device.getDeviceId();
        optResponse = checkParamIsEmpty(deviceId, "device.deviceId");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        //endregion

        TransactionStatus status;
        DeviceChangeSource dcs = device.getDeviceChangeSource();
        if (DeviceChangeSource.BY_HEARTBEAT.isSame(dcs) || DeviceChangeSource.BY_DCDP_HEARTBEAT.isSame(dcs)) {
            //心跳包因为变更频率高，且变更的内容不会与其他流程冲突，尽管冲突也不会有太大的问题，因此不进事务，减少因锁而造成等待
            status = null;
        } else {
            status = platformTransactionManager.getTransaction(TransactionDefinition.withDefaults());
        }
        boolean isSuccess = true;
        try {
            AiopsKitDevice existDevice = deviceMapper.selectDeviceByDeviceId(deviceId);
            //新增场景
            if (Objects.isNull(existDevice)) {
                BaseResponse response = addDeviceInfo(device);
                if (!response.checkIsSuccess()) {
                    isSuccess = false;
                }
                return response;
            }
            //更新场景
            BaseResponse response = modifyDeviceInfo(device, existDevice);
            if (!response.checkIsSuccess()) {
                isSuccess = false;
            }
            return response;
        } catch (Exception ex) {
            isSuccess = false;
            log.error("saveDeviceInfo deviceId:" + device.getDeviceId() + " dcs:" + dcs + " exception:", ex);
            return BaseResponse.error(ResponseCode.INTERNAL_ERROR, ex);
        } finally {
            if (Objects.nonNull(status)) {
                if (isSuccess) {
                    platformTransactionManager.commit(status);
                } else {
                    platformTransactionManager.rollback(status);
                }
            }
        }
    }

    private BaseResponse addDeviceInfo(AiopsKitDevice device) {
        //首次(可能是地端维护，也可能是心跳包)
        Long eid = device.getEid();
        if (LongUtil.isEmpty(eid)) {
            eid = RequestUtil.getHeaderEid();
            device.setEid(eid);
        }
        Long finalEid = eid;
        Long adId = SNOWFLAKE.newId();
        device.setId(adId);
        int res = deviceMapper.insertDevice(device);
        boolean isDcdpHeartBeat = DeviceChangeSource.BY_DCDP_HEARTBEAT.equals(device.getDeviceChangeSource());
        List<AiopsUpgradeDcdpDetail> upgradeDcdpDetailList;
        if (isDcdpHeartBeat) {
            upgradeDcdpDetailList = new ArrayList<>(0);
        } else {
            //只有异动来源不是数采才需要同步到数采
            upgradeDcdpDetailList = Stream.of(device).map(x -> {
                AiopsUpgradeDcdpDetail detail = new AiopsUpgradeDcdpDetail(UpgradeType.UPDATE,
                        DeviceUpgradeEnum.DEVICE.getTableName(), BeanUtil.object2Map(device));
                Map<String, Object> paramsMap = detail.getParamsMap();
                paramsMap.put(EID, finalEid);
                paramsMap.put(DEVICE_ID, x.getDeviceId());
                detail.setNeedSaveOperationLog(true);
                Map<String, Object> operationInfoMap = OperationUtil.getHeaderOperationInfo();
                operationInfoMap.put("operationObjectClassification", "device");
                operationInfoMap.put("operationObject", "device");
                operationInfoMap.put("operationBehavior", "adddevice");
                detail.setOperationInfoMap(operationInfoMap);
                return detail;
            }).collect(Collectors.toList());
        }
        List<AiopsKitDeviceTypeMapping> mappingList = device.getDeviceTypeMappingList();
        if (CollectionUtils.isEmpty(mappingList)) {
            if (!isDcdpHeartBeat) {
                BaseResponse response = executeUpgradeToDcdp(upgradeDcdpDetailList);
                if (!response.checkIsSuccess()) {
                    return response;
                }
            }
            return createDeviceChangeResponse(res, true, null, device);
        }
        List<AiopsItemContext> aicList = new ArrayList<>(mappingList.size());
        String deviceId = device.getDeviceId();
        mappingList.forEach(x -> {
            x.setId(SNOWFLAKE.newId());
            x.setAdId(adId);
            x.setDeviceId(deviceId);
            AiopsItemContext aic = new AiopsItemContext(x.getDeviceType(), deviceId, deviceId);
            aic.setIsAddInstanceCollectToDevice(false);
            aic.setHasAddedAiopsInstance(false);
            aicList.add(aic);
        });
        //设置映射
        int result = deviceMapper.batchInsertDeviceTypeMapping(mappingList);
        //创建设备实例并关联设备
        BaseResponse response = createAiopsInstanceByAiopsItemContext(eid, device, aicList, true);
        if (!response.checkIsSuccess()) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return response;
        }
        //保存维护资产(首次新增资产，没有旧设备类型，新设备类型可能有也可能没有)
        saveMaintenanceAsset(eid, deviceId, "", getCurrentDeviceType(mappingList),
                DeviceChangeSource.BY_HEARTBEAT.isSame(device.getDeviceChangeSource()));
        if (!isDcdpHeartBeat) {
            //只有异动来源不是数采才需要同步到数采
            upgradeDcdpDetailList.addAll(mappingList.stream().map(x -> {
                AiopsUpgradeDcdpDetail detail = new AiopsUpgradeDcdpDetail(UpgradeType.UPDATE,
                        DeviceUpgradeEnum.DEVICE_TYPE_MAPPING.getTableName(), BeanUtil.object2Map(x));
                Map<String, Object> paramsMap = detail.getParamsMap();
                paramsMap.put(EID, finalEid);
                paramsMap.put(DEVICE_ID, deviceId);
                detail.setNeedSaveOperationLog(true);
                Map<String, Object> operationInfoMap = OperationUtil.getHeaderOperationInfo();
                operationInfoMap.put("operationObjectClassification", "instance");
                operationInfoMap.put("operationObject", "instance");
                operationInfoMap.put("operationBehavior", "adddevicetype");
                detail.setOperationInfoMap(operationInfoMap);
                return detail;
            }).collect(Collectors.toList()));
            response = executeUpgradeToDcdp(upgradeDcdpDetailList);
            if (!response.checkIsSuccess()) {
                TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                return response;
            }
        }
        return createDeviceChangeResponse(result, true, null, device);
    }

    private BaseResponse createDeviceChangeResponse(int effectRow, boolean hasChange,
                                                    AiopsKitDevice oldDevice, AiopsKitDevice newDevice) {
        Map<String, Object> map = new HashMap<>(2);
        map.put("effectRow", effectRow);
        if (hasChange || Objects.isNull(oldDevice) != Objects.isNull(newDevice)) {
            hasChange = true;
        } else {
            //判断关键内容是否有更新
            if (Objects.nonNull(oldDevice)) { //20241119:代码稽核，增加判空
                hasChange = oldDevice.getKeyFieldDiff(newDevice);
            }
        }
        map.put("hasChanged", hasChange);
        return BaseResponse.ok(map);
    }

    private void saveMaintenanceAsset(Long eid, String deviceId, String oldDeviceType, String newDeviceType,
                                      boolean isSaveByHeartbeat) {
        if (StringUtils.isBlank(oldDeviceType) && StringUtils.isBlank(newDeviceType)) {
            //如果新旧类型同时都是空的，就离开
            return;
        }
        JSONObject jo = new JSONObject();
        jo.put("eid", eid);
        jo.put("deviceId", deviceId);
        jo.put("oldDeviceType", oldDeviceType);
        jo.put("newDeviceType", newDeviceType);
        jo.put("isSaveByHeartbeat", isSaveByHeartbeat);

        rabbitTemplate.convertAndSend(MqConstant.ASSET_MAINTENANCE_EXCHANGE, MqConstant.ASSET_MAINTENANCE_ROUTING_KEY, jo.toJSONString());

        /*//异步进行
        CompletableFuture.runAsync(() -> {
            try {
                aioCmdbFeignClient.generateMaintenanceAsset(oldDeviceType, newDeviceType, deviceId, eid,
                        isSaveByHeartbeat);
            } catch (Exception ex) {
                log.error("generateMaintenanceAsset deviceId:" + deviceId + " oldDeviceType:" + oldDeviceType +
                        " newDeviceType:" + newDeviceType + " exception:", ex);
            }
        });*/
    }

    private BaseResponse modifyDeviceInfo(AiopsKitDevice device, AiopsKitDevice existDevice) {
        String deviceId = existDevice.getDeviceId();
        Long adId = existDevice.getId();
        device.setId(adId);
        DeviceChangeSource dcs = device.getDeviceChangeSource();
        boolean isHeartbeat = DeviceChangeSource.BY_HEARTBEAT.isSame(dcs);
        if (isHeartbeat) {
            //心跳包内的放置点与备注，不采用(因为地端的配置已作废，保存反而会将云端设定的值冲掉)，直接替换成数据库查到的值
            device.setPlacementPoint(existDevice.getPlacementPoint());
            device.setRemark(existDevice.getRemark());
            //心跳包设定设备类型，后面如果发生eid异动会使用到
            device.setDeviceTypeMappingList(existDevice.getDeviceTypeMappingList());
        }
        //上传完整数据视同报到
        //因微服务获取时间会因为不同微服务设定而产生差异，统一改由数据库时间
        //device.setLastCheckInTime(DateUtil.getLocalNow());
        int effectRow = deviceMapper.updateDevice(device);
        Long eid;
        boolean isSaveByCloud = DeviceChangeSource.BY_CLOUD.isSame(dcs);
        if (isSaveByCloud) {
            //云端储存直接给定旧值
            eid = existDevice.getEid();
        } else {
            //只有地端储存(包含心跳包)，才需要处理eid的迁移
            Long oldEid = existDevice.getEid();
            Long newEid = device.getEid();
            if (LongUtil.isEmpty(newEid)) {
                //新的eid若为空，就设定为旧的eid(不允许eid为空)
                eid = oldEid;
            } else {
                //新的eid有值，后面判断是否不同
                eid = newEid;
            }
            if (!eid.equals(oldEid)) {
                //不同进行迁移
                //因为迁移会是一个大流程，为了避免请求过长，改为异步进行
                CompletableFuture.runAsync(() -> {
                    try {
                        transferAiopsInstanceEid(oldEid, eid, deviceId, device);
                        //最后统一刷新下规则引擎
                        Map<String, Object> map = new HashMap<>(1);
                        map.put("deviceId", deviceId);
                        ruleEngineService.asyncModifyReAdditionalContentByMap(map);
                    } catch (Exception ex) {
                        log.error("saveDeviceInfo transferAiopsInstanceEid exception:", ex);
                    }
                });
            }
        }
        if (isHeartbeat) {
            //心跳包不处理设备类型，因为地端设备类型已经作废
            return createDeviceChangeResponse(effectRow, false, existDevice, device);
        }
        //心跳包的异动不更新回数采(因为数采会更新企业运维)
        List<AiopsUpgradeDcdpDetail> upgradeDcdpDetailList;
        if (isSaveByCloud) {
            //只有云端储存的需要同步回数采
            upgradeDcdpDetailList = new ArrayList<>(getDeviceUpgradeToDcdpDetail(deviceId, UpgradeType.UPDATE, (x) -> {
                x.setNeedSaveOperationLog(true);
                Map<String, Object> operationInfoMap = x.getOperationInfoMap();
                operationInfoMap.put("operationObjectClassification", "instance");
                operationInfoMap.put("operationObject", "instance");
                if (StringUtils.isBlank(Objects.toString(operationInfoMap.get("operationBehavior"), ""))) {
                    operationInfoMap.put("operationBehavior", "editinstance");
                }
            }, DeviceUpgradeEnum.DEVICE));
        } else {
            upgradeDcdpDetailList = new ArrayList<>(0);
        }
        //剩下异动设备类型映射
        return modifyDeviceTypeMapping(eid, effectRow, device, existDevice, upgradeDcdpDetailList);
    }

    private BaseResponse modifyDeviceTypeMapping(Long eid, int effectRow,
                                                 AiopsKitDevice device, AiopsKitDevice existDevice,
                                                 List<AiopsUpgradeDcdpDetail> upgradeDcdpDetailList) {
        boolean isSaveByCloud = DeviceChangeSource.BY_CLOUD.isSame(device.getDeviceChangeSource());
        String deviceId = device.getDeviceId();
        Long adId = existDevice.getId();
        List<AiopsKitDeviceTypeMapping> oriMapping = existDevice.getDeviceTypeMappingList();
        List<AiopsKitDeviceTypeMapping> mappingList = device.getDeviceTypeMappingList();
        Consumer<AiopsUpgradeDcdpRequest.DeviceUpgrade> deviceUpgradeConsumer = (deviceUpgrade) -> {
            deviceUpgrade.setNeedSaveOperationLog(true);
            Map<String, Object> operationInfoMap = deviceUpgrade.getOperationInfoMap();
            operationInfoMap.put("operationObjectClassification", "instance");
            operationInfoMap.put("operationObject", "instance");
            if (StringUtils.isBlank(Objects.toString(operationInfoMap.get("operationBehavior"), ""))) {
                operationInfoMap.put("operationBehavior", "editinstance");
            }
        };
        if (CollectionUtils.isEmpty(mappingList)) {
            //新的设备类型为空
            if (CollectionUtils.isEmpty(oriMapping)) {
                //旧的设备类型为空，离开
                return createDeviceChangeResponse(effectRow, false, existDevice, device);
            }
            //旧的设备类型有值，进行删除
            if (isSaveByCloud) {
                //只有云端储存的需要同步回数采
                upgradeDcdpDetailList.addAll(getDeviceUpgradeToDcdpDetail(oriMapping.stream()
                        .map(x -> new AiopsUpgradeDcdpRequest.DeviceUpgrade(deviceId,
                                DeviceUpgradeEnum.DEVICE_TYPE_MAPPING, UpgradeType.DELETE))
                        .peek(deviceUpgradeConsumer).collect(Collectors.toList())));
            }
            effectRow += deviceMapper.batchDeleteDeviceTypeMappingById(
                    oriMapping.stream().map(AiopsKitDeviceTypeMapping::getId).collect(Collectors.toList()));
            //清理设备运维实例映射、运维实例及相关设备收集项
            removeAdimAdcdAdcwAdwnmByAdtmList(eid, deviceId, oriMapping, isSaveByCloud, upgradeDcdpDetailList);
            //保存维护资产(旧的设备类型有值，新的设备类型为空)
            saveMaintenanceAsset(eid, deviceId, getCurrentDeviceType(oriMapping), "", false);
            if (isSaveByCloud) {
                //只有云端储存的需要同步回数采
                BaseResponse response = executeUpgradeToDcdp(upgradeDcdpDetailList);
                if (!response.checkIsSuccess()) {
                    return response;
                }
            }
            return createDeviceChangeResponse(effectRow, true, existDevice, device);
        }
        //更新映射
        String firstNewDeviceType = getCurrentDeviceType(mappingList);
        //1-1.如果没有查到结果，直接新增
        if (CollectionUtils.isEmpty(oriMapping)) {
            //旧的设备类型为空，新增设备类型
            List<AiopsItemContext> aicList = new ArrayList<>(mappingList.size());
            mappingList.forEach(x -> {
                x.setId(SNOWFLAKE.newId());
                x.setAdId(adId);
                x.setDeviceId(deviceId);
                AiopsItemContext aic = new AiopsItemContext(x.getDeviceType(), deviceId, deviceId);
                aic.setIsAddInstanceCollectToDevice(false);
                aic.setHasAddedAiopsInstance(false);
                aicList.add(aic);
            });
            //设置映射
            int result = deviceMapper.batchInsertDeviceTypeMapping(mappingList);
            BaseResponse response = createAiopsInstanceByAiopsItemContext(eid, existDevice, aicList,
                    true);
            if (!response.checkIsSuccess()) {
                TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                return response;
            }
            //保存维护资产(旧的设备类型为空，新的设备类型有值)
            saveMaintenanceAsset(eid, deviceId, "", firstNewDeviceType, false);
            if (isSaveByCloud) {
                //只有云端储存的需要同步回数采
                upgradeDcdpDetailList.addAll(getDeviceUpgradeToDcdpDetail(mappingList.stream()
                        .map(x -> new AiopsUpgradeDcdpRequest.DeviceUpgrade(deviceId,
                                DeviceUpgradeEnum.DEVICE_TYPE_MAPPING))
                        .peek(deviceUpgradeConsumer).collect(Collectors.toList())));
                response = executeUpgradeToDcdp(upgradeDcdpDetailList);
                if (!response.checkIsSuccess()) {
                    return response;
                }
            }
            return createDeviceChangeResponse(result, true, existDevice, device);
        }
        //1-2.有查到进行更新
        Map<String, AiopsKitDeviceTypeMapping> oriMappingMap = oriMapping.stream()
                .collect(Collectors.toMap(AiopsKitDeviceTypeMapping::getDeviceType, Function.identity(), (x, y) -> y));
        //排除掉已经存在的取得要新增的
        List<AiopsItemContext> aicList = new ArrayList<>(mappingList.size());
        List<AiopsKitDeviceTypeMapping> needInsertList = mappingList.stream().filter(x -> {
            String key = x.getDeviceType();
            if (oriMappingMap.containsKey(key)) {
                oriMappingMap.remove(key);
                return false;
            }
            return true;
        }).peek(x -> {
            x.setId(SNOWFLAKE.newId());
            x.setAdId(adId);
            x.setDeviceId(deviceId);
            AiopsItemContext aic = new AiopsItemContext(x.getDeviceType(), deviceId, deviceId);
            aic.setIsAddInstanceCollectToDevice(false);
            aic.setHasAddedAiopsInstance(false);
            aic.setChangeToUnauth(true);
            aicList.add(aic);
        }).collect(Collectors.toList());
        int result = 0;
        //若有旧存在，但新的不存的做删除
        if (!CollectionUtils.isEmpty(oriMappingMap)) {
            //清理设备运维实例映射、运维实例及相关设备收集项
            List<String> deviceTypeList = new ArrayList<>(oriMappingMap.size());
            List<Long> adtmIdList = oriMappingMap.values().stream().map(x -> {
                deviceTypeList.add(x.getDeviceType());
                return x.getId();
            }).collect(Collectors.toList());
            //删除不存在的映射
            if (isSaveByCloud) {
                //只有云端储存的需要同步回数采
                upgradeDcdpDetailList.addAll(getDeviceUpgradeToDcdpDetail(oriMappingMap.values().stream()
                        .map(x -> new AiopsUpgradeDcdpRequest.DeviceUpgrade(deviceId,
                                DeviceUpgradeEnum.DEVICE_TYPE_MAPPING, UpgradeType.DELETE))
                        .peek(deviceUpgradeConsumer).collect(Collectors.toList())));
            }
            result = deviceMapper.batchDeleteDeviceTypeMappingById(adtmIdList);
            removeAdimAdcdAdcwAdwnm(eid, deviceId, deviceTypeList, isSaveByCloud, upgradeDcdpDetailList);
        }
        String firstOriDeviceType = getCurrentDeviceType(oriMapping);
        //再做新增
        if (CollectionUtils.isEmpty(needInsertList)) {
            if (result > 0) {
                //表示有发生过删除(理应不会发生，因目前设备类型只有一个)
                //保存维护资产
                saveMaintenanceAsset(eid, deviceId, getCurrentDeviceType(oriMappingMap.values()), firstNewDeviceType,
                        false);
            }
            if (isSaveByCloud) {
                //只有云端储存的需要同步回数采
                BaseResponse response = executeUpgradeToDcdp(upgradeDcdpDetailList);
                if (!response.checkIsSuccess()) {
                    return response;
                }
            }
            //没有新增，表示没有异动直接返回
            return createDeviceChangeResponse(result, false, existDevice, device);
        }
        result += deviceMapper.batchInsertDeviceTypeMapping(needInsertList);
        BaseResponse response = createAiopsInstanceByAiopsItemContext(eid, existDevice, aicList,
                true);
        if (!response.checkIsSuccess()) {
            return response;
        }
        //保存维护资产
        saveMaintenanceAsset(eid, deviceId, firstOriDeviceType, firstNewDeviceType, false);
        if (isSaveByCloud) {
            //只有云端储存的需要同步回数采
            upgradeDcdpDetailList.addAll(getDeviceUpgradeToDcdpDetail(needInsertList.stream()
                    .map(x -> new AiopsUpgradeDcdpRequest.DeviceUpgrade(deviceId,
                            DeviceUpgradeEnum.DEVICE_TYPE_MAPPING))
                    .peek(deviceUpgradeConsumer).collect(Collectors.toList())));
            response = executeUpgradeToDcdp(upgradeDcdpDetailList);
            if (!response.checkIsSuccess()) {
                return response;
            }
        }
        return createDeviceChangeResponse(result, true, existDevice, device);
    }

    private String getCurrentDeviceType(Collection<AiopsKitDeviceTypeMapping> adtmList) {
        return adtmList.stream().filter(Objects::nonNull).map(AiopsKitDeviceTypeMapping::getDeviceType)
                .findFirst().orElse("");
    }

    private List<AiopsUpgradeDcdpDetail> getDeviceUpgradeToDcdpDetail(
            String deviceId, UpgradeType upgradeType,
            Consumer<AiopsUpgradeDcdpRequest.DeviceUpgrade> deviceUpgradeConsumer,
            DeviceUpgradeEnum... deviceUpgradeEnum) {
        if (StringUtils.isBlank(deviceId) || ArrayUtils.isEmpty(deviceUpgradeEnum)) {
            return new ArrayList<>(0);
        }
        AiopsUpgradeDcdpRequest request = new AiopsUpgradeDcdpRequest();
        List<AiopsUpgradeDcdpRequest.DeviceUpgrade> deviceUpgradeList = request.getDeviceUpgradeList();
        Arrays.stream(deviceUpgradeEnum).forEach(x -> {
            AiopsUpgradeDcdpRequest.DeviceUpgrade deviceUpgrade =
                    new AiopsUpgradeDcdpRequest.DeviceUpgrade(deviceId, x, upgradeType);
            if (Objects.nonNull(deviceUpgradeConsumer)) {
                deviceUpgradeConsumer.accept(deviceUpgrade);
            }
            deviceUpgradeList.add(deviceUpgrade);
        });
        return upgradeService.getUpgradeToDcdpDetail(request);
    }

    private List<AiopsUpgradeDcdpDetail> getDeviceUpgradeToDcdpDetail(
            List<AiopsUpgradeDcdpRequest.DeviceUpgrade> deviceUpgradeList) {
        return getUpgradeToDcdpDetail(deviceUpgradeList, null);
    }

    private List<AiopsUpgradeDcdpDetail> getInstanceUpgradeToDcdpDetail(
            List<AiopsUpgradeDcdpRequest.InstanceUpgrade> instanceUpgradeList) {
        return getUpgradeToDcdpDetail(null, instanceUpgradeList);
    }

    private List<AiopsUpgradeDcdpDetail> getUpgradeToDcdpDetail(
            List<AiopsUpgradeDcdpRequest.DeviceUpgrade> deviceUpgradeList,
            List<AiopsUpgradeDcdpRequest.InstanceUpgrade> instanceUpgradeList) {
        if (CollectionUtils.isEmpty(deviceUpgradeList) && CollectionUtils.isEmpty(instanceUpgradeList)) {
            return new ArrayList<>(0);
        }
        AiopsUpgradeDcdpRequest request = new AiopsUpgradeDcdpRequest();
        if (CollectionUtil.isNotEmpty(deviceUpgradeList)) {
            request.getDeviceUpgradeList().addAll(deviceUpgradeList);
        }
        if (CollectionUtil.isNotEmpty(instanceUpgradeList)) {
            request.getInstanceUpgradeList().addAll(instanceUpgradeList);
        }
        return upgradeService.getUpgradeToDcdpDetail(request);
    }

    private BaseResponse executeUpgradeToDcdp(List<AiopsUpgradeDcdpDetail> upgradeDcdpDetailList) {
        return upgradeService.upgradeToDcdpByDetail(UpgradeMode.SYNC, upgradeDcdpDetailList);
    }

    //创建运维实例
    private BaseResponse createAiopsInstanceByAiopsItemContext(Long eid, AiopsKitDevice device,
                                                               List<AiopsItemContext> aicList,
                                                               boolean refreshDeviceCollectCache) {
        BaseResponse response = authorizeService.checkAndCreateAiopsInstanceByAiopsItemContext(eid,
                null, aicList);
        if (!response.checkIsSuccess()) {
            return response;
        }
        if (device == null) {
            return response;
        }
        if (!BooleanUtils.toBoolean(device.getIsDeleted())) {
            //若设备未删除，直接返回
            return response;
        }
        //若设备已经删除，调整授权状态为已作废
        String deviceId = device.getDeviceId();
        BaseResponse authChangeResponse =
                instanceService.invalidDeviceAiopsInstanceAuthStatus(deviceId);
        if (!authChangeResponse.checkIsSuccess()) {
            return authChangeResponse;
        }
        executeUpgradeToDcdp(getInstanceUpgradeToDcdpDetail(Stream.of(deviceId).map(x -> {
            AiopsUpgradeDcdpRequest.InstanceUpgrade instanceUpgrade = new AiopsUpgradeDcdpRequest.InstanceUpgrade(x);
            instanceUpgrade.setUpgradeType(UpgradeType.UPDATE);
            Set<String> targetColumnSet = instanceUpgrade.getTargetColumnSet();
            targetColumnSet.add("id");
            targetColumnSet.add(AIOPS_ITEM_ID);
            targetColumnSet.add("aiopsAuthStatus");
            instanceUpgrade.setNeedSaveOperationLog(true);
            Map<String, Object> operationInfoMap = instanceUpgrade.getOperationInfoMap();
            operationInfoMap.put("operationObjectClassification", "instance");
            operationInfoMap.put("operationObject", "instance");
            operationInfoMap.put("operationBehavior", "editinstance");
            return instanceUpgrade;
        }).collect(Collectors.toList())));

        if (refreshDeviceCollectCache) {
            //清理设备收集项缓存
            collectConfigService.clearDeviceIdCollectConfigCache(deviceId);
        }

        return response;
    }

    private int removeAdimAdcdAdcwAdwnmByAdtmList(Long eid, String deviceId, List<AiopsKitDeviceTypeMapping> adtmList,
                                                  boolean isSaveByCloud, List<AiopsUpgradeDcdpDetail> upgradeDetailList) {
        if (CollectionUtils.isEmpty(adtmList)) {
            return 0;
        }
        return removeAdimAdcdAdcwAdwnm(eid, deviceId,
                adtmList.stream().map(AiopsKitDeviceTypeMapping::getDeviceType).collect(Collectors.toList()),
                isSaveByCloud, upgradeDetailList);
    }

    private Integer removeAdimAdcdAdcwAdwnm(Long eid, String deviceId, List<String> aiopsItemList,
                                            boolean isSaveByCloud, List<AiopsUpgradeDcdpDetail> upgradeDetailList) {
        //region 参数检查

        if (StringUtils.isBlank(deviceId) || CollectionUtils.isEmpty(aiopsItemList)) {
            return 0;
        }

        //删除预警项相关数据
        deviceService.deleteWarning(deviceId);

        //endregion

        Map<String, Object> map = new HashMap<>(4);
        //串查设备自定义收集项预警项的规则设定Id列表(后面要把这些规则设定删除)
        map.put("scopeId", deviceId);
        BaseResponse<List<RuleSetting>> rsListResponse = ruleEngineService.getRuleSettingByMap(map);
        map.clear();
        List<RuleSetting> rsList;
        if (rsListResponse.checkIsSuccess() && !CollectionUtils.isEmpty(rsList = rsListResponse.getData())) {
            map.put("isDeleteRs", true);
            map.put("reIdList", rsList.stream().map(RuleSetting::getId).collect(Collectors.toList()));
        }
        map.put("deviceId", deviceId);
        //删除设备所有规则引擎映射或设定
        ruleEngineService.removeReResmRsByMap(map);

        map.remove("isDeleteRs");
        map.remove("reIdList");
        map.put("aiopsItemList", aiopsItemList);
        map.put("needDeleteAiopsInstance", true);
        if (isSaveByCloud) {
            //只有云端储存的需要同步回数采
            processDcdpRemoveAdimAdcd(deviceId, map, upgradeDetailList);
        }
        //删除设备本身的实例映射、实例、设备收集项相关表(也需要把设备自定义的收集项移除)
        int result = deviceMapper.deleteAdimAdcdAdcwAdwnmByMap(map);

        map.remove("aiopsItemList");
        map.put("aiopsItemTypeNotDevice", true);
        map.put("needDeleteAiopsInstance", false);
        if (isSaveByCloud) {
            //只有云端储存的需要同步回数采
            processDcdpRemoveAdimAdcd(deviceId, map, upgradeDetailList);
        }
        //删除除设备外其他设备相关的实例映射、设备收集项相关表(也需要把设备自定义的收集项移除)
        result += deviceMapper.deleteAdimAdcdAdcwAdwnmByMap(map);

        //刷新授权数量
        BaseResponse<Map<Long, Integer>> response = instanceService.getAuthedCountByAiopsItemList(eid, aiopsItemList);
        if (response.checkIsSuccess()) {
            aioUserFeignClient.recalculateTmcdUsedCountByAuthUsedRequest(RequestUtil.getHeaderSid(),
                    new AuthUsedRequest(eid, false, aiopsItemList, response.getData()));
        }

        //清理设备收集项缓存
        collectConfigService.clearDeviceIdCollectConfigCache(deviceId);

        return result;
    }

    private void processDcdpRemoveAdimAdcd(String deviceId, Map<String, Object> map,
                                           List<AiopsUpgradeDcdpDetail> upgradeDcdpDetailList) {
        if (CollectionUtils.isEmpty(map)) {
            return;
        }
        List<Map<String, Object>> mapList = deviceMapper.selectAdimIdAiIdAdcdIdListByMap(map);
        if (CollectionUtils.isEmpty(mapList)) {
            return;
        }
        Set<String> existIdSet = new HashSet<>(mapList.size());
        List<AiopsUpgradeDcdpRequest.DeviceUpgrade> deviceUpgradeList = new ArrayList<>();
        List<AiopsUpgradeDcdpRequest.InstanceUpgrade> instanceUpgradeList = new ArrayList<>();
        mapList.forEach(x -> {
            //adim
            Long adimId = LongUtil.objectToLong(x.get("adimId"));
            String key;
            if (LongUtil.isNotEmpty(adimId) && !existIdSet.contains(key = "adimId_" + adimId)) {
                AiopsUpgradeDcdpRequest.DeviceUpgrade deviceUpgrade =
                        new AiopsUpgradeDcdpRequest.DeviceUpgrade(deviceId, DeviceUpgradeEnum.DEVICE_INSTANCE_MAPPING);
                deviceUpgrade.setUpgradeType(UpgradeType.DELETE);
                deviceUpgrade.setContainRelateInstance(false);
                deviceUpgrade.getOtherConditionMap().put("adim.id", adimId);
                deviceUpgrade.setNeedSaveOperationLog(true);
                Map<String, Object> operationInfoMap = deviceUpgrade.getOperationInfoMap();
                operationInfoMap.put("operationObjectClassification", "device");
                operationInfoMap.put("operationObject", "device");
                operationInfoMap.put("operationBehavior", "devicedeleteinstancemapping");
                deviceUpgradeList.add(deviceUpgrade);
                existIdSet.add(key);
            }
            //ai
            Long aiId = LongUtil.objectToLong(x.get("aiId"));
            if (LongUtil.isNotEmpty(aiId) && !existIdSet.contains(key = "aiId_" + aiId)) {
                AiopsUpgradeDcdpRequest.InstanceUpgrade instanceUpgrade =
                        new AiopsUpgradeDcdpRequest.InstanceUpgrade(aiId);
                instanceUpgrade.setUpgradeType(UpgradeType.DELETE);
                instanceUpgrade.setNeedSaveOperationLog(true);
                Map<String, Object> operationInfoMap = instanceUpgrade.getOperationInfoMap();
                operationInfoMap.put("operationObjectClassification", "instance");
                operationInfoMap.put("operationObject", "instance");
                operationInfoMap.put("operationBehavior", "deleteinstance");
                instanceUpgradeList.add(instanceUpgrade);
                existIdSet.add(key);
            }
            //adcd
            Long adcdId = LongUtil.objectToLong(x.get("adcdId"));
            if (LongUtil.isNotEmpty(adcdId) && !existIdSet.contains(key = "adcdId_" + adcdId)) {
                AiopsUpgradeDcdpRequest.DeviceUpgrade deviceUpgrade =
                        new AiopsUpgradeDcdpRequest.DeviceUpgrade(deviceId, DeviceUpgradeEnum.DEVICE_COLLECT_DETAIL);
                deviceUpgrade.setUpgradeType(UpgradeType.DELETE);
                deviceUpgrade.getOtherConditionMap().put("adcd.id", adcdId);
                deviceUpgrade.setNeedSaveOperationLog(true);
                Map<String, Object> operationInfoMap = deviceUpgrade.getOperationInfoMap();
                operationInfoMap.put("operationObjectClassification", "device_collect_detail");
                operationInfoMap.put("operationObject", "device_collect_detail");
                operationInfoMap.put("operationBehavior", "deletedevicecollectdetail");
                deviceUpgradeList.add(deviceUpgrade);
                existIdSet.add(key);
            }
        });
        upgradeDcdpDetailList.addAll(getUpgradeToDcdpDetail(deviceUpgradeList, instanceUpgradeList));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BaseResponse saveDeviceAgreement(AiopsKitDeviceAgreement deviceAgreement) {
        //region 参数检查

        Optional<BaseResponse> optResponse = checkParamIsEmpty(deviceAgreement, "deviceAgreement");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        //endregion

        String deviceId = deviceAgreement.getDeviceId();
        //取不到设备Id不做处理
        if (StringUtils.isEmpty(deviceId)) {
            return BaseResponse.ok(0);
        }
        Long adId = deviceAgreement.getAdId();
        if (LongUtil.isEmpty(adId)) {
            adId = getAdIdByDeviceId(deviceId);
            //查不到设备主键Id，进行主动修正
            if (LongUtil.isEmpty(adId)) {
                //进行新增设备信息
                AiopsKitDevice partDevice = new AiopsKitDevice();
                adId = SNOWFLAKE.newId();
                partDevice.setId(adId);
                partDevice.setDeviceId(deviceId);
                deviceMapper.insertDevicePart(partDevice);
            }
        }
        //填充设备主键Id
        deviceAgreement.setAdId(adId);

        AiopsKitDeviceAgreement tempDeviceAgreement = deviceMapper.selectDeviceAgreementByDeviceId(deviceId);
        if (tempDeviceAgreement == null) {
            deviceAgreement.setId(SNOWFLAKE.newId());
            return BaseResponse.ok(deviceMapper.insertDeviceAgreement(deviceAgreement));
        } else {
            deviceAgreement.setId(tempDeviceAgreement.getId());
            return BaseResponse.ok(deviceMapper.updateDeviceAgreement(deviceAgreement));
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BaseResponse saveDeviceCustomerInfo(String deviceId, Long eid) {
        //region 参数检查

        Optional<BaseResponse> optResponse = checkParamIsEmpty(deviceId, "deviceId");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        optResponse = checkParamIsEmpty(eid, "eid");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        //endregion

        AiopsKitDevice device = deviceMapper.selectDeviceByDeviceId(deviceId);
        boolean needTransferEid = false;
        Long oldEid = 0L;
        if (device == null) {
            device = new AiopsKitDevice();
            device.setDeviceId(deviceId);
            device.setEid(eid);
            //deviceId是唯一的，考虑可能此方法是第一次调用，因此要先产生id
            device.setId(SNOWFLAKE.newId());
            //因微服务获取时间会因为不同微服务设定而产生差异，统一改由数据库时间
            //device.setLastCheckInTime(DateUtil.getLocalNow());
        } else {
            oldEid = device.getEid();
            if (!eid.equals(oldEid)) {
                needTransferEid = true;
            }
            device.setEid(eid);
        }
        int result = deviceMapper.insertOrUpdateDeviceCustomerInfo(device);
        if (needTransferEid) {
            final Long finalOldEid = oldEid;
            final AiopsKitDevice finalDevice = device;
            //因为迁移会是一个大流程，为了避免请求过长，改为异步进行
            CompletableFuture.runAsync(() -> {
                try {
                    transferAiopsInstanceEid(finalOldEid, eid, deviceId, finalDevice);
                    //最后统一刷新下规则引擎
                    Map<String, Object> map = new HashMap<>(1);
                    map.put("deviceId", deviceId);
                    ruleEngineService.asyncModifyReAdditionalContentByMap(map);
                } catch (Exception ex) {
                    log.error("saveDeviceCustomerInfo transferAiopsInstanceEid exception:", ex);
                }
            });
        }
        return BaseResponse.ok(result);
    }

    @Transactional(rollbackFor = Exception.class)
    void transferAiopsInstanceEid(Long oldEid, Long eid, String deviceId, AiopsKitDevice device) {
        //查出除所有该设备下需要迁移oldEid实例
        List<Long> deviceMappingAiIdList =
                deviceMapper.selectDeviceMappingAiIdList(oldEid, deviceId, false);
        if (CollectionUtils.isEmpty(deviceMappingAiIdList)) {
            //若没有查到实例就离开
            return;
        }
        //查出除了该设备外，还有其他设备引用的运维实例Id列表(后面更新时做排除)
        List<Long> hasOtherDeviceMappingAiIdList =
                deviceMapper.selectDeviceMappingAiIdList(oldEid, deviceId, true);
        Set<Long> hasOtherDeviceMappingAiIdSet;
        if (CollectionUtils.isEmpty(hasOtherDeviceMappingAiIdList)) {
            hasOtherDeviceMappingAiIdSet = new HashSet<>(0);
        } else {
            hasOtherDeviceMappingAiIdSet = new HashSet<>(hasOtherDeviceMappingAiIdList);
        }
        //考虑到实例的tmcdId要改变，因此不直更新运维实例，改由实例保存时去处理
//        Map<String, Object> map = new HashMap<>();
//        map.put("oldEid", oldEid);
//        map.put("eid", eid);
//        map.put("deviceId", deviceId);
//        map.put("onlyOneDeviceMappingAiIdList", onlyOneDeviceMappingAiIdList);
//        //更新实例的eid
//        deviceMapper.updateOnlyOneDeviceAiopsInstanceEid(map);
        //查出实例
        List<AiopsInstance> aiList = instanceService.getAiopsInstanceByAiIdList(deviceMappingAiIdList);
        if (CollectionUtils.isEmpty(aiList)) {
            //如果没查到实例，离开(不可能出现)
            return;
        }
        //分类
        Map<String, List<AiopsInstance>> aiopsItemTypeGroupAiMap = aiList.stream()
                .collect(Collectors.groupingBy(AiopsInstance::getAiopsItemType));
        //int modifySize = 0;
        List<String> aiopsItemList = new ArrayList<>();
        List<AiopsItemContext> needUpdateAicList = new ArrayList<>();

        Supplier<StringBuilder> sbLogSupplier = () -> {
            StringBuilder sbLog = new StringBuilder("transferAiopsInstanceEid deviceId:");
            sbLog.append(deviceId);
            sbLog.append(" oldEid:");
            sbLog.append(oldEid);
            sbLog.append(" eid:");
            sbLog.append(eid);
            return sbLog;
        };

        //region 设备本身

        List<AiopsInstance> deviceAiList = aiopsItemTypeGroupAiMap.get(DEVICE);
        if (!CollectionUtils.isEmpty(deviceAiList)) {
            Optional<String> optDeviceType = device.getFirstDeviceType();
            if (optDeviceType.isPresent()) {
                String deviceType = optDeviceType.get();
                deviceAiList.forEach(x -> {
                    AiopsItemContext aic = new AiopsItemContext(deviceType, deviceId, null);
                    //因eid移转迁移，要异动为未授权
                    aic.setChangeToUnauth(true);
                    needUpdateAicList.add(aic);
                    aiopsItemList.add(deviceType);
                });
                //modifySize++;
            }
        }

        //endregion

        //region 数据库

        List<AiopsInstance> dbAiList = aiopsItemTypeGroupAiMap.get(DATABASE);
        if (!CollectionUtils.isEmpty(dbAiList)) {
            Optional<BaseResponse> optErrResponse = dbAiList.stream().map(x -> {
                try {
                    AiopsKitDeviceDataSource db =
                            deviceMapper.selectDeviceDataSourceByDeviceIdAndDbId(deviceId, x.getAiopsItemId());
                    if (db == null) {
                        return BaseResponse.ok(0);
                    }
                    db.setEid(eid);
                    db.setDbOriginalDbId(db.getDbId());
                    db.setDbId("");
                    BaseResponse<Long> response = savePartDeviceDataSourceCore(deviceId, db, null);
                    if (!response.checkIsSuccess()) {
                        return response;
                    }
                    Long aiId = response.getData();
                    AiopsItemContext aic = createDeviceDataSourceAiopsItemContext(eid, aiId, deviceId, db, false);
                    if (hasOtherDeviceMappingAiIdSet.contains(aiId)) {
                        //表示不只有一个设备使用，设定后面要进行分离运维实例
                        aic.setNeedSeparateAiopsInstance(true);
                    }
                    //因eid移转迁移，要异动为未授权(后续逻辑段会自动刷新已授权数量)
                    aic.setChangeToUnauth(true);
                    needUpdateAicList.add(aic);
                    aiopsItemList.add(processDbTypeToAiopsItem(db.getDbType()));
                    return BaseResponse.ok(1);
                } catch (Exception ex) {
                    return BaseResponse.error(ResponseCode.INTERNAL_ERROR, ex);
                }
            }).filter(x -> !x.checkIsSuccess()).findFirst();
            if (optErrResponse.isPresent()) {
                BaseResponse res = optErrResponse.get();
                StringBuilder sbError = sbLogSupplier.get().append(" db has error response code:")
                        .append(res.getCode()).append(" not zero, errMsg:").append(res.getErrMsg());
                log.error(sbError.toString());
                printStackTrace();
                TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                return;
            }
            //modifySize += dbAiList.size();
        }

        //endregion

        //region SNMP

        List<AiopsInstance> snmpAiList = aiopsItemTypeGroupAiMap.get(SNMP);
        if (!CollectionUtils.isEmpty(snmpAiList)) {
            Optional<BaseResponse> optErrResponse = snmpAiList.stream().map(x -> {
                try {
                    AiopsSNMPInstance asi = instanceService.getSNMPInstanceBySNMPId(x.getAiopsItemId());
                    if (asi == null) {
                        return BaseResponse.ok(0);
                    }
                    asi.setEid(eid);
                    BaseResponse<Long> response = instanceService.saveSNMPInstance(asi);
                    if (!response.checkIsSuccess()) {
                        return response;
                    }
                    Long aiId = response.getData();
                    //创建运维项目上下文
                    AiopsItemContext aic = new AiopsItemContext(asi.getSnmpType(),
                            asi.getSnmpId(), null);
                    if (hasOtherDeviceMappingAiIdSet.contains(aiId)) {
                        //表示不只有一个设备使用，设定后面要进行分离运维实例
                        aic.setNeedSeparateAiopsInstance(true);
                    }
                    aic.setAiId(aiId);
                    aic.setSourceDeviceId(deviceId);
                    if (!LongUtil.isEmpty(aiId)) {
                        aic.setNeedFixExecParamsContent(true);
                    }
                    //因eid移转迁移，要异动为未授权(后续逻辑段会自动刷新已授权数量)
                    aic.setChangeToUnauth(true);
                    needUpdateAicList.add(aic);
                    aiopsItemList.add(asi.getSnmpType());
                    return BaseResponse.ok(1);
                } catch (Exception ex) {
                    return BaseResponse.error(ResponseCode.INTERNAL_ERROR, ex);
                }
            }).filter(x -> !x.checkIsSuccess()).findFirst();
            if (optErrResponse.isPresent()) {
                BaseResponse res = optErrResponse.get();
                StringBuilder sbError = sbLogSupplier.get().append(" snmp has error response code:")
                        .append(res.getCode()).append(" not zero, errMsg:").append(res.getErrMsg());
                log.error(sbError.toString());
                printStackTrace();
                TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                return;
            }
            //modifySize += snmpAiList.size();
        }

        //endregion

        //region 备份软件

        List<AiopsInstance> backupSoftwareAiList = aiopsItemTypeGroupAiMap.get(BACKUP);
        if (!CollectionUtils.isEmpty(backupSoftwareAiList)) {
            Optional<BaseResponse> optErrResponse = backupSoftwareAiList.stream().map(x -> {
                try {
                    AiopsBackupSoftwareInstance absi =
                            instanceService.getBackupSoftwareInstanceByBackupSoftwareId(x.getAiopsItemId());
                    if (absi == null) {
                        return BaseResponse.ok(0);
                    }
                    absi.setEid(eid);
                    BaseResponse<Long> response = instanceService.saveBackupSoftwareInstance(absi);
                    if (!response.checkIsSuccess()) {
                        return response;
                    }
                    Long aiId = response.getData();
                    //创建运维项目上下文
                    AiopsItemContext aic = new AiopsItemContext(absi.getBackupSoftwareType(),
                            absi.getBackupSoftwareId(), null);
                    if (hasOtherDeviceMappingAiIdSet.contains(aiId)) {
                        //表示不只有一个设备使用，设定后面要进行分离运维实例
                        aic.setNeedSeparateAiopsInstance(true);
                    }
                    aic.setAiId(aiId);
                    aic.setSourceDeviceId(deviceId);
                    if (!LongUtil.isEmpty(aiId)) {
                        aic.setNeedFixExecParamsContent(true);
                    }
                    //因eid移转迁移，要异动为未授权(后续逻辑段会自动刷新已授权数量)
                    aic.setChangeToUnauth(true);
                    needUpdateAicList.add(aic);
                    aiopsItemList.add(absi.getBackupSoftwareType());
                    return BaseResponse.ok(1);
                } catch (Exception ex) {
                    return BaseResponse.error(ResponseCode.INTERNAL_ERROR, ex);
                }
            }).filter(x -> !x.checkIsSuccess()).findFirst();
            if (optErrResponse.isPresent()) {
                BaseResponse res = optErrResponse.get();
                StringBuilder sbError = sbLogSupplier.get().append(" backupSoftware has error response code:")
                        .append(res.getCode()).append(" not zero, errMsg:").append(res.getErrMsg());
                log.error(sbError.toString());
                printStackTrace();
                TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                return;
            }
            //modifySize += snmpAiList.size();
        }

        //endregion

        //region 产品应用/服务
        //因为产品应用/服务，与租户无关，因此不做迁移
//        List<AiopsInstance> appAiList = aiopsItemTypeGroupAiMap.get(PRODUCT_APP);
//        if (!CollectionUtils.isEmpty(appAiList)) {
//            Long adId = getAdIdByDeviceId(deviceId);
//            Map<String, Long> appIdAiIdMap = new HashMap<>();
//            List<Map<String, Object>> mapList = appAiList.stream().map(x -> {
//                Map<String, Object> map = new HashMap<>(2);
//                map.put("adId", adId);
//                String appId = x.getAiopsItemId();
//                map.put("appId", appId);
//                appIdAiIdMap.put(appId, x.getId());
//                return map;
//            }).collect(Collectors.toList());
//            List<DeviceProductMapping> adpmList = deviceMapper.selectAdpmListByMapList(mapList);
//            if (!CollectionUtils.isEmpty(adpmList)) {
//                adpmList = adpmList.stream().filter(x -> {
//                    String oldAppId = x.getAppId();
//                    x.setEid(eid);
//                    x.fixAppId();
//                    String newAppId = x.getAppId();
//                    if (newAppId.equals(oldAppId)) {
//                        return false;
//                    }
//                    String modelCode = x.getModelCode();
//                    //创建运维项目上下文
//                    AiopsItemContext aic = new AiopsItemContext(modelCode, newAppId, null);
//                    aic.setAiId(appIdAiIdMap.get(oldAppId));
//                    aic.setSourceDeviceId(deviceId);
//                    //因eid移转迁移，要异动为未授权(后续逻辑段会自动刷新已授权数量)
//                    aic.setChangeToUnauth(true);
//                    needUpdateAicList.add(aic);
//                    aiopsItemList.add(modelCode);
//                    return true;
//                }).collect(Collectors.toList());
//                deviceMapper.batchUpdateAiopsDeviceProductMapping(adpmList);
//            }
//        }

        //endregion

        if (CollectionUtils.isEmpty(needUpdateAicList)) {
            return;
        }
        BaseResponse response = createAiopsInstanceByAiopsItemContext(eid, device, needUpdateAicList, true);
        if (!response.checkIsSuccess()) {
            StringBuilder sbError = sbLogSupplier.get().append(" createAiopsInstanceByAiopsItemContext has error response code:")
                    .append(response.getCode()).append(" not zero, errMsg:").append(response.getErrMsg());
            log.error(sbError.toString());
            printStackTrace();
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return;
        }
        //因为目前迁移后，会自动将已授权的异动为未授权，因此这里不检查授权数量
//        //重新取得授权数量进行校验
//        response = authorizeService.checkAuthCount(eid, needUpdateAicList, true, new HashSet<>());
//        if (!response.checkIsSuccess()) {
//            //不通过，直接回滚，不予许修改eid
//            return response;
//        }
        //刷新旧的eid的授权数量
        //因为事务还未提交，这里直接串查，并进行刷新
        BaseResponse<Map<Long, Integer>> tmcdIdUsedCountMapResponse =
                instanceService.getAuthedCountByAiopsItemList(oldEid, aiopsItemList);
        if (!response.checkIsSuccess()) {
            StringBuilder sbError = sbLogSupplier.get().append(" getAuthedCountByAiopsItemList has error response code:")
                    .append(response.getCode()).append(" not zero, errMsg:").append(response.getErrMsg());
            log.error(sbError.toString());
            printStackTrace();
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return;
        }
        response = aioUserFeignClient.recalculateTmcdUsedCountByAuthUsedRequest(RequestUtil.getHeaderSid(),
                new AuthUsedRequest(oldEid, false, aiopsItemList, tmcdIdUsedCountMapResponse.getData()));
        if (!response.checkIsSuccess()) {
            StringBuilder sbError = sbLogSupplier.get().append(" recalculateTmcdUsedCountByAuthUsedRequest has error response code:")
                    .append(response.getCode()).append(" not zero, errMsg:").append(response.getErrMsg());
            log.error(sbError.toString());
            printStackTrace();
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BaseResponse saveDeviceDataSource(String deviceId, List<AiopsKitDeviceDataSource> aiopsKitDataSourceList) { //huly: 修复漏洞/bug 去掉函数入参 Long adId
        //region 参数检查

        Optional<BaseResponse> optResponse = checkParamIsEmpty(deviceId, "deviceId");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        //endregion
        Long adId = 0L; //huly: 修复漏洞/bug 去掉函数入参 Long adId；重新定义一个入参
        AiopsKitDevice ad = deviceMapper.selectDeviceByDeviceId(deviceId);
        if (ad == null) {
            //查不到设备，进行主动修正
            //进行新增设备信息
            ad = new AiopsKitDevice();
            adId = SNOWFLAKE.newId();
            ad.setId(adId);
            ad.setDeviceId(deviceId);
            deviceMapper.insertDevicePart(ad);
        } else {
            adId = ad.getId();
        }
        Long eid = ad.getEid();
        if (LongUtil.isEmpty(eid)) {
            eid = RequestUtil.getHeaderEid();
        }

        //数据源列表为空，批量标记删除
        if (CollectionUtils.isEmpty(aiopsKitDataSourceList)) {
            List<AiopsKitDeviceDataSource> dataSourceList = deviceMapper.selectDeviceDataSourceByDeviceId(deviceId);
            if (CollectionUtils.isEmpty(dataSourceList)) {
                return BaseResponse.ok();
            }
            int result = deviceMapper.updateDeviceDataSourceIsDeleteByDeviceId(deviceId, null, true);
            //应该随即将设备添加的数据库运维实例给移除(如果有的话)
            BaseResponse response = removeDeviceAiopsInstanceMapping(deviceId,
                    dataSourceList.stream().map(x -> x.getDbId()).collect(Collectors.toList()));
            if (!response.checkIsSuccess()) {
                TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                return response;
            }
            return BaseResponse.ok(result);
        }

        Long finalAdId = adId;
        Long finalEid = eid;

        List<AiopsKitDeviceDataSource> oriDataSource = deviceMapper.selectDeviceDataSourceByDeviceId(deviceId);
        //新增的场景
        if (CollectionUtils.isEmpty(oriDataSource)) {
            //全部新增
            List<AiopsItemContext> aicList = new ArrayList<>(aiopsKitDataSourceList.size());
            aiopsKitDataSourceList.stream().forEach(x -> {
                x.setId(SNOWFLAKE.newId());
                x.setAdId(finalAdId);
                x.setDeviceId(deviceId);
                x.setEid(finalEid);
                String dbId = x.getDbId();
                if (StringUtils.isBlank(dbId)) {
                    x.setDbId(x.getDbIdMd5());
                }
                //数据库从地端新增时不与设备关联
                aicList.add(new AiopsItemContext(processDbTypeToAiopsItem(x.getDbType()),
                        x.getDbId(), null));
            });
            int result = deviceMapper.batchInsertOrUpdateDeviceDataSource(aiopsKitDataSourceList);
            BaseResponse response = createAiopsInstanceByAiopsItemContext(eid, ad, aicList, true);
            if (!response.checkIsSuccess()) {
                TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                return response;
            }
            return BaseResponse.ok(result);
        }
        //更新的场景
        Map<String, AiopsKitDeviceDataSource> oriDataSourceMap = oriDataSource.stream()
                .map(x -> {
                    //填充eid，方便后面计算dbId
                    x.setEid(finalEid);
                    return x;
                })
                .collect(Collectors.toMap(AiopsKitDeviceDataSource::getDbId, Function.identity(), (x1, x2) -> x1));
        //排除掉已经存在的并取得要新增的
        List<AiopsItemContext> aicList = new ArrayList<>(aiopsKitDataSourceList.size());
        List<AiopsKitDeviceDataSource> needInsertOrUpdateList = aiopsKitDataSourceList.stream()
                .filter(x -> {
                    x.setEid(finalEid);
                    //修正dbId(如果需要的话)
                    if (StringUtils.isBlank(x.getDbId())) {
                        x.setDbId(x.getDbIdMd5());
                    }
                    String oriDbId = x.getDbOriginalDbId();
                    if (StringUtils.isBlank(oriDbId)) {
                        oriDbId = x.getDbId();
                    }
                    AiopsKitDeviceDataSource orids = oriDataSourceMap.get(oriDbId);
                    if (orids != null) {
                        //有查到，表示更新
                        x.setId(orids.getId());
                        x.setAdId(orids.getAdId());
                        x.setDeviceId(orids.getDeviceId());
                        oriDataSourceMap.remove(oriDbId);
                        //如果删除了，而新的列表能扫到，表示要做更新(改为未删除)
                        //如果orids.dbId为空或者与上传的不相等表示需要更新dbId
                        String dbId = orids.getDbId();
                        if (StringUtils.isBlank(dbId) || !dbId.equals(x.getDbId())) {
                            return true;
                        }
                        Boolean isDelete = orids.getIsDelete();
                        return isDelete == null ? false : isDelete;
                    }
                    return true;
                }).map(x -> {
                    if (LongUtil.isEmpty(x.getId())) {
                        x.setId(SNOWFLAKE.newId());
                        x.setAdId(finalAdId);
                        x.setDeviceId(deviceId);
                    }
                    String dbId = x.getDbId();
                    //数据库从地端新增时不与设备关联
                    AiopsItemContext aic = new AiopsItemContext(processDbTypeToAiopsItem(x.getDbType()),
                            dbId, null);
                    String dbOriginalDbId = x.getDbOriginalDbId();
                    if (StringUtils.isNotBlank(dbOriginalDbId) && !dbOriginalDbId.equals(dbId)) {
                        //查出已有的数据库运维实例，进行大迁移(包括设备收集项明细的执行参数)
                        BaseResponse<Long> response = instanceService.getAiIdByAiopsItemId(dbOriginalDbId);
                        if (response.checkIsSuccess()) {
                            aic.setAiId(response.getData());
                            aic.setNeedFixExecParamsContent(true);
                        }
                    }
                    aicList.add(aic);
                    return x;
                }).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(needInsertOrUpdateList)) {
            deviceMapper.batchInsertOrUpdateDeviceDataSource(needInsertOrUpdateList);
            //新增
            BaseResponse response = createAiopsInstanceByAiopsItemContext(eid, ad, aicList, true);
            if (!response.checkIsSuccess()) {
                TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                return response;
            }
        }
        //如果没有数据了，就离开
        if (CollectionUtils.isEmpty(oriDataSourceMap)) {
            return BaseResponse.ok(1);
        }
        List<String> dbIdList = new ArrayList<>(oriDataSourceMap.size());
        //新的不存在将旧的标记删除
        int result = deviceMapper.batchUpdateDeviceDataSourceIsDeleteById(oriDataSourceMap.values()
                .stream().map(x -> {
                    dbIdList.add(x.getDbId());
                    return x.getId();
                }).collect(Collectors.toList()), true);
        //应该随即将设备添加的数据库运维实例给移除(如果有的话)
        BaseResponse response = removeDeviceAiopsInstanceMapping(deviceId, dbIdList);
        if (!response.checkIsSuccess()) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return response;
        }
        return BaseResponse.ok(result);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BaseResponse savePartDeviceDataSource(String deviceId, Boolean tryOpenAuth,
                                                 AiopsKitDeviceDataSource aiopsKitDataSource,
                                                 Boolean addCollItem, Consumer<AiopsItemContext> aicConsumer) {
        BaseResponse<Long> response = savePartDeviceDataSourceCore(deviceId, aiopsKitDataSource, addCollItem);
        if (!response.checkIsSuccess()) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return response;
        }
        Long eid = aiopsKitDataSource.getEid();

        AiopsItemContext aic = createDeviceDataSourceAiopsItemContext(eid, response.getData(), deviceId,
                aiopsKitDataSource, BooleanUtils.toBoolean(addCollItem));
        if (Objects.nonNull(aicConsumer)) {
            aicConsumer.accept(aic);
        }
        List<AiopsItemContext> aicList = Stream.of(aic).collect(Collectors.toList());
        response = createAiopsInstanceByAiopsItemContext(eid, null, aicList, true);
        if (!response.checkIsSuccess()) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return response;
        }
        if (BooleanUtils.toBoolean(tryOpenAuth)) {
            authorizeService.modifyInstanceAuthStatus(eid, AiopsAuthStatus.AUTHED, aicList);
        }

        return BaseResponse.ok(1);
    }

    private BaseResponse savePartDeviceDataSourceCore(String deviceId, AiopsKitDeviceDataSource aiopsKitDataSource,
                                                      Boolean addCollItem) {
        //region 参数检查

        Optional<BaseResponse> optResponse = checkParamIsEmpty(deviceId, "deviceId");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        optResponse = checkParamIsEmpty(aiopsKitDataSource, "aiopsKitDataSource");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        //endregion

        AiopsKitDevice ad = deviceMapper.selectDeviceByDeviceId(deviceId);
        Long adId;
        if (ad == null) {
            //查不到设备，进行主动修正
            //进行新增设备信息
            ad = new AiopsKitDevice();
            adId = SNOWFLAKE.newId();
            ad.setId(adId);
            ad.setDeviceId(deviceId);
            deviceMapper.insertDevicePart(ad);
        } else {
            adId = ad.getId();
        }
        Long eid = ad.getEid();
        if (LongUtil.isEmpty(eid)) {
            eid = RequestUtil.getHeaderEid();
        }

        String dbId = aiopsKitDataSource.getDbId();
        if (StringUtils.isBlank(dbId)) {
            aiopsKitDataSource.setEid(eid);
            dbId = aiopsKitDataSource.getDbIdMd5();
            aiopsKitDataSource.setDbId(dbId);
        }
        Long aiId = 0L;
        String dbOriginalDbId = aiopsKitDataSource.getDbOriginalDbId();
        if (StringUtils.isNotBlank(dbOriginalDbId) && !dbId.equals(dbOriginalDbId)) {
            //查出已有的数据库运维实例，进行大迁移(包括设备收集项明细的执行参数)
            BaseResponse<Long> response = instanceService.getAiIdByAiopsItemId(dbOriginalDbId);
            if (!response.checkIsSuccess()) {
                return response;
            }
            aiId = response.getData();
            //如果原始数据库Id与保存的数据库Id不同，先将旧的数据库Id删除
            deviceMapper.updateDeviceDataSourceIsDeleteByDeviceId(deviceId, dbOriginalDbId, true);
        }
        if (BooleanUtils.toBoolean(addCollItem)) {
            deviceMapper.updateDeviceDataSourceByDbId(dbId);
        }
        aiopsKitDataSource.setId(SNOWFLAKE.newId());
        aiopsKitDataSource.setAdId(adId);
        aiopsKitDataSource.setDeviceId(deviceId);
        aiopsKitDataSource.setIsDelete(false);
        aiopsKitDataSource.setEid(eid);
        aiopsKitDataSource.setDbHostDevice(addCollItem);
        List<AiopsKitDeviceDataSource> dataSourceList = new ArrayList<>(1);
        dataSourceList.add(aiopsKitDataSource);
        deviceMapper.batchInsertOrUpdateDeviceDataSource(dataSourceList);
        return BaseResponse.ok(aiId);
    }

    private AiopsItemContext createDeviceDataSourceAiopsItemContext(Long eid, Long aiId, String deviceId,
                                                                    AiopsKitDeviceDataSource aiopsKitDataSource,
                                                                    boolean addCollItem) {
        //数据库从地端新增时不与设备关联
        AiopsItemContext aic = new AiopsItemContext(processDbTypeToAiopsItem(aiopsKitDataSource.getDbType()),
                aiopsKitDataSource.getDbId(), addCollItem ? deviceId : null);
        aic.setAiId(aiId);
        aic.setSourceDeviceId(deviceId);
        if (!LongUtil.isEmpty(aiId) || addCollItem) {
            //如果存在运维实例，就需要修正执行参数
            aic.setNeedFixExecParamsContent(true);
        }
        return aic;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BaseResponse deletePartDeviceDataSource(String deviceId, String dbId) {
        //region 参数检查

        Optional<BaseResponse> optResponse = checkParamIsEmpty(deviceId, "deviceId");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        optResponse = checkParamIsEmpty(dbId, "dbId");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        //endregion

        //因为后面会需要检查有无其他设备使用，因此全查出来，后面再过滤
        Map<String, Object> map = new HashMap<>(2);
        map.put("dbId", dbId);
        List<AiopsKitDeviceDataSource> dataSourceList = deviceMapper.selectDeviceDataSourceByMap(map);
        if (CollectionUtils.isEmpty(dataSourceList)) {
            return BaseResponse.ok();
        }
        long notEqualDeviceIdDataSourceCount = dataSourceList.stream().filter(x -> !deviceId.equals(x.getDeviceId()))
                .count();
        //已授权先不控管
//        BaseResponse<AiopsInstance> response = instanceService.getAiopsInstanceByAiopsItemId(dbId);
//        if (!response.checkIsSuccess()) {
//            return response;
//        }
//        AiopsInstance ai = response.getData();
//        if (ai != null) {
//            if (AiopsAuthStatus.AUTHED.equals(ai.getAiopsAuthStatus())) {
//                return BaseResponse.error(ResponseCode.AUTHED_AIOPS_INSTANCE_CAN_NOT_DELETE, ai);
//            }
//        }
        if (notEqualDeviceIdDataSourceCount == dataSourceList.size()) {
            //全部设备Id都不相等表示当前设备Id不存在
            return BaseResponse.ok();
        }
        int result = deviceMapper.updateDeviceDataSourceIsDeleteByDeviceId(deviceId, dbId, true);
        //dbId已经包含eid信息，因此不同设备相同dbId的eid会相同，取一笔eid不为空的即可
        Long eid = dataSourceList.stream().filter(Objects::nonNull).map(AiopsKitDeviceDataSource::getEid)
                .filter(LongUtil::isNotEmpty).findFirst().orElse(0L);
        if (LongUtil.isNotEmpty(eid) && LongUtil.isEmpty(notEqualDeviceIdDataSourceCount)) {
            //数据库没有其他设备设定，就可以作废实例
            map.clear();
            map.put("eid", eid);
            map.put("aiopsItemId", dbId);
            List<AiopsInstance> aiList = instanceMapper.selectAiopsInstanceByMap(map);
            if (CollectionUtil.isNotEmpty(aiList)) {
                //有查到实例，进行作废
                List<AiopsItemContext> aicList = aiList.stream().map(x -> {
                    AiopsItemContext aic = new AiopsItemContext(x.getAiopsItem(), x.getAiopsItemId(), deviceId);
                    aic.setIsAddInstanceCollectToDevice(false);
                    aic.setIsMapInstancesToDevices(false);
                    return aic;
                }).collect(Collectors.toList());
                Long sid = RequestUtil.getHeaderSidOrDefault(defaultSid);
                AiopsAuthStatus aiopsAuthStatus = AiopsAuthStatus.INVALID;
                BaseResponse response = authorizeService.checkAndProcessAiopsItemContext(sid, eid,
                        aiopsAuthStatus, aicList);
                if (!response.checkIsSuccess()) {
                    return response;
                }
                BaseResponse authResponse = authorizeService.modifyInstanceAuthStatus(eid, aiopsAuthStatus, aicList);
                if (!authResponse.checkIsSuccess()) {
                    //授权处理异常不先中止，打印日志
                    log.warn("deletePartDeviceDataSource eid:{} dbId:{} invalid auth fail, errMsg:{}", eid, dbId,
                            authResponse.getErrMsg());
                }
            }
        }
        //应该随即将设备添加的数据库运维实例给移除(如果有的话)
        BaseResponse response = removeDeviceAiopsInstanceMapping(deviceId,
                Stream.of(dbId).collect(Collectors.toList()));
        if (!response.checkIsSuccess()) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return response;
        }
        return BaseResponse.ok(result);
    }

    @Override
    public BaseResponse getDeviceBasicInfo(String deviceId) {
        //region 参数检查

        Optional<BaseResponse> optResponse = checkParamIsEmpty(deviceId, "deviceId");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        //endregion

        return BaseResponse.ok(deviceMapper.selectDeviceByDeviceId(deviceId));
    }

    @Override
    public BaseResponse getDeviceAuthStatus(String deviceId) {
        //region 参数检查

        Optional<BaseResponse> optResponse = checkParamIsEmpty(deviceId, "deviceId");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        //endregion

        String result = deviceMapper.selectDeviceAuthStatus(deviceId);
        if (StringUtils.isBlank(result)) {
            //如果查不到，表示设备Id不存在(可能是地端还没上传，这里让其返回未授权)
            result = AiopsAuthStatus.UNAUTH.name();
        }
        return BaseResponse.ok(result);
    }

    @Override
    public BaseResponse getDeviceIsUndefined(String deviceId) {
        //region 参数检查

        Optional<BaseResponse> optResponse = checkParamIsEmpty(deviceId, "deviceId");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        //endregion

        return BaseResponse.ok(deviceMapper.selectDeviceIsUndefined(deviceId));
    }

    @Override
    public AiopsKitDevice getDeviceInfoByEidAndDeviceId(Long eid, String deviceId) {
        return deviceMapper.selectDeviceInfoByEidAndDeviceId(eid, deviceId);
    }

    @Override
    public AiopsKitDevice getDeviceByDeviceId(String deviceId) {
        return deviceMapper.selectDeviceByDeviceId(deviceId);
    }

    @Override
    public Long getAdIdByDeviceId(String deviceId) {
        return deviceMapper.selectAdIdByDeviceId(deviceId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BaseResponse aiTrustInfoSave(Map<String, Object> aiTrustInfoMap) {
        //region 参数检查

        Optional<BaseResponse> optResponse = checkParamIsEmpty(aiTrustInfoMap, "aiTrustInfoMap");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        //endregion

        Map<String, Object> otherParams = MapUtil.getMap(aiTrustInfoMap, "otherParams").orElse(null);
        if (CollectionUtils.isEmpty(otherParams)) {
            //没有实例参数，不处理
            return BaseResponse.ok(0);
        }
        aiTrustInfoMap.remove("otherParams");
        String deviceId = Objects.toString(otherParams.get("deviceId"), "");
        if (StringUtils.isBlank(deviceId)) {
            //没有设备Id，不处理
            return BaseResponse.ok(0);
        }
        String aiopsItemType = Objects.toString(otherParams.get("aiopsItemType"), "");
        if (StringUtils.isBlank(aiopsItemType)) {
            //没有运维项目类型，不处理
            return BaseResponse.ok(0);
        }
        Boolean autoOpenAuth = BooleanUtil.objectToBoolean(otherParams.get("autoOpenAuth"));
        if ("DB".equals(aiopsItemType)) {
            Boolean addCollItem = BooleanUtil.objectToBoolean(otherParams.get("addCollItem"));
            aiTrustInfoMap.put("id", LongUtil.objectToLong(aiTrustInfoMap.get("id")));
            aiTrustInfoMap.put("adId", LongUtil.objectToLong(aiTrustInfoMap.get("adId")));
            Long eid = LongUtil.objectToLong(aiTrustInfoMap.get("eid"));
            if (LongUtil.isEmpty(eid)) {
                aiTrustInfoMap.put("eid", LongUtil.objectToLong(otherParams.get("eid")));
            } else {
                aiTrustInfoMap.put("eid", eid);
            }
            //为确保能影响到企业运维的纪录需要做特殊处理
            Long aiId = LongUtil.objectToLong(otherParams.get("aiId"));
            Long adimId = LongUtil.objectToLong(otherParams.get("adimId"));
            if (LongUtil.isEmpty(aiId)) {
                //若不存在aiId，则移除dbOriginalDbId
                aiTrustInfoMap.remove("dbOriginalDbId");
            } else {
                //从aiId串查oldAiopsItemId
                String oldAiopsItemId = instanceMapper.selectAiopsItemIdByAiId(aiId);
                if (StringUtils.isBlank(oldAiopsItemId)) {
                    //查不到则移除dbOriginalDbId
                    aiTrustInfoMap.remove("dbOriginalDbId");
                } else {
                    //查到则更新dbOriginalDbId
                    aiTrustInfoMap.put("dbOriginalDbId", oldAiopsItemId);
                }
            }
            AiopsKitDeviceDataSource add = BeanUtil.map2Object(aiTrustInfoMap, AiopsKitDeviceDataSource.class);
            if (Objects.isNull(add)) {
                return BaseResponse.ok(0);
            }
            //从数采来的dbId会跟企业运维不同，要主动修正
            add.setDbId(add.getDbIdMd5());
            //TODO:理应所有数采的设备都会(根据心跳包)同步到企业运维的设备，为确保后续数据库设定正确，可能会因为找不到设备而修正设备
            return savePartDeviceDataSource(deviceId, autoOpenAuth, add, addCollItem, (aic) -> {
                aic.setSpecifyCreateAiId(aiId);
                aic.setSpecifyCreateAdimId(adimId);
            });
        }
        String jsonString = SerializeUtil.JsonSerialize(aiTrustInfoMap);
        return trustInstanceFactoryService.saveByJsonString(aiopsItemType, autoOpenAuth, deviceId,
                jsonString, otherParams);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BaseResponse aiTrustInfoRemove(Map<String, Object> aiTrustInfoMap) {
        //region 参数检查

        Optional<BaseResponse> optResponse = checkParamIsEmpty(aiTrustInfoMap, "aiTrustInfoMap");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        //endregion

        String deviceId = Objects.toString(aiTrustInfoMap.get("deviceId"), "");
        if (StringUtils.isBlank(deviceId)) {
            //没有设备Id，不处理
            return BaseResponse.ok(0);
        }
        String aiopsItemType = Objects.toString(aiTrustInfoMap.get("aiopsItemType"), "");
        if (StringUtils.isBlank(aiopsItemType)) {
            //没有运维项目类型，不处理
            return BaseResponse.ok(0);
        }
        if ("DB".equals(aiopsItemType)) {
            Long aiId = LongUtil.objectToLong(aiTrustInfoMap.get("aiId"));
            if (LongUtil.isEmpty(aiId)) {
                //没有运维实例Id，不处理
                return BaseResponse.ok(0);
            }
            //从aiId查询正确的aiopsItemId
            String aiopsItemId = instanceMapper.selectAiopsItemIdByAiId(aiId);
            if (StringUtils.isBlank(aiopsItemId)) {
                //没有运维项目Id，不处理
                return BaseResponse.ok(0);
            }
            return deletePartDeviceDataSource(deviceId, aiopsItemId);
        }
        //TODO:其他运维项目类型暂时没有删除逻辑，故直接返回成功
        return BaseResponse.ok(0);
    }

    @Override
    public BaseResponse getDeviceList(DeviceRequest deviceRequest) {
        //region 参数检查

        Optional<BaseResponse> optResponse = checkParamIsEmpty(deviceRequest, "deviceRequest");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        Long eid = deviceRequest.getEid();
        optResponse = checkParamIsEmpty(eid, "deviceRequest.eid");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        Boolean needPaging = BooleanUtils.toBoolean(deviceRequest.getNeedPaging());

        //endregion

        Map<String, Object> map = BeanUtil.object2Map(deviceRequest);
        List<String> aiopsAuthStatusList = deviceRequest.getAiopsAuthStatusList();
        if (!CollectionUtils.isEmpty(aiopsAuthStatusList)) {
            aiopsAuthStatusList = aiopsAuthStatusList.stream().filter(StringUtils::isNotBlank)
                    .map(AiopsAuthStatus::getStatusByString)
                    .filter(Optional::isPresent)
                    .map(x -> x.get().name())
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(aiopsAuthStatusList)) {
                map.put("aiopsAuthStatusList", null);
            } else {
                map.put("aiopsAuthStatusList", aiopsAuthStatusList);
            }
        }

        return getDeviceListCore(needPaging, deviceRequest.getPageNum(), deviceRequest.getPageSize(), map);
    }

    private BaseResponse getDeviceListCore(Boolean needPaging, int pageNum, int pageSize, Map<String, Object> map) {
        //查询
        if (needPaging) {
            pageNum = pageNum == 0 ? DEFAULT_PAGE_NO : pageNum;
            pageSize = pageSize == 0 ? DEFAULT_PAGE_SIZE : pageSize;
            Page page = PageHelper.startPage(pageNum, pageSize);
            deviceMapper.getDeviceList(map);
            PageInfo<DeviceInfo> pageInfo = new PageInfo<>(page);
            return BaseResponse.ok(pageInfo);
        }
        return BaseResponse.ok(deviceMapper.getDeviceList(map));
    }

    @Override
    public DeviceInfo getDeviceDetail(Map<String, Object> map) {
        map.put("sid", RequestUtil.getHeaderSid());

        // 获取设备详情
        DeviceInfo deviceDetail = deviceMapper.getDeviceDetail(map);
        if (deviceDetail == null) {
            return null;
        }

        // 获取授权模块列表
        ResponseBase<List<String>> authTmcResponse = aioUserFeignClient.getAuthTmc(deviceDetail.getEid());
        if (authTmcResponse != null && authTmcResponse.checkIsSuccess()) {
            deviceDetail.setTmcList(authTmcResponse.getData());
        }

        return deviceDetail;
    }

    @Override
    public BaseResponse<PageInfo> getDeviceDetailByType(long id, String type, String search, int pageNum, int pageSize) {
        //region 参数检查

        Optional<BaseResponse> optResponse = checkParamIsEmpty(type, "type");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        //endregion

        if ("product".equals(type)) {
            //分页查找产品线
            pageNum = pageNum == 0 ? DEFAULT_PAGE_NO : pageNum;
            pageSize = pageSize == 0 ? DEFAULT_PAGE_SIZE : pageSize;
            Page page = PageHelper.startPage(pageNum, pageSize);
            deviceMapper.getDeviceProduct(id, search);
            PageInfo<SupplierProductApp> pageInfo = new PageInfo<>(page);
            return BaseResponse.ok(pageInfo);
        } else if ("collect".equals(type)) {
            //分页查找收集项列表
            //分页查找产品线
            pageNum = pageNum == 0 ? DEFAULT_PAGE_NO : pageNum;
            pageSize = pageSize == 0 ? DEFAULT_PAGE_SIZE : pageSize;
            Page page = PageHelper.startPage(pageNum, pageSize);
            List<DeviceCollectDetail> deviceCollectDetailList = deviceMapper.getDeviceCollectDetailList(id, search);
            PageInfo<DeviceCollectDetail> pageInfo = new PageInfo<>(page);
            //以下是为了给收集项设置预警的开关
            if (CollectionUtil.isNotEmpty(deviceCollectDetailList)) {
                Long eid = deviceCollectDetailList.get(0).getEid();
                Map<Long, DeviceCollectDetail> v2AccIdAdcdMap = new HashMap<>();
                Map<String, List<DeviceCollectDetail>> codeGroupAdcdMap = deviceCollectDetailList.stream().peek(x -> {
                            //设置是否能设置cron表达式
                            boolean isSettingCron = settingCron(x.getCollectType(), x.getCollectorModelCode());
                            x.setSettingCron(isSettingCron);
                            if ("v2".equalsIgnoreCase(x.getRuntimeVersion())) {
                                v2AccIdAdcdMap.put(x.getAccId(), x);
                            }
                            //默认关闭，只要有一个预警项开启，就是开启
                            x.setIsWarningEnable(false);
                            x.setIsWarningEnable(false);
                            List<CollectWarning> collectWarningList = x.getCollectWarningList();
                            if (CollectionUtils.isEmpty(collectWarningList)) {
                                return;
                            }
                            Optional<CollectWarning> optCollectWarning = collectWarningList.stream()
                                    .filter(CollectWarning::isWarningEnable).findFirst();
                            if (optCollectWarning.isPresent()) {
                                x.setIsWarningEnable(true);
                            }
                            x.getCollectWarningList().forEach(cw -> {
                                cw.setEid(eid);
                            });
                        }).filter(x -> BooleanUtils.toBoolean(x.getFindDisplayNameByModel()))
                        .collect(Collectors.groupingBy(DeviceCollectDetail::getAiopsInstanceName));
                if (!CollectionUtils.isEmpty(codeGroupAdcdMap)) {
                    Long sid = RequestUtil.getHeaderSidOrDefault(defaultSid);
                    //处理需要从模型获取外显值的内容
                    setDisplayNameByModel(sid, codeGroupAdcdMap.keySet(), (x) -> {
                        String key = x.getKey();
                        List<DeviceCollectDetail> adcdList = codeGroupAdcdMap.get(key);
                        if (CollectionUtils.isEmpty(adcdList)) {
                            return;
                        }
                        String value = x.getValue();
                        adcdList.forEach(y -> y.setAiopsInstanceName(value));
                    });
                }
                if (CollectionUtil.isNotEmpty(v2AccIdAdcdMap)) {
                    //v2特殊字段从数采批量取得
                    Map<String, Object> map = new HashMap<>(4);
                    map.put("accIdList", v2AccIdAdcdMap.keySet());
                    map.put("specialColumns", new String[]{"acc.id", "acc.dfiId", "acc.isOldDataSync"});
                    map.put("containIsDataSync", true);
                    BaseResponse<List<Map<String, Object>>> dcdpAccRes = restItemsUtil.getAccSimpleInfoByMap(map);
                    if (!dcdpAccRes.checkIsSuccess()) {
                        return BaseResponse.error(dcdpAccRes);
                    }
                    List<Map<String, Object>> mapList = dcdpAccRes.getData();
                    if (CollectionUtil.isNotEmpty(mapList)) {
                        mapList.forEach(x -> {
                            Long accId = LongUtil.objectToLong(x.get("id"));
                            if (LongUtil.isEmpty(accId)) {
                                return;
                            }
                            DeviceCollectDetail adcd = v2AccIdAdcdMap.get(accId);
                            if (Objects.isNull(adcd)) {
                                return;
                            }
                            adcd.setIsDataSync(BooleanUtil.objectToBoolean(x.get("isDataSync")));
                            adcd.setIsOldDataSync(BooleanUtil.objectToBoolean(x.get("isOldDataSync")));
                        });
                    }
                }
            }

            // 從 dcdp 取得最新的 modelName
            this.mapDcDpModelName(pageInfo);

            return BaseResponse.ok(pageInfo);
        } else {
            return BaseResponse.ok();
        }
    }

    private void mapDcDpModelName(PageInfo<DeviceCollectDetail> pageInfo) {
        if (Objects.isNull(pageInfo)) {
            return;
        }
        List<DeviceCollectDetail> pageList = pageInfo.getList();
        if (CollectionUtils.isEmpty(pageList)) {
            return;
        }
        List<String> modelCodes = pageList.stream()
                .filter(x -> BooleanUtils.toBoolean(x.getFindDisplayNameByModel()))
                .map(DeviceCollectDetail::getAiopsInstanceName)
                .distinct()
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(modelCodes)) {
            return;
        }
        BaseResponse dcdpModelRes = restUtil.getModelDetailList(modelCodes);
        Map<String, Object> dcDpModelData = (Map<String, Object>) dcdpModelRes.getData();
        if (CollectionUtils.isEmpty(dcDpModelData)) {
            return;
        }

        Map<String, String> dcDpModelNameMap = new HashMap<>();
        for (Map.Entry<String, Object> entry : dcDpModelData.entrySet()) {
            String key = entry.getKey();
            Object value = entry.getValue();
            Map<String, Object> dcdpModelMap = (Map<String, Object>) value;
            Map<String, Object> fieldMap = (Map<String, Object>) dcdpModelMap.get("field");
            if (Objects.isNull(fieldMap)) {
                continue;
            }
            String dcdpModelName = fieldMap.computeIfAbsent("modelName", k -> "NotFound").toString();
            dcDpModelNameMap.put(key, dcdpModelName);
        }
        // setting current modelName
        pageInfo.getList().forEach(row -> {
            String key = row.getAiopsInstanceName();
            String cursInstanceName = dcDpModelNameMap.computeIfAbsent(key, k -> row.getAiopsInstanceName());
            row.setAiopsInstanceName(cursInstanceName);
        });

    }

    @Override
    public BaseResponse getDeviceCollectWarningSetList(Long adId, Integer pageNum, Integer pageSize) {
        //region 参数检查

        Optional<BaseResponse> optResponse = checkParamIsEmpty(adId, "adId");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        pageNum = IntegerUtil.isEmpty(pageNum) ? DEFAULT_PAGE_NO : pageNum;
        pageSize = IntegerUtil.isEmpty(pageSize) ? DEFAULT_PAGE_SIZE : pageSize;

        //endregion

        // 查询设备eid
        AiopsKitDevice aiopsKitDevice = deviceMapper.selectKitDeviceIdByAdId(adId);
        Page page = PageHelper.startPage(pageNum, pageSize);
        Map<String, Object> map = new HashMap<>(2);
        map.put("adId", adId);
        map.put("innerRunnerAccIdSet", innerRunnerAccIdSet);
        map.put("eid", aiopsKitDevice.getEid());
        deviceMapper.selectDeviceCollectWarningSetList(map);
        return BaseResponse.ok(new PageInfo(page));
    }

    @Override
    public BaseResponse getWarningListByAiId(Long aiId, Boolean needPaging, Integer pageNum, Integer pageSize) {
        //region 参数检查

        Optional<BaseResponse> optResponse = checkParamIsEmpty(aiId, "aiId");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        needPaging = BooleanUtils.toBoolean(needPaging);

        //endregion

        if (needPaging) {
            pageNum = IntegerUtil.isEmpty(pageNum) ? DEFAULT_PAGE_NO : pageNum;
            pageSize = IntegerUtil.isEmpty(pageSize) ? DEFAULT_PAGE_SIZE : pageSize;
            Page page = PageHelper.startPage(pageNum, pageSize);
            deviceMapper.selectWarningListByAiId(aiId);
            return BaseResponse.ok(new PageInfo(page));
        }
        return BaseResponse.ok(deviceMapper.selectWarningListByAiId(aiId));
    }

    private DeviceInfo getDeviceDetailByDeviceId(long sid, String deviceId) {
        Map<String, Object> map = new HashMap<>();
        map.put("deviceId", deviceId);
        map.put("sid", sid);
        return deviceMapper.getDeviceDetail(map);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BaseResponse addProductAppForDevice(long spId, long adId, String deviceId,
                                               List<DeviceProductMapping> deviceProductMappingList) {
        //region 参数检查

        Optional<BaseResponse> optResponse = checkParamIsEmpty(spId, "spId");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        optResponse = checkParamIsEmpty(adId, "adId");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        optResponse = checkParamIsEmpty(deviceId, "deviceId");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        if (CollectionUtils.isEmpty(deviceProductMappingList)) {
            return BaseResponse.ok(false);
        }

        //endregion

        Long sid = RequestUtil.getHeaderSidOrDefault(defaultSid);

        //1.添加产品线时，先判断改产品线是否已经添加过，如果已经添加过，不给重复添加
        List<DeviceProductMapping> appList = getProductAppForDevice(spId, adId, null);
        if (!CollectionUtils.isEmpty(appList)) {
            return BaseResponse.error(ResponseCode.PRODUCT_IS_EXIST);
        }

        //2.添加产品下面的应用
        log.info(deviceId + "addProductAppForDevice");
        //处理相关默认值
        setProcessDeviceProductMapping(spId, adId, deviceId, deviceProductMappingList);
        deviceMapper.addProductAppForDevice(deviceProductMappingList);
        BaseResponse response = createAdpmAiopsInstance(spId, deviceId, null, deviceProductMappingList);
        if (!response.checkIsSuccess()) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return response;
        }
        //将设备与应用/服务关系储存到关系实例表
        return addDeviceProductAppRelate(sid, deviceId, deviceProductMappingList);
    }

    private BaseResponse addDeviceProductAppRelate(Long sid, String deviceId, List<DeviceProductMapping> deviceProductMappingList) {
        if (StringUtils.isBlank(deviceId) || CollectionUtils.isEmpty(deviceProductMappingList)) {
            //设备Id或者映射列表为空，离开
            return BaseResponse.ok();
        }
        //设备产品/服务映射不会重复
        List<Long> apaIdList = deviceProductMappingList.stream()
                .map(x -> x.getApaId())
                .filter(x -> !LongUtil.isEmpty(x))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(apaIdList)) {
            //表示没有产品/服务Id，离开
            return BaseResponse.ok();
        }
        //1.查询所有apa对应的模型代号
        List<Map<String, Object>> apaIdModelCodeMapList = deviceMapper.selectApaModelCodeByApaIdList(apaIdList);
        if (CollectionUtils.isEmpty(apaIdModelCodeMapList)) {
            //表示没有取到模型代号，离开
            return BaseResponse.ok();
        }
        //2.组织成字典
        Map<Long, String> apaIdModelCodeMap = apaIdModelCodeMapList.stream()
                .filter(x -> !LongUtil.isEmpty((Long) x.get("apaId")))
                .collect(Collectors.toMap(x -> (Long) x.get("apaId"),
                        x -> ObjectUtils.toString(x.get("modelCode")), (x, y) -> x));

        List<ModelRelateInstance> modelRelateInstanceList = deviceProductMappingList.stream()
                .filter(x -> apaIdModelCodeMap.containsKey(x.getApaId()))
                .map(x -> {
                    String modelCode = apaIdModelCodeMap.get(x.getApaId());
                    ModelRelateInstance modelRelateInstance =
                            createDeviceProductAppRelateInstance(sid, deviceId, modelCode, x.getId());
                    return modelRelateInstance;
                }).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(modelRelateInstanceList)) {
            //没有关系，离开
            return BaseResponse.ok();
        }

//        return aioCmdbFeignClient.saveModelRelateInstanceComplete(false, modelRelateInstanceList);
        return restUtil.saveModelRelateInstanceComplete(false, modelRelateInstanceList);
    }

    private ModelRelateInstance createDeviceProductAppRelateInstance(Long sid, String deviceId, String modelCode, Long adpmId) {
        ModelRelateInstance modelRelateInstance = createBaseDeviceProductAppRelateInstance(sid, deviceId);
        modelRelateInstance.setTargetModelCode(modelCode);
        modelRelateInstance.setTargetFieldCode(APP_SERVICE_ID);
        //应用/服务映射Id即为实例应用/服务Id
        modelRelateInstance.setTargetFieldValue(Long.toString(adpmId));
        modelRelateInstance.setModelRelateCode(String.join("_", modelRelateInstance.getSourceModelCode(),
                modelRelateInstance.getTargetModelCode()));
        return modelRelateInstance;
    }

    private ModelRelateInstance createBaseDeviceProductAppRelateInstance(Long sid, String deviceId) {
        ModelRelateInstance modelRelateInstance = new ModelRelateInstance();
        modelRelateInstance.setSid(sid);
        //设备与应用/服务关系是部属
        modelRelateInstance.setRelateType(ModelRelateTypeEnum.DEPLOY.name());
        modelRelateInstance.setSourceModelCode(DEVICE);
        modelRelateInstance.setSourceFieldCode(Constants.Device.DEVICE_ID);
        modelRelateInstance.setSourceFieldValue(deviceId);
        return modelRelateInstance;
    }

    @Async
    @Override
    public void addAppCollectForDevice(Long adId, String deviceId, boolean autoAddCollect,
                                       List<DeviceProductMapping> deviceProductMappingList) {
        //3.自动添加收集项 需要添加收集项
        //region 参数检查

        if (LongUtil.isEmpty(adId)) {
            return;
        }

        if (StringUtils.isBlank(deviceId)) {
            return;
        }
        Map<String, Object> map = new HashMap<>();
        if (!autoAddCollect) {
            //如果不自动添加，就只添加，系统级别(必选)的
            map.put("isSystem", true);
        }

        if (CollectionUtils.isEmpty(deviceProductMappingList)) {
            return;
        }

        //endregion

        Long sid = RequestUtil.getHeaderSidOrDefault(defaultSid);
        Long eid = getEidByDeviceId(deviceId);

        //3.1单头 单头已存在就无需新增
        DeviceCollect deviceCollect = dealDeviceCollect(adId, deviceId);

        //3.2 单身
        DeviceInfo deviceInfo = getDeviceDetailByDeviceId(sid, deviceId);
        //查所有选中的应用下面的所有收集项
        Set<String> aiopsItemSet = new HashSet<>();
        List<Long> apaIdList = deviceProductMappingList.stream().map(o -> {
            aiopsItemSet.add(o.getProductCode());
            return o.getApaId();
        }).collect(Collectors.toList());
        map.put("adId", adId);
        map.put("apaIds", apaIdList);
        //不限制只有启动的才添加
        //map.put("isEnable", true);
        map.put("scopeId", DEFAULT_SCOPE_ID);
        //这个条件是为了排除已经添加过的收集项
        map.put("adcId", deviceCollect.getId());
        //设备的平台要包含在收集项的平台里面
        if (deviceInfo != null && !StringUtils.isEmpty(deviceInfo.getPlatform().toString())) {
            map.put("devicePlatform", deviceInfo.getPlatform().toString());
        }
        //设备的设备类型要包含收集项的收集项设备类型
        if (deviceInfo != null && !CollectionUtils.isEmpty(deviceInfo.getDeviceTypeMappingList())) {
            map.put("deviceType", deviceInfo.getDeviceTypeMappingList().stream().map(o -> o.getDeviceType())
                    .collect(Collectors.toList()));
        }
        map.put("needFilterValidSamcdIdList", true);
        BaseResponse<List<Long>> response = getValidSamcdIdListCore(sid, eid, aiopsItemSet);
        if (response.checkIsSuccess()) {
            map.put("validSamcdIdList", response.getData());
        }

        List<CollectAppMapping> collectConfigWarningList = collectAppMapper.getProductCollectList(map);
        List<Map<String, Object>> execParamsProcessMapList = batchDeviceCollectDetail(deviceCollect.getId(), deviceId,
                collectConfigWarningList);
        //设定必填收集项的执行参数
        setSystemCollectDefaultExecParams(eid, execParamsProcessMapList, deviceProductMappingList);
    }

    private DeviceCollect dealDeviceCollect(Long adId, String deviceId) {
        DeviceCollect deviceCollect = deviceMapper.getDeviceCollect(deviceId);
        //为空 需要新增一笔新的
        if (deviceCollect == null) {
            if (LongUtil.isEmpty(adId)) {
                adId = getAdIdByDeviceId(deviceId);
            }
            deviceCollect = new DeviceCollect();
            deviceCollect.setDeviceId(deviceId);
            deviceCollect.setAdId(adId);
            deviceCollect.setId(SNOWFLAKE.newId());

            log.info("addDeviceCollect deviceId: {}", deviceId);
            deviceMapper.addDeviceCollect(deviceCollect);
            //可能因为并发忽略了，重新查一下，确保正确
            deviceCollect = deviceMapper.getDeviceCollect(deviceId);
            executeUpgradeToDcdp(Stream.of(deviceCollect).map(x -> {
                AiopsUpgradeDcdpDetail detail = new AiopsUpgradeDcdpDetail(
                        DeviceUpgradeEnum.DEVICE_COLLECT.getTableName(), BeanUtil.object2Map(x));
                Map<String, Object> paramsMap = detail.getParamsMap();
                paramsMap.put(DEVICE_ID, x.getDeviceId());
                detail.setNeedSaveOperationLog(true);
                Map<String, Object> operationInfoMap = OperationUtil.getHeaderOperationInfo();
                operationInfoMap.put("operationObjectClassification", "instance");
                operationInfoMap.put("operationObject", "instance");
                operationInfoMap.put("operationBehavior", "adddevicecollect");
                detail.setOperationInfoMap(operationInfoMap);
                return detail;
            }).collect(Collectors.toList()));
        }
        return deviceCollect;
    }

    //设备批量新增收集项
    private List<Map<String, Object>> batchDeviceCollectDetail(long adcId, String deviceId,
                                                               List<CollectAppMapping> collectConfigWarningList) {
        if (CollectionUtils.isEmpty(collectConfigWarningList)) {
            return null;
        }
        int length = collectConfigWarningList.size();

        List<Map<String, Object>> resultMapList = new ArrayList<>(length);

        //批量新增收集项到设备中
        //对collectConfigWarningList 里面的收集项不做去重，因为不同的应用可能会添加相同的收集项，但是运维实例会不同
        //Set<Long> existCollectConfigSet = new HashSet<>(length);
        String[] emptyResult = collectConfigService.modifyExecParams("", 0L, false);
        List<Map<String, Object>> delayExecuteList = new ArrayList<>(length);
        List<DeviceCollectDetail> deviceCollectDetailList = collectConfigWarningList.stream()
                .filter(x -> x != null)
                //.filter(x -> !existCollectConfigSet.contains(x.getAccId()))
                .map(x -> {
                    Long accId = x.getAccId();
                    DeviceCollectDetail deviceCollectDetail = new DeviceCollectDetail();
                    Long adcdId = SNOWFLAKE.newId();
                    deviceCollectDetail.setId(adcdId);
                    deviceCollectDetail.setAdcId(adcId);
                    deviceCollectDetail.setAccId(accId);
                    deviceCollectDetail.setCollectName(x.getCollectName());
                    //设定产品收集项定义的启动状态
                    deviceCollectDetail.setIsEnable(x.getIsEnable());
                    deviceCollectDetail.setAiId(x.getAiId());
                    deviceCollectDetail.setAdimId(x.getAdimId());
                    boolean hasExecParams = x.getHasExecParams();
                    //填充是否必填与应用/服务Id，供后面自动填写执行参数使用
                    if (BooleanUtils.isTrue(x.getIsSystem()) && hasExecParams) {
                        Map<String, Object> resultMap = new HashMap<>(3);
                        resultMap.put("adcdId", adcdId);
                        resultMap.put("apaId", x.getApaId());
                        resultMap.put("execParamsModelCode", x.getExecParamsModelCode());
                        resultMapList.add(resultMap);
                    }
                    if (!hasExecParams) {
                        //没有执行参数，直接填空
                        deviceCollectDetail.setExecParamsVersion(emptyResult[0]);
                        deviceCollectDetail.setExecParamsContent(emptyResult[1]);
                    }
                    //existCollectConfigSet.add(accId);
                    //因为规则引擎需要查询，因此将添加预警相关内容抽到添加完成设备收集项明细后执行
                    Map<String, Object> map = new HashMap<>(2);
                    map.put("adcdId", deviceCollectDetail.getId());

                    // 新设备接入
                    List<CollectWarning> collectWarningList = getCollectWarningList(deviceId, x.getCollectWarningList());
                    map.put("awcList", collectWarningList);
                    delayExecuteList.add(map);
                    return deviceCollectDetail;
                }).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(deviceCollectDetailList)) {
            return resultMapList;
        }
        log.info("batchDeviceCollectDetail");
        //批量新增收集项
        deviceMapper.batchDeviceCollectDetail(deviceCollectDetailList);
        BaseResponse response = upgradeService.upgradeToDcdpAsyncWaitByDetail(getDeviceUpgradeToDcdpDetail(
                deviceCollectDetailList.stream().map(x -> {
                    AiopsUpgradeDcdpRequest.DeviceUpgrade deviceUpgrade = new AiopsUpgradeDcdpRequest.DeviceUpgrade(
                            deviceId, DeviceUpgradeEnum.DEVICE_COLLECT_DETAIL);
                    deviceUpgrade.getOtherConditionMap().put("adcd.id", x.getId());
                    deviceUpgrade.setNeedSaveOperationLog(true);
                    Map<String, Object> operationInfoMap = deviceUpgrade.getOperationInfoMap();
                    operationInfoMap.put("operationObjectClassification", "device_collect_detail");
                    operationInfoMap.put("operationObject", "device_collect_detail");
                    operationInfoMap.put("operationBehavior", "adddevicecollectconfig");
                    return deviceUpgrade;
                }).collect(Collectors.toList())));
        if (!response.checkIsSuccess()) {
            log.error("batchDeviceCollectDetail executeUpgradeToDcdpAsyncWait fail:{}", response.getErrMsg());
        }
        //产生设备收集项相关预警项
        delayExecuteList.forEach(x -> batchDeviceCollectWarning(LongUtil.objectToLong(x.get("adcdId")),
                (List<CollectWarning>) x.get("awcList"), null, deviceId));
        //清除设备缓存
        collectConfigService.clearDeviceIdCollectConfigCache(deviceId);
        return resultMapList;
    }

    private void batchDeviceCollectWarning(Long adcdId, List<CollectWarning> collectWarningList,
                                           Long eid, String deviceId) {
        if (CollectionUtils.isEmpty(collectWarningList)) {
            return;
        }
        //不管是否预警，都进行添加
        List<DeviceCollectWarning> deviceCollectWarningList = collectWarningList.stream().filter(Objects::nonNull)
                .map(x -> new DeviceCollectWarning(adcdId, x.getId(), x.isWarningEnable(), x.getReAuthStatus()))
                .collect(Collectors.toList());
        log.info("batchDeviceCollectWarning");
        //批量新增设备收集项预警项的关联
        if (!CollectionUtils.isEmpty(deviceCollectWarningList)) {
            deviceMapper.batchDeviceCollectWarning(deviceCollectWarningList);
            //批量新增规则引擎
            ruleEngineService.batchSaveRuleEngineByAdcwList(deviceCollectWarningList);
            //带入默认通知群组
            batchAddDefaultDeviceWarningNotifyMapping(deviceCollectWarningList, eid, deviceId);
        }
    }

    @Override
    public void batchAddDefaultDeviceWarningNotifyMapping(List<DeviceCollectWarning> deviceCollectWarningList,
                                                           Long eid, String deviceId) {
        //如果没有设备收集项预警列表，离开
        if (CollectionUtils.isEmpty(deviceCollectWarningList)) {
            return;
        }
        if (LongUtil.isEmpty(eid)) {
            //如果没有设备Id离开
            if (StringUtils.isBlank(deviceId)) {
                return;
            }
        }
        Long sid = RequestUtil.getHeaderSidOrDefault(defaultSid);

        Optional<String> optSourceId = this.getTenantNoticeDefaultGroupId(sid, eid, deviceId);
        if (!optSourceId.isPresent()) {
            log.error(String.join(" ", "eid:", LongUtil.safeToString(eid), "deviceId:", deviceId,
                    "sid:", LongUtil.safeToString(sid),
                    "batchAddDefaultDeviceWarningNotifyMapping return by not get sourceId"));
            return;
        }
        String sourceId = optSourceId.get();
        //3.批量进行新增
        List<DeviceWarningNotifyMapping> deviceWarningNotifyMappingList = deviceCollectWarningList.stream().map(x -> {
            DeviceWarningNotifyMapping deviceWarningNotifyMapping = new DeviceWarningNotifyMapping();
            deviceWarningNotifyMapping.setId(SNOWFLAKE.newId());
            deviceWarningNotifyMapping.setAdcwId(x.getId());
            deviceWarningNotifyMapping.setNotifyCategory(BY_GROUP.name());
            deviceWarningNotifyMapping.setSourceId(sourceId);
            deviceWarningNotifyMapping.setIsDefault(true);
            return deviceWarningNotifyMapping;
        }).collect(Collectors.toList());
        deviceMapper.batchInsertDeviceWarningNotify(deviceWarningNotifyMappingList);
    }

    private Optional<String> getTenantNoticeDefaultGroupId(Long sid, Long eid, String deviceId) {
        //1.透过devcieId串查eid
        if (LongUtil.isEmpty(eid)) {
            if (StringUtils.isBlank(deviceId)) {
                return Optional.empty();
            }
            eid = getEidByDeviceId(deviceId);
        }
        //如果查不到eid，离开
        if (LongUtil.isEmpty(eid)) {
            return Optional.empty();
        }
        //2.获取该eid默认的通知群组
        Map<String, Object> map = new HashMap<>();
        map.put("sid", sid);
        map.put("eid", eid);
        map.put("dft", 1);
        ResponseBase responseBase = aioUserFeignClient.getTenantNoticeGroupList(map);
        if (!ResponseCode.SUCCESS.getCode().equals(responseBase.getCode())) {
            System.out.println(String.join(" ", "deviceId:", deviceId,
                    "batchAddDefaultDeviceWarningNotifyMapping fail. error:", responseBase.getErrMsg()));
            return Optional.empty();
        }
        Object data = responseBase.getData();
        if (data == null) {
            return Optional.empty();
        }

        Map<String, Object> resultMap = (Map<String, Object>) data;
        Object listItem = resultMap.get(LIST_FIELD_KEY);
        if (listItem == null) {
            return Optional.empty();
        }
        List<Map<String, Object>> listMap = (List<Map<String, Object>>) listItem;
        if (CollectionUtils.isEmpty(listMap)) {
            return Optional.empty();
        }
        Object objId = listMap.get(0).get(ID_FIELD_KEY);
        if (objId == null) {
            return Optional.empty();
        }
        return Optional.of(objId.toString());
    }

    private void setSystemCollectDefaultExecParams(Long eid, List<Map<String, Object>> execParamsProcessMapList,
                                                   List<DeviceProductMapping> deviceProductMappingList) {
        //huly: 修复漏洞/bug CollectionUtils.isEmpty(execParamsProcessMapList) 改成 execParamsProcessMapList == null
        if (execParamsProcessMapList == null) {
            return;
        }
        //批量取得执行参数模型的空json
        List<String> execParamsModelCodes = execParamsProcessMapList.stream()
                .map(x -> ObjectUtils.toString(x.get("execParamsModelCode")))
                .collect(Collectors.toList());
//        BaseResponse response = aioCmdbFeignClient.getModelJson(execParamsModelCodes);
        BaseResponse response = restUtil.getModelJson(execParamsModelCodes);
        Optional<Map<String, Object>> optResponse = response.checkAndGetCurrentData();
        if (!optResponse.isPresent()) {
            return;
        }
        Map<String, Object> modelsStruct = optResponse.get();
        //产生apaId字典，加速后面进行设置执行参数
        Map<Long, Long> apaIdAndAdpmIdMap = deviceProductMappingList.stream()
                .collect(Collectors.toMap(x -> x.getApaId(), x -> x.getId(), (x, y) -> x));
        execParamsProcessMapList.stream().filter(x -> apaIdAndAdpmIdMap.containsKey(x.get("apaId")))
                .forEach(x -> {
                    Long adpmId = apaIdAndAdpmIdMap.get(x.get("apaId"));
                    Long adcdId = (Long) x.get("adcdId");
                    //执行参数模型编号必定有值
                    String execParamsModelCode = ObjectUtils.toString(x.get("execParamsModelCode"));
                    //1.获取执行参数空模型
                    Optional<Map<String, Object>> optModelStruct = MapUtil.getMap(modelsStruct, execParamsModelCode);
                    if (!optModelStruct.isPresent()) {
                        return;
                    }
                    Map<String, Object> modelStruct = optModelStruct.get();
                    if (CollectionUtils.isEmpty(modelStruct)) {
                        return;
                    }
                    Map<String, Object> paramMap = new HashMap<>(1);
                    paramMap.put(ADPM_ID, LongUtil.safeToString(adpmId));
                    //深拷贝
                    modelStruct = SerializeUtil.JsonDeserialize(SerializeUtil.JsonSerialize(modelStruct),
                            HashMap.class);
                    optModelStruct = execParamsFactoryService.getExecParamMap(execParamsModelCode, modelStruct,
                            paramMap);
                    if (!optModelStruct.isPresent()) {
                        return;
                    }
                    modelStruct = optModelStruct.get();
                    if (CollectionUtils.isEmpty(modelStruct)) {
                        return;
                    }
                    collectConfigService.modifyExecParamsByMap(modelStruct, adcdId, true,
                            eid, true, execParamsModelCode);
                });
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BaseResponse addDeviceCollectDetailByAppMappingList(Long adId, String deviceId,
                                                               List<CollectAppMapping> collectAppMappingList) {
        //region 参数检查

        Optional<BaseResponse> optResponse = checkParamIsEmpty(adId, "adId");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        optResponse = checkParamIsEmpty(deviceId, "device");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        //endregion

        //1.新增或获取单头
        DeviceCollect deviceCollect = dealDeviceCollect(adId, deviceId);
        //如果没有内容要处理，直接返回设备收集项版本Id(方便后面操作)
        if (CollectionUtils.isEmpty(collectAppMappingList)) {
            return BaseResponse.ok(deviceCollect.getId());
        }
        //2.处理单身
        //规则：
        //1.如果已经添加过了，不再重复进行添加
        //2.如果前端取消勾选，不进行删除(换言之只处理勾选的内容)，要删除请使用设备收集项维护页面进行处理
        //2.1.不处理删除是考量到，有可能同个收集项重复使用，如果这里进行删除，可能会把复用的一并删除(而无法单独选择)
        //日后如果有需要应另外处理
        Map<String, Object> map = new HashMap<>();
        map.put("adcId", deviceCollect.getId());
        List<DeviceCollectDetail> deviceCollectDetailList =
                deviceMapper.selectDeviceCollectDetailByAdcId(map);
        if (CollectionUtils.isEmpty(deviceCollectDetailList)) {
            //表示完全新增
            Long adcId = deviceCollect.getId();
            batchDeviceCollectDetail(adcId, deviceId, collectAppMappingList);
            return BaseResponse.ok(adcId);
        }
        //设备收集项详情，同一个收集项有可能出现重复项(复用场景)，因此产生字典要小心
        Map<String, DeviceCollectDetail> existCollectMap = deviceCollectDetailList.stream().filter(x -> x != null)
                .collect(Collectors.toMap(x -> String.join("_", LongUtil.safeToString(x.getAdimId()),
                                LongUtil.safeToString(x.getAiId()), LongUtil.safeToString(x.getAccId())), x -> x,
                        (x1, x2) -> x2));
        //获取过滤掉存在的收集项，进行新增
        List<CollectAppMapping> newCollectAppMap = collectAppMappingList.stream()
                .filter(x -> x != null)
                .filter(x -> !existCollectMap.containsKey(String.join("_", LongUtil.safeToString(x.getAdimId()),
                        LongUtil.safeToString(x.getAiId()), LongUtil.safeToString(x.getAccId()))))
                .collect(Collectors.toList());
        batchDeviceCollectDetail(deviceCollect.getId(), deviceId, newCollectAppMap);
        return BaseResponse.ok(deviceCollect.getId());
    }

    @Override
    public BaseResponse getDeviceCollectDetailExecParams(Long adcId, List<CollectAppMapping> collectAppMappingList) {
        //region 参数检查

        Optional<BaseResponse> optResponse = checkParamIsEmpty(adcId, "adcId");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        if (collectAppMappingList == null) {
            return BaseResponse.ok();
        }

        //endregion

        //因为向导可能在中间保存后中断，如果是同个收集项复用场景，这里没有办法知道哪些是本次向导添加的收集项
        //因此直接抓出本次选择(包含复用)的收集项返回进行执行参数设置
        Map<String, Object> map = new HashMap<>();
        map.put("adcId", adcId);
        map.put("accIdList", collectAppMappingList.stream().filter(x -> x != null)
                .map(x -> x.getAccId()).collect(Collectors.toList()));
        return BaseResponse.ok(deviceMapper.selectDeviceCollectDetailByAdcId(map));
    }

    @Override
    public BaseResponse getDeviceCollectWarningOneLevelList(Long adcId, List<CollectAppMapping> collectAppMappingList) {
        //region 参数检查

        Optional<BaseResponse> optResponse = checkParamIsEmpty(adcId, "adcId");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        if (CollectionUtils.isEmpty(collectAppMappingList)) {
            return BaseResponse.ok();
        }

        //endregion

        Map<String, Object> map = new HashMap<>();
        map.put("adcId", adcId);
        map.put("defaultScopeId", DEFAULT_SCOPE_ID);
        map.put("accIdList", collectAppMappingList.stream().filter(x -> x != null)
                .map(x -> x.getAccId()).collect(Collectors.toList()));
        return BaseResponse.ok(deviceMapper.selectDeviceCollectWarningByAdcIdAndAccIdList(map));
    }

    @Override
    public BaseResponse getDeviceCollectWarning(Long adcwId, Long adcdId, List<CollectAppMapping> collectAppMappingList) {
        //region 参数检查

        Optional<BaseResponse> optResponse = checkParamIsEmpty(adcdId, "adcdId");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        if (CollectionUtils.isEmpty(collectAppMappingList)) {
            return BaseResponse.ok();
        }

        //endregion

        //如果adcdId为空，抓取收集项底下所有预警设定
        Map<String, Object> map = new HashMap<>();
        map.put("adcwId", adcwId);
        map.put("adcdId", adcdId);
        map.put("defaultScopeId", DEFAULT_SCOPE_ID);
        map.put("accIdList", collectAppMappingList.stream().filter(x -> x != null)
                .map(x -> x.getAccId()).collect(Collectors.toList()));
        return BaseResponse.ok(deviceMapper.selectDeviceCollectWarningByAdcdIdOrAdcwId(map));
    }

    @Override
    public List<DeviceProductMapping> getProductAppForDevice(long spId, long adId, String modelCode) {
        Map<String, Object> map = new HashMap<>();
        map.put("spId", spId);
        map.put("adId", adId);
        map.put("modelCode", modelCode);
        //10832【後端API】【数据服务】数据服务的报表模板可以在智管家呈现给客户
        if (spId == 0 && StringUtils.isBlank(modelCode)) {
            throw new RuntimeException(
                    "spId and modelCode is all empty, Please check whether either 'spId' or 'modelCode' has a value");
        }
        return getProductAppForDeviceCore(map);
    }


    private List<DeviceProductMapping> getProductAppForDeviceCore(Map<String, Object> map) {
        return deviceMapper.getProductAppForDevice(map);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BaseResponse updateProductAppForDevice(long spId, long adId, String deviceId, String aiopsItem,
                                                  List<DeviceProductMapping> deviceProductMappingList) {
        //region 参数检查

        //10832【後端API】【数据服务】数据服务的报表模板可以在智管家呈现给客户
        Optional<BaseResponse> optResponse = checkParamIsEmpty(spId, "spId");
        if (optResponse.isPresent() && StringUtils.isBlank(aiopsItem)) {
            return BaseResponse.error(
                    new RuntimeException(
                            "spId and aiopsItem is all empty, Please check whether either 'spId' or 'aiopsItem' has a value")
            );
        }

//        Optional<BaseResponse> optResponse = checkParamIsEmpty(spId, "spId");
//        if (optResponse.isPresent()) {
//            return optResponse.get();
//        }

        optResponse = checkParamIsEmpty(adId, "adId");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        optResponse = checkParamIsEmpty(deviceId, "deviceId");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        optResponse = checkParamIsEmpty(deviceId, "deviceId");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        //endregion

        Long sid = RequestUtil.getHeaderSidOrDefault(defaultSid);

        log.info(deviceId + "modifyProductAppForDevice");
        Map<String, Object> map = new HashMap<>();
        map.put("adId", adId);
        map.put("spId", spId);

        //10832【後端API】【数据服务】数据服务的报表模板可以在智管家呈现给客户
        map.put("modelCode", aiopsItem); //modelCode = aiopsItem = productCode ?


        if (CollectionUtils.isEmpty(deviceProductMappingList)) {
            BaseResponse response = removeAdimAndAdpmByMap(deviceId, map); //FIXME 可能也要調整
            if (!response.checkIsSuccess()) {
                TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                return response;
            }
            //删除所有设备应用/服务关系实例
            List<ModelRelateInstance> modelRelateInstanceList = new ArrayList<>(1);
            modelRelateInstanceList.add(createBaseDeviceProductAppRelateInstance(sid, deviceId));
//            response = aioCmdbFeignClient.batchRemoveModelRelateInstanceByCondition(modelRelateInstanceList);
            response = restUtil.batchRemoveModelRelateInstanceByCondition(modelRelateInstanceList);
            if (!response.checkIsSuccess()) {
                return response;
            }
            return BaseResponse.ok();
        }

        List<AiopsUpgradeDcdpDetail> upgradeDcdpDetailList = new ArrayList<>();
        //处理相关默认值
        setProcessDeviceProductMapping(spId, adId, deviceId, deviceProductMappingList); //FIXME 可能也要調整
        //查询已存在的产品应用/服务映射纪录
        List<DeviceProductMapping> existDeviceProductMappingList = getProductAppForDeviceCore(map);
        if (CollectionUtils.isEmpty(existDeviceProductMappingList)) {
            //没有已存在的，直接进行新增
            deviceMapper.addProductAppForDevice(deviceProductMappingList);
            upgradeDcdpDetailList.addAll(getInstanceUpgradeToDcdpDetail(deviceProductMappingList.stream().map(x -> {
                AiopsUpgradeDcdpRequest.InstanceUpgrade instanceUpgrade =
                        new AiopsUpgradeDcdpRequest.InstanceUpgrade(x.getAppId());
                instanceUpgrade.getParamsMap().put("checkDeviceId", deviceId);
                instanceUpgrade.setNeedSaveOperationLog(true);
                Map<String, Object> operationInfoMap = instanceUpgrade.getOperationInfoMap();
                operationInfoMap.put("operationObjectClassification", "instance");
                operationInfoMap.put("operationObject", "instance");
                operationInfoMap.put("operationBehavior", "addinstance");
                return instanceUpgrade;
            }).collect(Collectors.toList())));

            // 建立實例
            BaseResponse response = createAdpmAiopsInstance(spId, deviceId, aiopsItem, deviceProductMappingList);
            if (!response.checkIsSuccess()) {
                TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                return response;
            }

            response = addDeviceProductAppRelate(sid, deviceId, deviceProductMappingList);
            if (!response.checkIsSuccess()) {
                TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                return response;
            }

            response = executeUpgradeToDcdp(upgradeDcdpDetailList);
            if (!response.checkIsSuccess()) {
                TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                return response;
            }
            //成功后把处理后的结果返回，使之能进行后面步骤
            return BaseResponse.ok(deviceProductMappingList);
        }
        //产生字典
        Map<String, DeviceProductMapping> existMap = existDeviceProductMappingList.stream()
                .collect(Collectors.toMap(x -> String.join("_", Long.toString(x.getApaId()), Long.toString(x.getAdId())), x -> x));
        List<DeviceProductMapping> needUpdateList;
        if (CollectionUtils.isEmpty(existMap)) {
            needUpdateList = new ArrayList<>(0);
        } else {
            needUpdateList = new ArrayList<>(existMap.size());
        }
        //挑选出新增与删除项
        List<DeviceProductMapping> notExistList = deviceProductMappingList.stream()
                .filter(x -> {
                    String key = String.join("_", Long.toString(x.getApaId()), Long.toString(x.getAdId()));
                    DeviceProductMapping existItem = existMap.get(key);
                    if (existItem != null) {
                        x.setId(existItem.getId());
                        needUpdateList.add(x);
                        existMap.remove(key);
                        return false;
                    }
                    return true;
                })
                .collect(Collectors.toList());
        //来源不存在的进行新增
        if (!CollectionUtils.isEmpty(notExistList)) {
            deviceMapper.addProductAppForDevice(notExistList);
            upgradeDcdpDetailList.addAll(getInstanceUpgradeToDcdpDetail(deviceProductMappingList.stream().map(x -> {
                AiopsUpgradeDcdpRequest.InstanceUpgrade instanceUpgrade =
                        new AiopsUpgradeDcdpRequest.InstanceUpgrade(x.getAppId());
                instanceUpgrade.getParamsMap().put("checkDeviceId", deviceId);
                instanceUpgrade.setNeedSaveOperationLog(true);
                Map<String, Object> operationInfoMap = instanceUpgrade.getOperationInfoMap();
                operationInfoMap.put("operationObjectClassification", "instance");
                operationInfoMap.put("operationObject", "instance");
                operationInfoMap.put("operationBehavior", "addinstance");
                return instanceUpgrade;
            }).collect(Collectors.toList())));

            // 建實例
            BaseResponse response = createAdpmAiopsInstance(spId, deviceId, aiopsItem, notExistList);
            if (!response.checkIsSuccess()) {
                TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                return response;
            }

            response = addDeviceProductAppRelate(sid, deviceId, notExistList);
            if (!response.checkIsSuccess()) {
                TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                return response;
            }
        }
        //来源存在，目标不存在的进行删除
        if (!CollectionUtils.isEmpty(existMap)) {
            List<String> appIdList = new ArrayList<>(existMap.size());
            List<Long> adpmIdList = existMap.values().stream().map(x -> {
                appIdList.add(x.getAppId());
                return x.getId();
            }).collect(Collectors.toList());
            map.put("adpmIdList", adpmIdList);
            //删除设备应用/服务关系实例
            BaseResponse response = deleteDeviceProductAppRelateInstance(sid, deviceId, existMap.values());
            if (!response.checkIsSuccess()) {
                TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                return response;
            }
            //删除产品，也要把设备所对应的运维实例的映射给移除
            response = removeDeviceAiopsInstanceMapping(deviceId, appIdList);
            if (!response.checkIsSuccess()) {
                TransactionAspectSupport.currentTransactionStatus();
                return response;
            }
            upgradeDcdpDetailList.addAll(getInstanceUpgradeToDcdpDetail(appIdList.stream().map(x -> {
                AiopsUpgradeDcdpRequest.InstanceUpgrade instanceUpgrade =
                        new AiopsUpgradeDcdpRequest.InstanceUpgrade(x);
                instanceUpgrade.setUpgradeType(UpgradeType.DELETE);
                instanceUpgrade.setNeedSaveOperationLog(true);
                Map<String, Object> operationInfoMap = instanceUpgrade.getOperationInfoMap();
                operationInfoMap.put("operationObjectClassification", "instance");
                operationInfoMap.put("operationObject", "instance");
                operationInfoMap.put("operationBehavior", "deleteinstance");
                return instanceUpgrade;
            }).collect(Collectors.toList())));
            deviceMapper.deleteProductAppForDevice(map);
        }
        //皆存在，进行运维实例更新
        if (!CollectionUtils.isEmpty(needUpdateList)) {
            deviceMapper.batchUpdateAiopsDeviceProductMapping(needUpdateList);
            upgradeDcdpDetailList.addAll(getInstanceUpgradeToDcdpDetail(needUpdateList.stream().map(x -> {
                AiopsUpgradeDcdpRequest.InstanceUpgrade instanceUpgrade =
                        new AiopsUpgradeDcdpRequest.InstanceUpgrade(x.getAppId());
                instanceUpgrade.setUpgradeType(UpgradeType.UPDATE);
                instanceUpgrade.getParamsMap().put("checkDeviceId", deviceId);
                instanceUpgrade.setNeedSaveOperationLog(true);
                Map<String, Object> operationInfoMap = instanceUpgrade.getOperationInfoMap();
                operationInfoMap.put("operationObjectClassification", "instance");
                operationInfoMap.put("operationObject", "instance");
                operationInfoMap.put("operationBehavior", "editinstance");
                return instanceUpgrade;
            }).collect(Collectors.toList())));

            // 檢查是否授權
            BaseResponse response = createAdpmAiopsInstance(spId, deviceId, aiopsItem, needUpdateList);
            if (!response.checkIsSuccess()) {
                TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                return response;
            }
        }
        BaseResponse response = executeUpgradeToDcdp(upgradeDcdpDetailList);
        if (!response.checkIsSuccess()) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return response;
        }
        //成功后把处理后的结果返回，使之能进行后面步骤
        return BaseResponse.ok(deviceProductMappingList);
    }

    private void setProcessDeviceProductMapping(long spId, long adId, String deviceId,
                                                List<DeviceProductMapping> deviceProductMappingList) {
        if (CollectionUtils.isEmpty(deviceProductMappingList)) {
            return;
        }
        deviceProductMappingList.stream().forEach(x -> {
            x.setSpId(spId);
            x.setAdId(adId);
            x.setDeviceId(deviceId);
            x.setModelCode(null);
            if (LongUtil.isEmpty(x.getId())) {
                x.setId(SNOWFLAKE.newId());
            }
            x.fixAppId();
        });
    }

    private BaseResponse createAdpmAiopsInstance(Long spId, String deviceId, String aiopsItem,
                                                 List<DeviceProductMapping> deviceProductMappingList) {
        AiopsKitDevice ad = deviceMapper.selectDeviceByDeviceId(deviceId);
        if (ad == null) {
            return BaseResponse.dynamicError(deviceId, ResponseCode.DEVICE_NOT_FOUND, deviceId);
        }
        Long eid = ad.getEid();
        Long sid = RequestUtil.getHeaderSidOrDefault(defaultSid);
        BaseResponse<Map<Long, String>> response = aioUserFeignClient.getProductCodeMapByIdList(sid,
                Stream.of(spId).collect(Collectors.toList()));
        if (!response.checkIsSuccess()) {
            return response;
        }
        Map<Long, String> spIdCodeMap = response.getData();
        String productCode;
        if (CollectionUtils.isEmpty(spIdCodeMap)) {
            productCode = "UNKNOWN_PRODUCT_CODE";
        } else {
            productCode = spIdCodeMap.get(spId);
        }
        BaseResponse<List<AiopsItemContext>> aicListResponse = createDeviceSelfInAicList(ad, null);
        if (!aicListResponse.checkIsSuccess()) {
            return aicListResponse;
        }
        List<AiopsItemContext> aicList = aicListResponse.getData();
        aicList.addAll(deviceProductMappingList.stream().map(deviceProductMapping -> {
//            deviceProductMapping.setProductCode(productCode);
//            AiopsItemContext aic = new AiopsItemContext(productCode, deviceProductMapping.getAppId(), deviceId);

            //10832【後端API】【数据服务】数据服务的报表模板可以在智管家呈现给客户
            String curAioPsItem;
            if (StringUtils.isNotBlank(aiopsItem)) {
                deviceProductMapping.setModelCode(aiopsItem);
                curAioPsItem = aiopsItem;
            } else {
                curAioPsItem = productCode;
            }
            deviceProductMapping.setProductCode(curAioPsItem);
            AiopsItemContext aic = new AiopsItemContext(curAioPsItem, deviceProductMapping.getAppId(), deviceId);
            aic.setIsAddInstanceCollectToDevice(false);

            // 記錄實例計算後的, 最終結果.
            deviceProductMapping.setAiopsItemContext(aic);
            return aic;
        }).collect(Collectors.toList()));

        // 處理 aiops_instance 實例
        BaseResponse res = authorizeService.checkAndCreateAiopsInstanceByAiopsItemContext(eid,
                null, aicList);
        if (!res.checkIsSuccess()) {
            return res;
        }
        return BaseResponse.ok();
    }

    private BaseResponse deleteDeviceProductAppRelateInstance(Long sid, String deviceId, Collection<DeviceProductMapping> deviceProductMappingList) {
        if (StringUtils.isBlank(deviceId) || CollectionUtils.isEmpty(deviceProductMappingList)) {
            return BaseResponse.ok();
        }
        List<ModelRelateInstance> modelRelateInstanceList = deviceProductMappingList.stream()
                .filter(x -> !LongUtil.isEmpty(x.getId()))
                .map(x -> createDeviceProductAppRelateInstance(sid, deviceId, x.getModelCode(), x.getId()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(modelRelateInstanceList)) {
            return BaseResponse.ok();
        }
//        return aioCmdbFeignClient.batchRemoveModelRelateInstanceByCondition(modelRelateInstanceList);
        return restUtil.batchRemoveModelRelateInstanceByCondition(modelRelateInstanceList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BaseResponse deleteProductAppForDevice(long sid, long spId, long adId) {
        Map<String, Object> map = new HashMap<>();
        map.put("spId", spId);
        map.put("adId", adId);
        //查询设备映射Id
        List<DeviceProductMapping> deviceProductMappingList = getProductAppForDeviceCore(map);
        String deviceId = deviceMapper.selectDeviceIdByAdId(adId);
        //删除设备所有应用/服务关系实例
        BaseResponse response = deleteDeviceProductAppRelateInstance(sid, deviceId, deviceProductMappingList);
        if (!response.checkIsSuccess()) {
            TransactionAspectSupport.currentTransactionStatus();
            return response;
        }
        return removeAdimAndAdpmByMap(deviceId, map);
    }

    private BaseResponse removeAdimAndAdpmByMap(String deviceId, Map<String, Object> map) {
        //map主要需要spId与adId
        if (CollectionUtils.isEmpty(map)) {
            return BaseResponse.ok();
        }
        List<String> appIdList = deviceMapper.selectAdpmAppIdListByMap(map);
        if (CollectionUtils.isEmpty(appIdList)) {
            //如果没有查到应用Id(可能是旧数据只处理特殊的收集项就好)
            //删除应用需要从设备上解绑的收集项
            map.put("isAppServiceUnbind", true);
            BaseResponse response = ruleEngineService.removeReResmRsByMap(map);
            if (!response.checkIsSuccess()) {
                TransactionAspectSupport.currentTransactionStatus();
                return response;
            }
            if (deviceMapper.deleteDeviceCollectConfigByMap(map)) {
                //刷新设备收集项缓存
                collectConfigService.clearDeviceIdCollectConfigCache(deviceId);
            }
            map.remove("isAppServiceUnbind");
        } else {
            //删除产品，也要把设备所对应的运维实例的映射给移除
            BaseResponse response = removeDeviceAiopsInstanceMapping(deviceId, appIdList);
            if (!response.checkIsSuccess()) {
                TransactionAspectSupport.currentTransactionStatus();
                return response;
            }
        }
        return BaseResponse.ok(deviceMapper.deleteProductAppForDevice(map));
    }

    @Override
    public BaseResponse getCollectConfigsNotInDevice(long sid, String deviceId, String scopeId, String collectItem) {
        Map<String, Object> map = new HashMap<>();
        //本来是通过deviceId去排除已选的收集项的，现在去掉
        map.put("deviceId", deviceId);
//        map.put("scopeId", scopeId);
        map.put("collectItem", collectItem);
        DeviceInfo deviceInfo = getDeviceDetailByDeviceId(sid, deviceId);

        //设备的平台要包含在收集项的平台里面
        if (deviceInfo != null && !StringUtils.isEmpty(deviceInfo.getPlatform().toString())) {
            map.put("devicePlatform", deviceInfo.getPlatform().toString());
        }
        //设备的设备类型要包含收集项的收集项设备类型
        if (deviceInfo != null && !CollectionUtils.isEmpty(deviceInfo.getDeviceTypeMappingList())) {
            map.put("deviceType", deviceInfo.getDeviceTypeMappingList().stream().map(o -> o.getDeviceType()).collect(Collectors.toList()));
        }
        //huly: 修复漏洞/bug 增加if 判断
        if (deviceInfo != null) {
            map.put("scopeId", "('" + deviceId + "','" + DEFAULT_SCOPE_ID + "','" + deviceInfo.getEid() + "')");
        }
        //查询租户有效的运维商运维模组类别明细Id列表，后面做收集项过滤
        Long eid = deviceMapper.selectEidByDeviceId(deviceId);
        if (!LongUtil.isEmpty(eid)) {
            BaseResponse<List<Long>> response = getValidSamcdIdListCore(sid, eid,
                    new ArrayList<>(0));
            if (!response.checkIsSuccess()) {
                return response;
            }
            map.put("validSamcdIdList", response.getData());
        }
        return BaseResponse.ok(deviceMapper.getCollectConfigsNotInDevice(map));
    }

    @Override
    public BaseResponse addCollectDetailForDevice(long sid, long adcdId, long adId, String deviceId, String collectName,
                                                  long accId, String scopeId) {
        //设备收集项单头
        DeviceCollect deviceCollect = dealDeviceCollect(adId, deviceId);

        Long aiId;
        Long adimId;
        String execParamsModelCode = collectConfigMapper.selectCollectConfigExecParamsModelCode(accId);
        if (StringUtils.isBlank(execParamsModelCode)) {
            //执行参数模型代号为空，表示是设备本身的运维实例
            Map<String, Object> map = getDeviceAiIdAndAdimId(deviceId);
            aiId = LongUtil.objectToLong(map.get("aiId"));
            adimId = LongUtil.objectToLong(map.get("adimId"));
        } else {
            aiId = null;
            adimId = null;
        }
        //验证collectName+deviceId 是否唯一
        //名称检查需要包含运维实例(即同运维实例，收集项名称不可以相同，不同则可以相同)
        //若没有取到运维实例Id，暂时检查所有的
        int existCollectNameCount = deviceMapper.getDeviceCollectName(deviceCollect.getId(), accId, aiId, collectName);
        if (existCollectNameCount > 0) {
            return BaseResponse.error(ResponseCode.COLLECTNAME_IS_EXIST);
        }
        DeviceCollectDetail deviceCollectDetail = createNewAdcd(deviceCollect.getId(), accId,
                aiId, adimId, collectName, true);

        //设备收集项单身
        boolean res = deviceMapper.addCollectDetailForDevice(deviceCollectDetail);
        BaseResponse response = executeUpgradeToDcdp(getDeviceUpgradeToDcdpDetail(Stream.of(deviceCollectDetail)
                .map(x -> {
                    AiopsUpgradeDcdpRequest.DeviceUpgrade deviceUpgrade = new AiopsUpgradeDcdpRequest.DeviceUpgrade(
                            deviceId, DeviceUpgradeEnum.DEVICE_COLLECT_DETAIL);
                    deviceUpgrade.getOtherConditionMap().put("adcd.id", x.getId());
                    deviceUpgrade.setNeedSaveOperationLog(true);
                    Map<String, Object> operationInfoMap = deviceUpgrade.getOperationInfoMap();
                    operationInfoMap.put("operationObjectClassification", "device");
                    operationInfoMap.put("operationObject", "device");
                    operationInfoMap.put("operationBehavior", "adddevicecollectconfig");
                    return deviceUpgrade;
                }).collect(Collectors.toList())));
        if (!response.checkIsSuccess()) {
            return response;
        }

        //设备收集项的预警项
        // 新设备接入

        BaseResponse<List<CollectWarning>> warningSetResponse =
                collectWarningService.getCollectWarningSetList(accId, scopeId);
        if (warningSetResponse.checkIsSuccess()) {
            List<CollectWarning> acwList = getCollectWarningList(deviceId, warningSetResponse.getData());
            batchDeviceCollectWarning(deviceCollectDetail.getId(), acwList, null, deviceId);
        }

        //清理设备缓存
        collectConfigService.clearDeviceIdCollectConfigCache(deviceId);
        return BaseResponse.ok(res);
    }

    private DeviceCollectDetail createNewAdcd(Long adcId, Long accId, Long aiId, Long adimId,
                                              String collectName, Boolean isEnable) {
        DeviceCollectDetail adcd = new DeviceCollectDetail();
        adcd.setId(SNOWFLAKE.newId());
        adcd.setAdcId(adcId);
        adcd.setAccId(accId);
        adcd.setCollectName(collectName);
        adcd.setIsEnable(BooleanUtils.toBoolean(isEnable));
        adcd.setAiId(aiId);
        adcd.setAdimId(adimId);
        return adcd;
    }

    private Map<String, Object> getDeviceAiIdAndAdimId(String deviceId) {
        //返回字典关键运维实例Id(aiId)与设备运维实例映射Id(adimId)
        List<Map<String, Object>> mapList = deviceMapper.selectDeviceInstanceMappingByDeviceId(deviceId);
        if (!CollectionUtils.isEmpty(mapList)) {
            Optional<Map<String, Object>> optMap = mapList.stream().filter(x -> x != null).findFirst();
            if (optMap.isPresent()) {
                return optMap.get();
            }
        }
        //TODO:实例理论上应该会存在，如果不存在属于数据异常
        return new HashMap<>(0);
    }

    @Override
    public BaseResponse updateCollectDetailForDevice(long adcdId, String collectName, String deviceId) {
        //region 参数检查

        Optional<BaseResponse> optResponse = checkParamIsEmpty(adcdId, "adcdId");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        optResponse = checkParamIsEmpty(collectName, "collectName");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        optResponse = checkParamIsEmpty(deviceId, "deviceId");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        //检查收集项名称在同设备上是否已重复了
        //名称检查需要包含运维实例(即同运维实例，收集项名称不可以相同，不同则可以相同)
        //若没有取到运维实例Id，暂时检查所有的
        Map<String, Object> map = new HashMap<>();
        map.put("id", adcdId);
        map.put("collectName", collectName);
        map.put("deviceId", deviceId);
        if (deviceMapper.selectCollectNameExistInDeviceCollectDetail(map)) {
            return BaseResponse.error(ResponseCode.COLLECTNAME_IS_EXIST);
        }

        //endregion

        //更新
        map = new HashMap<>();
        map.put("id", adcdId);
        map.put("collectName", collectName);
        deviceMapper.updateCollectDetailForDevice(map);

        //清理设备缓存
        collectConfigService.clearDeviceIdCollectConfigCache(deviceId);
        return BaseResponse.ok();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BaseResponse deleteCollectDetailForDevice(long adcdId, long accId, String deviceId, String scopeId,
                                                     List<CollectWarning> collectWarningList) {
        //region 参数检查

        Optional<BaseResponse> optResponse = checkParamIsEmpty(deviceId, "deviceId");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        optResponse = checkParamIsEmpty(scopeId, "scopeId");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        //endregion

        //1 如果是自定义收集项，需要删除自定义收集项的预警项相关的设定
        if (!CollectionUtils.isEmpty(collectWarningList)) {
            //删除自定义收集项预警设定
            optResponse = collectWarningList.stream()
                    .filter(x -> x.getScopeId().equals(deviceId))
                    .map(x -> collectWarningService.deleteCollectWarning(x.getId(), x.getScopeId(), true))
                    .filter(x -> !x.checkIsSuccess()).findFirst();
            if (optResponse.isPresent()) {
                return optResponse.get();
            }
        }
        if (!DEFAULT_SCOPE_ID.equals(scopeId)) {
            //删除自定义收集项
            BaseResponse response = collectConfigService.deleteCollectConfig(accId, scopeId, true);
            if (!response.checkIsSuccess()) {
                return response;
            }
        }

        Map<String, Object> map = new HashMap<>();
        map.put("adcdId", adcdId);
        map.put("accId", accId);
        //2.删预警映射
        deviceMapper.deleteCollectWarningForDevice(map);
        //3.删收集项映射
        executeUpgradeToDcdp(getDeviceUpgradeToDcdpDetail(Stream.of(adcdId).map(x -> {
            AiopsUpgradeDcdpRequest.DeviceUpgrade deviceUpgrade =
                    new AiopsUpgradeDcdpRequest.DeviceUpgrade(deviceId, DeviceUpgradeEnum.DEVICE_COLLECT_DETAIL);
            deviceUpgrade.setUpgradeType(UpgradeType.DELETE);
            deviceUpgrade.getOtherConditionMap().put("adcd.id", x);
            deviceUpgrade.setNeedSaveOperationLog(true);
            Map<String, Object> operationInfoMap = deviceUpgrade.getOperationInfoMap();
            operationInfoMap.put("operationObjectClassification", "device_collect_detail");
            operationInfoMap.put("operationObject", "device_collect_detail");
            operationInfoMap.put("operationBehavior", "deletedevicecollectdetail");
            return deviceUpgrade;
        }).collect(Collectors.toList())));
        deviceMapper.deleteCollectDetailForDevice(adcdId);
        //4.删除规则引擎
        ruleEngineService.removeRuleEngineByAdcdId(adcdId, deviceId, accId);
        //5.清理设备缓存
        collectConfigService.clearDeviceIdCollectConfigCache(deviceId);
        return BaseResponse.ok(true);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BaseResponse execParamsSetForDevice(Long eid, boolean changeInterval, long adId, String deviceId,
                                               DeviceCollectDetail deviceCollectDetail) {
        //region 参数检查

        Optional<BaseResponse> optResponse = checkParamIsEmpty(deviceId, "deviceId");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        optResponse = checkParamIsEmpty(deviceCollectDetail, "deviceCollectDetail");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        String scopeId = deviceCollectDetail.getScopeId();
        optResponse = checkParamIsEmpty(scopeId, "deviceCollectDetail.scopeId");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        //endregion

        if (LongUtil.isEmpty(eid)) {
            eid = getEidByDeviceId(deviceId);
        }
        //1.changeInterval = true 表示前端修改了时间，这时候判断是否需要新增自定义收集项，如果已经是自定义收集项，就不用新增；
        if (changeInterval) {
            Long accId = deviceCollectDetail.getAccId();
            //内置收集项，不允许自定义
            if (innerRunnerAccIdSet.contains(accId)) {
                return BaseResponse.error(ResponseCode.INNER_RUNNER_UNSUPPORTED_CUSTOMIZE);
            }
            Long newAccId;
            //判断当前插件是否是自定义
            if (DEFAULT_SCOPE_ID.equals(scopeId)) {
                //需要新增自定义插件
                CollectConfig defaultCollectConfig = collectConfigMapper.getCollectConfigDetail(accId);

                CollectConfig collectConfig = new CollectConfig();
                BeanUtils.copyProperties(defaultCollectConfig, collectConfig);
                collectConfig.setScopeId(deviceId);
                collectConfig.setInterval(deviceCollectDetail.getInterval());
                collectConfig.setSourceAccId(accId);
                //保存设备的自定义收集项
                BaseResponse<CollectConfigSavedResponse> baseResponse =
                        collectConfigService.insertCollectConfig(collectConfig);
                if (!baseResponse.checkIsSuccess()) {
                    return baseResponse;
                }
                //将设备与老收集项、预警项的关联全部更新到新自定义收集项中
                //获取老的设备收集项
                long adcdId = deviceCollectDetail.getId();
                DeviceCollectDetail existAdcd = deviceMapper.getDeviceCollectDetail(adId, adcdId);
                if (Objects.isNull(existAdcd)) {
                    TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                    return BaseResponse.dynamicError(ResponseCode.AIOPS_DEVICE_COLLECT_DETAIL_ID_NOT_EXIST, adcdId);
                }
                newAccId = collectConfig.getId();
                existAdcd.setAccId(newAccId);

                //更新CollectorContent中_cron内容
                updateCollectConfigCollectorContent(newAccId, deviceCollectDetail.getCron());

                //更新设备收集项，将老的accId 换成新的accId
                deviceMapper.updateDeviceCollectDetail(accId, newAccId, adcdId);

                //將規則引擎的accId換成新的accId
                //TODO:目前因为考虑效能，因此没有替换additionalContent里面的accId，如果将来真的需要，在考虑做替换
                BaseResponse response = ruleEngineService.modifyReAccIdByAdcdId(adcdId, accId, newAccId);
                if (!response.checkIsSuccess()) {
                    TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                    return response;
                }

                //处理预警
                List<CollectWarning> collectWarningList = deviceCollectDetail.getCollectWarningList();
                if (!CollectionUtils.isEmpty(collectWarningList)) {
                    Optional<BaseResponse<Long>> optReturnResponse = collectWarningList.stream().map(x -> {
                        //如果已经是自定义预警，就只需更新新的accId 和awcId之间的关系aiops_collect_warning,，如果是默认预警，需要自定义预警
                        String collectWarningScopeId = x.getScopeId();
                        if (!StringUtils.isEmpty(collectWarningScopeId) && x.getScopeId().equals(deviceId)) {
                            deviceMapper.updateCollectWarning(accId, newAccId, x.getId());
                        } else {
                            long oldAcwId = x.getId();
                            x.setAccId(collectConfig.getId());
                            x.setScopeId(deviceId);
                            x.setSourceAcwId(oldAcwId);
                            BaseResponse<Long> baseResponse2 =
                                    collectWarningService.addCollectWarning(x, true);
                            if (!baseResponse2.checkIsSuccess()) {
                                return baseResponse2;
                            }
                            Long newAcwId = baseResponse2.getData();
                            if (!LongUtil.isEmpty(newAcwId)) {
                                //更新aiops_device_collect_warning表，将老的acwId换成新的acwId
                                deviceMapper.updateDeviceCollectWarning(oldAcwId, newAcwId, adcdId, x.isWarningEnable());
                                //改变规则映射
                                ruleEngineService.modifyResmRsIdByAdcdIdOldAcwId(adcdId, deviceId, accId,
                                        oldAcwId, newAcwId);
                            }
                        }
                        return null;
                    }).filter(Objects::nonNull).findFirst();
                    if (optReturnResponse.isPresent()) {
                        TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                        return optReturnResponse.get();
                    }
                }
            } else {
                newAccId = deviceCollectDetail.getAccId();
                //下面表示当前的收集项已经是自定义的了
                if (deviceCollectDetail.isSettingCron()) {
                    //更新CollectorContent中_cron内容
                    updateCollectConfigCollectorContent(newAccId, deviceCollectDetail.getCron());
                } else {
                    //只需要更新下时间就行
                    deviceMapper.updateDeviceCollectInterval(deviceCollectDetail);
                }
            }
            //清除acc缓存
            collectConfigService.clearCollectConfigCache(newAccId, false);

            //自定义收集项升级到数采
            AiopsUpgradeDcdpAccRequest request = new AiopsUpgradeDcdpAccRequest();
            request.getAccIdList().add(newAccId);
            request.setScopeId(deviceId);
            request.setTargetSameDbgAccId(accId);
            request.setTargetEid(eid);
            BaseResponse upgradeAccRes = upgradeService.upgradeAccToDcdp(request);
            if (!upgradeAccRes.checkIsSuccess()) {
                TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                return upgradeAccRes;
            }
        }

        //更新执行参数、执行开关等
        deviceMapper.updateDeviceCollectDetailEnable(deviceCollectDetail);
        //更新运维实例相关Id
        BaseResponse response = processDeviceCollectDetailAiIdAndAdimId(adId, deviceId, deviceCollectDetail);
        if (!response.checkIsSuccess()) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return response;
        }
        //更新设备收集项执行参数
        collectConfigService.modifyExecParamsByFullContent(deviceCollectDetail.getExecParamsContent(),
                deviceCollectDetail.getId(), true, eid, true,
                deviceCollectDetail.getExecParamsModelCode());
        //清除缓存
        collectConfigService.clearDeviceIdCollectConfigCache(deviceId);
        return BaseResponse.ok();
    }

    private void updateCollectConfigCollectorContent(long accId, String cron) {
        if (StringUtils.isEmpty(cron)) {
            return;
        }
        String collectorContent = deviceMapper.selectCollectConfigCollectorContent(accId);

        if (StringUtils.isNotEmpty(collectorContent)) {
            ObjectMapper objectMapper = new ObjectMapper();
            try {
                Map<String, Object> map = objectMapper.readValue(collectorContent, LinkedHashMap.class);
                for (Map.Entry<String, Object> entry : map.entrySet()) {
                    if (entry.getKey().endsWith("_cron")) {
                        map.put(entry.getKey(), cron);
                    }
                }
                collectorContent = JSON.toJSONString(map);
                log.info("[updateCollectConfigCollectorContent],update content：{}", collectorContent);
                deviceMapper.updateCollectConfigCollectorContent(accId, collectorContent, cron);
            } catch (JsonProcessingException e) {
                log.error("updateCollectConfigCollectorContent accId:{} exception", accId, e);
            }
        }
    }

    private BaseResponse processDeviceCollectDetailAiIdAndAdimId(Long adId, String deviceId,
                                                                 DeviceCollectDetail deviceCollectDetail) {
        if (deviceCollectDetail == null) {
            return BaseResponse.ok();
        }
        //考虑到异动的可能性，因此每次都进行设置
        String aiopsItemId = deviceCollectDetail.getAiopsItemId();
        if (StringUtils.isBlank(aiopsItemId)) {
            //如果没有指定运维项目Id，实例相关Id就设置设备本身的
            Map<String, Object> map = getDeviceAiIdAndAdimId(deviceId);
            deviceCollectDetail.setAiId(LongUtil.objectToLong(map.get("aiId")));
            deviceCollectDetail.setAdimId(LongUtil.objectToLong(map.get("adimId")));
            deviceMapper.updateDeviceCollectDetailAiIdAndAdimId(deviceCollectDetail);
            executeUpgradeToDcdp(getDeviceUpgradeToDcdpDetail(Stream.of(deviceCollectDetail.getId()).map(x -> {
                AiopsUpgradeDcdpRequest.DeviceUpgrade deviceUpgrade =
                        new AiopsUpgradeDcdpRequest.DeviceUpgrade(deviceId, DeviceUpgradeEnum.DEVICE_COLLECT_DETAIL);
                deviceUpgrade.setUpgradeType(UpgradeType.UPDATE);
                deviceUpgrade.getOtherConditionMap().put("adcd.id", x);
                deviceUpgrade.setNeedSaveOperationLog(true);
                Map<String, Object> operationInfoMap = deviceUpgrade.getOperationInfoMap();
                operationInfoMap.put("operationObjectClassification", "device_collect_detail");
                operationInfoMap.put("operationObject", "device_collect_detail");
                operationInfoMap.put("operationBehavior", "editdevicecollectdetailinstance");
                return deviceUpgrade;
            }).collect(Collectors.toList())));
            //实例改变须更新规则引擎
            return ruleEngineService.modifyRuleEngineByAdcdId(deviceCollectDetail.getId());
        }
        String oriAiopsItemId = deviceCollectDetail.getOriAiopsItemId();
        if (StringUtils.isNotBlank(oriAiopsItemId)) {
            //透过原始实例Id去检查是否满足可切换实例规则
            BaseResponse<AiopsInstance> response = instanceService.getAiopsInstanceByAiopsItemId(oriAiopsItemId);
            if (!response.checkIsSuccess()) {
                return response;
            }
            AiopsInstance ai = response.getData();
            if (ai != null && !LongUtil.isEmpty(ai.getId())) {
                //实例存在检查一下，当前实例是否包含执行参数模型
                /** 如果运维实例本身有执行参数模型，才允许收集项修改实例，反之则不允许修改实例
                 场景1：由数据库A切换到数据库B
                 因为数据库实例会设定DbExecParams执行参数模型，因此实例会进行切换
                 场景2：由SNMP_A切换到SNMP_B
                 因为SNMP实例有设定SNMPExecParams执行参数模型，因此实例会进行切换
                 场景3：由备份排程A切到备份排程B
                 虽然备份排程有设定BackupSoftwareExecParam执行参数模型，但是因为其设定是走模型渲染，理应不会产生运维实例Id，因此不会进行切换
                 场景4：由产品A切换到产品B
                 因为产品没有设定执行参数模型，因此实例不会进行切换
                 */
                if (StringUtils.isBlank(ai.getExecParamsModelCode())) {
                    //如果不包含不允许切换实例
                    return BaseResponse.ok();
                }
            }
        }
        Long adcdId = deviceCollectDetail.getId();
        AiopsItemContext aic = new AiopsItemContext(deviceCollectDetail.getAiopsItem(), aiopsItemId, deviceId);
        aic.setIsAddInstanceCollectToDevice(false);
        //执行参数相关内容，在后面的流程会处理，因此不交由实例去处理
        //aic.setNeedUpdateDeviceCollectExecParamsContent(true);
        //aic.setOriExecParamsContent(deviceCollectDetail.getExecParamsContent());
        //aic.setAdcdId(adcdId);
        BaseResponse addRelateResponse = addDeviceAiopsInstanceMapping(aic, true);
        if (!addRelateResponse.checkIsSuccess()) {
            return addRelateResponse;
        }

//        Long dimId = deviceMapper.selectAdimIdByDeviceIdAndAiId(deviceId, aiId);
//        if (LongUtil.isEmpty(dimId)) {
//            //创建设备实例映射
//            DeviceInstanceMapping dim = new DeviceInstanceMapping(adId, deviceId, aiId);
//            deviceMapper.batchInsertAiopsDeviceInstanceMapping(Stream.of(dim).collect(Collectors.toList()));
//            dimId = dim.getId();
//        }
        deviceCollectDetail.setAiId(aic.getAiId());
        deviceCollectDetail.setAdimId(aic.getAdimId());
        deviceMapper.updateDeviceCollectDetailAiIdAndAdimId(deviceCollectDetail);
        executeUpgradeToDcdp(getDeviceUpgradeToDcdpDetail(Stream.of(deviceCollectDetail.getId()).map(x -> {
            AiopsUpgradeDcdpRequest.DeviceUpgrade deviceUpgrade =
                    new AiopsUpgradeDcdpRequest.DeviceUpgrade(deviceId, DeviceUpgradeEnum.DEVICE_COLLECT_DETAIL);
            deviceUpgrade.setUpgradeType(UpgradeType.UPDATE);
            deviceUpgrade.getOtherConditionMap().put("adcd.id", x);
            deviceUpgrade.setNeedSaveOperationLog(true);
            Map<String, Object> operationInfoMap = deviceUpgrade.getOperationInfoMap();
            operationInfoMap.put("operationObjectClassification", "device_collect_detail");
            operationInfoMap.put("operationObject", "device_collect_detail");
            operationInfoMap.put("operationBehavior", "editdevicecollectdetailinstance");
            return deviceUpgrade;
        }).collect(Collectors.toList())));
        //实例改变须更新规则引擎
        return ruleEngineService.modifyRuleEngineByAdcdId(adcdId);
    }

    @Override
    public BaseResponse customizeAcc(Map<String, Object> accMap, Long adcdId, String deviceId) {
        //region 参数检查

        Optional<BaseResponse> optResponse = checkParamIsEmpty(accMap, "accMap");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        optResponse = checkParamIsEmpty(adcdId, "adcdId");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        optResponse = checkParamIsEmpty(deviceId, "deviceId");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        //endregion

        //只有首次自定义会触发
        Long accId = LongUtil.objectToLong(accMap.get("id"));
        Long rootSourceAccId = LongUtil.objectToLong(accMap.get("rootSourceAccId"));
        Map<String, Object> map = new HashMap<>();
        map.put("accId", rootSourceAccId);
        map.put("specialColumns", new String[]{"id", "collectCategory", "collectDeviceType"});
        List<CollectConfig> accList = collectConfigMapper.selectAccByMap(map);
        if (CollectionUtil.isNotEmpty(accList)) {
            CollectConfig acc = accList.stream().filter(Objects::nonNull).findFirst().orElse(null);
            if (Objects.nonNull(acc)) {
                //填充基本信息
                accMap.put("collectCategory", acc.getCollectCategory());
                accMap.put("collectDeviceType", acc.getCollectDeviceType());
            }
        }
        String collectType = Objects.toString(accMap.get("collectType"), "");
        if (StringUtils.isNotBlank(collectType)) {
            accMap.put("collectType", CollectType.valueOf(collectType));
        }
        //企业运维不管租户得来源，需换为根源accId
        accMap.put("sourceAccId", rootSourceAccId);

        BaseResponse response = collectConfigService.batchSaveByV2Acc(Stream.of(accMap).collect(Collectors.toList()));
        if (!response.checkIsSuccess()) {
            return response;
        }
        //更新设备收集项的accId
        deviceMapper.updateDeviceCollectDetail(rootSourceAccId, accId, adcdId);

        //TODO:执行参数暂时不做处理
        //实例的执行参数一般是挂到设备上时确定，不会随着收集项异动而异动
        //设备基础的执行参数，一般根据收集项设定来，但这边会是v2的收集项，不由企业运维管理
        //尽管后面将v2变成了v1，那也需要手动去维护执行参数

        //清除缓存
        collectConfigService.clearDeviceIdCollectConfigCache(deviceId);
        return response;
    }

    @Override
    public BaseResponse modifyCollectDetailEnableForDevice(long adcdId, boolean isEnable, String deviceId) {
        Map<String, Object> map = new HashMap<>();
        map.put("adcdId", adcdId);
        map.put("isEnable", isEnable);

        boolean result = deviceMapper.modifyCollectDetailEnableForDevice(map);
        if (result) {
            BaseResponse response = executeUpgradeToDcdp(getDeviceUpgradeToDcdpDetail(Stream.of(adcdId).map(x -> {
                AiopsUpgradeDcdpRequest.DeviceUpgrade deviceUpgrade =
                        new AiopsUpgradeDcdpRequest.DeviceUpgrade(deviceId, DeviceUpgradeEnum.DEVICE_COLLECT_DETAIL);
                deviceUpgrade.setUpgradeType(UpgradeType.UPDATE);
                deviceUpgrade.getOtherConditionMap().put("adcd.id", x);
                deviceUpgrade.getParamsMap().put("onlyUpdateEnable", true);
                deviceUpgrade.setNeedSaveOperationLog(true);
                Map<String, Object> operationInfoMap = deviceUpgrade.getOperationInfoMap();
                operationInfoMap.put("operationObjectClassification", "device_collect_detail");
                operationInfoMap.put("operationObject", "device_collect_detail");
                if (StringUtils.isBlank(Objects.toString(operationInfoMap.get("operationBehavior"), ""))) {
                    if (isEnable) {
                        operationInfoMap.put("operationBehavior", "enabledevicecollectdetail");
                    } else {
                        operationInfoMap.put("operationBehavior", "disabledevicecollectdetail");
                    }
                }
                return deviceUpgrade;
            }).collect(Collectors.toList())));
            if (!response.checkIsSuccess()) {
                return response;
            }
            //清理设备缓存
            collectConfigService.clearDeviceIdCollectConfigCache(deviceId);
        }

        return BaseResponse.ok(result);
    }

    @Override
    public BaseResponse modifyCollectConfigWarningEnableForDevice(long adcdId, boolean isWarningEnable,
                                                                  List<CollectWarning> collectWarningList) {
        /*Map<String, Object> map = new HashMap<>();
        map.put("adcdId", adcdId);
        map.put("isWarningEnable", isWarningEnable);
        //先更新设备关联的预警项里面的设定
        deviceMapper.modifyCollectConfigWarningEnableForDevice(map);
        //*/
        if (CollectionUtils.isEmpty(collectWarningList)) {
            return BaseResponse.ok(0);
        }
        Optional<BaseResponse> optResponse = collectWarningList.stream().map(x ->
                //异动规则引擎
                modifyWarningEnableForDeviceWarning(adcdId, x.getId(), isWarningEnable)
        ).filter(x -> !x.checkIsSuccess()).findFirst();
        return optResponse.orElseGet(() -> BaseResponse.ok(collectWarningList.size()));
    }

    @Override
    public BaseResponse modifyWarningEnableForDeviceWarning(long adcdId, long acwId, boolean isWarningEnable) {

        Map<String, Object> map = new HashMap<>();
        map.put("adcdId", adcdId);
        map.put("acwId", acwId);
        //更新设备收集项表
        map.put("isWarningEnable", isWarningEnable);
        boolean res = deviceMapper.modifyWarningEnableForDeviceWarning(map);
        //异动规则引擎
        BaseResponse response = ruleEngineService.modifyResmEnableByAdcdIdAcwId(adcdId, acwId, isWarningEnable);
        if (!response.checkIsSuccess()) {
            return response;
        }
        return BaseResponse.ok(res);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BaseResponse modifyWarningEnableForDeviceWarningBatch(Long acwId, boolean isWarningEnable, Long eid,
                                                                 List<Long> adcwIdList) {
        if (CollectionUtils.isEmpty(adcwIdList)) {
            return BaseResponse.dynamicError(ResponseCode.INTERNAL_ERROR, "device collect item details is empty");
        }
        // 去重处理
        List<Long> distinctAdcwIds = adcwIdList.stream()
                .distinct()
                .collect(Collectors.toList());

//        // 查询设备信息
//        List<Map<String, Object>> mapList = deviceV2Mapper.selectDeviceIdAndAccIdByAdcdIdBatch(distinctAdcdIds);
//        mapList.forEach(item -> {
//            // 安全获取adcdId和adcwId，处理可能的null值
//            Long adcdId = Optional.ofNullable(item.get("adcdId"))
//                    .map(LongUtil::objectToLong)
//                    .orElse(null);
//            Long dbAcwId = Optional.ofNullable(item.get("acwId"))
//                    .map(LongUtil::objectToLong)
//                    .orElse(null);
//
//            // 跳过无效条目
//            if (adcdId == null || dbAcwId == null) {
//                log.warn("无效数据项，缺失adcdId或dbAcwId: {}", item);
//                return;
//            }
//
//            Map<String, Object> map = new HashMap<>();
//            map.put("adcdId", adcdId);
//            map.put("acwId", dbAcwId);
//            //更新设备收集项表
//            map.put("isWarningEnable", isWarningEnable);
//            deviceMapper.modifyWarningEnableForDeviceWarning(map);
//        });

        boolean res = deviceMapper.modifyWarningEnableForDeviceWarningBatch(isWarningEnable, distinctAdcwIds);
        //异动规则引擎
        BaseResponse response = ruleEngineService.modifyResmEnableByAdcdIdAcwIdBatch(acwId, isWarningEnable, eid, distinctAdcwIds);
        if (!response.checkIsSuccess()) {
            return response;
        }
        return BaseResponse.ok(res);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BaseResponse modifyWarningSetForDevice(long adcdId, long acwId, String deviceId,
                                                  CollectWarning collectWarning) {
        Map<String, Object> map = new HashMap<>();
        map.put("adcdId", adcdId);
        map.put("acwId", acwId);
        BaseResponse<Long> baseResponse;
        AiopsKitDevice aiopsKitDevice = deviceV2Mapper.selectDeviceByDeviceId(deviceId);
        //判断是否是自定义
        boolean isCustom;
        if (Objects.nonNull(aiopsKitDevice)) {
            isCustom = !LongUtil.safeToString(aiopsKitDevice.getEid()).equals(collectWarning.getScopeId()) && !DEFAULT_SCOPE_ID.equals(collectWarning.getScopeId());
        }else {
            isCustom = !DEFAULT_SCOPE_ID.equals(collectWarning.getScopeId());
        }
        //如果已经是自定义预警项，直接更新预警项设定，否则自定义预警项
        if (isCustom) {

            //已经自定义直接更新预警项设定
            baseResponse = collectWarningService.modifyCollectWarning(collectWarning, true);
            if (!baseResponse.checkIsSuccess()) {
                return baseResponse;
            }
            Long newAcwId = baseResponse.getData();
            //多串查adcwId做返回(理论上一定会存在)
            List<Long> adcwIdList = deviceMapper.selectAdcwIdByMap(map);
            Map<String, Object> resultMap = new HashMap<>(2);
            resultMap.put("acwId", newAcwId);
            long adcwId;
            if (CollectionUtils.isEmpty(adcwIdList)) {
                adcwId = 0L;
            } else {
                adcwId = adcwIdList.stream().filter(LongUtil::isNotEmpty).findFirst().orElse(0L);
            }
            resultMap.put("adcwId", adcwId);
            return BaseResponse.ok(resultMap);
        }
        //需要自定义
        collectWarning.setScopeId(deviceId);
        collectWarning.setSourceAcwId(collectWarning.getId());
        baseResponse = collectWarningService.addCollectWarning(collectWarning, true);
        if (!baseResponse.checkIsSuccess()) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return baseResponse;
        }
        Long newAcwId = baseResponse.getData();
        //更新aiops_device_collect_warning表，将老的acwId换成新的acwId
        //先查有没有历史的预警记录，没有就新增，有就更新
        boolean isWarningEnable = BooleanUtils.toBoolean(collectWarning.isWarningEnable());
        Long adcwId;
        Long accId = collectWarning.getAccId();
        List<Long> adcwIdList = deviceMapper.selectAdcwIdByMap(map);
        if (CollectionUtils.isEmpty(adcwIdList)) {
            DeviceCollectWarning adcw = new DeviceCollectWarning(adcdId, newAcwId, isWarningEnable);
            adcwId = adcw.getId();
            deviceMapper.insertDeviceCollectWarning(adcw);
        } else {
            adcwId = adcwIdList.stream().filter(LongUtil::isNotEmpty).findFirst().orElse(0L);
            deviceMapper.updateDeviceCollectWarning(acwId, newAcwId, adcdId, isWarningEnable);
            //改变规则映射
            BaseResponse response = ruleEngineService.modifyResmRsIdByAdcdIdOldAcwId(adcdId, deviceId, accId,
                    acwId, newAcwId);
            if (!response.checkIsSuccess()) {
                TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                return response;
            }
        }
        //保存规则引擎设定映射
        BaseResponse response = ruleEngineService.saveResmByAdcdIdAcwIdEnable(adcdId, deviceId, accId,
                newAcwId, adcwId, isWarningEnable);
        if (!response.checkIsSuccess()) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return response;
        }
        Map<String, Object> resultMap = new HashMap<>(2);
        resultMap.put("acwId", newAcwId);
        resultMap.put("adcwId", adcwId);
        return BaseResponse.ok(resultMap);
    }

    @Override
    public BaseResponse batchModifyWarningSetForDevice(BatchModifyWarningSetForDeviceRequest request) {
        TransactionStatus transactionStatus =
                platformTransactionManager.getTransaction(TransactionDefinition.withDefaults());


        CollectWarning collectWarning = request.getCollectWarning();
        BaseResponse baseResponse = checkWarning(collectWarning);
        if (!baseResponse.checkIsSuccess()) {
            return baseResponse;
        }

        List<DeviceIdAdcdIdMapping> deviceIdAdcdIdMappingList = new ArrayList<>(request.getDeviceIdAdcdIdMappingList());
        Long oldAcwId = request.getAcwId();
        String warningCode = collectWarning.getWarningCode();
        boolean isWarningEnable = collectWarning.isWarningEnable();
        Long accId = collectWarning.getAccId();
        Long sourceAcwId;
        // 全新自定义设备预警设定
        sourceAcwId = oldAcwId;
        List<String> deviceIdList = deviceIdAdcdIdMappingList.stream()
                .map(DeviceIdAdcdIdMapping::getDeviceId)
                .collect(Collectors.toList());

        List<DeviceIdAdcdIdMapping> deviceAcwMappingList = collectWarningMapper.selectAcwByDeviceIdList(warningCode, deviceIdList);
        Map<String, List<Long>> deviceIdMap = deviceAcwMappingList
                .stream().collect(Collectors.groupingBy(DeviceIdAdcdIdMapping::getDeviceId, Collectors.mapping(DeviceIdAdcdIdMapping::getAcwId, Collectors.toList())));
        List<DeviceIdAdcdIdMapping> existAcwDeviceIdAdcdIdMappingList = new ArrayList<>();
        List<DeviceIdAdcdIdMapping> notExistAcwDeviceIdAdcdIdMappingList = new ArrayList<>();
        for (DeviceIdAdcdIdMapping deviceIdAdcdIdMapping : deviceIdAdcdIdMappingList) {
            if (deviceIdMap.containsKey(deviceIdAdcdIdMapping.getDeviceId())) {
                deviceIdAdcdIdMapping.setAcwId(deviceIdMap.get(deviceIdAdcdIdMapping.getDeviceId()).get(0));
                existAcwDeviceIdAdcdIdMappingList.add(deviceIdAdcdIdMapping);
            } else {
                notExistAcwDeviceIdAdcdIdMappingList.add(deviceIdAdcdIdMapping);
            }
        }

        Map<String, List<DeviceWarningNotifyMapping>> memoMap = new HashMap<>();
        List<CollectWarning> updateRuleSettingList = new ArrayList<>();
        List<Map<String, Object>> retMap = new ArrayList<>();

        try {
            for (DeviceIdAdcdIdMapping acwMapping : existAcwDeviceIdAdcdIdMappingList) {

                Long mappingAccId = acwMapping.getAccId();
                Long acwId = acwMapping.getAcwId();
                Long adcdId = acwMapping.getAdcdId();
                String deviceId = acwMapping.getDeviceId();

                Map<String, Object> paramMap = new HashMap<>(3);
                paramMap.put("accId", mappingAccId);
                paramMap.put("acwId", acwId);
                paramMap.put("adcdId", adcdId);

                CollectWarning existCollectWarning = collectWarningMapper.getCollectWarningSet(paramMap);
                if (existCollectWarning == null) {
                    throw new RuntimeException("预警项查询错误");
                }

                Map<String, WarningSetting> levelCodeMap = existCollectWarning.getWarningSettingList().stream()
                        .collect(Collectors.toMap(
                                WarningSetting::getLevelCode,
                                Function.identity(),
                                (existing, replacement) -> replacement
                        ));
                List<WarningSetting> warningSettingList = new ArrayList<>();
                for (WarningSetting settingWarningSetting : collectWarning.getWarningSettingList()) {
                    WarningSetting sourceSetting = levelCodeMap.get(settingWarningSetting.getLevelCode());
                    if (sourceSetting != null) {
                        sourceSetting.setWarningAnalysisList(settingWarningSetting.getWarningAnalysisList());
                        sourceSetting.setWarningConditionList(settingWarningSetting.getWarningConditionList());
                        sourceSetting.setWarningNotifyTemplateList(settingWarningSetting.getWarningNotifyTemplateList());
                    } else {
                        sourceSetting = new WarningSetting();
                        BeanUtils.copyProperties(settingWarningSetting, sourceSetting);
                        sourceSetting.setId(0L);

                    }
                    warningSettingList.add(sourceSetting);
                }
                existCollectWarning.setWarningSettingList(warningSettingList);
                BaseResponse modificationResponse = collectWarningService.modifyWarningSettingV2(acwId, existCollectWarning.getWarningSettingList());
                if (!modificationResponse.checkIsSuccess()) {
                    if (!transactionStatus.isCompleted()) {
                        platformTransactionManager.rollback(transactionStatus);
                    }
                    return modificationResponse;
                }

                CollectWarning updateRuleSetting = new CollectWarning();
                BeanUtils.copyProperties(collectWarning, updateRuleSetting);
                updateRuleSetting.setId(acwId);
                updateRuleSettingList.add(updateRuleSetting);
//                for (DeviceIdAdcdIdMapping mapping : deviceIdAdcdIdMappingList) {
//                    Map<String, Object> map = new HashMap<>();
//                    map.put("adcdId", mapping.getAdcdId());
//                    map.put("acwId", acwId);
//                    List<Long> adcwIdList = deviceMapper.selectAdcwIdByMap(map);
//                    if (!CollectionUtils.isEmpty(adcwIdList)) {
//                        Long adcwId = adcwIdList.stream().filter(LongUtil::isNotEmpty).findFirst().orElse(0L);
//                        List<DeviceWarningNotifyMapping> adwnmList = deviceV2Mapper.selectDeviceWarningNotify(adcwId, null);
//                        memoMap.put(mapping.getAdcdId() + "_" + acwId, adwnmList);
//                        deviceV2Mapper.deleteDeviceCollectWarningById(adcwId);
//                    }
//                }
            }
            if (!CollectionUtils.isEmpty(existAcwDeviceIdAdcdIdMappingList)) {
                baseResponse = processExistDeviceWarningMappings(existAcwDeviceIdAdcdIdMappingList, isWarningEnable, accId, retMap);
                if (!baseResponse.checkIsSuccess()) {
                    if (!transactionStatus.isCompleted()){
                        platformTransactionManager.rollback(transactionStatus);
                    }
                    return baseResponse;
                }
            }
//            if (!CollectionUtils.isEmpty(acwIdList)) {
//
//                collectWarningMapper.deleteAcwByIdList(acwIdList);
//                collectWarningMapper.deleteOtherWarningSettingV2(acwIdList);
//                // 不用删除re表
//                ruleEngineDao.deleteRuleSettingByAcwIdList(acwIdList);
//                // 删除 aws 的预测定时任务
//                predictionOfflineDataService.deleteOfflineProcessAsync(new ArrayList<>(acwIdList));
//
//            }
            if (!CollectionUtils.isEmpty(updateRuleSettingList)) {
                ruleEngineService.batchSaveRuleSettingByCollectWarningV2(updateRuleSettingList);
            }

            if (notExistAcwDeviceIdAdcdIdMappingList.isEmpty()) {
                platformTransactionManager.commit(transactionStatus);
                return BaseResponse.ok(baseResponse.getData());
            }

            // 构建预警列表对象
            List<CollectWarning> acwList = notExistAcwDeviceIdAdcdIdMappingList.stream().map(mapping -> {
                CollectWarning newAcw = new CollectWarning();
                BeanUtils.copyProperties(collectWarning, newAcw);
                long acwId = LongUtil.isEmpty(mapping.getAcwId()) ? SNOWFLAKE.newId() : mapping.getAcwId();
                newAcw.setId(acwId);
                mapping.setAcwId(acwId);
                newAcw.setSourceAcwId(sourceAcwId);
                newAcw.setScopeId(mapping.getDeviceId());

                return newAcw;
            }).collect(Collectors.toList());
            // 创建 预测定时任务存储数据
            Map<Long, List<Long>> acwIdMap = new HashMap<>();
            for (CollectWarning acw : acwList) {
                List<Long> awsIdList = new ArrayList<>();
                List<WarningSetting> warningSettingList = new ArrayList<>();
                for (WarningSetting warningSetting : acw.getWarningSettingList()) {
                    if (warningSetting != null) {
                        WarningSetting newWarningSetting = new WarningSetting();
                        BeanUtils.copyProperties(warningSetting, newWarningSetting);
                        newWarningSetting.setId(SNOWFLAKE.newId());
                        newWarningSetting.setAcwId(acw.getId());

                        // 复制 WarningAnalysis 列表
                        List<WarningAnalysis> newWarningAnalysisList = new ArrayList<>();
                        for (WarningAnalysis warningAnalysis : warningSetting.getWarningAnalysisList()) {
                            if (warningAnalysis != null) {
                                WarningAnalysis newWarningAnalysis = new WarningAnalysis();
                                BeanUtils.copyProperties(warningAnalysis, newWarningAnalysis);
                                newWarningAnalysis.setId(SNOWFLAKE.newId());
                                newWarningAnalysis.setAwsId(newWarningSetting.getId());
                                newWarningAnalysisList.add(newWarningAnalysis);
                            }
                        }
                        newWarningSetting.setWarningAnalysisList(newWarningAnalysisList);

                        // 复制 WarningCondition 列表
                        List<WarningCondition> newWarningConditionList = new ArrayList<>();
                        for (WarningCondition warningCondition : warningSetting.getWarningConditionList()) {
                            if (warningCondition != null) {
                                WarningCondition newWarningCondition = new WarningCondition();
                                BeanUtils.copyProperties(warningCondition, newWarningCondition);
                                newWarningCondition.setId(SNOWFLAKE.newId());
                                newWarningCondition.setAwsId(newWarningSetting.getId());
                                newWarningConditionList.add(newWarningCondition);
                            }
                        }
                        newWarningSetting.setWarningConditionList(newWarningConditionList);

                        // 复制 WarningNotifyTemplate 列表
                        List<WarningNotifyTemplate> newWarningNotifyTemplateList = new ArrayList<>();
                        for (WarningNotifyTemplate warningNotifyTemplate : warningSetting.getWarningNotifyTemplateList()) {
                            if (warningNotifyTemplate != null) {
                                WarningNotifyTemplate newWarningNotifyTemplate = new WarningNotifyTemplate();
                                BeanUtils.copyProperties(warningNotifyTemplate, newWarningNotifyTemplate);
                                newWarningNotifyTemplate.setId(SNOWFLAKE.newId());
                                newWarningNotifyTemplate.setAwsId(newWarningSetting.getId());
                                newWarningNotifyTemplateList.add(newWarningNotifyTemplate);
                            }
                        }
                        newWarningSetting.setWarningNotifyTemplateList(newWarningNotifyTemplateList);

                        awsIdList.add(newWarningSetting.getId());

                        warningSettingList.add(newWarningSetting);
                    }

                }
                acwIdMap.put(acw.getId(), awsIdList);

                acw.setWarningSettingList(warningSettingList);
            }

            collectWarningMapper.batchInsertOrUpdateCollectWarning(acwList);

            List<WarningSetting> awsList = acwList.stream().flatMap(acw -> acw.getWarningSettingList().stream())
                    .collect(Collectors.toList());
            collectWarningMapper.batchInsertOrUpdateWarningSetting(awsList);

            List<WarningAnalysis> awaList = awsList.stream().flatMap(warningSetting -> warningSetting.getWarningAnalysisList().stream())
                    .collect(Collectors.toList());
            List<WarningCondition> awcList = awsList.stream().flatMap(warningSetting -> warningSetting.getWarningConditionList().stream())
                    .collect(Collectors.toList());
            List<WarningNotifyTemplate> awntList = awsList.stream().flatMap(warningSetting -> warningSetting.getWarningNotifyTemplateList().stream())
                    .collect(Collectors.toList());

            if (!awntList.isEmpty()) {
                collectWarningMapper.batchInsertWarningNotifyTemplate(awntList);
            }
            if (!awcList.isEmpty()) {
                collectWarningMapper.batchInsertWarningCondition(awcList);
            }
            if (!awaList.isEmpty()) {
                collectWarningMapper.batchInsertWarningAnalysisV2(awaList);
            }
            BaseResponse reResponse = ruleEngineService.batchSaveRuleSettingByCollectWarningV2(acwList);
            if (!reResponse.checkIsSuccess()) {
                if (!transactionStatus.isCompleted()){
                    platformTransactionManager.rollback(transactionStatus);
                }
            }

//                for (BatchModifyWarningSetRequest.DeviceIdAdcdIdMapping mapping : deviceIdAdcdIdMappingList) {
//                    // 更新adcw 表 因为 通知模板（aiops_device_warning_notify_mapping）使用到这个表的id
//
//                    Long adcwId;
//
//                    Long adcdId = mapping.getAdcdId();
//                    Long mappingAcwId = mapping.getAcwId();
//                    String deviceId = mapping.getDeviceId();
//                    Map<String, Object> map = new HashMap<>();
//                    map.put("adcdId", adcdId);
//                    map.put("acwId", oldAcwId);
//                    List<Long> adcwIdList = deviceMapper.selectAdcwIdByMap(map);
//                    if (CollectionUtils.isEmpty(adcwIdList)) {
//                        DeviceCollectWarning adcw = new DeviceCollectWarning(adcdId, mappingAcwId, isWarningEnable);
//                        adcwId = adcw.getId();
//                        deviceMapper.insertDeviceCollectWarning(adcw);
//                    } else {
//                        adcwId = adcwIdList.stream().filter(LongUtil::isNotEmpty).findFirst().orElse(0L);
//                        deviceMapper.updateDeviceCollectWarning(oldAcwId, mappingAcwId, adcdId, isWarningEnable);
//                        //改变规则映射
//                        BaseResponse response = ruleEngineService.modifyResmRsIdByAdcdIdOldAcwIdV2(adcdId, deviceId, accId,
//                                oldAcwId, mappingAcwId);
//                        if (!response.checkIsSuccess()) {
//                            platformTransactionManager.rollback(transactionStatus);
//                            return response;
//                        }
//                    }
//
//                    //保存规则引擎设定映射
//                    BaseResponse response = ruleEngineService.saveResmByAdcdIdAcwIdEnableV2(adcdId, deviceId, accId,
//                            mappingAcwId, adcwId, isWarningEnable);
//
//                    if (!response.checkIsSuccess()) {
//                        platformTransactionManager.rollback(transactionStatus);
//                        return BaseResponse.error(ResponseCode.INTERNAL_ERROR);
//                    }
//                }


            baseResponse = processDeviceWarningMappings(notExistAcwDeviceIdAdcdIdMappingList, oldAcwId, isWarningEnable, accId, null, null, memoMap, retMap);

            if (!baseResponse.checkIsSuccess()) {
                if (!transactionStatus.isCompleted()){
                    platformTransactionManager.rollback(transactionStatus);
                }
                return baseResponse;
            }

            for (Map.Entry<Long, List<Long>> acwE : acwIdMap.entrySet()) {
                predictionOfflineDataService.saveOfflineProcessAsync(acwE.getKey(), acwE.getValue());
            }

        } catch (Exception e) {
            log.error("[batchModifyWarningSetForDevice] batch save device warning error : {}", e.getMessage(), e);
            if (!transactionStatus.isCompleted()){
                platformTransactionManager.rollback(transactionStatus);
            }
            return BaseResponse.error(ResponseCode.INTERNAL_ERROR);
        }
        platformTransactionManager.commit(transactionStatus);

        return BaseResponse.ok(retMap);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public BaseResponse batchModifyWarningSet(BatchModifyWarningSetRequest batchModifyWarningSetRequest) {
        //租户自定义预警项 就是 scopeId=租户id 租户自定义
        //查询预警项详情和查询预警项列表接口
        // todo 新添加的预警项 如果该租户下有自定义预警项 则应用自定义
        CollectWarning collectWarning = batchModifyWarningSetRequest.getCollectWarning();
        BaseResponse checkBaseResponse = checkWarning(collectWarning);
        if (!checkBaseResponse.checkIsSuccess()) {
            return checkBaseResponse;
        }

        String customerId = batchModifyWarningSetRequest.getCustomerId();
        Long oldAcwId = batchModifyWarningSetRequest.getAcwId();
        long accId = collectWarning.getAccId();
        final boolean isWarningEnable = collectWarning.isWarningEnable();

//        Map<String, Object> map = new HashMap<>();
//        map.put("adcdId", adcdId);
//        map.put("acwId", acwId);
        BaseResponse<Long> baseResponse;
        Long newAcwId;
        //如果已经是自定义预警项，直接更新预警项设定，否则自定义预警项
        if (!DEFAULT_SCOPE_ID.equals(collectWarning.getScopeId())) {
            //已经自定义直接更新预警项设定
            baseResponse = collectWarningService.modifyCollectWarning(collectWarning, true);
            if (!baseResponse.checkIsSuccess()) {
                return baseResponse;
            }
            newAcwId = baseResponse.getData();

        } else {
            collectWarning.setScopeId(customerId);
            collectWarning.setSourceAcwId(collectWarning.getId());

            baseResponse = collectWarningService.addCollectWarning(collectWarning, true);
            if (!baseResponse.checkIsSuccess()) {
                TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                return baseResponse;
            }
            newAcwId = baseResponse.getData();

        }

        IAiopsDeviceCollectedDetailService detailService = servicesMap.get(batchModifyWarningSetRequest.getCustomerType());

        BaseResponse<List<DeviceIdAdcdIdMapping>> mappingListRb = detailService.
                getDeviceIdAdcdIdMapping(new IAiopsDeviceCollectedDetailServiceRequest(customerId, oldAcwId, newAcwId,
                        batchModifyWarningSetRequest.getAiopsItem()));
        //设置自定义预警开关 这边需要取租户自定义，如果没有在取系统

        detailService.setCustomerEnable(customerId, oldAcwId, newAcwId, isWarningEnable);

        if (!mappingListRb.checkIsSuccess()) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return mappingListRb;
        }

        List<DeviceIdAdcdIdMapping> mappingList = mappingListRb.getData();

        List<Map<String, Object>> resAdcwIdList = new ArrayList<>();
        BaseResponse mappingRb = processDeviceWarningMappings(mappingList, oldAcwId, isWarningEnable, accId, detailService
                , customerId, null,resAdcwIdList);

        if (!mappingRb.checkIsSuccess()) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return mappingRb;
        }

        return BaseResponse.ok(resAdcwIdList);
    }


    @Override
    public BaseResponse updateTenantWarningEnable(AiopsTenantWarning tenantWarning) {
        Long acwId = tenantWarning.getAcwId();
        Long eid = tenantWarning.getEid();
        Boolean isWarningEnable = tenantWarning.getIsWarningEnable();

        //更新 或 新增 租户预警表
        tenantWarning.setId(SnowFlake.getInstance().newId());
        tenantWarning.setCreateTime(new Date());
        tenantWarning.setUpdateTime(new Date());
        collectWarningMapper.tenantWarningInsertOrUpdate(tenantWarning);

        List<Map<String, Object>> customizeDevice = new ArrayList<>();
        List<AiopsItemCollectWarningSet.AiopsItemCollectWarningSetDetail> details = customiseDeviceCollectWarningSetList(eid, acwId);

        if (!CollectionUtils.isEmpty(details)) {
            Set<Long> acwIds = details.stream()
                    .map(AiopsItemCollectWarningSet.AiopsItemCollectWarningSetDetail::getAcwId)
                    .collect(Collectors.toSet());
            Map<Long, CollectWarning> collectWarningMap = collectWarningMapper.selectCollectWarningsByIds(acwIds).stream()
                    .collect(Collectors.toMap(CollectWarning::getId, Function.identity()));

            details.forEach(detail -> {
                Map<String, Object> map = new HashMap<>(3);
                map.put("adcwId", detail.getAdcwId());

                if (!isWarningEnable) {
                    map.put("enable", false);
                    customizeDevice.add(map);
                    return;
                }

                CollectWarning cw = collectWarningMap.get(detail.getAcwId());
                if (cw == null) {
                    log.warn("CollectWarning not found for acwId: {}", detail.getAcwId());
                    return;
                }


                //判断是否是设备自定义
                boolean isCustomDevice = cw.getScopeId().equals(detail.getDeviceId()) || cw.getScopeId().equals(LongUtil.safeToString(detail.getAiId()));
//                Boolean dbEnable = isCustomDevice ?
//                        deviceV2Mapper.selectResmEnableStatusV2(eid, detail.getAdcdId(), acwId, detail.getAcwId()) :
//                        deviceV2Mapper.selectResmEnableStatus(eid, detail.getAdcdId(), acwId);
//
//                map.put("enable", dbEnable != null ? dbEnable : false);
                Boolean enable = deviceV2Mapper.selectResmEnableStatusV2(
                        eid, detail.getAdcdId(),
                        isCustomDevice ? detail.getAcwId() : acwId,
                        detail.getWarningCode()
                );
                map.put("enable", enable != null ? enable : false);

                customizeDevice.add(map);
            });
        }

        Boolean success = transactionTemplate.execute(status -> {
            try {
                if (!customizeDevice.isEmpty()) {
                    ruleEngineDao.updateResmEnableByAdcwIdBatch(customizeDevice);
                }
                collectWarningMapper.updateCollectWarningTimeByAcwId(acwId);
                return true;
            } catch (Exception e) {
                log.error("Transaction failed for acwId: {}, eid: {}. Error: {}", acwId, eid, e.getMessage(), e);
                status.setRollbackOnly();
                return false;
            }
        });

        if (!success) {
            return BaseResponse.dynamicError(ResponseCode.INTERNAL_ERROR, "更新失败，请稍后重试");
        }
        List<Map<String, Object>> resultList = details.stream().map(item -> {
            Map<String, Object> map = new HashMap<>();
            map.put("adcwId", item.getAdcwId());
            map.put("deviceId", item.getDeviceId());
            return map;
        }).collect(Collectors.toList());
        return BaseResponse.ok(resultList);
    }


//    @Override
//    public BaseResponse updateTenantWarningEnable(AiopsTenantWarning tenantWarning) {
//        Long acwId = tenantWarning.getAcwId();
//        Long eid = tenantWarning.getEid();
//        Boolean isWarningEnable = tenantWarning.getIsWarningEnable();
//
//        //更新 或 新增 租户预警表
//        tenantWarning.setId(SnowFlake.getInstance().newId());
//        tenantWarning.setCreateTime(new Date());
//        tenantWarning.setUpdateTime(new Date());
//        collectWarningMapper.tenantWarningInsertOrUpdate(tenantWarning);
//
//        List<Map<String, Object>> customiseDevice = new ArrayList<>();
//        List<AiopsItemCollectWarningSet.AiopsItemCollectWarningSetDetail> aiopsItemCollectWarningSetDetails = customiseDeviceCollectWarningSetList(eid, acwId);
//        if (!CollectionUtils.isEmpty(aiopsItemCollectWarningSetDetails)) {
//
//            for (AiopsItemCollectWarningSet.AiopsItemCollectWarningSetDetail detail : aiopsItemCollectWarningSetDetails) {
//                Long adcwId = detail.getAdcwId();
//                Long dbAcwId = detail.getAcwId();
//                Long adcdId = detail.getAdcdId();
//                Boolean dbEnable;
//                Map<String, Object> map = new HashMap<>(2);
//                if (!isWarningEnable) {
//                    map.put("adcwId", adcwId);
//                    map.put("enable", false);
//                    customiseDevice.add(map);
//                    continue;
//                }
//                //需要判断是否是设备自定义的
//                String deviceId = detail.getDeviceId();
//                CollectWarning collectWarning = collectWarningMapper.selectCollectWarningById(dbAcwId);
//                String scopeId = collectWarning.getScopeId();
//                //如果相等表明是设备自定义的
//                if (deviceId.equals(scopeId)) {
//                    dbEnable = deviceV2Mapper.selectResmEnableStatusV2(eid, adcdId, acwId, dbAcwId);
//                } else {
//                    dbEnable = deviceV2Mapper.selectResmEnableStatus(eid, adcdId, acwId);
//                }
//                map.put("adcwId", adcwId);
//                map.put("enable", dbEnable);
//                customiseDevice.add(map);
//            }
//        }
//
//        Boolean execute = transactionTemplate.execute((transactionStatus) -> {
//            try {
//                // 批量更新预警规则状态
//                if (!customiseDevice.isEmpty()) {
//                    ruleEngineDao.updateResmEnableByAdcwIdBatch(customiseDevice);
//                }
//
//                //更新预警项时间
//                collectWarningMapper.updateCollectWarningTimeByAcwId(acwId);
//            } catch (Exception e) {
//                log.error("insert warning exception, error:{}", e);
//                transactionStatus.setRollbackOnly();
//                return false;
//            }
//            return true;
//        });
//        if (!execute) {
//            return BaseResponse.dynamicError(ResponseCode.INTERNAL_ERROR, "update error");
//        }
//
//        //返回前端记录日志
//        List<Map<String, Object>> resultList = aiopsItemCollectWarningSetDetails.stream().map(item -> {
//            Map<String, Object> map = new HashMap<>();
//            map.put("adcwId", item.getAdcwId());
//            map.put("deviceId", item.getDeviceId());
//            return map;
//        }).collect(Collectors.toList());
//        return BaseResponse.ok(resultList);
//
//
//
//        //更新规则引擎
//        //1、当关闭时，需要关闭所有预警项下面的数据的，
//        //2、当开启时，需要判断系统级别的预警为开启态&租户级别的预警为开启态&实例对应的预警是开启态
////        Boolean isWarningEnable = tenantWarning.getIsWarningEnable();
////        List<Map<String, Object>> collectWarningList = collectWarningMapper.getCollectWarningList(Arrays.asList(eid), acwId);
////        if (isWarningEnable) {
////            List<Map<String, Object>> warningList = new ArrayList<>();
////            // 遍历处理每一条预警数据
////            collectWarningList.forEach(item -> {
////                Long adcwId = Optional.ofNullable(item.get("adcwId")).map(LongUtil::objectToLong).orElse(null);
////                Long adcdId = Optional.ofNullable(item.get("adcdId")).map(LongUtil::objectToLong).orElse(null);
////
////                // 获取预警状态
////                Boolean orgEnable = Optional.ofNullable(item.get("enable")).map(Object::toString).map(Boolean::parseBoolean)
////                        .orElse(false);
////
////                // 校验字段是否有效
////                if (LongUtil.isNotEmpty(eid) && LongUtil.isNotEmpty(adcdId) && LongUtil.isNotEmpty(acwId)) {
////                    // 获取预警状态
////                    Boolean enable = deviceV2Mapper.selectResmEnableStatus(eid, adcdId, acwId);
////                    if (!orgEnable.equals(enable)) {
////                        log.info("Updating warning status for eid={}, acwId={}, adcdId={},  enable={}",
////                                eid, acwId, adcdId, enable);
////                        Map<String, Object> map = new HashMap<>(2);
////                        map.put("adcwId", adcwId);
////                        map.put("enable", enable);
////                        warningList.add(map);
////                    }
////                }
////            });
////
////            // 批量更新预警规则状态
////            if (!warningList.isEmpty()) {
////                ruleEngineDao.updateResmEnableByAdcwIdBatch(warningList);
////            }
////
////        } else {
////            ruleEngineDao.updateResmEnable(tenantWarning);
////        }
//
//        //返回前端记录日志
////        List<Map<String, Object>> resultList = collectWarningList.stream().map(item -> {
////            Map<String, Object> map = new HashMap<>();
////            Long adcwId = Optional.ofNullable(item.get("adcwId")).map(LongUtil::objectToLong).orElse(null);
////            String deviceId = Optional.ofNullable(item.get("deviceId")).map(Object::toString).orElse("");
////            map.put("adcwId", adcwId);
////            map.put("deviceId", deviceId);
////            return map;
////        }).collect(Collectors.toList());
////        return BaseResponse.ok(resultList);
////        return BaseResponse.ok();
//    }


    private List<AiopsItemCollectWarningSet.AiopsItemCollectWarningSetDetail> customiseDeviceCollectWarningSetList(Long eid, Long acwId) {

//        Set<String> customiseDeviceSet = getCustomiseDeviceSet(eid);
        CollectWarning collectWarning = collectWarningMapper.selectCollectWarningById(acwId);
//        List<Long> acwIdList = new ArrayList<>();
//        acwIdList.add(acwId);
//        if (!collectWarning.getScopeId().equals(LongUtil.safeToString(eid))) {
//            List<AiopsItemCollectWarningSet> customiseDeviceWarningList = deviceMapper.
//                    selectCustomiseDeviceCollectWarningSetListByEid(customiseDeviceSet, eid, Stream.of(collectWarning.getWarningCode()).collect(Collectors.toList()), innerRunnerAccIdSet);
//            List<Long> otherAcwIdList = customiseDeviceWarningList.stream().map(AiopsItemCollectWarningSet::getAcwId).collect(Collectors.toList());
//            acwIdList.addAll(otherAcwIdList);
//        } else {
//            List<Long> tenantAcwIdList = deviceV2Mapper.selectAcwBySourceAcwId(acwId);
//            acwIdList.addAll(tenantAcwIdList);
//        }

        List<AiopsAuthStatus> aiopsAuthStatuses = getAuthList();

        List<AiopsItemCollectWarningSet.AiopsItemCollectWarningSetDetail> aicwsdList = deviceMapper.selectAiIdByAcwIdList(Stream.of(collectWarning.getWarningCode()).collect(Collectors.toList()), eid, null, aiopsAuthStatuses, innerRunnerAccIdSet);
        if (CollectionUtils.isEmpty(aicwsdList)) {
            return new ArrayList<>();
        } else {
            return aicwsdList;
        }

    }

    /**
     * 处理adcw表 和 resm表
     * @param deviceIdAdcdIdMappingList
     * @param oldAcwId
     * @param isWarningEnable
     * @param accId
     * @return
     */
    public BaseResponse<List<Map<String, Object>>> processDeviceWarningMappings(List<DeviceIdAdcdIdMapping> deviceIdAdcdIdMappingList,
                                                                                Long oldAcwId, boolean isWarningEnable, Long accId,
                                                                                IAiopsDeviceCollectedDetailService detailService,
                                                                                String customerId, Map<String,
            List<DeviceWarningNotifyMapping>> memoMap,List<Map<String, Object>> resAdcwIdList) {
        for (DeviceIdAdcdIdMapping mapping : deviceIdAdcdIdMappingList) {
            // todo 对于一起保存的预警 通知模板需要 一并复制吗 aiops_device_warning_notify_mapping 表的数据
            Long adcwId;
            Long adcdId = mapping.getAdcdId();
            Long mappingAcwId = mapping.getAcwId();
            String deviceId = mapping.getDeviceId();
            Map<String, Object> map = new HashMap<>();
            map.put("adcdId", adcdId);
            map.put("acwId", oldAcwId);
            List<Long> adcwIdList = deviceMapper.selectAdcwIdByMap(map);
            if (CollectionUtils.isEmpty(adcwIdList)) {

                DeviceCollectWarning adcw = new DeviceCollectWarning(adcdId, mappingAcwId, isWarningEnable);
                adcwId = adcw.getId();
                deviceMapper.insertDeviceCollectWarning(adcw);
                if (!CollectionUtils.isEmpty(memoMap)) {
                    List<DeviceWarningNotifyMapping> deviceWarningNotifyMappings = memoMap.get(adcdId + "_" + oldAcwId);
                    if (!CollectionUtils.isEmpty(deviceWarningNotifyMappings)) {
                        deviceWarningNotifyMappings.forEach(item -> {
                            item.setAdcwId(adcwId);
                        });
                        deviceV2Mapper.batchInsertDeviceWarningNotify(deviceWarningNotifyMappings);
                    }
                }

            } else {
                adcwId = adcwIdList.stream().filter(LongUtil::isNotEmpty).findFirst().orElse(0L);
                deviceMapper.updateDeviceCollectWarning(oldAcwId, mappingAcwId, adcdId, isWarningEnable);
                BaseResponse response = ruleEngineService.modifyResmRsIdByAdcdIdOldAcwIdV2(adcdId, deviceId, accId, oldAcwId, mappingAcwId);
                if (!response.checkIsSuccess()) {
                    return response;
                }
            }
            boolean custIsWarningEnable = isWarningEnable;
            //需重新计算 isWarningEnable
            if (Objects.nonNull(detailService)) {
                custIsWarningEnable = detailService.getResmEnableStatus(customerId, adcdId, mappingAcwId);
            }

            BaseResponse response = ruleEngineService.saveResmByAdcdIdAcwIdEnableV2(adcdId, deviceId, accId, mappingAcwId, adcwId, custIsWarningEnable);
            if (!response.checkIsSuccess()) {
                return response;
            }
            Map<String, Object> adMap = new HashMap<>();
            adMap.put("deviceId", deviceId);
            adMap.put("adcwId", adcwId);
            resAdcwIdList.add(adMap);
        }
        return BaseResponse.ok(resAdcwIdList);
    }

    public BaseResponse<List<Map<String, Object>>> processExistDeviceWarningMappings(List<DeviceIdAdcdIdMapping> deviceIdAdcdIdMappingList,
                                                                                boolean isWarningEnable, Long accId,List<Map<String, Object>> resAdcwIdList) {
        for (DeviceIdAdcdIdMapping mapping : deviceIdAdcdIdMappingList) {
            // todo 对于一起保存的预警 通知模板需要 一并复制吗 aiops_device_warning_notify_mapping 表的数据
            Long adcwId;
            Long adcdId = mapping.getAdcdId();
            Long mappingAcwId = mapping.getAcwId();
            String deviceId = mapping.getDeviceId();
            Map<String, Object> map = new HashMap<>();
            map.put("adcdId", adcdId);
            map.put("acwId", mappingAcwId);
            List<Long> adcwIdList = deviceMapper.selectAdcwIdByMap(map);
            if (CollectionUtils.isEmpty(adcwIdList)) {

                DeviceCollectWarning adcw = new DeviceCollectWarning(adcdId, mappingAcwId, isWarningEnable);
                adcwId = adcw.getId();
                deviceMapper.insertDeviceCollectWarning(adcw);

            } else {
                adcwId = adcwIdList.stream().filter(LongUtil::isNotEmpty).findFirst().orElse(0L);
                deviceMapper.updateDeviceCollectWarning(mappingAcwId, mappingAcwId, adcdId, isWarningEnable);
                BaseResponse response = ruleEngineService.modifyResmRsIdByAdcdIdOldAcwIdV2(adcdId, deviceId, accId, mappingAcwId, mappingAcwId);
                if (!response.checkIsSuccess()) {
                    return response;
                }
            }
            boolean custIsWarningEnable = isWarningEnable;

            BaseResponse response = ruleEngineService.saveResmByAdcdIdAcwIdEnableV2(adcdId, deviceId, accId, mappingAcwId, adcwId, custIsWarningEnable);
            if (!response.checkIsSuccess()) {
                return response;
            }
            Map<String, Object> adMap = new HashMap<>();
            adMap.put("deviceId", deviceId);
            adMap.put("adcwId", adcwId);
            resAdcwIdList.add(adMap);
        }
        return BaseResponse.ok(resAdcwIdList);
    }

    private BaseResponse checkWarning(CollectWarning collectWarning) {
        if (collectWarning == null) {
            return BaseResponse.error(ResponseCode.INTERNAL_ERROR);
        }

        //1.1 收集项不能为空
        if (collectWarning.getAccId() == 0) {
            return BaseResponse.error(ResponseCode.ACCID_IS_NULL);
        }
        //1.2 预警项编号不能为空
        if (org.apache.commons.lang.StringUtils.isEmpty(collectWarning.getWarningCode())) {
            return BaseResponse.error(ResponseCode.WARNINGCODE_IS_NULL);
        }
        String scopeId = collectWarning.getScopeId();
        //1.3 预警项编号唯一性校验(默认才校验)
        if (org.apache.commons.lang.StringUtils.isBlank(scopeId)) {
            return BaseResponse.error(ResponseCode.WARNING_SCOPE_ID_NULL);
        }
        return BaseResponse.ok(0L);
    }

    @Override
    public BaseResponse getWarningListForDeviceCache(String deviceId, long accId, Long adcdId) {
        return BaseResponse.ok(warningRuleCache.getWarningListForDeviceCache(deviceId, accId, adcdId));
    }

    @Override
    public BaseResponse removeWarningListForDeviceCache(String deviceId, long accId, Long adcdId) {
        warningRuleCache.removeWarningListForDeviceCache(deviceId, accId, adcdId);
        return BaseResponse.ok();
    }

    public DeviceInfoNew getWarningListForDevice(String deviceId, long accId, Long adcdId) {
        Map<String, Object> map = new HashMap<>();
        map.put("serviceArea", serviceArea);
        map.put("deviceId", deviceId);
        map.put("accId", accId);
        map.put("adcdId", adcdId);
        DeviceInfoNew deviceInfo = deviceMapper.getDeviceDetailNew(map);
        if (deviceInfo == null) {
            return deviceInfo;
        }
        if (BooleanUtils.toBoolean(deviceInfo.getFindDisplayNameByModel())) {
            //处理需要从模型获取外显值的内容
            Set<String> codeGroupSet = Stream.of(deviceInfo.getAiopsInstanceName()).collect(Collectors.toSet());
            setDisplayNameByModel(defaultSid, codeGroupSet, (x) -> {
                deviceInfo.setAiopsInstanceName(x.getValue());
            });
        }
        List<CollectWarning> warningList = deviceInfo.getWarningList();
        if (CollectionUtils.isEmpty(warningList)) {
            return deviceInfo;
        }
//        //有自定义仅返回自定义
//        if (warningList.stream().filter(z -> z.getScopeId().equals(deviceId)).findFirst().isPresent()) {
//            List<CollectWarning> collectWarningList1 = warningList.stream().filter(z -> z.getScopeId().equals(deviceId)).collect(Collectors.toList());
//            List<CollectWarning> warningListNew = new ArrayList<>();
//            for (CollectWarning collectWarning : collectWarningList1) {
//                if (collectWarning.isWarningEnable()) {
//                    warningListNew.add(collectWarning);
//                }
//            }
//            deviceInfo.setWarningList(warningListNew);
//            //没有自定义仅返回默认
//        } else if (warningList.stream().filter(z -> z.getScopeId().equals(DEFAULT_SCOPE_ID)).findFirst().isPresent()) {
//            List<CollectWarning> collectWarningList2 = warningList.stream().filter(z -> z.getScopeId().equals(DEFAULT_SCOPE_ID)).collect(Collectors.toList());
//            List<CollectWarning> warningListNew = new ArrayList<>();
//            for (CollectWarning collectWarning : collectWarningList2) {
//                if (collectWarning.isWarningEnable()) {
//                    warningListNew.add(collectWarning);
//                }
//            }
//            deviceInfo.setWarningList(warningListNew);
//        }
        List<CollectWarning> warningEnableList = warningList.stream()
                .filter(x -> x != null)
                .filter(x -> x.isWarningEnable())
                .map(x -> {
                    //填充离线模板
                    List<WarningSetting> warningSettingList = x.getWarningSettingList();
                    if (CollectionUtils.isEmpty(warningSettingList)) {
                        return x;
                    }
                    warningSettingList.stream()
                            .filter(y -> y != null)
                            .filter(y -> y.getNotUploadWarning()) //过滤出有启用离线预警的设定
                            .forEach(y -> {
                                //查询策略(顺序进行，有查到结果就返回)
                                //1.透过acwId + isOffline 查询->表示该预警级别有另外定义离线通知模板
                                //2.透过acwId IS NULL + isOffline 查询->表示默认离线通知模板
                                List<WarningNotifyTemplate> warningNotifyTemplateList =
                                        collectWarningMapper.selectOfflineWarningNotifyTemplate(y.getId(), "");
                                if (CollectionUtils.isEmpty(warningNotifyTemplateList)) {
                                    y.setOfflineWarningNotifyTemplateList(
                                            collectWarningMapper.selectOfflineWarningNotifyTemplate(0L, ""));
                                } else {
                                    y.setOfflineWarningNotifyTemplateList(warningNotifyTemplateList);
                                }
                            });
                    return x;
                })
                .collect(Collectors.toList());

        deviceInfo.setWarningList(warningEnableList);
        return deviceInfo;
    }

    @Override
    public List<AiopsKitDeviceDataSource> getDeviceDataSourceList(Long adId, String dbType,
                                                                  Collection<String> dbIdCollection) {
        //region 参数检查

        if (LongUtil.isEmpty(adId)) {
            return null;
        }

        //endregion

        return deviceMapper.selectDeviceDataSourceListByAdId(adId, dbType, dbIdCollection);
    }

    @Override
    public List<DeviceAppInfo> getDeviceAppIdList(Long adId) {
        //region 参数检查

        if (LongUtil.isEmpty(adId)) {
            return null;
        }

        //endregion

        return deviceMapper.selectDeviceAppIdListByAdId(adId);
    }

    @Override
    public BaseResponse getWarningNotifyMapping(Long adcwId, Boolean containContinueStopSetting) {
        //region 参数检查

        Optional<BaseResponse> optResponse = checkParamIsEmpty(adcwId, "adcwId");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        containContinueStopSetting = BooleanUtils.toBoolean(containContinueStopSetting);

        //endregion

        DeviceWarningNotify deviceWarningNotify = new DeviceWarningNotify();

        List<DeviceWarningNotifyMapping> deviceWarningNotifyMappingByGroupList =
                deviceMapper.selectDeviceWarningNotify(adcwId, BY_GROUP.name());

        deviceWarningNotify.setDeviceWarningNotifyMappingByGroupList(deviceWarningNotifyMappingByGroupList);

        List<DeviceWarningNotifyMapping> deviceWarningNotifyMappingByUserList =
                deviceMapper.selectDeviceWarningNotify(adcwId, NotifyCategory.BY_USER.name());

        deviceWarningNotify.setDeviceWarningNotifyMappingByUserList(deviceWarningNotifyMappingByUserList);

        //调用API串查通知人员列表
        if (CollectionUtils.isEmpty(deviceWarningNotifyMappingByGroupList)) {
            //取得eid
            Long eid = deviceMapper.selectDeviceEidByAdcwId(adcwId);
            if (!LongUtil.isEmpty(eid)) {
                //从默认通知群组取内容
                BaseResponse<List<TenantNotifyGroupRespDTO>> response =
                        aioUserFeignClient.getUserNoticeContactByServiceCode(LongUtil.safeToString(eid), true);
                List<TenantNotifyGroupRespDTO> tngrList;
                if (response.checkIsSuccess() && !CollectionUtils.isEmpty(tngrList = response.getData())) {
                    Optional<TenantNotifyGroupRespDTO> optFirstTngr = tngrList.stream().findFirst();
                    if (optFirstTngr.isPresent()) {
                        TenantNotifyGroupRespDTO tngr = optFirstTngr.get();
                        DeviceWarningNotifyMapping dwnm = new DeviceWarningNotifyMapping();
                        dwnm.setAdcwId(adcwId);
                        dwnm.setNotifyCategory(BY_GROUP.name());
                        dwnm.setIsDefault(true);
                        dwnm.setSourceId(LongUtil.safeToString(tngr.getId()));
                        deviceWarningNotify.setDeviceWarningNotifyMappingByGroupList(Stream.of(dwnm)
                                .collect(Collectors.toList()));
                        deviceWarningNotify.setUserNotifyContactList(tngr.getUserNotifyContactList());
                    }
                }
            }
        } else {
            //组织list
            List<Long> requestList = deviceWarningNotifyMappingByGroupList.stream()
                    .map(x -> LongUtil.tryParseLongByString(x.getSourceId()))
                    .filter(x -> x.isPresent())
                    .map(x -> x.get())
                    .collect(Collectors.toList());
            //调用请求获取通知人员列表
            ResponseBase response = aioUserFeignClient.getUserNoticeContact(requestList);
            if (response != null && ResponseCode.SUCCESS.getCode().equals(response.getCode())) {
                deviceWarningNotify.setUserNotifyContactList((List<UserNotifyContact>) response.getData());
            }
        }
        if (containContinueStopSetting) {
            //如果需要停发通知，就进行查询
            CollectWarning collectWarning = deviceMapper.selectContinueStopSettingByAdcwId(adcwId);
            if (collectWarning != null) {
                deviceWarningNotify.setContinueStopSettingUnit(collectWarning.getContinueStopSettingUnit());
                deviceWarningNotify.setContinueStopSettingValue(collectWarning.getContinueStopSettingValue());
            }
        }

        return BaseResponse.ok(deviceWarningNotify);
    }

    @Override
    public BaseResponse checkNotifyMappingExist(Long sourceId, String notifyCategoryString) {
        //region 参数检查

        Optional<BaseResponse> optResponse = checkParamIsEmpty(sourceId, "sourceId");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }
        final String finalNotifyCategoryString;
        if (StringUtils.isBlank(notifyCategoryString)) {
            finalNotifyCategoryString = BY_GROUP.name();
        } else if (Arrays.stream(NotifyCategory.values())
                .filter(x -> x.isSame(notifyCategoryString))
                .count() <= 0) {
            return BaseResponse.ok(false);
        } else {
            finalNotifyCategoryString = notifyCategoryString;
        }

        //endregion

        return BaseResponse.ok(deviceMapper.selectDeviceWarningNotifyExist(sourceId, finalNotifyCategoryString));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BaseResponse saveDeviceWarningNotify(Long adcwId, DeviceWarningNotify deviceWarningNotify) {
        //region 参数检查

        Optional<BaseResponse> optResponse = checkParamIsEmpty(adcwId, "adcwId");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        if (deviceWarningNotify == null) {
            return BaseResponse.ok();
        }

        //endregion

        //1.BY_GROUP
        int resultByGroup = saveDeviceWarningNotify(adcwId, BY_GROUP, deviceWarningNotify.getDeviceWarningNotifyMappingByGroupList());

        //2.BY_USER
        int resultByUser = saveDeviceWarningNotify(adcwId, NotifyCategory.BY_USER, deviceWarningNotify.getDeviceWarningNotifyMappingByUserList());

        Long acwId = deviceWarningNotify.getAcwId();
        Map<String, Object> resultMap = new HashMap<>(2);
        resultMap.put("acwId", acwId);
        resultMap.put("adcwId", adcwId);
        resultMap.put("isSuccess", resultByGroup > 0 || resultByUser > 0);
        //有异动停发通知，进行自定义
        if (deviceWarningNotify.getIsChangedContinueStopSetting()) {
            //透过acwId查出收集项预警项
            Map<String, Object> map = new HashMap<>();
            map.put("accId", "");
            map.put("acwId", acwId);
            CollectWarning collectWarning = collectWarningMapper.getCollectWarningSet(map);
            if (collectWarning != null) {
                collectWarning.setContinueStopSettingUnit(deviceWarningNotify.getContinueStopSettingUnit());
                collectWarning.setContinueStopSettingValue(deviceWarningNotify.getContinueStopSettingValue());
                BaseResponse<Map<String, Object>> res = this.modifyWarningSetForDevice(deviceWarningNotify.getAdcdId(),
                        acwId, deviceWarningNotify.getDeviceId(), collectWarning);
                if (!res.checkIsSuccess()) {
                    return res;
                }
                resultMap.putAll(res.getData());
            }
        }
        return BaseResponse.ok(resultMap);
    }

    private int saveDeviceWarningNotify(Long adcwId, NotifyCategory notifyCategory,
                                        List<DeviceWarningNotifyMapping> deviceWarningNotifyMappingList) {
        //未知的不做处理，离开
        if (notifyCategory == NotifyCategory.UNKNOWN) {
            return 0;
        }
        String notifyCategoryString = notifyCategory.name();
        if (CollectionUtils.isEmpty(deviceWarningNotifyMappingList)) {
            //进行清理
            Map<String, Object> map = new HashMap<>();
            map.put("adcwId", adcwId);
            map.put("notifyCategory", notifyCategoryString);
            return deviceMapper.deleteDeviceWarningNotify(map);
        }
        List<DeviceWarningNotifyMapping> oriDeviceWarningNotifyMappingLIst =
                deviceMapper.selectDeviceWarningNotify(adcwId, notifyCategoryString);
        if (CollectionUtils.isEmpty(oriDeviceWarningNotifyMappingLIst)) {
            //直接批量新增
            //填充相关值
            deviceWarningNotifyMappingList.stream().forEach(x -> {
                x.setId(SNOWFLAKE.newId());
                x.setAdcwId(adcwId);
                x.setNotifyCategory(notifyCategoryString);
            });
            return deviceMapper.batchInsertDeviceWarningNotify(deviceWarningNotifyMappingList);
        }
        Map<String, DeviceWarningNotifyMapping> oldMap = oriDeviceWarningNotifyMappingLIst.stream()
                .collect(Collectors.toMap(DeviceWarningNotifyMapping::getKey, (x) -> x, (x1, x2) -> x2));
        List<DeviceWarningNotifyMapping> newDeviceWarningNotifyMappingList = deviceWarningNotifyMappingList.stream()
                .filter(x -> x != null)
                .filter(x -> {
                    String key = x.getKey();
                    if (oldMap.containsKey(key)) {
                        oldMap.remove(key);
                        return false;
                    }
                    return true;
                })
                .map(x -> {
                    x.setId(SNOWFLAKE.newId());
                    x.setAdcwId(adcwId);
                    return x;
                })
                .collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(newDeviceWarningNotifyMappingList)) {
            //表内不存在的，进行批量新增
            deviceMapper.batchInsertDeviceWarningNotify(newDeviceWarningNotifyMappingList);
        }
        //将其他的批量删除
        int size = oldMap.size();
        if (size > 0) {
            Map<String, Object> map = new HashMap<>();
            map.put("adcwId", adcwId);
            map.put("notifyCategory", notifyCategory.name());
            map.put("adwnmIdList", oldMap.values().stream().filter(x -> x != null)
                    .map(x -> x.getId()).collect(Collectors.toList()));
            deviceMapper.deleteDeviceWarningNotify(map);
        }
        return 1;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BaseResponse removeDeviceWarningNotify(Long adcwId, Long adwnmId, String notifyCategoryString) {
        //region 参数检查

        Optional<BaseResponse> optResponse = checkParamIsEmpty(adcwId, "adcwId");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        if (LongUtil.isEmpty(adwnmId) || adwnmId < 0) {
            adwnmId = 0L;
        }

        final String notifyCategoryStringCondition;
        if (StringUtils.isBlank(notifyCategoryString) || Arrays.stream(NotifyCategory.values())
                .filter(x -> x.isSame(notifyCategoryString))
                .count() <= 0) {
            notifyCategoryStringCondition = "";
        } else {
            notifyCategoryStringCondition = notifyCategoryString;
        }

        //endregion

        Map<String, Object> map = new HashMap<>();
        map.put("adcwId", adcwId);
        map.put("adwnmId", adwnmId);
        map.put("notifyCategory", notifyCategoryStringCondition);
        return BaseResponse.ok(deviceMapper.deleteDeviceWarningNotify(map) > 0);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BaseResponse changeDeviceWarningNotifyDefault(Long oriSourceId, Long sourceId, String notifyCategoryString) {
        //region 参数检查

        Optional<BaseResponse> optResponse = checkParamIsEmpty(oriSourceId, "oriSourceId");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        optResponse = checkParamIsEmpty(sourceId, "sourceId");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        final String notifyCategoryStringCondition;
        if (StringUtils.isBlank(notifyCategoryString)) {
            notifyCategoryStringCondition = BY_GROUP.name();
        } else if (Arrays.stream(NotifyCategory.values())
                .filter(x -> x.isSame(notifyCategoryString))
                .count() <= 0) {
            return BaseResponse.ok(false);
        } else {
            notifyCategoryStringCondition = notifyCategoryString;
        }

        //endregion

        //1.查出那些设备收集项预警，有使用原始的来源Id
        List<Long> oriDefaultAdcwIdList = deviceMapper.selectAdcwIdListByDefault(notifyCategoryString, oriSourceId);
        if (CollectionUtils.isEmpty(oriDefaultAdcwIdList)) {
            return BaseResponse.ok(true);
        }
        //2.删除要更新的内容中，sourceId已经存在的adcwId列表
        deviceMapper.deleteAdcwIdListByExistNewDefault(notifyCategoryString, sourceId, oriDefaultAdcwIdList);

        //3.统一将oriSourceId换成sourceId，并设置isDefault
        return BaseResponse.ok(deviceMapper.updateDeviceWarningNotifyDefault(oriSourceId, sourceId, notifyCategoryStringCondition) > 0);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BaseResponse fixDeviceCollectWarning(Long accId, Long acwId, Boolean isWarningEnable) {
        //获取设备收集项详情列表内容
        Map<String, Object> map = new HashMap<>(2);
        map.put("accId", accId);
        List<DeviceCollectDetail> deviceCollectDetailList = deviceMapper.selectAdcdByMap(map);
        if (CollectionUtils.isEmpty(deviceCollectDetailList)) {
            //如果没有查到设备收集项明细，直接返回
            return BaseResponse.ok();
        }
        deviceCollectDetailList.stream().forEach(x -> {
            Long adcdId = x.getId();
            //acwId如果为空，表示修正所有
            List<DeviceCollectWarning> deviceCollectWarningList;
            List<CollectWarning> acwList = x.getCollectWarningList();
            if (LongUtil.isEmpty(acwId)) {
                if (CollectionUtils.isEmpty(acwList)) {
                    //没有查到预警项直接添加
                    deviceCollectWarningList = Stream.of(new DeviceCollectWarning(adcdId, acwId, isWarningEnable))
                            .collect(Collectors.toList());
                } else {
                    //有查到预警项，检查是否已经添加过
                    deviceCollectWarningList = x.getCollectWarningList().stream()
                            .filter(y -> {
                                map.clear();
                                map.put("adcdId", adcdId);
                                map.put("acwId", y.getId());
                                //只针对没有设备收集项预警的部分作处理，其他不需要(也不用管是否自定义)
                                return CollectionUtils.isEmpty(deviceMapper.selectAdcwIdByMap(map));
                            }).map(y -> new DeviceCollectWarning(adcdId, y.getId(), y.isWarningEnable()))
                            .collect(Collectors.toList());
                }
            } else {
                if (!CollectionUtils.isEmpty(acwList)) {
                    map.clear();
                    map.put("adcdId", adcdId);
                    map.put("acwId", acwId);
                    //如果已经存在了，离开
                    if (!CollectionUtils.isEmpty(deviceMapper.selectAdcwIdByMap(map))) {
                        return;
                    }
                }
                deviceCollectWarningList = Stream.of(new DeviceCollectWarning(adcdId, acwId, isWarningEnable))
                        .collect(Collectors.toList());
            }
            //过滤查询后如果没有需要新增，直接忽略
            if (CollectionUtils.isEmpty(deviceCollectWarningList)) {
                return;
            }
            //批量新增设备收集项预警项的关联
            deviceMapper.batchDeviceCollectWarning(deviceCollectWarningList);
            //批量新增规则引擎
            ruleEngineService.batchSaveRuleEngineByAdcwList(deviceCollectWarningList);
            //加默认设备通知
            DeviceCollect deviceCollect = deviceMapper.selectDeviceCollectById(x.getAdcId());
            if (deviceCollect == null) {
                return;
            }
            //设置默认设备通知群组
            batchAddDefaultDeviceWarningNotifyMapping(deviceCollectWarningList, null, deviceCollect.getDeviceId());
        });
        return BaseResponse.ok();
    }

    @Async
    @Override
    public void fixDeviceCollectWarningAsync(Long accId, Long acwId, Boolean isWarningEnable) {
        fixDeviceCollectWarning(accId, acwId, isWarningEnable);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BaseResponse clearDeviceCollectWarning(Long accId, Long acwId) {
        return clearDeviceCollectWarningCore(accId, acwId);
    }

    private BaseResponse clearDeviceCollectWarningCore(Long accId, Long acwId) {
        //获取设备收集项详情列表内容
        Map<String, Object> map = new HashMap<>(2);
        map.put("accId", accId);
        List<DeviceCollectDetail> deviceCollectDetailList = deviceMapper.selectAdcdByMap(map);
        if (CollectionUtils.isEmpty(deviceCollectDetailList)) {
            return BaseResponse.ok(0);
        }
        deviceCollectDetailList.stream()
                .filter(x -> !CollectionUtils.isEmpty(x.getCollectWarningList()))
                .forEach(x -> {
                    Long adcdId = x.getId();
                    map.clear();
                    map.put("adcdId", adcdId);
                    map.put("acwId", acwId);
                    List<Long> adcwIdList = deviceMapper.selectAdcwIdByMap(map);
                    if (CollectionUtils.isEmpty(adcwIdList)) {
                        return;
                    }
                    adcwIdList.stream().filter(y -> !LongUtil.isEmpty(y)).forEach(y -> {
                        map.clear();
                        map.put("adcwId", y);
                        //设备预警通知
                        deviceMapper.deleteDeviceWarningNotify(map);
                        //删除设备收集项预警
                        deviceMapper.deleteDeviceCollectWarningById(y);
                        //删除规则引擎设定映射
                        ruleEngineService.removeRuleEngineSettingMappingByAdcwId(y);
                    });
                });
        return BaseResponse.ok(deviceCollectDetailList.size());
    }

    @Async
    @Override
    public void asyncClearDeviceCollectWarning(Long accId, Long acwId) {
        BaseResponse response = clearDeviceCollectWarningCore(accId, acwId);
        if (!response.checkIsSuccess()) {
            StringBuilder sbError = new StringBuilder("asyncClearDeviceCollectWarning accId:");
            sbError.append(accId);
            sbError.append(" acwId:");
            sbError.append(acwId);
            sbError.append(" error by code:");
            sbError.append(response.getCode());
            sbError.append(" not zero errMsg:");
            sbError.append(response.getErrMsg());
            log.error(sbError.toString());
        }
    }

    @Override
    public BaseResponse getDeviceTypeList() {
        return BaseResponse.ok(deviceMapper.selectDeviceTypeList());
    }

    @Override
    public List<DeviceWarningNotifyMapping> getDeviceWarningNotifyMapping(Long adcwId, String notifyCategory) {
        return deviceMapper.selectDeviceWarningNotify(adcwId, notifyCategory);
    }

    @Override
    public BaseResponse getDeviceTypeOnlineStatistic(Long eid) {
        //region 参数检查

        Optional<BaseResponse> optResponse = checkHeaderIsEmpty(eid, "eid");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        //endregion

        return BaseResponse.ok(deviceMapper.selectDeviceTypeOnlineStatistic(eid));
    }

    @Override
    public BaseResponse getDeviceOfflineTotal(Long eid) {
        //region 参数检查

        Optional<BaseResponse> optResponse = checkHeaderIsEmpty(eid, "eid");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        //endregion

        return BaseResponse.ok(deviceMapper.selectDeviceOfflineTotal(eid));
    }

    @Override
    public BaseResponse getDeviceScpUserMappingList(Long eid, String serviceCode, Boolean needPaging,
                                                    Integer pageNum, Integer pageSize, String searchText) {
        //region 参数检查

        Optional<BaseResponse> optResponse = checkHeaderIsEmpty(eid, "eid");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        optResponse = checkParamIsEmpty(serviceCode, "serviceCode");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        needPaging = BooleanUtils.toBoolean(needPaging);

        //endregion

        //1.查询eid下设备列表
        List<DeviceInfo> deviceInfoList;
        PageInfo<DeviceInfo> deviceInfoPageInfo = null;
        Map<String, Object> map = new HashMap<>();
        map.put("eid", eid);
        map.put("searchText", searchText);

        BaseResponse response = getDeviceListCore(needPaging, pageNum, pageSize, map);
        if (needPaging) {
            Optional<PageInfo<DeviceInfo>> optResult = response.checkAndGetCurrentData();
            if (!optResult.isPresent()) {
                return response;
            }
            deviceInfoPageInfo = optResult.get();
            deviceInfoList = deviceInfoPageInfo.getList();
        } else {
            Optional<List<DeviceInfo>> optResult = response.checkAndGetCurrentData();
            if (!optResult.isPresent()) {
                return response;
            }
            deviceInfoList = optResult.get();
        }
        if (CollectionUtils.isEmpty(deviceInfoList)) {
            return BaseResponse.ok();
        }

        List<String> deviceIdList = deviceInfoList.stream().map(x -> x.getDeviceId()).collect(Collectors.toList());
        //2.透过该设备列表串查云管家登入信息
        response = userV2FeignClient.getUserAiopsDeviceMapping(eid, serviceCode, deviceIdList);
        Optional<List<Map<String, Object>>> optResult = response.checkAndGetCurrentData();
        if (!optResult.isPresent()) {
            return response;
        }
        Map<String, UserAiopsDeviceMapping> deviceIdResultMap;
        List<Map<String, Object>> resultListMap = optResult.get();
        if (CollectionUtils.isEmpty(resultListMap)) {
            deviceIdResultMap = new HashMap<>();
        } else {
            //TODO:目前因为machineName包含windows用户信息(如：machine$user1、machine$user2)
            // 有可能同一个deviceId会对应到多个userId，将来若希望都呈现，可能会无法进行分页(或者要特殊处理)
            deviceIdResultMap = resultListMap.stream()
                    .filter(x -> x != null)
                    .map(x -> SerializeUtil.JsonDeserialize(SerializeUtil.JsonSerialize(x),
                            UserAiopsDeviceMapping.class))
                    .filter(x -> StringUtils.isNotBlank(x.getDeviceId()))
                    .collect(Collectors.toMap(x -> x.getDeviceId(), x -> x,
                            (x, y) -> {
                                //暂时取最后登入时间较新的的纪录做输出
                                String xScpLastLoginTime = x.getScpLastLoginTime();
                                String yScpLastLoginTime = y.getScpLastLoginTime();
                                if (StringUtils.isBlank(xScpLastLoginTime)) {
                                    return y;
                                }
                                if (xScpLastLoginTime.compareTo(yScpLastLoginTime) > 0) {
                                    return x;
                                }
                                return y;
                            }));
        }
        //3.重新将Map组织成UserAiopsDeviceMapping对象
        List<UserAiopsDeviceMapping> userAiopsDeviceMappingList = deviceInfoList.stream().map(x -> {
            UserAiopsDeviceMapping uadm = new UserAiopsDeviceMapping();
            String deviceId = x.getDeviceId();
            UserAiopsDeviceMapping resultUadm = deviceIdResultMap.get(deviceId);
            if (resultUadm != null) {
                BeanUtils.copyProperties(resultUadm, uadm);
            }
            BeanUtils.copyProperties(x, uadm);
            uadm.setDeviceId(deviceId);
            uadm.setEid(eid);
            uadm.setServiceCode(serviceCode);
            return uadm;
        }).collect(Collectors.toList());
        //分页返回就完善分页内容
        if (needPaging && deviceInfoPageInfo != null) {
            //TODO:将来如果允许同设备不同machineName可以有多笔纪录输出，这里会无法进行分页
            PageInfo<UserAiopsDeviceMapping> pageInfo = new PageInfo<>(userAiopsDeviceMappingList);
            BeanUtils.copyProperties(deviceInfoPageInfo, pageInfo, "list");
            return BaseResponse.ok(pageInfo);
        }
        return BaseResponse.ok(userAiopsDeviceMappingList);
    }

    @Override
    public BaseResponse getAdcdSeriesList(Long adId) {
        //region 参数检查

        Optional<BaseResponse> optResponse = checkParamIsEmpty(adId, "adId");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        //endregion

        return BaseResponse.ok(deviceMapper.selectAdcdSeriesList(adId));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BaseResponse modifyUndefineDeviceIsDeletedStatus(String deviceId, Boolean isDeleted) {
        //region 参数检查

        Optional<BaseResponse> optResponse = checkParamIsEmpty(deviceId, "deviceId");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        isDeleted = BooleanUtils.toBoolean(isDeleted);

        //确定该设备为未定义
        AiopsKitDevice device = deviceMapper.selectDeviceByDeviceId(deviceId);
        if (device == null) {
            return BaseResponse.dynamicError(deviceId, ResponseCode.DEVICE_NOT_FOUND, deviceId);
        }

        if (!CollectionUtils.isEmpty(device.getDeviceTypeMappingList())) {
            return BaseResponse.dynamicError(device, ResponseCode.DEVICE_NOT_UNDEFINED, deviceId);
        }

        //endregion

        return BaseResponse.ok(deviceMapper.updateDeviceIsDeleteStatus(device.getId(), isDeleted));
    }

    @Override
    public BaseResponse batchSaveDeviceInstanceMappingByAicList(List<AiopsItemContext> aicList) {
        //region 参数检查

        Optional<BaseResponse> optResponse = checkParamIsEmpty(aicList, "aicList");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        int size = aicList.size();
        List<AiopsItemContext> invalidAiiList = new ArrayList<>(size);
        List<Map<String, Object>> mapList = new ArrayList<>(size);
        //先取出需要映射到设备的实例列表，並檢查是否合法
        aicList = aicList.stream()
                .filter(Objects::nonNull)
                .filter(x -> BooleanUtils.toBoolean(x.getIsMapInstancesToDevices()))
                .filter(x -> !BooleanUtils.toBoolean(x.getAdcdOnlyMappingByAiId()))
                .peek(x -> {
                    String deviceId = x.getSourceDeviceId();
                    if (StringUtils.isBlank(deviceId)) {
                        invalidAiiList.add(x);
                    } else if (!BooleanUtils.toBoolean(x.getAllowDuplicateMapping())) {
                        Map<String, Object> map = new HashMap<>(2);
                        map.put("aiId", x.getAiId());
                        map.put("deviceId", deviceId);
                        mapList.add(map);
                    }
                })
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(aicList)) {
            return BaseResponse.ok();
        }

        //检查是否有要设置映射，但是不允许且没有设定来源设备Id
        if (!CollectionUtils.isEmpty(invalidAiiList)) {
            return BaseResponse.error(ResponseCode.DEVICE_MAPPING_INSTANCE_ERROR_BY_SOURCE_DEVICE_ID_IS_EMPTY,
                    invalidAiiList);
        }

        //检查不允许重复映射到设备的实例(如果有的话)
        if (!CollectionUtils.isEmpty(mapList)) {
            List<Map<String, Object>> invalidMapList = deviceMapper.selectExistDeviceInstanceMapping(mapList);
            if (!CollectionUtils.isEmpty(invalidMapList)) {
                return BaseResponse.error(ResponseCode.NOT_ALLOW_DUPLICATE_MAPPING_ERROR, invalidMapList);
            }
        }

        //endregion

        BaseResponse<List<DeviceInstanceMapping>> response = getDeviceInstanceMappingByAicList(aicList);
        if (!response.checkIsSuccess()) {
            return response;
        }

        List<DeviceInstanceMapping> existAdimList = response.getData();
        Map<String, DeviceInstanceMapping> adimMap;
        if (!CollectionUtils.isEmpty(existAdimList)) {
            adimMap = existAdimList.stream().collect(Collectors.toMap(DeviceInstanceMapping::getBusinessKey,
                    x -> x, (x, y) -> y));
        } else {
            adimMap = new HashMap<>(0);
        }

        List<AiopsUpgradeDcdpDetail> upgradeDcdpDetailList = new ArrayList<>();
        //产生需要新增及需要更新的列表
        List<DeviceInstanceMapping> needInsertAdimList = new ArrayList<>(aicList.size());
        List<DeviceInstanceMapping> needUpdateAdimList = aicList.stream().map(x -> {
            String deviceId = x.getSourceDeviceId();
            Long aiId = x.getAiId();
            String key = DeviceInstanceMapping.getKey(deviceId, aiId);
            DeviceInstanceMapping adim = adimMap.get(key);
            if (adim == null) {
                //表示新增
                Long adId = getAdIdByDeviceId(deviceId);
                if (LongUtil.isEmpty(adId)) {
                    //如果没有取到对应的adId，暂时先不处理
                    return null;
                }
                adim = new DeviceInstanceMapping(adId, deviceId, x.getAiId(),
                        BooleanUtils.toBoolean(x.getHasAddedAiopsInstance()));
                Long specifyCreateAdimId = x.getSpecifyCreateAdimId();
                if (LongUtil.isNotEmpty(specifyCreateAdimId)) {
                    adim.setId(specifyCreateAdimId);
                }
                x.setAdimId(adim.getId());
                adim.setEid(x.getEid());
                adimMap.put(key, adim);
                needInsertAdimList.add(adim);
                return null;
            }
            //填充设备主键Id(减少查询)
            x.setAdId(adim.getAdId());
            //表示更新
            adim.setHasAddedAiopsInstance(BooleanUtils.toBoolean(x.getHasAddedAiopsInstance()));
            x.setAdimId(adim.getId());
            adim.setEid(x.getEid());
            return adim;
        }).filter(Objects::nonNull).collect(Collectors.toList());
        //处理更新的内容
        if (!CollectionUtils.isEmpty(needUpdateAdimList)) {
            deviceMapper.batchUpdateAiopsDeviceInstanceMapping(needUpdateAdimList);
            upgradeDcdpDetailList.addAll(needUpdateAdimList.stream().map(x -> {
                AiopsUpgradeDcdpDetail detail = new AiopsUpgradeDcdpDetail(UpgradeType.UPDATE,
                        DeviceUpgradeEnum.DEVICE_INSTANCE_MAPPING.getTableName(), BeanUtil.object2Map(x));
                Map<String, Object> paramsMap = detail.getParamsMap();
                paramsMap.put(EID, x.getEid());
                paramsMap.put(DEVICE_ID, x.getDeviceId());
                detail.setNeedSaveOperationLog(true);
                Map<String, Object> operationInfoMap = OperationUtil.getHeaderOperationInfo();
                operationInfoMap.put("operationObjectClassification", "device");
                operationInfoMap.put("operationObject", "device");
                operationInfoMap.put("operationBehavior", "devicechangeinstancemapping");
                detail.setOperationInfoMap(operationInfoMap);
                return detail;
            }).collect(Collectors.toList()));
        }
        //处理新增的内容
        if (!CollectionUtils.isEmpty(needInsertAdimList)) {
            deviceMapper.batchInsertAiopsDeviceInstanceMapping(needInsertAdimList);
            upgradeDcdpDetailList.addAll(needInsertAdimList.stream().map(x -> {
                AiopsUpgradeDcdpDetail detail = new AiopsUpgradeDcdpDetail(
                        DeviceUpgradeEnum.DEVICE_INSTANCE_MAPPING.getTableName(), BeanUtil.object2Map(x));
                Map<String, Object> paramsMap = detail.getParamsMap();
                paramsMap.put(EID, x.getEid());
                paramsMap.put(DEVICE_ID, x.getDeviceId());
                detail.setNeedSaveOperationLog(true);
                Map<String, Object> operationInfoMap = OperationUtil.getHeaderOperationInfo();
                operationInfoMap.put("operationObjectClassification", "device");
                operationInfoMap.put("operationObject", "device");
                operationInfoMap.put("operationBehavior", "deviceaddinstancemapping");
                detail.setOperationInfoMap(operationInfoMap);
                return detail;
            }).collect(Collectors.toList()));
        }
        executeUpgradeToDcdp(upgradeDcdpDetailList);

        // 處理運維模組用到的收集項
        return processModuleCollectByAiopsItemContext(aicList);
    }

    @Override
    public BaseResponse processAdcdOnlyMappingAiId(List<AiopsItemContext> aicList) {
        //region 参数检查

        Optional<BaseResponse> optResponse = checkParamIsEmpty(aicList,
                "aicList");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        aicList = aicList.stream().filter(x -> BooleanUtils.toBoolean(x.getAdcdOnlyMappingByAiId()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(aicList)) {
            return BaseResponse.ok(0);
        }

        //endregion
        // 處理運維模組用到的收集項
        return processModuleCollectByAiopsItemContext(aicList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BaseResponse batchSaveAiopsDeviceInstanceMapping(List<DeviceInstanceMapping> deviceInstanceMappingList) {
        //region 参数检查

        Optional<BaseResponse> optResponse = checkParamIsEmpty(deviceInstanceMappingList,
                "deviceInstanceMappingList");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        //endregion

        return BaseResponse.ok(deviceMapper.batchInsertOrUpdateAiopsDeviceInstanceMapping(deviceInstanceMappingList));
    }

    private BaseResponse processModuleCollectByAiopsItemContext(List<AiopsItemContext> aicList) {
        //region 参数检查

        if (CollectionUtils.isEmpty(aicList)) {
            return BaseResponse.ok(0);
        }

        //取出需要添加实例收集项到设备的实例列表
        aicList = aicList.stream()
                .filter(Objects::nonNull)
                .filter(x -> BooleanUtils.toBoolean(x.getIsAddInstanceCollectToDevice()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(aicList)) {
            //若没有了，返回成功
            return BaseResponse.ok(0);
        }

        //检查有无要添加实例收集项到设备，但是没有填写设备Id
        List<AiopsItemContext> invalidAiiList = aicList.stream()
                .filter(x -> StringUtils.isBlank(x.getSourceDeviceId()) &&
                        !BooleanUtils.toBoolean(x.getAdcdOnlyMappingByAiId()))
                .collect(Collectors.toList());

        if (!CollectionUtils.isEmpty(invalidAiiList)) {
            return BaseResponse.error(ResponseCode.DEVICE_ADD_INSTANCE_COLLECT_ERROR_BY_SOURCE_DEVICE_ID_IS_EMPTY,
                    invalidAiiList);
        }

        //endregion

        aicList.forEach(x -> {
            if (x.isCreateAdcdBySync()) {
                // 开放同步产生收集项, 由创建运维时, 可以指定
                // 需要留意, 如果预期收集项产生较多, 还是要走异步执行.
                this.createDeviceCollection(x);
            } else {
                x.fillOperationInfoMap();
                //因为添加收集项会是一个大流程，为了避免请求过长，改为异步进行
                CompletableFuture.runAsync(() -> this.createDeviceCollection(x));
            }
        });
        return BaseResponse.ok(aicList.size());
    }

    private void createDeviceCollection(AiopsItemContext aic) {
        try {
            addModuleCollectForDevice(aic);
            BaseResponse response = upgradeService.upgradeToDcdpAsyncWaitByDetail(aic.getUpgradeDcdpDetailList());
            Exception resEx;
            if (response.checkIsSuccess()) {
                resEx = null;
            } else {
                resEx = new Exception(response.getErrMsg());
            }
            saveAddModuleCollectForDeviceLog(aic, resEx);
        } catch (Exception ex) {
            saveAddModuleCollectForDeviceLog(aic, ex);
            log.error("createDeviceCollection exception:", ex);
        }
    }

    private void saveAddModuleCollectForDeviceLog(AiopsItemContext aic, Exception ex) {
        if (aic == null) {
            return;
        }
        int mode = IntegerUtil.objectToInteger(addModuleCollectLogMode);
        String validSamcdIdResult = aic.getValidSamcdIdResult();
        boolean isSuccess = ex == null && (StringUtils.isBlank(validSamcdIdResult) ||
                !validSamcdIdResult.startsWith("error"));
        switch (mode) {
            case 1: //只有失败纪录
                if (isSuccess) {
                    break;
                }
            case 2: //全部都记录
                AddModuleCollectLog amcl = new AddModuleCollectLog(aic);
                amcl.setIsSuccess(isSuccess);
                if (ex != null) {
                    StringWriter sw = new StringWriter();
                    ex.printStackTrace(new PrintWriter(sw));
                    amcl.setExceptionMsg(StringUtil.cutByMysqlTextMaxLength(sw.toString()));
                }
                deviceMapper.insertAddModuleCollectLog(amcl);
                break;
            default: //不纪录
                break;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BaseResponse batchFixAdcdExecParamsContent(List<AiopsItemContext> aicList) {
        //region 参数检查

        Optional<BaseResponse> optResponse = checkParamIsEmpty(aicList, "aicList");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        //endregion

        Set<String> deviceIdSet = new HashSet<>(aicList.size());
        List<DeviceCollectDetail> needUpdateAdcdList = aicList.stream()
                .filter(x ->
                        BooleanUtils.toBoolean(x.getNeedUpdateDeviceCollectExecParamsContent())
                                || BooleanUtils.toBoolean(x.getNeedFixExecParamsContent())
                ).filter(x -> !LongUtil.isEmpty(x.getAiId()))
                .filter(x -> StringUtils.isNotBlank(x.getOriExecParamsContent()))
                .map(x -> {
                    Long aiId = x.getAiId();
                    Long adimId = x.getAdimId();
                    Long adcdId = x.getAdcdId();
                    List<DeviceCollectDetail> adcdList;
                    if (LongUtil.isEmpty(adcdId)) {
                        //查出运维实例对应的设备收集项明细列表
                        //修正段adimId不需要完全命中
                        adcdList = deviceMapper.selectAdcdListByAiId(aiId, adimId, true);
                    } else {
                        Map<String, Object> map = new HashMap<>(1);
                        map.put("adcdId", adcdId);
                        adcdList = deviceMapper.selectAdcdByMap(map);
                    }
                    if (CollectionUtils.isEmpty(adcdList)) {
                        return null;
                    }
                    Optional<Map<String, Object>> optMap = tryGetFullExecParamsMap(x.getOriExecParamsContent());
                    if (!optMap.isPresent()) {
                        return null;
                    }
                    Map<String, Object> map = optMap.get();
                    Long eid = x.getEid();
                    adcdList.forEach(y -> {
                        deviceIdSet.add(y.getDeviceId());
                        String[] result = collectConfigService.modifyExecParamsByMap(map, y.getId(),
                                false, eid, true, x.getExecParamsModelCode());
                        y.setExecParamsVersion(result[0]);
                        y.setExecParamsContent(result[1]);
                    });
                    return adcdList;
                })
                .filter(Objects::nonNull)
                .flatMap(Collection::stream)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(needUpdateAdcdList)) {
            return BaseResponse.ok(0);
        }
        //批量保存
        Integer result = deviceMapper.batchUpdateDeviceExecParamsContent(needUpdateAdcdList);
        executeUpgradeToDcdp(getDeviceUpgradeToDcdpDetail(needUpdateAdcdList.stream().map(x -> {
            AiopsUpgradeDcdpRequest.DeviceUpgrade deviceUpgrade =
                    new AiopsUpgradeDcdpRequest.DeviceUpgrade(x.getDeviceId(),
                            DeviceUpgradeEnum.DEVICE_COLLECT_DETAIL);
            deviceUpgrade.getOtherConditionMap().put("adcd.id", x.getId());
            deviceUpgrade.setNeedSaveOperationLog(true);
            Map<String, Object> operationInfoMap = deviceUpgrade.getOperationInfoMap();
            operationInfoMap.put("operationObjectClassification", "device_collect_detail");
            operationInfoMap.put("operationObject", "device_collect_detail");
            operationInfoMap.put("operationBehavior", "editdevicecollectdetailinstance");
            return deviceUpgrade;
        }).collect(Collectors.toList())));
        //清缓存
        deviceIdSet.forEach(x -> collectConfigService.clearDeviceIdCollectConfigCache(x));
        return BaseResponse.ok(result);
    }

    @Override
    public BaseResponse getDeviceInstanceMappingByAicList(List<AiopsItemContext> aicList) {
        //region 参数检查

        Optional<BaseResponse> optResponse = checkParamIsEmpty(aicList, "aicList");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        //endregion

        return BaseResponse.ok(deviceMapper.selectDeviceInstanceMappingByAicList(aicList));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addModuleCollectForDevice(AiopsItemContext aiopsItemContext) {
        //region 参数检查

        if (aiopsItemContext == null) {
            return;
        }

        if (!BooleanUtils.toBoolean(aiopsItemContext.getIsAddInstanceCollectToDevice())) {
            return;
        }

        String deviceId = aiopsItemContext.getSourceDeviceId();
        boolean adcdOnlyMappingByAiId = BooleanUtils.toBoolean(aiopsItemContext.getAdcdOnlyMappingByAiId());
        if (StringUtils.isBlank(deviceId) && !adcdOnlyMappingByAiId) {
            return;
        }

        Long aiId = aiopsItemContext.getAiId();
        if (LongUtil.isEmpty(aiId)) {
            return;
        }

        Long adimId = aiopsItemContext.getAdimId();

        //endregion

        //獲取是否有指定收集項Id列表
        Map<String, Object> map = new HashMap<>();
        Collection<Long> addAccIdList = aiopsItemContext.getAddAccIdList();
        if (CollectionUtils.isEmpty(addAccIdList)) {
            //如果沒有指定收集項Id列表，查询当前模组对应的设备收集项明细，如果已经存在，就不做处理，离开
            List<DeviceCollectDetail> adcdList = deviceMapper.selectAdcdListByAiId(aiId, adimId, false);
            if (!CollectionUtils.isEmpty(adcdList)) {
                aiopsItemContext.setAddedAdcdIdResult("add adcd ignore by exist adcd id " +
                        adcdList.stream().map(x -> LongUtil.safeToString(x.getId()))
                                .collect(Collectors.joining(",")));
                return;
            }
        } else {
            map.put("addAccIdList", addAccIdList);
        }

        Long adcId;
        if (adcdOnlyMappingByAiId) {
            adcId = null;
        } else {
            //获取设备(单头)
            AiopsKitDevice device = deviceMapper.selectDeviceByDeviceId(deviceId);
            if (device == null) {
                //若设备本身不存在，后续不处理(理论上不存在此状况)
                return;
            }
            //获取设备收集项(子单头)
            DeviceCollect deviceCollect = dealDeviceCollect(device.getId(), deviceId);
            adcId = deviceCollect.getId();
            //填充设备类型列表
            List<AiopsKitDeviceTypeMapping> adtmList = device.getDeviceTypeMappingList();
            if (!CollectionUtils.isEmpty(adtmList)) {
                List<String> deviceTypeList = adtmList.stream().filter(Objects::nonNull)
                        .map(AiopsKitDeviceTypeMapping::getDeviceType).collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(deviceTypeList)) {
                    map.put("collectDeviceTypeList", deviceTypeList);
                }
            }
            //设备的平台要包含在收集项的平台里面
            DevicePlatform platform = device.getPlatform();
            if (platform != null) {
                map.put("devicePlatform", platform.name());
            }
            map.put("aiIdNotInDevice", aiId);
            map.put("adimId", adimId);
        }
        map.put("scopeId", DEFAULT_SCOPE_ID);
        //查模组下面的所有收集项
        map.put("samcdId", aiopsItemContext.getSamcdId());
        //查询租户有效的运维商运维模组类别明细Id列表，后面做收集项过滤
        Long eid = aiopsItemContext.getEid();
        if (!LongUtil.isEmpty(eid)) {
            Long sid = RequestUtil.getHeaderSidOrDefault(defaultSid);
            BaseResponse<List<Long>> response = getValidSamcdIdListCore(sid, eid,
                    Stream.of(aiopsItemContext.getAiopsItem()).collect(Collectors.toList()));
            if (response.checkIsSuccess()) {
                List<Long> validSamcdIdList = response.getData();
                aiopsItemContext.setValidSamcdIdResult(validSamcdIdList.stream()
                        .map(LongUtil::safeToString).collect(Collectors.joining(",")));
                map.put("validSamcdIdList", validSamcdIdList);
            } else {
                aiopsItemContext.setValidSamcdIdResult("error by response code:" + response.getCode() +
                        " not zero, errMsg:" + response.getErrMsg());
            }
        }
        //产品应用比较特殊，要单独处理
        if (PRODUCT_APP.equals(aiopsItemContext.getAiopsItemType())
                || DATA_SRV.equals(aiopsItemContext.getAiopsItemType())
        ) {
            //如果是产品应用/服务，运维项目即为产品代号
            map.put("appId", aiopsItemContext.getAiopsItem());
        }

        List<ModuleCollectMapping> amcmList = moduleCollectMapper.selectAiopsModuleCollectMappingByMap(map);
        batchDeviceCollectDetailByAmcmList(eid, adcId, deviceId, aiopsItemContext, amcmList);
    }

    private BaseResponse<List<Long>> getValidSamcdIdListCore(Long sid, Long eid, Collection<String> aiopsItemList) {
        int retryCount = getValidSamcdIdRetryCount;
        Exception lastEx;
        do {
            try {
                return aioUserFeignClient.getTenantValidSamcdIdList(sid, eid, aiopsItemList);
            } catch (Exception ex) {
                lastEx = ex;
            }
            retryCount--;
            if (retryCount <= 0) {
                break;
            }
            //休息几秒钟再继续
            try {
                Thread.sleep(1500);
            } catch (Exception ex) {
                //不太需要管此异常
            }
        } while (true);
        log.error("getTenantValidSamcdIdList retry count " + getValidSamcdIdRetryCount + " last exception:", lastEx);
        return BaseResponse.dynamicError(ResponseCode.GET_TENANT_VALID_SAMCD_ID_LIST_ERROR,
                String.join(",", aiopsItemList), lastEx.toString());
    }

    //设备批量新增收集项
    private void batchDeviceCollectDetailByAmcmList(Long eid, Long adcId, String deviceId,
                                                    AiopsItemContext aiopsItemContext,
                                                    List<ModuleCollectMapping> amcmList) {
        if (CollectionUtils.isEmpty(amcmList)) {
            return;
        }

        //填充执行参数
        Long aiId = aiopsItemContext.getAiId();
        Long adimId = aiopsItemContext.getAdimId();
        String execParamsModelCode = aiopsItemContext.getExecParamsModelCode();
        String oriExecParamsContent = aiopsItemContext.getOriExecParamsContent();
        List<Long> execParamsSeelctAccIdList = aiopsItemContext.getExecParamsSelectAccIdList();
        Map<String, Pair<DeviceCollectDetail, Map<String, Object>>> adcdExecParamsMapCache = new HashMap<>();
        String[] emptyResult;
        Map<String, Object> map;
        if (StringUtils.isBlank(execParamsModelCode) || StringUtils.isBlank(oriExecParamsContent)) {
            //没有执行参数，填空
            emptyResult = collectConfigService.modifyExecParams("", 0L, false);
            map = new HashMap<>();
        } else {
            //有执行参数，反序列化，后面继续处理
            emptyResult = null;
            map = SerializeUtil.JsonDeserialize(oriExecParamsContent, Map.class);
        }
        List<Map<String, Object>> delayExecuteList = new ArrayList<>(amcmList.size());
        Boolean reAuthStatus = aiopsItemContext.getReAuthStatus();
        //批量新增收集项到设备中
        List<DeviceCollectDetail> deviceCollectDetailList = amcmList.stream()
                .filter(Objects::nonNull)
                .map(x -> {
                    Long accId = x.getAccId();
                    DeviceCollectDetail deviceCollectDetail = createNewAdcd(adcId, accId, aiId, adimId,
                            x.getCollectName(), x.getIsEnable());
                    Long adcdId = deviceCollectDetail.getId();
                    //填充执行参数
                    String[] result;
                    if (Objects.isNull(emptyResult)) {
                        //若没有空执行参数结果要储存执行参数到大数据平台
                        result = collectConfigService.modifyExecParamsByMap(map, adcdId, false, eid,
                                true, execParamsModelCode);
                    } else {
                        //若有空执行参数结果，判断下是否有执行参数
                        String execParamsContent;
                        boolean hasExecParams = x.getHasExecParams();
                        String accExecParamsModelCode = x.getExecParamsModelCode();

                        Optional<String[]> alternateResult;
                        if (hasExecParams && StringUtils.isNotBlank(execParamsContent = x.getExecParamsContent())) {
                            //收集项本身有执行参数，这里进行设置
                            result = collectConfigService.modifyExecParamsByFullContent(execParamsContent, adcdId,
                                    false, eid, true, accExecParamsModelCode);
                        } else if (hasExecParams && (alternateResult = processAlternateAccIdList(eid, adcId, adcdId, aiId,
                                accExecParamsModelCode, execParamsSeelctAccIdList, adcdExecParamsMapCache)).isPresent()) {
                            result = alternateResult.get();
                        } else {
                            //没有执行参数直接设置为空结果
                            result = emptyResult;
                        }
                    }
                    deviceCollectDetail.setExecParamsVersion(result[0]);
                    deviceCollectDetail.setExecParamsContent(result[1]);
                    //因为规则引擎需要查询，因此将添加预警相关内容抽到添加完成设备收集项明细后执行
                    Map<String, Object> delayMap = new HashMap<>(2);
                    delayMap.put("adcdId", adcdId);
                    // 新设备接入

                    List<CollectWarning> acwList = getCollectWarningList(deviceId, x.getCollectWarningList());
                    if (Objects.nonNull(reAuthStatus)) {
                        //若有指定授权状态，这里进行填写
                        acwList.forEach(y -> y.setReAuthStatus(reAuthStatus));
                    }
                    delayMap.put("awcList", acwList);
                    delayExecuteList.add(delayMap);
                    return deviceCollectDetail;
                }).collect(Collectors.toList());
        log.info("batchDeviceCollectDetailByAmcmList");
        //批量新增收集项
        deviceMapper.batchDeviceCollectDetail(deviceCollectDetailList);
        aiopsItemContext.getUpgradeDcdpDetailList().addAll(getDeviceUpgradeToDcdpDetail(deviceCollectDetailList
                .stream().map(x -> {
                    AiopsUpgradeDcdpRequest.DeviceUpgrade deviceUpgrade =
                            new AiopsUpgradeDcdpRequest.DeviceUpgrade(deviceId, DeviceUpgradeEnum.DEVICE_COLLECT_DETAIL);
                    deviceUpgrade.getOtherConditionMap().put("adcd.id", x.getId());
                    if (StringUtils.isBlank(deviceId)) {
                        deviceUpgrade.getOtherConditionMap().put("adcd.aiId", x.getAiId());
                    }
                    //考虑可能是异步进来的，因此从aic拿出来使用
                    Map<String, Object> aicOperationInfoMap = aiopsItemContext.getOperationInfoMap();
                    Map<String, Object> operationInfoMap;
                    if (CollectionUtils.isEmpty(aicOperationInfoMap)) {
                        operationInfoMap = deviceUpgrade.getOperationInfoMap();
                    } else {
                        //为确保不污染，(浅)拷贝一份新的
                        operationInfoMap = new HashMap<>(aicOperationInfoMap);
                    }
                    deviceUpgrade.setNeedSaveOperationLog(true);
                    operationInfoMap.put("operationObjectClassification", "device");
                    operationInfoMap.put("operationObject", "device");
                    operationInfoMap.put("operationBehavior", "adddevicecollectconfig");
                    deviceUpgrade.setOperationInfoMap(operationInfoMap);
                    return deviceUpgrade;
                }).collect(Collectors.toList())));
        //产生设备收集项相关预警项
        delayExecuteList.forEach(x -> batchDeviceCollectWarning(LongUtil.objectToLong(x.get("adcdId")),
                (List<CollectWarning>) x.get("awcList"), eid, deviceId));
        BiConsumer<AiopsItemContext, List<DeviceCollectDetail>> addedAdcdConsumer =
                aiopsItemContext.getAddedAdcdConsumer();
        if (addedAdcdConsumer != null) {
            //播发回调(如果有的話)
            addedAdcdConsumer.accept(aiopsItemContext, deviceCollectDetailList);
        }
        aiopsItemContext.setAddedAdcdIdResult(deviceCollectDetailList.stream()
                .map(x -> LongUtil.safeToString(x.getId())).collect(Collectors.joining(",")));
        if (aiopsItemContext.getNeedClearDeviceCollectCache() && StringUtils.isNotBlank(deviceId)) {
            //清除设备缓存
            collectConfigService.clearDeviceIdCollectConfigCache(deviceId);
        }
    }

    private Optional<String[]> processAlternateAccIdList(
            Long eid, Long adcId, Long adcdId, Long aiId, String execParamsModelCode,
            List<Long> execParamsSelectAccIdList,
            Map<String, Pair<DeviceCollectDetail, Map<String, Object>>> adcdExecParamsMapCache) {
        if (CollectionUtils.isEmpty(execParamsSelectAccIdList)) {
            return Optional.empty();
        }
        Pair<DeviceCollectDetail, Map<String, Object>> adcdExecParamsMap;
        if (adcdExecParamsMapCache.containsKey(execParamsModelCode)) {
            //键存在表示曾经尝试获取过，但有可能是null
            adcdExecParamsMap = adcdExecParamsMapCache.get(execParamsModelCode);
        } else {
            //表示存在备选项，进行选择填充
            Map<String, Object> conditionMap = new HashMap<>(4);
            conditionMap.put("accIdList", execParamsSelectAccIdList);
            conditionMap.put("adcId", adcId);
            conditionMap.put("execParamsModelCode", execParamsModelCode);
            conditionMap.put("priorityMatchAiId", aiId);
            List<DeviceCollectDetail> alternateAdcdList = deviceMapper.selectDeviceCollectDetailByAdcId(conditionMap);
            if (CollectionUtils.isEmpty(alternateAdcdList)) {
                //找不到结果，填null
                adcdExecParamsMap = null;
            } else {
                //依序选择(通常备选不会太多，直接循环找)
                adcdExecParamsMap = execParamsSelectAccIdList.stream()
                        .filter(LongUtil::isNotEmpty)
                        .map(y -> alternateAdcdList.stream().filter(z -> y.equals(z.getAccId())).findFirst())
                        .filter(Optional::isPresent)
                        .map(y -> Pair.of(y.get(), tryGetLocalExecParamsMap(y.get().getExecParamsContent())))
                        .filter(y -> y.getRight().isPresent())
                        .map(y -> Pair.of(y.getLeft(), parseToFullExecParamsMap(y.getRight().get())))
                        .findFirst().orElse(null);
            }
            //缓存起来，减少查找次数
            adcdExecParamsMapCache.put(execParamsModelCode, adcdExecParamsMap);
        }
        if (Objects.isNull(adcdExecParamsMap)) {
            return Optional.empty();
        }
        return Optional.of(collectConfigService.modifyExecParamsByMap(adcdExecParamsMap.getRight(),
                adcdId, false, eid, true,
                adcdExecParamsMap.getLeft().getExecParamsModelCode()));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BaseResponse modifyDeviceDeletedStatusByAicList(List<AiopsItemContext> aicList,
                                                           AiopsAuthStatus authStatus) {
        //region 参数检查

        Optional<BaseResponse> optResponse = checkParamIsEmpty(aicList, "aicList");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        if (authStatus != AiopsAuthStatus.INVALID && authStatus != AiopsAuthStatus.UNAUTH) {
            //非作废或者非取消作废，直接返回成功
            return BaseResponse.ok();
        }

        List<String> deviceIdList = aicList.stream().filter(Objects::nonNull)
                .filter(x -> DEVICE.equals(x.getAiopsItemType()))
                .map(AiopsItemContext::getSourceDeviceId)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(deviceIdList)) {
            return BaseResponse.ok();
        }
        // 增加数据库实例授权收回
        if (authStatus == AiopsAuthStatus.INVALID) {
            List<DBIdTmcdIdMapping> dbIdMappingList = deviceMapper.selectDbIdsByDeviceIdList(deviceIdList);

            for (DBIdTmcdIdMapping dbIdMapping : dbIdMappingList) {
                deviceMapper.updateDbInstanceAuthStatusUnauth(dbIdMapping.getDbId());

                Map<String, Object> tmcdUseCountMap = new HashMap<>(2);
                tmcdUseCountMap.put("tmcdId", dbIdMapping.getTmcdId());
                aioUserFeignClient.fixTmcdUseCount(Stream.of(tmcdUseCountMap).collect(Collectors.toList()));
            }


        }

        //endregion

        return BaseResponse.ok(deviceMapper.updateDeviceDeleteStatus(deviceIdList,
                authStatus == AiopsAuthStatus.INVALID));
    }

    @Override
    public BaseResponse getDeviceCollectDetailByAiopsItemId(String deviceId, String aiopsItemId) {
        //region 参数检查

        Optional<BaseResponse> optResponse = checkParamIsEmpty(deviceId, "deviceId");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        optResponse = checkParamIsEmpty(aiopsItemId, "aiopsItemId");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        //endregion

        return processLayeredInfo(deviceMapper.selectDeviceCollectDetailByAiopsItemId(deviceId, aiopsItemId),
                true);
    }

    private BaseResponse processLayeredInfo(List<ModuleCollectLayered> moduleCollectLayeredList,
                                            boolean needDCDPInfo) {
        if (CollectionUtils.isEmpty(moduleCollectLayeredList)) {
            return BaseResponse.ok(new ArrayList<>(0));
        }
        Map<Long, ModuleCollectMapping> v2AccIdMcmMap = new HashMap<>();
        Map<Long, ModuleCollectLayered> moduleCollectLayeredMap = moduleCollectLayeredList.stream()
                .filter(Objects::nonNull)
                .peek(x -> Optional.ofNullable(x.getModuleCollectMappingList()).ifPresent(y ->
                        y.forEach(moduleCollect -> {
                            boolean settingCron = settingCron(moduleCollect.getCollectType(),
                                    moduleCollect.getCollectorModelCode());
                            moduleCollect.setSettingCron(settingCron);
                            if ("v2".equalsIgnoreCase(moduleCollect.getRuntimeVersion())) {
                                v2AccIdMcmMap.put(moduleCollect.getAccId(), moduleCollect);
                            }
                        }))).collect(Collectors.toMap(ModuleCollectLayered::getSamclId, x -> x, (x, y) -> y));
        BaseResponse response =
                aioUserFeignClient.getSupplierAiopsModuleClassDetailBySamclIdList(moduleCollectLayeredMap.keySet());
        if (!response.checkIsSuccess()) {
            return response;
        }
        if (CollectionUtil.isNotEmpty(v2AccIdMcmMap) && needDCDPInfo) {
            //v2特殊字段从数采批量取得
            Map<String, Object> map = new HashMap<>(4);
            map.put("accIdList", v2AccIdMcmMap.keySet());
            map.put("specialColumns", new String[]{"acc.id", "acc.isDataSync", "acc.isOldDataSync"});
            BaseResponse<List<Map<String, Object>>> dcdpAccRes = restItemsUtil.getAccSimpleInfoByMap(map);
            if (!dcdpAccRes.checkIsSuccess()) {
                return dcdpAccRes;
            }
            List<Map<String, Object>> mapList = dcdpAccRes.getData();
            if (CollectionUtil.isNotEmpty(mapList)) {
                mapList.forEach(x -> {
                    Long accId = LongUtil.objectToLong(x.get("id"));
                    if (LongUtil.isEmpty(accId)) {
                        return;
                    }
                    ModuleCollectMapping mcm = v2AccIdMcmMap.get(accId);
                    if (Objects.isNull(mcm)) {
                        return;
                    }
                    mcm.setIsDataSync(BooleanUtil.objectToBoolean(x.get("isDataSync")));
                    mcm.setIsOldDataSync(BooleanUtil.objectToBoolean(x.get("isOldDataSync")));
                });
            }
        }
        Optional<List<SupplierAiopsModuleCollectLayered>> optSamclList = response.checkAndGetCurrentData();
        List<SupplierAiopsModuleCollectLayered> newSamclList;
        if (!optSamclList.isPresent() || CollectionUtils.isEmpty(newSamclList = optSamclList.get())) {
            //若没有取到名称，先填入未知
            moduleCollectLayeredList.forEach(this::setUnknownCollectLayered);
            return BaseResponse.ok(moduleCollectLayeredList);
        }
        newSamclList.forEach(x -> {
            Long key = x.getId();
            ModuleCollectLayered mcl = moduleCollectLayeredMap.get(key);
            if (mcl == null) {
                return;
            }
            BeanUtils.copyProperties(x, mcl);
            moduleCollectLayeredMap.remove(key);
        });
        if (!CollectionUtils.isEmpty(moduleCollectLayeredMap)) {
            //若有没找到层级的就填充未知(一般此状况属于基础数据没有维护造成)
            moduleCollectLayeredMap.values().forEach(this::setUnknownCollectLayered);
        }
        //最终排序一下，确保level小的在前面
        moduleCollectLayeredList = moduleCollectLayeredList.stream()
                .sorted((x, y) -> ObjectUtils.compare(x.getLevel(), y.getLevel()))
                .collect(Collectors.toList());
        return BaseResponse.ok(moduleCollectLayeredList);
    }

    private void setUnknownCollectLayered(ModuleCollectLayered mcl) {
        if (mcl == null) {
            return;
        }
        String unknown = "UNKNOWN";
        mcl.setLevel(0);
        mcl.setName(unknown);
        mcl.setName_CN(unknown);
        mcl.setName_TW(unknown);
    }

    @Override
    public BaseResponse getDeviceAiopsInstanceByAiopsItemType(String deviceId, String aiopsItemGroup,
                                                              String aiopsItemType) {
        //region 参数检查

        Optional<BaseResponse> optResponse = checkParamIsEmpty(deviceId, "deviceId");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        if (StringUtils.isBlank(aiopsItemGroup) && StringUtils.isBlank(aiopsItemType)) {
            return BaseResponse.dynamicError(ResponseCode.PARAM_IS_EMPTY, "aiopsItemGroup and aiopsItemType");
        }

        //endregion


        Long sid = RequestUtil.getHeaderSidOrDefault(defaultSid);

        List<DeviceAiopsItemInstance> deviceAiopsInstanceList =
                deviceMapper.selectDeviceAiopsInstanceByAiopsItemType(deviceId, getAigCodeList(aiopsItemGroup), aiopsItemType);
        findDisplayNameByModel(sid, deviceAiopsInstanceList);
        return BaseResponse.ok(deviceAiopsInstanceList);
    }

    private List<String> getAigCodeList(String aiopsItemGroup) {
        List<AiopsItemGroup> aigList = deviceMapper.selectAiopsItemGroup();
        Map<String, AiopsItemGroup> codeMap = aigList.stream()
                .collect(Collectors.toMap(AiopsItemGroup::getCode, Function.identity()));
        Map<Long, AiopsItemGroup> idMap = aigList.stream()
                .filter(aig -> Objects.nonNull(aig.getParentId()))
                .collect(Collectors.toMap(AiopsItemGroup::getParentId, Function.identity()));

        AiopsItemGroup aig = codeMap.get(aiopsItemGroup);
        List<String> aigCodeList = new ArrayList<>();

        while (aig != null) {
            aigCodeList.add(aig.getCode());
            aig = idMap.get(aig.getId());
        }
        return aigCodeList;
    }

    @Override
    public BaseResponse getDeviceAiopsItemSetting(String deviceId, String aiopsItemGroup, String aiopsItemType,
                                                  Boolean needPaging, Integer pageNum, Integer pageSize) {
        //region 参数检查

        Optional<BaseResponse> optResponse = checkParamIsEmpty(deviceId, "deviceId");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        //endregion

        if (BooleanUtils.toBoolean(needPaging)) {
            pageNum = IntegerUtil.isEmpty(pageNum) ? DEFAULT_PAGE_NO : pageNum;
            pageSize = IntegerUtil.isEmpty(pageSize) ? DEFAULT_PAGE_SIZE : pageSize;
            Page page = PageHelper.startPage(pageNum, pageSize);
            deviceMapper.selectDeviceAiopsItemSetting(deviceId, aiopsItemGroup, aiopsItemType);
            PageInfo<DeviceInfo> pageInfo = new PageInfo<>(page);
            return BaseResponse.ok(pageInfo);
        }
        return BaseResponse.ok(deviceMapper.selectDeviceAiopsItemSetting(deviceId, aiopsItemGroup, aiopsItemType));
    }

    private void findDisplayNameByModel(Long sid, List<DeviceAiopsItemInstance> daiiList) {
        if (CollectionUtils.isEmpty(daiiList)) {
            return;
        }
        List<DeviceAiopsInstance> daiList = daiiList.stream().map(DeviceAiopsItemInstance::getDeviceAiopsInstanceList)
                .filter(x -> !CollectionUtils.isEmpty(x)).flatMap(Collection::stream).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(daiList)) {
            return;
        }
        Map<String, List<DeviceAiopsInstance>> codeGroupMap = daiList.stream().filter(Objects::nonNull)
                .filter(x -> BooleanUtils.toBoolean(x.getFindDisplayNameByModel()))
                .filter(x -> StringUtils.isNotBlank(x.getDisplayName()))
                .collect(Collectors.groupingBy(DeviceAiopsInstance::getDisplayName));
        if (CollectionUtils.isEmpty(codeGroupMap)) {
            return;
        }
        setDisplayNameByModel(sid, codeGroupMap.keySet(), (x) -> {
            String code = x.getKey();
            List<DeviceAiopsInstance> currentDaiList = codeGroupMap.get(code);
            if (CollectionUtils.isEmpty(currentDaiList)) {
                return;
            }
            String name = x.getValue();
            currentDaiList.forEach(y -> y.setDisplayName(name));
        });
        //再做一次排序
        daiiList.forEach(x -> x.setDeviceAiopsInstanceList(x.getDeviceAiopsInstanceList().stream()
                .sorted(Comparator.comparing(DeviceAiopsInstance::getDisplayName)).collect(Collectors.toList())));
    }

    private void setDisplayNameByModel(Long sid, Collection<String> codeCollection,
                                       Consumer<Map.Entry<String, String>> setter) {
//        BaseResponse<Map<String, String>> response = aioCmdbFeignClient.getModelNameMapByCodeList(sid,
//                codeCollection);
        BaseResponse<Map<String, String>> response = restUtil.getModelNameMapByCodeList(sid, codeCollection);
        if (!response.checkIsSuccess()) {
            return;
        }
        Map<String, String> codeNameMap = response.getData();
        if (CollectionUtils.isEmpty(codeNameMap)) {
            return;
        }
        codeNameMap.entrySet().stream().filter(Objects::nonNull).forEach(setter);
    }

    @Override
    public BaseResponse getDeviceAiopsInstanceCollectByAiopsItemId(String deviceId, String aiopsItemId) {
        //region 参数检查

        Optional<BaseResponse> optResponse = checkParamIsEmpty(deviceId, "deviceId");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        optResponse = checkParamIsEmpty(aiopsItemId, "aiopsItemId");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        //endregion

        //查询租户有效的运维商运维模组类别明细Id列表，后面做收集项过滤
        List<Long> validSamcdIdList = null;
        Long eid = deviceMapper.selectEidByDeviceId(deviceId);
        if (!LongUtil.isEmpty(eid)) {
            Long sid = RequestUtil.getHeaderSidOrDefault(defaultSid);
            //透过运维项目Id查询运维实例
            BaseResponse<List<AiopsInstance>> response = instanceService.getAiopsInstanceByAiopsItemIdList(
                    Stream.of(aiopsItemId).collect(Collectors.toList()));
            List<AiopsInstance> aiList;
            if (!response.checkIsSuccess()) {
                return response;
            } else if (CollectionUtils.isEmpty(aiList = response.getData())) {
                return BaseResponse.dynamicError(aiopsItemId, ResponseCode.AIOPS_INSTANCE_NOT_EXIST, aiopsItemId);
            }
            //optAiList如果是null，就会返回失败，因此这里可以直接使用
            List<String> aiopsItemList = aiList.stream().map(AiopsInstance::getAiopsItem).collect(Collectors.toList());
            //填充运维实例的运维项目
            BaseResponse<List<Long>> samcdIdListResponse = getValidSamcdIdListCore(sid, eid, aiopsItemList);
            if (!samcdIdListResponse.checkIsSuccess()) {
                return samcdIdListResponse;
            }
            validSamcdIdList = samcdIdListResponse.getData();
        }

        return processLayeredInfo(deviceMapper.selectDeviceAiopsInstanceCollectByAiopsItemId(deviceId, aiopsItemId,
                validSamcdIdList), false);
    }

    @Override
    public BaseResponse addDeviceCollectDetailByModuleCollectLayered(String deviceId, String aiopsItemId,
                                                                     List<ModuleCollectLayered> mclList) {
        //region 参数检查

        Optional<BaseResponse> optResponse = checkParamIsEmpty(deviceId, "deviceId");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        optResponse = checkParamIsEmpty(aiopsItemId, "aiopsItemId");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        //endregion

        //透过运维项目Id串查运维实例
        BaseResponse response = instanceService.getAiopsInstanceByAiopsItemId(aiopsItemId);
        Optional<AiopsInstance> optAi = response.checkAndGetCurrentData();
        if (!optAi.isPresent()) {
            return BaseResponse.dynamicError(ResponseCode.AIOPS_INSTANCE_NOT_EXIST, aiopsItemId);
        }
        AiopsInstance ai = optAi.get();
        //查询出设备中该实例已经存在的收集项Id列表
        List<Long> existDeviceAiAccIdList = deviceMapper.selectDeviceCollectAccIdByAiId(deviceId, ai.getId());
        Set<Long> existDeviceAiAccId;
        if (CollectionUtils.isEmpty(existDeviceAiAccIdList)) {
            existDeviceAiAccId = new HashSet<>(0);
        } else {
            existDeviceAiAccId = new HashSet<>(existDeviceAiAccIdList);
        }

        List<ModuleCollectMapping> mcmList;
        if (CollectionUtils.isEmpty(mclList)) {
            mcmList = new ArrayList<>(0);
        } else {
            //整理出要新增的收集项(顺便排除掉实例已经添加到设备过的收集项)
            mcmList = mclList.stream().filter(Objects::nonNull)
                    .map(ModuleCollectLayered::getModuleCollectMappingList)
                    .filter(Objects::nonNull)
                    .flatMap(Collection::stream)
                    .filter(x -> !existDeviceAiAccId.contains(x.getAccId()))
                    .collect(Collectors.toList());
        }

        Long aiId = ai.getId();
        //获取设备收集项(子单头)
        DeviceCollect deviceCollect = dealDeviceCollect(0L, deviceId);
        AiopsItemContext aic = new AiopsItemContext(ai.getAiopsItem(), aiopsItemId, deviceId);
        aic.setAiId(aiId);
        aic.setExecParamsModelCode(ai.getExecParamsModelCode());
        aic.setOriExecParamsContent(ai.getOriExecParamsContent());
        List<AiopsUpgradeDcdpDetail> upgradeDcdpDetailList = aic.getUpgradeDcdpDetailList();

        //查询运维实例与设备的映射
        List<DeviceInstanceMapping> dimList = deviceMapper.selectDeviceInstanceMappingByAicList(Stream.of(aic)
                .collect(Collectors.toList()));
        if (CollectionUtils.isEmpty(dimList)) {
            //若关系不存在，就建立关系
            AiopsKitDevice ad = deviceMapper.selectDeviceByDeviceId(deviceId);
            if (ad == null) {
                return BaseResponse.dynamicError(deviceId, ResponseCode.DEVICE_NOT_FOUND, deviceId);
            }
            DeviceInstanceMapping dim = new DeviceInstanceMapping(ad.getId(), deviceId, aiId, true);
            deviceMapper.batchInsertOrUpdateAiopsDeviceInstanceMapping(Stream.of(dim).collect(Collectors.toList()));
            //严谨要再查一次
            dimList = deviceMapper.selectDeviceInstanceMappingByAicList(Stream.of(aic)
                    .collect(Collectors.toList()));
        }
        //理論上要能取到，沒取到讓其拋異常
        DeviceInstanceMapping dim = dimList.get(0);
        Long adimId = dim.getId();
        if (!BooleanUtils.toBoolean(dim.getHasAddedAiopsInstance())) {
            //更新设备运维实例映射，表示已添加运维实例
            deviceMapper.updateAdimHasAddedAiopsInstance(adimId, true);
        }
        upgradeDcdpDetailList.addAll(getDeviceUpgradeToDcdpDetail(dimList.stream().map((x) -> {
            AiopsUpgradeDcdpRequest.DeviceUpgrade deviceUpgrade =
                    new AiopsUpgradeDcdpRequest.DeviceUpgrade(deviceId, DeviceUpgradeEnum.DEVICE_INSTANCE_MAPPING);
            deviceUpgrade.setContainRelateInstance(false);
            deviceUpgrade.getOtherConditionMap().put("adim.id", x.getId());
            deviceUpgrade.setNeedSaveOperationLog(true);
            Map<String, Object> operationInfoMap = deviceUpgrade.getOperationInfoMap();
            operationInfoMap.put("operationObjectClassification", "instance");
            operationInfoMap.put("operationObject", "instance");
            if (StringUtils.isBlank(Objects.toString(operationInfoMap.get("operationBehavior"), ""))) {
                operationInfoMap.put("operationBehavior", "deviceaddinstance");
            }
            return deviceUpgrade;
        }).collect(Collectors.toList())));
        aic.setAdimId(adimId);
        batchDeviceCollectDetailByAmcmList(ai.getEid(), deviceCollect.getId(), deviceId, aic, mcmList);
        response = executeUpgradeToDcdp(upgradeDcdpDetailList);
        if (!response.checkIsSuccess()) {
            return response;
        }
        return BaseResponse.ok(mcmList.size());
    }

    @Override
    public BaseResponse getDeviceMappingAiopsInstanceItemType(String deviceId) {
        //region 参数检查

        Optional<BaseResponse> optResponse = checkParamIsEmpty(deviceId, "deviceId");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        //endregion

        return BaseResponse.ok(deviceMapper.selectDeviceMappingAiopsInstanceItemType(deviceId));
    }

    @Override
    public BaseResponse getDeviceMappingAiopsInstanceItemGroup(String deviceId) {
        //region 参数检查

        Optional<BaseResponse> optResponse = checkParamIsEmpty(deviceId, "deviceId");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        //endregion
        List<AiopsItemGroup> aiopsItemGroupList = deviceMapper.selectDeviceMappingAiopsInstanceItemGroup(deviceId);
        aiopsItemGroupList.forEach(aig -> {
            if (Objects.nonNull(aig.getParentId())) {
                AiopsItemGroup topParent = findTopParent(aig.getParentId());
                BeanUtils.copyProperties(topParent, aig);
            }
        });

        return BaseResponse.ok(aiopsItemGroupList);
    }


    public AiopsItemGroup findTopParent(Long id) {
        AiopsItemGroup aiopsItemGroup = deviceMapper.selectAiopsItemGroupById(id);// 从数据源中查询当前ID的实体
        if (aiopsItemGroup != null && aiopsItemGroup.getParentId() != null && aiopsItemGroup.getParentId() != 0) {

            return findTopParent(aiopsItemGroup.getParentId()); // 递归查找父项
        }
        return aiopsItemGroup; // 当parentId为空时，返回aiopsItemGroup
    }

    @Override
    public BaseResponse addDeviceAiopsInstanceMapping(AiopsItemContext aiopsItemContext,
                                                      boolean clearDeviceCollectConfigCache) {
        //region 参数检查

        Optional<BaseResponse> optResponse = checkParamIsEmpty(aiopsItemContext, "aiopsItemContext");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        String deviceId = aiopsItemContext.getSourceDeviceId();
        optResponse = checkParamIsEmpty(deviceId, "aiopsItemContext.deviceId");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        String aiopsItem = aiopsItemContext.getAiopsItem();
        optResponse = checkParamIsEmpty(aiopsItem, "aiopsItemContext.aiopsItem");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        //endregion

        AiopsKitDevice ad = deviceMapper.selectDeviceByDeviceId(deviceId);
        if (ad == null) {
            return BaseResponse.dynamicError(deviceId, ResponseCode.DEVICE_NOT_FOUND, deviceId);
        }

        BaseResponse<List<AiopsItemContext>> response = createDeviceSelfInAicList(ad, aiopsItem);
        if (!response.checkIsSuccess()) {
            return response;
        }

        List<AiopsItemContext> aicList = response.getData();
        aicList.add(aiopsItemContext);

        return createAiopsInstanceByAiopsItemContext(ad.getEid(), ad, aicList, clearDeviceCollectConfigCache);
    }

    private BaseResponse createDeviceSelfInAicList(AiopsKitDevice ad, String filterAiopsItem) {
        String deviceId = ad.getDeviceId();
        List<AiopsKitDeviceTypeMapping> mapping = ad.getDeviceTypeMappingList();
        if (CollectionUtils.isEmpty(mapping)) {
            return BaseResponse.dynamicError(deviceId, ResponseCode.DEVICE_MAPPING_INSTANCE_ERROR_NOT_DEVICE_TYPE,
                    deviceId);
        }

        if (StringUtils.isNotEmpty(filterAiopsItem) &&
                mapping.stream().anyMatch(x -> filterAiopsItem.equals(x.getDeviceType()))) {
            //任一个需要检查的设备类型已经添加了，就直接返回
            return BaseResponse.ok(new ArrayList<AiopsItemContext>());
        }
        //这里这样添加，是为了让其去检查基础收集项，如果没有添加会自动添加到设备上
        return BaseResponse.ok(mapping.stream()
                .map(x -> new AiopsItemContext(x.getDeviceType(), deviceId, deviceId))
                .collect(Collectors.toList()));
    }

    @Override
    public BaseResponse addDeviceAiopsInstanceMappingByArc(AdimRelateContext arc) {
        //因为有异步进行的行为，这方法不进事务，不然会造成数据异常
        //region 参数检查

        Optional<BaseResponse> optResponse = checkParamIsEmpty(arc, "arc");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }
        String deviceId = arc.getDeviceId();
        optResponse = checkParamIsEmpty(deviceId, "arc.deviceId");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }
        String truestInstanceTableName = arc.getTruestInstanceTableName();
        checkParamIsEmpty(truestInstanceTableName, "arc.truestInstanceTableName");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }
        Map<String, Object> businessPKValueMap = arc.getBusinessPKValueMap();
        checkParamIsEmpty(businessPKValueMap, "arc.businessPKValueMap");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }
        String aiopsItemIdColumnName = arc.getAiopsItemIdColumnName();
        checkParamIsEmpty(aiopsItemIdColumnName, "arc.aiopsItemIdColumnName");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }
        String aiopsItem = arc.getAiopsItem();
        checkParamIsEmpty(aiopsItem, "arc.aiopsItem");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        //endregion

        //查真实表并取得aiopsItemId
        String aiopsItemId = instanceService.getAiopsItemIdByRelate(truestInstanceTableName, businessPKValueMap,
                aiopsItemIdColumnName);
        if (StringUtils.isBlank(aiopsItemId)) {
            return BaseResponse.ok(0);
        }

        //如果设备与实例映射已经存在，后面流程就不进行(减少服务端压力)
        Map<String, Object> conditionMap = new HashMap<>();
        conditionMap.put("aiopsItemId", aiopsItemId);
        conditionMap.put("aiopsItem", aiopsItem);
        conditionMap.put("deviceId", deviceId);
        List<Map<String, Object>> mapList = deviceMapper.selectExistAdimByMap(conditionMap);
        if (CollectionUtil.isNotEmpty(mapList)) {
            return BaseResponse.ok(0);
        }

        //组织上下文
        AiopsItemContext aic = new AiopsItemContext(aiopsItem, aiopsItemId, deviceId);
        aic.setIsAddInstanceCollectToDevice(false);
        //添加设备实例映射，以及设备基础添加
        BaseResponse response = addDeviceAiopsInstanceMapping(aic, false);
        if (!response.checkIsSuccess()) {
            return response;
        }
        //处理实例已经添加的设备收集项明细(移转到设备上)
        DeviceCollect adc = dealDeviceCollect(aic.getAdId(), deviceId);
        Map<String, Object> map = new HashMap<>(1);
        map.put("aiId", aic.getAiId());
        deviceMapper.updateAdcdAdcIdAndAdimIdByMap(adc.getId(), aic.getAdimId(), map);
        //刷新实例下所有规则引擎
        ruleEngineService.asyncModifyReAdditionalContentByMap(map);
        //刷新设备收集项缓存
        if (BooleanUtils.toBoolean(arc.getNeedClearDeviceCollectConfigCache())) {
            collectConfigService.clearDeviceIdCollectConfigCache(deviceId);
        }
        return response;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BaseResponse removeDeviceAiopsInstanceMapping(String deviceId, List<String> aiopsItemIdList) {
        //region 参数检查

        Optional<BaseResponse> optResponse = checkParamIsEmpty(deviceId, "deviceId");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        optResponse = checkParamIsEmpty(aiopsItemIdList, "aiopsItemIdList");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        //endregion

        List<AiopsUpgradeDcdpDetail> upgradeDcdpDetailList = new ArrayList<>();
        Map<String, Object> map = new HashMap<>(2);
        map.put("deviceId", deviceId);
        map.put("aiopsItemIdList", aiopsItemIdList);

        //删除规则引擎
        BaseResponse response = ruleEngineService.removeReResmRsByMap(map);
        if (!response.checkIsSuccess()) {
            return response;
        }
        processDcdpRemoveAdimAdcd(deviceId, map, upgradeDcdpDetailList);
        int result = deviceMapper.deleteAdimAdcdAdcwAdwnmByMap(map);
        if (result > 0) {
            // 特殊处理 删除 第三方设备实例 第三方设备实例的运维项目变化需要重新生成一个实例，并且旧的实例需要删除，才会再移除的时候删除第三方实例 应为第三方实例只会有一个
            // 删除ai表和 atmi表的数据
            // todo 如果想处理合约明细的数据 需要前端特殊传入是三方实例处理
            if (!CollectionUtils.isEmpty(aiopsItemIdList)) {
//                map.put("aiopsItemId", dbId);
//                List<AiopsInstance> aiList = instanceMapper.selectAiopsInstanceByMap(map);
//                new AiopsItemContext(x.getAiopsItem(), x.getAiopsItemId(), deviceId)
//                authorizeService.modifyInstanceAuthStatus(eid, AiopsAuthStatus.INVALID, aicList);
                //删除产品应用映射关系
                deviceMapper.deleteProductApp(deviceId, aiopsItemIdList);
                List<AiopsUpgradeDcdpRequest.InstanceUpgrade> instanceUpgradeList = aiopsItemIdList.stream().map(x -> {
                    AiopsUpgradeDcdpRequest.InstanceUpgrade instanceUpgrade =
                            new AiopsUpgradeDcdpRequest.InstanceUpgrade(x);
                    instanceUpgrade.setUpgradeType(UpgradeType.DELETE);
                    instanceUpgrade.getOtherConditionMap().put("ai.aiopsItemType",
                            InstanceUpgradeEnum.TP_MODULE_INSTANCE.getAiopsItemType());
                    instanceUpgrade.setNeedSaveOperationLog(true);
                    Map<String, Object> operationInfoMap = instanceUpgrade.getOperationInfoMap();
                    operationInfoMap.put("operationObjectClassification", "instance");
                    operationInfoMap.put("operationObject", "instance");
                    operationInfoMap.put("operationBehavior", "deleteinstance");
                    return instanceUpgrade;
                }).collect(Collectors.toList());
                upgradeDcdpDetailList.addAll(getInstanceUpgradeToDcdpDetail(instanceUpgradeList));
                deviceMapper.deleteTpInstance(map);
            }
            //清理设备收集项缓存
            collectConfigService.clearDeviceIdCollectConfigCache(deviceId);
        }
        response = executeUpgradeToDcdp(upgradeDcdpDetailList);
        if (!response.checkIsSuccess()) {
            return response;
        }
        return BaseResponse.ok(result);
    }

    @Override
    public BaseResponse removeDeviceAiopsInstanceMappingByArc(AdimRelateContext arc) {
        //因为有异步进行的行为，这方法不进事务，不然会造成数据异常
        //region 参数检查

        Optional<BaseResponse> optResponse = checkParamIsEmpty(arc, "arc");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }
        String deviceId = arc.getDeviceId();
        optResponse = checkParamIsEmpty(deviceId, "arc.deviceId");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }
        String aiopsItem = arc.getAiopsItem();
        checkParamIsEmpty(aiopsItem, "arc.aiopsItem");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        //endregion

        //查出aiopsItemIdList列表
        Map<String, Object> map = new HashMap<>();
        String truestInstanceTableName = arc.getTruestInstanceTableName();
        Map<String, Object> businessPKValueMap = arc.getBusinessPKValueMap();
        String aiopsItemIdColumnName = arc.getAiopsItemIdColumnName();
        List<String> aiopsItemIdList;
        if (StringUtils.isNotBlank(truestInstanceTableName) &&
                !CollectionUtils.isEmpty(businessPKValueMap) &&
                StringUtils.isNotBlank(aiopsItemIdColumnName)) {
            String aiopsItemId = instanceService.getAiopsItemIdByRelate(truestInstanceTableName, businessPKValueMap,
                    aiopsItemIdColumnName);
            if (StringUtils.isBlank(aiopsItemId)) {
                return BaseResponse.ok(0);
            }
            aiopsItemIdList = Stream.of(aiopsItemId).collect(Collectors.toList());
        } else {
            map.put("deviceId", deviceId);
            map.put("aiopsItem", aiopsItem);
            aiopsItemIdList = instanceService.getAiopsItemIdListByMap(map);
            if (CollectionUtils.isEmpty(aiopsItemIdList)) {
                return BaseResponse.ok(0);
            }
            map.remove("aiopsItem");
        }
        if (BooleanUtils.toBoolean(arc.getNeedRemoveAdcdList())) {
            return removeDeviceAiopsInstanceMapping(deviceId, aiopsItemIdList);
        }
        BaseResponse<List<AiopsInstance>> response =
                instanceService.getAiopsInstanceByAiopsItemIdList(aiopsItemIdList);
        if (!response.checkIsSuccess()) {
            return response;
        }
        List<AiopsInstance> aiList = response.getData();
        if (CollectionUtils.isEmpty(aiList)) {
            return BaseResponse.ok(0);
        }
        map.put("aiIdList", aiList.stream().map(AiopsInstance::getId).collect(Collectors.toList()));
        //删除设备实例映射
        deviceMapper.deleteAdimByMap(map);
        //清除设备收集项明细上的adcId与adimId
        deviceMapper.updateAdcdAdcIdAndAdimIdByMap(null, null, map);
        //刷新实例下所有规则引擎
        ruleEngineService.asyncModifyReAdditionalContentByMap(map);
        //刷新设备收集项缓存
        collectConfigService.clearDeviceIdCollectConfigCache(deviceId);
        return BaseResponse.ok(1);
    }

    @Override
    public BaseResponse getCurrentDeviceTypeDeviceList(Long eid, String deviceType, String selfDeviceId) {
        //region 参数检查

        Optional<BaseResponse> optResponse = checkParamIsEmpty(eid, "eid");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        optResponse = checkParamIsEmpty(deviceType, "deviceType");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        //endregion

        return BaseResponse.ok(deviceMapper.selectCurrentDeviceTypeDeviceList(eid, deviceType, selfDeviceId));
    }

    @Override
    public BaseResponse getDeviceDataSourceByEid(Long eid) {
        //region 参数检查

        Optional<BaseResponse> optResponse = checkParamIsEmpty(eid, "eid");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        //endregion

        return BaseResponse.ok(deviceMapper.selectDeviceDataSourceByEid(eid));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BaseResponse modifySourceAdimToTargetByAiopsItemIdList(String sourceDeviceId, Long targetAdId,
                                                                  String targetDeviceId, List<String> aiopsItemIdList) {
        //region 参数检查

        Optional<BaseResponse> optResponse = checkParamIsEmpty(sourceDeviceId, "sourceDeviceId");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        optResponse = checkParamIsEmpty(targetAdId, "targetAdId");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        optResponse = checkParamIsEmpty(targetDeviceId, "targetDeviceId");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        //endregion

        Integer effectRow = deviceMapper.updateSourceAdimToTargetByAiopsItemIdList(sourceDeviceId, targetAdId,
                targetDeviceId, aiopsItemIdList);
        //TODO:企业运维升级数采，移转实例可能存在来源或目标尚未升级的状况
        Map<String, Object> map = new HashMap<>(4);
        map.put("containAi", true);
        map.put("deviceId", targetDeviceId);
        map.put("aiopsItemIdList", aiopsItemIdList);
        List<DeviceInstanceMapping> adimList = deviceMapper.selectAdimByMap(map);
        if (CollectionUtil.isNotEmpty(adimList)) {
            BaseResponse response = executeUpgradeToDcdp(adimList.stream().map(x -> {
                AiopsUpgradeDcdpDetail detail = new AiopsUpgradeDcdpDetail(UpgradeType.UPDATE,
                        DeviceUpgradeEnum.DEVICE_INSTANCE_MAPPING.getTableName(), BeanUtil.object2Map(x));
                Map<String, Object> paramsMap = detail.getParamsMap();
                paramsMap.put(EID, x.getEid());
                paramsMap.put(DEVICE_ID, x.getDeviceId());
                detail.setNeedSaveOperationLog(true);
                Map<String, Object> operationInfoMap = OperationUtil.getHeaderOperationInfo();
                operationInfoMap.put("operationObjectClassification", "device");
                operationInfoMap.put("operationObject", "device");
                operationInfoMap.put("operationBehavior", "devicechangeinstancemapping");
                detail.setOperationInfoMap(operationInfoMap);
                return detail;
            }).collect(Collectors.toList()));
            if (!response.checkIsSuccess()) {
                TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                return response;
            }
        }
        return BaseResponse.ok(effectRow);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BaseResponse modifySourceAdcdToTargetByAiopsItemIdList(String sourceDeviceId, Long targetAdId,
                                                                  String targetDeviceId, List<String> aiopsItemIdList) {
        //region 参数检查

        Optional<BaseResponse> optResponse = checkParamIsEmpty(sourceDeviceId, "sourceDeviceId");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        optResponse = checkParamIsEmpty(targetAdId, "targetAdId");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        optResponse = checkParamIsEmpty(targetDeviceId, "targetDeviceId");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        //endregion

        DeviceCollect adc = dealDeviceCollect(targetAdId, targetDeviceId);

        Integer effectRow = deviceMapper.updateSourceAdcdToTargetByAiopsItemIdList(sourceDeviceId, adc.getId(),
                aiopsItemIdList);
        Map<String, Object> map = new HashMap<>(4);
        map.put("aiopsItemIdList", aiopsItemIdList);
        List<Long> aiIdList = instanceMapper.selectAiIdListByMap(map);
        if (CollectionUtil.isNotEmpty(aiIdList)) {
            BaseResponse response = executeUpgradeToDcdp(getDeviceUpgradeToDcdpDetail(aiIdList.stream().map(x -> {
                AiopsUpgradeDcdpRequest.DeviceUpgrade deviceUpgrade = new AiopsUpgradeDcdpRequest.DeviceUpgrade(
                        targetDeviceId, DeviceUpgradeEnum.DEVICE_COLLECT_DETAIL, UpgradeType.UPDATE);
                deviceUpgrade.getOtherConditionMap().put("adcd.aiId", x);
                deviceUpgrade.setNeedSaveOperationLog(true);
                Map<String, Object> operationInfoMap = deviceUpgrade.getOperationInfoMap();
                operationInfoMap.put("operationObjectClassification", "device_collect_detail");
                operationInfoMap.put("operationObject", "device_collect_detail");
                operationInfoMap.put("operationBehavior", "editdevicecollectconfiginstancemapping");
                return deviceUpgrade;
            }).collect(Collectors.toList())));
            if (!response.checkIsSuccess()) {
                TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                return response;
            }
        }
        return BaseResponse.ok(effectRow);
    }

    @Override
    public BaseResponse getDeviceDatasourceMatchDbId(String deviceId, Collection<String> dbIdCollection) {
        //region 参数检查

        Optional<BaseResponse> optResponse = checkParamIsEmpty(deviceId, "deviceId");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        //endregion

        return BaseResponse.ok(deviceMapper.selectDeviceDatasourceMatchDbId(deviceId, dbIdCollection));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BaseResponse removeAdcdByAiopsItemIdList(String deviceId, List<String> aiopsItemIdList) {
        //region 参数检查

        Optional<BaseResponse> optResponse = checkParamIsEmpty(deviceId, "deviceId");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        //endregion

        Map<String, Object> map = new HashMap<>(2);
        map.put("aiopsItemIdList", aiopsItemIdList);
        List<Long> aiIdList = instanceMapper.selectAiIdListByMap(map);
        List<AiopsUpgradeDcdpDetail> upgradeDcdpDetailList;
        if (CollectionUtils.isEmpty(aiIdList)) {
            upgradeDcdpDetailList = new ArrayList<>(0);
        } else {
            upgradeDcdpDetailList = getDeviceUpgradeToDcdpDetail(aiIdList.stream().map(x -> {
                AiopsUpgradeDcdpRequest.DeviceUpgrade deviceUpgrade = new AiopsUpgradeDcdpRequest.DeviceUpgrade(
                        deviceId, DeviceUpgradeEnum.DEVICE_COLLECT_DETAIL, UpgradeType.DELETE);
                deviceUpgrade.getOtherConditionMap().put("adcd.aiId", x);
                deviceUpgrade.setNeedSaveOperationLog(true);
                Map<String, Object> operationInfoMap = deviceUpgrade.getOperationInfoMap();
                operationInfoMap.put("operationObjectClassification", "device_collect_detail");
                operationInfoMap.put("operationObject", "device_collect_detail");
                operationInfoMap.put("operationBehavior", "deletedevicecollectdetail");
                return deviceUpgrade;
            }).collect(Collectors.toList()));
        }

        Integer effectRow = deviceMapper.deleteAdcdByAiopsItemIdList(deviceId, aiopsItemIdList);
        BaseResponse response = executeUpgradeToDcdp(upgradeDcdpDetailList);
        if (!response.checkIsSuccess()) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return response;
        }
        return BaseResponse.ok(effectRow);
    }

    @Override
    public List<DeviceInfo> getNotAiopsInstanceDeviceList(Map<String, Object> map) {
        return deviceMapper.selectNotAiopsInstanceDeviceList(map);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BaseResponse modifyDeviceCollectDetailAiIdAdimIdByAdcdIdList(Long aiId, Long adimId, List<Long> adcdIdList) {
        //region 参数检查

        Optional<BaseResponse> optResponse = checkParamIsEmpty(aiId, "aiId");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        optResponse = checkParamIsEmpty(adimId, "adimId");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        optResponse = checkParamIsEmpty(adcdIdList, "adcdIdList");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        //endregion

        return BaseResponse.ok(deviceMapper.updateDeviceCollectDetailAiIdAdimIdByAdcdIdList(aiId, adimId, adcdIdList));
    }

    @Override
    public List<AiopsKitDeviceDataSource> getDeviceDataSourceByMap(Map<String, Object> map) {
        return deviceMapper.selectDeviceDataSourceByMap(map);
    }

    @Override
    public List<DeviceCollectDetail> getNotAiIdAndAdimIdAdcdByMap(Map<String, Object> map) {
        return deviceMapper.selectNotAiIdAndAdimIdAdcdByMap(map);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer batchModifyDeviceInstanceMappingAiIdByMapList(List<Map<String, Object>> mapList) {
        return deviceMapper.batchUpdateDeviceInstanceMappingAiIdByMapList(mapList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer batchModifyDeviceCollectDetailAiIdByMapList(List<Map<String, Object>> mapList) {
        return deviceMapper.batchUpdateDeviceCollectDetailAiIdByMapList(mapList);
    }

    @Override
    public List<DeviceProductMapping> getAdpmListByMapList(List<Map<String, Object>> mapList) {
        return deviceMapper.selectAdpmListByMapList(mapList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer removeAdpmByAdpmIdList(List<Long> adpmIdList) {
        return deviceMapper.deleteAdpmByAdpmIdList(adpmIdList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer modifyAdpmAdIdByAdpmIdList(Long adId, String deviceId, List<Long> adpmIdList) {
        return deviceMapper.updateAdpmAdIdByAdpmIdList(adId, deviceId, adpmIdList);
    }

    @Override
    public Long getEidByDeviceId(String deviceId) {
        return deviceMapper.selectEidByDeviceId(deviceId);
    }

    @Override
    public BaseResponse getGroupAdcdExecParam(String deviceId, List<String> aiopsItemIdList) {
        //region 参数检查

        Optional<BaseResponse> optResponse = checkParamIsEmpty(deviceId, "deviceId");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        optResponse = checkParamIsEmpty(aiopsItemIdList, "aiopsItemIdList");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        //endregion

        Map<String, Object> map = new HashMap<>(4);
        map.put("deviceId", deviceId);
        map.put("aiopsItemIdList", aiopsItemIdList);
        Long eid = deviceMapper.selectEidByDeviceId(deviceId);
        if (!LongUtil.isEmpty(eid)) {
            map.put("eid", eid);
            Set<String> aiopsItemList = instanceMapper.selectAiopsItemListByMap(map);
            Long sid = RequestUtil.getHeaderSidOrDefault(defaultSid);
            BaseResponse<List<Long>> response = getValidSamcdIdListCore(sid, eid, aiopsItemList);
            if (!response.checkIsSuccess()) {
                return response;
            }
            map.put("validSamcdIdList", response.getData());
        }
        List<GroupAdcdExecParam> gaepList = deviceMapper.selectGroupAdcdExecParamByMap(map);
        findGaepDisplayNameByModel(RequestUtil.getHeaderSidOrDefault(defaultSid), gaepList);
        //再做一次排序后返回
        return BaseResponse.ok(gaepList.stream().sorted(Comparator.comparing(GroupAdcdExecParam::getAiopsInstanceName))
                .collect(Collectors.toList()));
    }

    private void findGaepDisplayNameByModel(Long sid, List<GroupAdcdExecParam> gaepList) {
        if (CollectionUtils.isEmpty(gaepList)) {
            return;
        }
        Map<String, List<GroupAdcdExecParam>> codeGroupMap = gaepList.stream().filter(Objects::nonNull)
                .filter(x -> BooleanUtils.toBoolean(x.getFindDisplayNameByModel()))
                .filter(x -> StringUtils.isNotBlank(x.getAiopsInstanceName()))
                .collect(Collectors.groupingBy(GroupAdcdExecParam::getAiopsInstanceName));
        if (CollectionUtils.isEmpty(codeGroupMap)) {
            return;
        }
        setDisplayNameByModel(sid, codeGroupMap.keySet(), (x) -> {
            String code = x.getKey();
            List<GroupAdcdExecParam> currentDaiList = codeGroupMap.get(code);
            if (CollectionUtils.isEmpty(currentDaiList)) {
                return;
            }
            String name = x.getValue();
            currentDaiList.forEach(y -> y.setAiopsInstanceName(name));
        });
    }

    @Override
    public BaseResponse saveGroupAdcdExecParam(String deviceId, Long adId, Long eid,
                                               List<GroupAdcdExecParam> groupAdcdExecParamList) {
        //TODO:此方法因为考虑异步添加中的设备收集项明细，因此不起事务

        //region 参数检查

        Optional<BaseResponse> optResponse = checkParamIsEmpty(groupAdcdExecParamList, "groupAdcdExecParamList");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        //endregion

        Long finalEid;
        if (LongUtil.isEmpty(eid)) {
            finalEid = deviceMapper.selectEidByDeviceId(deviceId);
        } else {
            finalEid = eid;
        }

        DeviceCollect adc = dealDeviceCollect(adId, deviceId);
        Long adcId = adc.getId();

        Map<Long, List<GroupAdcdExecParam>> aiIdGroup = groupAdcdExecParamList.stream()
                .collect(Collectors.groupingBy(GroupAdcdExecParam::getAiId));

        Set<Long> unityExecParamsAiIdSet = deviceMapper.selectUnityExecParamsAiId(aiIdGroup.keySet());

        List<AiopsItemContext> aicList = new ArrayList<>();
        List<DeviceCollectDetail> newAdcdList = new ArrayList<>();
        Map<Long, List<CollectWarning>> adcdAcwListMap = new HashMap<>();
        Map<Long, GroupAdcdExecParam.GroupAdcdExecParamDetail> newGroupAdcdExecParamDetailMap = new HashMap<>();
        Map<Long, GroupAdcdExecParam.LazyModifyTarget> adcdIdLmtMap = new HashMap<>();
        List<AiopsUpgradeDcdpDetail> upgradeDcdpDetailList = new ArrayList<>();
        //处理数据
        List<GroupAdcdExecParam.GroupAdcdExecParamDetail> gaepdList = groupAdcdExecParamList.stream().map(x -> {
            Long aiId = x.getAiId();
            Long adimId = x.getAdimId();
            String aiopsItem = x.getAiopsItem();
            String execParamsModelCode = x.getExecParamsModelCode();
            String oriExecParamsContent = x.getOriExecParamsContent();
            Optional<Map<String, Object>> optMap = tryGetFullExecParamsMap(oriExecParamsContent);
            if (!optMap.isPresent()) {
                return null;
            }
            Map<String, Object> jsonObjectMap = optMap.get();
            if (unityExecParamsAiIdSet.contains(aiId)) {
                Optional<Map<String, Object>> optExecParamsMap = getExecParamMap(jsonObjectMap);
                if (!optExecParamsMap.isPresent()) {
                    return null;
                }
                Map<String, Object> execParamsMap = optExecParamsMap.get();
                Map<String, Object> paramsMap = new HashMap<>();
                paramsMap.put("eid", eid);
                paramsMap.put(DEVICE_ID, deviceId);
                paramsMap.put("oriAiopsItem", aiopsItem);
                paramsMap.put("oriAiopsItemId", x.getAiopsItemId());
                Optional<Map<String, Object>> optAiopsItemInfo = execParamsFactoryService
                        .getCurrentAiopsItemInfo(execParamsModelCode, execParamsMap, paramsMap);
                if (!optAiopsItemInfo.isPresent()) {
                    return null;
                }
                Map<String, Object> aiopsItemInfo = optAiopsItemInfo.get();
                String newAiopsItem = Objects.toString(aiopsItemInfo.get("aiopsItem"), "");
                String newAiopsItemId = Objects.toString(aiopsItemInfo.get("aiopsItemId"), "");
                String oriAiopsItemId = x.getAiopsItemId();
                if (StringUtils.isBlank(oriAiopsItemId) || !oriAiopsItemId.equals(newAiopsItemId)) {
                    //旧的运维项目Id与新的运维项目Id不同时才需要处理
                    AiopsItemContext aic = new AiopsItemContext(newAiopsItem, newAiopsItemId, deviceId);
                    //映射到设备，但不添加收集项，后面直接将adim及adcd中的aiId换成新的aiId
                    //映射设备主要要使其进行实例重复映射检查
                    aic.setIsAddInstanceCollectToDevice(false);
                    aic.setOriAiId(aiId);
                    aic.setOriAdimId(adimId);
                    aicList.add(aic);
                }
            }
            List<GroupAdcdExecParam.GroupAdcdExecParamDetail> detailList = x.getGroupAdcdExecParamDetailList();
            if (CollectionUtils.isEmpty(detailList)) {
                return null;
            }
            //若內容為空，清除所有執行參數內容
            if (StringUtils.isBlank(oriExecParamsContent)) {
                String[] result =
                        collectConfigService.modifyExecParams("", 0L, false);
                detailList.forEach(y -> {
                    y.setExecParamsContent(result[0]);
                    y.setExecParamsVersion(result[1]);
                });
                return detailList;
            }
            detailList.forEach(y -> {
                Long adcdId = y.getAdcdId();
                if (LongUtil.isEmpty(adcdId)) {
                    //考虑到添加收集项是异步进行的，因此这里尝试获取adcdId
                    adcdId = getAdcdIdByAiIdAdimIdAccId(aiId, adimId, y.getAccId());
                    if (LongUtil.isEmpty(adcdId)) {
                        //如果还是查不到，就进行添加
                        Long accId = y.getAccId();
                        DeviceCollectDetail adcd = createNewAdcd(adcId, accId, aiId, adimId,
                                y.getCollectName(), y.getIsEnable());
                        adcdId = adcd.getId();
                        newAdcdList.add(adcd);
                        //串查预警项
                        BaseResponse<List<CollectWarning>> warningSetResponse =
                                collectWarningService.getCollectWarningSetList(accId, DEFAULT_SCOPE_ID);
                        if (warningSetResponse.checkIsSuccess()) {
                            adcdAcwListMap.put(adcdId, warningSetResponse.getData());
                        }
                        newGroupAdcdExecParamDetailMap.put(adcdId, y);
                    }
                    y.setAdcdId(adcdId);
                }
                adcdIdLmtMap.put(adcdId, new GroupAdcdExecParam.LazyModifyTarget(jsonObjectMap, adcdId,
                        x.getExecParamsModelCode(), y));
            });
            return detailList;
        }).filter(Objects::nonNull).flatMap(Collection::stream).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(aicList)) {
            BaseResponse response = authorizeService.checkAndCreateAiopsInstanceByAiopsItemContext(eid,
                    null, aicList);
            //如果出现错误(例如重复映射异常)，那就直接抛出错误
            if (!response.checkIsSuccess()) {
                return response;
            }
        }
        if (CollectionUtils.isEmpty(gaepdList)) {
            processTransformAiId(aicList);
            return BaseResponse.ok(0);
        }
        if (!CollectionUtils.isEmpty(newAdcdList)) {
            //為了避免极端的情况下，可能重复添加收集项，這裡添加前再查一次
            List<DeviceCollectDetail> curNewAdcdList = newAdcdList.stream().filter(x -> {
                Long adcdId = getAdcdIdByAiIdAdimIdAccId(x.getAiId(), x.getAdimId(), x.getAccId());
                if (LongUtil.isEmpty(adcdId)) {
                    return true;
                }
                //查到进行替换
                Long oriAdcdId = x.getId();
                GroupAdcdExecParam.GroupAdcdExecParamDetail groupAdcdExecParamDetail =
                        newGroupAdcdExecParamDetailMap.get(oriAdcdId);
                if (Objects.nonNull(groupAdcdExecParamDetail)) {
                    GroupAdcdExecParam.LazyModifyTarget lmt = adcdIdLmtMap.get(oriAdcdId);
                    if (Objects.nonNull(lmt)) {
                        lmt.setAdcdId(adcdId);
                    }
                    groupAdcdExecParamDetail.setAdcdId(adcdId);
                }
                adcdAcwListMap.remove(adcdId);
                return false;
            }).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(curNewAdcdList)) {
                //TODO: 这里可能存在没有对应授权，但会把收集项加到设备的问题，将来若有发生，可以考虑在上面再透过samcdId过滤一次
                deviceMapper.batchDeviceCollectDetail(curNewAdcdList);
                upgradeDcdpDetailList.addAll(getDeviceUpgradeToDcdpDetail(curNewAdcdList.stream().map(x -> {
                    AiopsUpgradeDcdpRequest.DeviceUpgrade deviceUpgrade =
                            new AiopsUpgradeDcdpRequest.DeviceUpgrade(deviceId, DeviceUpgradeEnum.DEVICE_COLLECT_DETAIL);
                    deviceUpgrade.getOtherConditionMap().put("adcd.id", x.getId());
                    deviceUpgrade.setNeedSaveOperationLog(true);
                    Map<String, Object> operationInfoMap = deviceUpgrade.getOperationInfoMap();
                    operationInfoMap.put("operationObjectClassification", "device");
                    operationInfoMap.put("operationObject", "device");
                    operationInfoMap.put("operationBehavior", "adddevicecollectconfig");
                    return deviceUpgrade;
                }).collect(Collectors.toList())));
                if (!CollectionUtils.isEmpty(adcdAcwListMap)) {
                    adcdAcwListMap.forEach((k, v) -> batchDeviceCollectWarning(k, v, finalEid, deviceId));
                }
            }
        }
        //最后统一保存到大数据平台
        adcdIdLmtMap.values().forEach(x -> {
            String[] result = collectConfigService.modifyExecParamsByMap(x.getJsonObjectMap(), x.getAdcdId(),
                    false, finalEid, true, x.getExecParamsModelCode());
            GroupAdcdExecParam.GroupAdcdExecParamDetail gaepd = x.getTargetGaepd();
            gaepd.setExecParamsVersion(result[0]);
            gaepd.setExecParamsContent(result[1]);
        });
        Integer effectRow = deviceMapper.updateAdcdExecParamsByGroupAdcdExecParamDetail(gaepdList);
        upgradeDcdpDetailList.addAll(getDeviceUpgradeToDcdpDetail(gaepdList.stream().map(x -> {
            AiopsUpgradeDcdpRequest.DeviceUpgrade deviceUpgrade =
                    new AiopsUpgradeDcdpRequest.DeviceUpgrade(deviceId, DeviceUpgradeEnum.DEVICE_COLLECT_DETAIL);
            deviceUpgrade.setUpgradeType(UpgradeType.UPDATE);
            deviceUpgrade.getOtherConditionMap().put("adcd.id", x.getAdcdId());
            deviceUpgrade.setNeedSaveOperationLog(true);
            Map<String, Object> operationInfoMap = deviceUpgrade.getOperationInfoMap();
            operationInfoMap.put("operationObjectClassification", "device_collect_detail");
            operationInfoMap.put("operationObject", "device_collect_detail");
            operationInfoMap.put("operationBehavior", "editdevicecollectdetailinstance");
            return deviceUpgrade;
        }).collect(Collectors.toList())));
        processTransformAiId(aicList);
        BaseResponse response = executeUpgradeToDcdp(upgradeDcdpDetailList);
        if (!response.checkIsSuccess()) {
            return response;
        }
        //清理设备收集项缓存
        collectConfigService.clearDeviceIdCollectConfigCache(deviceId);
        return BaseResponse.ok(effectRow);
    }

    @Override
    public BaseResponse queryAiopsItemInstance(String deviceId, String aiopsItemId) {
        Map<String, Object> instance = deviceMapper.queryAiopsItemByAiopsItemId(deviceId, aiopsItemId);
        Map<String, Object> resultParams = new HashMap<>();
        resultParams.put("aiopsItemIsExist", false);
        resultParams.put("aiopsAuthStatus", null);
        if (MapUtils.isNotEmpty(instance)) {
            resultParams.put("aiopsAuthStatus", instance.get("aiopsAuthStatus"));
            resultParams.put("aiopsItemIsExist", true);
        }
        return BaseResponse.ok(resultParams);
    }

    @Override
    public BaseResponse getDeviceDashboard(Long eid) {


        List<AiopsItemParam> aiopsItemParamList = new ArrayList<>();

        //获取租户下所有设备实例
        BaseResponse response = instanceService.getStatisticsValidAiopsItemListByEid(eid);
        Map<String, AiopsItemStatistics> aiopsItemStatisticsMap = new HashMap<>();
        if (!response.checkIsSuccess() || response.getData() == null) {
            return response;
        }
        List<AiopsItemStatistics> aiopsItemStatisticsList = ((ArrayList<AiopsItemStatistics>) response.getData());
        //初始化设备在线缓存
        initDeviceOnline(eid);

        if (!CollectionUtils.isEmpty(aiopsItemStatisticsList)) {
            aiopsItemStatisticsMap = aiopsItemStatisticsList.stream()
                    .collect(Collectors.toMap(AiopsItemStatistics::getAiopsItem, s -> s,
                            (oldData, newData) -> oldData));
        }
        List<String> aiopsItemList = aiopsItemStatisticsList.stream().map(AiopsItemStatistics::getAiopsItem)
                .collect(Collectors.toList());
        Long sid = RequestUtil.getHeaderSid();
        //获取所有授权信息
        BaseResponse<List<TenantModuleContractDetail>> baseResponse =
                userFeignClient.getTenantModuleContractClassDetailByAiopsItemList(sid, eid, aiopsItemList);

        if (!baseResponse.checkIsSuccess()) {
            return response;
        }
        List<TenantModuleContractDetail> tmcdList = baseResponse.getData();
        if (!CollectionUtils.isEmpty(tmcdList)) {
            Map<String, TenantModuleContractDetail> contractDetailMap = tmcdList.stream()
                    .collect(Collectors.toMap(s -> s.getClassCode(), s -> s, (oldData, newData) -> oldData));
            //服务器 区分云厂商
            buildServer(contractDetailMap, aiopsItemStatisticsMap, aiopsItemParamList, "HOST", eid);
            //虚拟设备
            buildVirtualDevice(contractDetailMap, aiopsItemStatisticsMap, aiopsItemParamList, null, eid);

            //其他所有实例
            List<String> filterHostAiopsItemList = aiopsItemList.stream().filter(aiopsItem -> !"HOST".equals(aiopsItem))
                    .collect(Collectors.toList());
            for (String aiopsItem : filterHostAiopsItemList) {
                buildDeviceAiopsItem(contractDetailMap, aiopsItemStatisticsMap, aiopsItemParamList, aiopsItem, null);
            }
        }
        //清除设备缓存
        clearDeviceOnlineCache();
        return BaseResponse.ok(aiopsItemParamList);


    }

    private void initDeviceOnline(long eid) {
        //串查samcdId列表
        BaseResponse<List<Long>> impiResponse = aioUserFeignClient.getSamcdIdListByClassCode("IPMI");
        BaseResponse<List<Long>> crhResponse = aioUserFeignClient.getSamcdIdListByClassCode("CRH");
        List<Long> samcdIdList = new ArrayList<>();
        if (!Objects.isNull(impiResponse.getData()) || !Objects.isNull(crhResponse.getData())) {
            samcdIdList.addAll(impiResponse.getData());
            samcdIdList.addAll(crhResponse.getData());
        }

        Map<Object, List<Map<Object, Object>>> aiopsItemMap = instanceMapper.queryDeviceOnline(eid, samcdIdList)
                .stream()
                .collect(Collectors.groupingBy(item -> item.get("aiopsItem")));
        Integer currentIndex = 1;
        StringBuffer sb = new StringBuffer();
        sb.append("SELECT  aiopsItem, aiId, second, collectedTime FROM(");
        for (Map.Entry<Object, List<Map<Object, Object>>> entry : aiopsItemMap.entrySet()) {
            String key = entry.getKey().toString();
            String aiIds = entry.getValue()
                    .stream()
                    .map(map -> map.get("aiopsInstanceId").toString())
                    .collect(Collectors.joining(",", "(", ")"));

            //3天内才算在线
            sb.append(" SELECT '" + key + "' as aiopsItem, aiId,TIMESTAMPDIFF(SECOND, from_unixtime(flumeTimestamp/1000), NOW()) second, ");
            sb.append(" collectedTime, ");
            sb.append(" ROW_NUMBER() OVER (PARTITION BY aiId ORDER BY collectedTime DESC) AS row_num ");
            sb.append(" FROM servicecloud.OfflineCheckCollected_sr_primary ");
            sb.append(" WHERE aiId in ").append(aiIds);
            sb.append(" AND eid = '").append(eid).append("' ");
            if (aiopsItemMap.size() > currentIndex) {
                sb.append(" UNION ALL ");
                currentIndex++;
            }
        }
        sb.append(" ) AS subquery");
        sb.append(" WHERE row_num = 1 ");
        List<Map<String, Object>> mapList = bigDataUtil.srQuery(sb.toString());
        if (!CollectionUtils.isEmpty(mapList)) {
            Map<String, List<Map<String, Object>>> aiopsItemIsOnlineMap =
                    mapList.stream()
                            .collect(Collectors.groupingBy(item -> item.get("aiopsItem").toString()));
            for (Map.Entry<String, List<Map<String, Object>>> entry : aiopsItemIsOnlineMap.entrySet()) {
                String key = entry.getKey();
                HashMap<String, Integer> isOnlineMap = new HashMap<>();
                List<Map<String, Object>> aiopsItems = entry.getValue();
                //获取到时间 且未超过三天显示在线，超过三天显示离线
                AtomicInteger onlineCount = new AtomicInteger(0);
                AtomicInteger offlineCount = new AtomicInteger(0);
                aiopsItems.forEach(item -> {
                    if (Long.valueOf(item.get("second").toString()) <= 259200L) {
                        onlineCount.getAndIncrement();
                    } else {
                        offlineCount.getAndIncrement();
                    }
                });
                isOnlineMap.put("onlineCount", onlineCount.intValue());
                isOnlineMap.put("offlineCount", offlineCount.intValue());
                deviceOnlineMap.put(key, isOnlineMap);
            }
        }
        //统计终端设备在线离线数据 主机需要单独统计，因为需要区分公有云和私有云

    }

    private void clearDeviceOnlineCache() {
        deviceOnlineMap.clear();
    }

    private void buildServer(Map<String, TenantModuleContractDetail> contractDetailMap,
                             Map<String, AiopsItemStatistics> aiopsItemStatisticsMap, List<AiopsItemParam> aiopsItemParamList,
                             String aiopsItem, Long eid) {

        //查询主机信息 需要区分是否是云厂商 HOST
        List<DeviceAiopsItem> hostAiopsItemList = instanceMapper.selectAiopsItemListByEid(eid, Arrays.asList(aiopsItem));

        //主机和终端统计的是agent在线数
        Map<Object, List<Map<Object, Object>>> aiopsItemMap = instanceMapper.selectHostAndClientOnline(eid)
                .stream().collect(Collectors.groupingBy(item -> item.get("aiopsItem")));

        addClientIsOnline(aiopsItemMap);
        //获取主机在线离线数
        List<Map<Object, Object>> aiopsItemHostIsOnlineList = aiopsItemMap.get(aiopsItem);

        if (!CollectionUtils.isEmpty(hostAiopsItemList)) {
            String sql = buildManufacturerSql(hostAiopsItemList);
            List<Map<String, Object>> manufacturerList = bigDataUtil.srQuery(sql);
            //需要拿到云厂商已使用授权数
            Integer publicCloudAuthedCount = 0;
            //云厂商设备数量
            Integer publicCloudDeviceCount = 0;
            //云厂商设备id
            List<String> cloudDeviceIdList = new ArrayList<>();
            TenantModuleContractDetail tenantModuleContractDetail = contractDetailMap.get(aiopsItem);
            if (!CollectionUtils.isEmpty(manufacturerList)) {
                //获取云厂商数据
                List<String> cloudManufacturerList = deviceMapper.selectCloudManufacturer();

                List<String> publicCloudDeviceIdList = manufacturerList.stream()
                        .filter(item -> cloudManufacturerList.contains(item.get("bios_manufacturer")))
                        .map(item -> (String) item.get("deviceId"))
                        .collect(Collectors.toList());
                cloudDeviceIdList.addAll(publicCloudDeviceIdList);

                //云厂商
                List<DeviceAiopsItem> publicCloudAiopsItemList = hostAiopsItemList.stream()
                        .filter(item -> publicCloudDeviceIdList.contains(item.getDeviceId()))
                        .collect(Collectors.toList());
                publicCloudDeviceCount = publicCloudAiopsItemList.size();
                AiopsItemStatistics publicCloudAiopsItem = new AiopsItemStatistics();
                publicCloudAiopsItem.setTotal(publicCloudDeviceCount);

                //统计云厂商在线离线数
                if (!CollectionUtils.isEmpty(aiopsItemHostIsOnlineList)) {
                    List<Map<Object, Object>> publicCloudIsOnlineList = aiopsItemHostIsOnlineList.stream()
                            .filter(item -> publicCloudDeviceIdList.contains(item.get("deviceId")))
                            .collect(Collectors.toList());

                    publicCloudAiopsItem.setOnlineCount(publicCloudIsOnlineList.stream()
                            .filter(item -> !Objects.isNull(item.get("isOnLine")))
                            .filter(item -> "1".equals(item.get("isOnLine").toString()))
                            .collect(Collectors.toList()).size()
                    );
                    publicCloudAiopsItem.setOfflineCount(publicCloudIsOnlineList.stream()
                            .filter(item -> !Objects.isNull(item.get("isOnLine")))
                            .filter(item -> "0".equals(item.get("isOnLine").toString()))
                            .collect(Collectors.toList()).size()
                    );
                }
                aiopsItemStatisticsMap.put(aiopsItem, publicCloudAiopsItem);

                if (!CollectionUtils.isEmpty(publicCloudDeviceIdList)) {
                    publicCloudAuthedCount = instanceMapper.selectAiAuthedCountByAiopsItemIds(publicCloudDeviceIdList);
                }
                TenantModuleContractDetail publicCloudTenantModuleContractDetail = new TenantModuleContractDetail();
                BeanUtils.copyProperties(tenantModuleContractDetail, publicCloudTenantModuleContractDetail);
                publicCloudTenantModuleContractDetail.setUsedCount(publicCloudAuthedCount);
                contractDetailMap.put(aiopsItem, publicCloudTenantModuleContractDetail);
                buildDeviceAiopsItem(contractDetailMap, aiopsItemStatisticsMap, aiopsItemParamList, aiopsItem, "publicCloud");

            }
            //私有云
            Integer privateCloudDeviceCount = hostAiopsItemList.size() - publicCloudDeviceCount;
            AiopsItemStatistics privateCloudAiopsItem = new AiopsItemStatistics();
            privateCloudAiopsItem.setTotal(privateCloudDeviceCount);

            //统计私有云在线离线数
            if (!CollectionUtils.isEmpty(aiopsItemHostIsOnlineList)) {
                List<Map<Object, Object>> privateCloudIsOnlineList = aiopsItemHostIsOnlineList.stream()
                        .filter(item -> !cloudDeviceIdList.contains(item.get("deviceId")))
                        .collect(Collectors.toList());

                privateCloudAiopsItem.setOnlineCount(privateCloudIsOnlineList.stream()
                        .filter(item -> !Objects.isNull(item.get("isOnLine")))
                        .filter(item -> "1".equals(item.get("isOnLine").toString()))
                        .collect(Collectors.toList()).size()
                );
                privateCloudAiopsItem.setOfflineCount(privateCloudIsOnlineList.stream()
                        .filter(item -> !Objects.isNull(item.get("isOnLine")))
                        .filter(item -> "0".equals(item.get("isOnLine").toString()))
                        .collect(Collectors.toList()).size()
                );
            }
            aiopsItemStatisticsMap.put(aiopsItem, privateCloudAiopsItem);

            TenantModuleContractDetail privateCloudTenantModuleContractDetail = new TenantModuleContractDetail();
            BeanUtils.copyProperties(tenantModuleContractDetail, privateCloudTenantModuleContractDetail);
            Integer privateCloudAuthedCount = tenantModuleContractDetail.getUsedCount() - publicCloudAuthedCount;
            privateCloudTenantModuleContractDetail.setUsedCount(privateCloudAuthedCount);
            contractDetailMap.put(aiopsItem, privateCloudTenantModuleContractDetail);
            buildDeviceAiopsItem(contractDetailMap, aiopsItemStatisticsMap, aiopsItemParamList, aiopsItem, "privateCloud");
        }

    }

    private void addClientIsOnline(Map<Object, List<Map<Object, Object>>> aiopsItemMap) {
        List<Map<Object, Object>> clientIsOnlineList = Optional.ofNullable(aiopsItemMap.get("CLIENT")).orElse(new ArrayList<>());
        if (!CollectionUtils.isEmpty(clientIsOnlineList)) {
            Integer onlineCount = clientIsOnlineList.stream()
                    .filter(item -> !Objects.isNull(item.get("isOnLine")))
                    .filter(item -> "1".equals(item.get("isOnLine").toString()))
                    .collect(Collectors.toList()).size();
            Integer offlineCount = clientIsOnlineList.stream()
                    .filter(item -> !Objects.isNull(item.get("isOnLine")))
                    .filter(item -> "0".equals(item.get("isOnLine").toString()))
                    .collect(Collectors.toList()).size();
            HashMap<String, Integer> deviceIsOnlineMap = new HashMap<>();
            deviceIsOnlineMap.put("onlineCount", onlineCount);
            deviceIsOnlineMap.put("offlineCount", offlineCount);
            deviceOnlineMap.put("CLIENT", deviceIsOnlineMap);
        }

    }

    private void buildDeviceAiopsItem(Map<String, TenantModuleContractDetail> contractDetailMap,
                                      Map<String, AiopsItemStatistics> aiopsItemStatisticsMap,
                                      List<AiopsItemParam> aiopsItemParamList,
                                      String aiopsItem, String deviceType) {

        AiopsItemStatistics aiopsItemStatistics = Optional.ofNullable(aiopsItemStatisticsMap.get(aiopsItem))
                .orElse(new AiopsItemStatistics());

        Integer deviceCount = aiopsItemStatistics.getTotal();

        AiopsItemParam aiopsItemParam = new AiopsItemParam();

        //todo 设备在线统计需要调整
        if (!"HOST".equals(aiopsItem)) {
            Map<String, Integer> deviceOnline = Optional.ofNullable(deviceOnlineMap.get(aiopsItem)).orElse(new HashMap<>());
            aiopsItemParam.setOnlineCount(deviceOnline.getOrDefault("onlineCount", 0));
            aiopsItemParam.setOfflineCount(deviceOnline.getOrDefault("offlineCount", 0));
        } else {
            aiopsItemParam.setOnlineCount(aiopsItemStatistics.getOnlineCount());
            aiopsItemParam.setOfflineCount(aiopsItemStatistics.getOfflineCount());
        }

        aiopsItemParam.setDeviceCount(deviceCount);

        TenantModuleContractDetail tenantModuleContractDetail = contractDetailMap.get(aiopsItem);
        if (Optional.ofNullable(tenantModuleContractDetail).isPresent()) {
            aiopsItemParam.setAvailableCount(tenantModuleContractDetail.getAvailableCount());
            aiopsItemParam.setUsedCount(tenantModuleContractDetail.getUsedCount());
            aiopsItemParam.setContainHoldAuth(tenantModuleContractDetail.getIsContainHoldAuth());
            aiopsItemParam.setAiopsItemName(tenantModuleContractDetail.getClassName());
            aiopsItemParam.setAiopsItemName_CN(tenantModuleContractDetail.getClassName_CN());
            aiopsItemParam.setAiopsItemName_TW(tenantModuleContractDetail.getClassName_TW());
        }

        aiopsItemParam.setAiopsItem(aiopsItem);
        aiopsItemParam.setType(deviceType);
        aiopsItemParamList.add(aiopsItemParam);
    }


    private void buildVirtualDevice(Map<String, TenantModuleContractDetail> contractDetailMap,
                                    Map<String, AiopsItemStatistics> aiopsItemStatisticsMap,
                                    List<AiopsItemParam> aiopsItemParamList,
                                    String deviceType, Long eid) {
        //虚拟设备 ESXI、iMM、XCC、iDRAC、iLO

        List<DeviceAiopsItem> hostAiopsItemList = instanceMapper.selectAiopsItemListByEid(eid,
                Arrays.stream(VirtualDeviceType.values()).map(String::valueOf).collect(Collectors.toList()));

        for (VirtualDeviceType device : VirtualDeviceType.values()) {
            String aiopsItem = device.toString();
            List<DeviceAiopsItem> deviceAiopsItemList = hostAiopsItemList.stream()
                    .filter(item -> aiopsItem.equals(item.getAiopsItem()))
                    .collect(Collectors.toList());

            AiopsItemStatistics aiopsItemStatistics = new AiopsItemStatistics();
            aiopsItemStatistics.setTotal(deviceAiopsItemList.size());
            aiopsItemStatisticsMap.put(aiopsItem, aiopsItemStatistics);
            buildDeviceAiopsItem(contractDetailMap, aiopsItemStatisticsMap, aiopsItemParamList, aiopsItem, deviceType);
        }
    }


    private String buildManufacturerSql(List<DeviceAiopsItem> deviceAiopsItemList) {

        List<String> deviceIdList = deviceAiopsItemList.stream().map(DeviceAiopsItem::getDeviceId)
                .collect(Collectors.toList());

        StringBuilder sb = new StringBuilder();
        sb.append(" SELECT get_json_string(bios_info,'$.bios_manufacturer') as bios_manufacturer, deviceId ");
        sb.append(" FROM ").append(bigDataUtil.getSrDbName()).append(".DeviceAdvancedInfoCollected_sr_primary");
        sb.append(" WHERE ");
        sb.append(" deviceId IN ")
                .append(deviceIdList.stream()
                        .map(s -> "'" + s + "'")
                        .collect(Collectors.joining(",", "(", ")"))
                );
        log.info("[buildManufacturerSql] SQL: {}", sb);
        return sb.toString();
    }

    private Long getAdcdIdByAiIdAdimIdAccId(Long aiId, Long adimId, Long accId) {
        Map<String, Object> conditionMap = new HashMap<>(3);
        conditionMap.put("aiId", aiId);
        conditionMap.put("adimId", adimId);
        conditionMap.put("accId", accId);
        return deviceMapper.selectAdcdIdByMap(conditionMap);
    }

    private void processTransformAiId(List<AiopsItemContext> aicList) {
        if (CollectionUtils.isEmpty(aicList)) {
            return;
        }
        aicList = aicList.stream().filter(Objects::nonNull).filter(x -> LongUtil.isNotEmpty(x.getOriAiId()))
                .filter(x -> LongUtil.isNotEmpty(x.getOriAdimId()))
                .filter(x -> LongUtil.isNotEmpty(x.getAiId()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(aicList)) {
            return;
        }
        //如果移转的实例已经存在，将新旧收集项做合并(目标已经存在的就不添加，目标不存在才迁移)
        List<Long> adimExistAccIdList = deviceMapper.selectAdimExistAccId(aicList);
        deviceMapper.updateAiIdByAicList(aicList, adimExistAccIdList);
        //TODO: 企业运维升级数采待实现
        deviceMapper.deleteNotUseAdimAdcd(aicList);
    }

    private boolean settingCron(String modelCode, String collectorModelCode) {
        if ("METRIC".equals(modelCode) || modelCodeList.contains(collectorModelCode)) {
            return false;
        }
        return true;
    }

    @Override
    public List<Map<String, Object>> getAdcdTrueEnableByMap(Map<String, Object> map) {
        return deviceMapper.selectAdcdTrueEnableByMap(map);
    }

    @Override
    public BaseResponse getDashboardUpperData(Long eid, List<String> aiopsItemList) {

        BaseResponse ddbr = getDeviceDashboard(eid);
        if (!ddbr.checkIsSuccess() || Objects.isNull(ddbr.getData())) {
            return BaseResponse.ok();
        }
        if (CollectionUtils.isEmpty(aiopsItemList)) {
            return BaseResponse.ok();
        }
        List<DeviceDashboardDetail> dddList = instanceMapper.selectDeviceDashboardDetail(eid);

        List<String> aiopsItemIdList = dddList
                .stream()
                .filter(i -> aiopsItemList.contains(i.getAiopsItem()))
                .map(DeviceDashboardDetail::getAiopsItemId)
                .collect(Collectors.toList());

//        Map<String, List<DeviceDashboardDetail>> dddMap = dddList.stream().collect(Collectors.groupingBy(DeviceDashboardDetail::getAiopsItem));

        List<AiopsItemParam> aiopsItemParamList = ((List<AiopsItemParam>) ddbr.getData()).stream()
                .filter(i -> aiopsItemList.contains(i.getAiopsItem())).collect(Collectors.toList());
        int totalCount = aiopsItemParamList.stream()
                .mapToInt(AiopsItemParam::getDeviceCount)
                .reduce(Integer::sum).orElse(0);
        int offlineCount = aiopsItemParamList.stream()
                .mapToInt(AiopsItemParam::getOfflineCount)
                .reduce(Integer::sum).orElse(0);
        // 为了查询运维项目体检分数
        Set<String> aiopsItemSet = aiopsItemParamList.stream()
                .map(AiopsItemParam::getAiopsItem).collect(Collectors.toSet());

        // 根据运维项目和eid查询客户最近一次体检运维项目分数
        int highLevelDeviceCount = getHighLevelDeviceCount(eid, aiopsItemIdList);
        // 根据eid和运维项目查询设备体检分数和预警
        int warningDeviceCount = getWarningDeviceCount(aiopsItemIdList);

        DeviceDashboardRet ret1 = new DeviceDashboardRet("deviceCount", buildDdc(offlineCount, totalCount, highLevelDeviceCount, warningDeviceCount));

        List<ScoreResult.ScoreItem<AiopsExamItemScore>> scoreItemsList = buildSiList(aiopsItemSet, eid);
        DeviceDashboardRet ret2 = new DeviceDashboardRet("aiopsItemScore", scoreItemsList);

        return BaseResponse.ok(Stream.of(ret1, ret2).collect(Collectors.toList()));
    }

    @Override
    public BaseResponse queryServerList(long eid, ImpalaQuery q) {

        List<Map<String, Object>> impalaResult = bigDataUtil.queryImpalaQuery(q);
        if (CollectionUtils.isEmpty(impalaResult)) {
            return BaseResponse.ok();
        }
        List<String> deviceNameList = impalaResult.stream()
                .map(item -> String.valueOf(item.get("aiopsinstancename")))
                .collect(Collectors.toList());
        List<Map<String, Object>> dbResult = deviceMapper.selectLatestDevicesByEidAndDeviceNames(eid, deviceNameList);
        if (CollectionUtils.isEmpty(dbResult)) {
            return BaseResponse.ok(impalaResult);
        }
        Map<String, Map<String, Object>> dbResultMap = dbResult.stream()
                .collect(Collectors.toMap(
                        item -> String.valueOf(item.get("deviceName")),
                        item -> item,
                        (existing, replacement) -> existing
                ));
        List<Map<String, Object>> mergedResult = impalaResult.stream()
                .map(impalaItem -> {
                    Map<String, Object> mergedItem = new HashMap<>(impalaItem);
                    String deviceName = String.valueOf(impalaItem.get("aiopsinstancename"));
                    Optional.ofNullable(dbResultMap.get(deviceName)).orElse(new HashMap<>()).forEach((key, value) -> {
                        if (!mergedItem.containsKey(key)) {
                            mergedItem.put(key, value);
                        }
                    });
                    return mergedItem;
                })
                .collect(Collectors.toList());
        return BaseResponse.ok(mergedResult);
    }

    @Override
    public BaseResponse getAwcByEidAndAiopsItem(Long eid, String aiopsItem, Integer pageNum, Integer pageSize, String warningCode,
                                                String warningName, String warningLevelString, Boolean customize) {
//        Set<String> customizeList = new HashSet<>();
//        customizeList.add(LongUtil.safeToString(eid));
        List<String> warningLevelList = Arrays.stream(warningLevelString.split(","))
                .filter(StringUtils::isNotBlank).collect(Collectors.toList());
        PageHelper.startPage(pageNum, pageSize);


        List<AiopsItemCollectWarningSet> codeList = deviceMapper.
                selectDeviceCollectWarningSetListByEidAndAiopsItemPage(eid, aiopsItem, null, warningName,
                        warningLevelList, DEFAULT_SCOPE_ID, customize);


        if (CollectionUtils.isEmpty(codeList)) {
            return BaseResponse.ok(codeList);
        }
        List<String> warningCodeList = codeList.stream().map(AiopsItemCollectWarningSet::getWarningCode).collect(Collectors.toList());
//        List<Long> acwIdList = codeList.stream().map(AiopsItemCollectWarningSet::getAcwId).collect(Collectors.toList());
        List<AiopsItemCollectWarningSet> customizeList = deviceMapper.
                selectDeviceCollectWarningSetListByEidAndAiopsItemPage(eid, aiopsItem, warningCodeList, null,
                        null, LongUtil.safeToString(eid), null);

        Map<String, AiopsItemCollectWarningSet> customizeMap = customizeList
                .stream()
                .collect(Collectors.toMap(AiopsItemCollectWarningSet::getWarningCode, Function.identity(), (o, n) -> n));
//        List<AiopsItemCollectWarningSet> customiseDeviceWarningList = deviceMapper
//                .selectCustomiseDeviceCollectWarningSetListByEid(customizeList, eid, warningCodeList, innerRunnerAccIdSet);

//        List<Long> deviceAcwIdList = customiseDeviceWarningList.stream().map(AiopsItemCollectWarningSet::getAcwId)
//                .collect(Collectors.toList());
//        acwIdList.addAll(deviceAcwIdList);
//        Map<String, List<Long>> customiseCodeMap = customiseDeviceWarningList
//                .stream()
//                .collect(Collectors.groupingBy(AiopsItemCollectWarningSet::getWarningCode, Collectors.mapping(AiopsItemCollectWarningSet::getAcwId, Collectors.toList())));

        List<AiopsAuthStatus> aiopsAuthStatuses = getAuthList();

        List<AiopsItemCollectWarningSet.AiopsItemCollectWarningSetDetail> aicwsdList = deviceMapper
                .selectAiIdByAcwIdList(warningCodeList, eid, aiopsItem, aiopsAuthStatuses, innerRunnerAccIdSet);
        Map<String, Set<Long>> codeMap = aicwsdList.stream()
                .collect(Collectors.groupingBy(AiopsItemCollectWarningSet.AiopsItemCollectWarningSetDetail::getWarningCode,
                        Collectors.mapping(AiopsItemCollectWarningSet.AiopsItemCollectWarningSetDetail::getAiId, Collectors.toSet())));

//        Map<Long, Boolean> enableMap = getTenantWarningEnable(null, eid);

        codeList
                .forEach(item -> {
                    if (customizeMap.containsKey(item.getWarningCode())) {
                        AiopsItemCollectWarningSet cust = customizeMap.get(item.getWarningCode());

                        BeanUtils.copyProperties(cust, item);
                        item.setIsWarningCustomize(true);

                    } else {
                        item.setIsWarningCustomize(false);
                    }
                    // 获取实例集合
                    Set<Long> instanceSet = codeMap.getOrDefault(item.getWarningCode(), new HashSet<>());
                    item.setAicwdListSize(instanceSet.size());
                });


        PageInfo<AiopsItemCollectWarningSet> pageInfo = new PageInfo<>(codeList);
        return BaseResponse.ok(pageInfo);
    }

    @Override
    public BaseResponse<List<WarningMergeStatisticsDetailInfo>> getAwcByEidAndAiopsItemAndAcwId(Long eid, String aiopsItem, Long acwId) {
        CollectWarning collectWarning = collectWarningMapper.selectCollectWarningById(acwId);


        List<AiopsAuthStatus> aiopsAuthStatuses = getAuthList();

        List<AiopsItemCollectWarningSet.AiopsItemCollectWarningSetDetail> aicwsdList = deviceMapper.selectAiIdByAcwIdList(Stream.of(collectWarning.getWarningCode()).collect(Collectors.toList()),
                eid, aiopsItem, aiopsAuthStatuses,
                innerRunnerAccIdSet);
        if (CollectionUtils.isEmpty(aicwsdList)) {
            return BaseResponse.ok(new ArrayList<>());
        }
        Map<Long, List<AiopsItemCollectWarningSet.AiopsItemCollectWarningSetDetail>> aiIdMap = aicwsdList.stream()
                .collect(Collectors.groupingBy(AiopsItemCollectWarningSet.AiopsItemCollectWarningSetDetail::getAiId));
        Map<Long, Integer> warningCountMap = getWarningCount(aiIdMap.keySet());
        Map<String, Object> param = new HashMap<>();
        param.put("aiIdList", aiIdMap.keySet());
        param.put("aiopsItemList", Stream.of(aiopsItem).collect(Collectors.toList()));
        param.put("eid", eid);
        param.put("aiopsAuthStatusList", Lists.newArrayList(AiopsAuthStatus.AUTHED, AiopsAuthStatus.UNAUTH, AiopsAuthStatus.NONE));
        List<MergeStatisticsDetailInfo> mergeStatisticsDetailInfos = instanceMapper.selectMergeStatisticsDetailByMap(param);


        List<WarningMergeStatisticsDetailInfo> retList = mergeStatisticsDetailInfos.stream()
                .map(wd -> {
                    WarningMergeStatisticsDetailInfo detailInfo = new WarningMergeStatisticsDetailInfo();
                    BeanUtils.copyProperties(wd, detailInfo);

                    List<AiopsItemCollectWarningSet.AiopsItemCollectWarningSetDetail> setDetailList = aiIdMap.getOrDefault(wd.getId(), new ArrayList<>());
                    detailInfo.setSetDetailList(setDetailList);
                    if (!CollectionUtils.isEmpty(setDetailList)) {
                        AiopsItemCollectWarningSet.AiopsItemCollectWarningSetDetail setDetail = setDetailList.get(0);
                        detailInfo.setAdcdId(setDetail.getAdcdId());
                        detailInfo.setDeviceId(setDetail.getDeviceId());
                        detailInfo.setAccId(setDetail.getAccId());

                    }

                    detailInfo.setIsWarningEnable(setDetailList.stream()
                            .anyMatch(AiopsItemCollectWarningSet.AiopsItemCollectWarningSetDetail::getIsWarningEnable));
                    // 处理scopeIdList
                    detailInfo.setIsWarningCustomize(setDetailList.stream()
                            .anyMatch(AiopsItemCollectWarningSet.AiopsItemCollectWarningSetDetail::getIsWarningCustomize));
                    detailInfo.setWarningCnt(warningCountMap.getOrDefault(wd.getId(), 0));

                    return detailInfo;
                })
                .collect(Collectors.toList());
        return BaseResponse.okT(retList);
    }

    private List<DeviceDashboardRet.DeviceDashboardCount> buildDdc(int offlineCount, int totalCount, int highLevelDeviceCount, int warningDeviceCount) {

        List<DeviceDashboardRet.DeviceDashboardCount> ddcList = new ArrayList<>();
        DeviceDashboardRet.DeviceDashboardCount offline = new DeviceDashboardRet.DeviceDashboardCount(OFFLINE, offlineCount);
        DeviceDashboardRet.DeviceDashboardCount total = new DeviceDashboardRet.DeviceDashboardCount(TOTAL, totalCount);
        DeviceDashboardRet.DeviceDashboardCount highRisk = new DeviceDashboardRet.DeviceDashboardCount(HIGH_RISK, highLevelDeviceCount);
        DeviceDashboardRet.DeviceDashboardCount highWarning = new DeviceDashboardRet.DeviceDashboardCount(HIGH_WARNING, warningDeviceCount);
        ddcList.add(offline);
        ddcList.add(total);
        ddcList.add(highRisk);
        ddcList.add(highWarning);
        return ddcList;
    }

    private List<ScoreResult.ScoreItem<AiopsExamItemScore>> buildSiList(Set<String> aiopsItemSet, Long eid) {
        AiopsExamScoreQueryService<AiopsExamItemScore> aiopsExamItemScoreServiceImpl = examScoreQueryFactory
                .getQueryService("AiopsExamItemScoreServiceImpl");
        ScoreResult<AiopsExamItemScore> aeisRet = aiopsExamItemScoreServiceImpl.queryScore(eid);
        List<ScoreResult.ScoreItem<AiopsExamItemScore>> scoreResultList = aeisRet.getScoreResultList()
                .stream().filter(ss -> aiopsItemSet.contains(ss.getScoreType().getAiopsItem()))
                .collect(Collectors.toList());
        aiopsItemSet.forEach(aiopsItem -> {
            boolean noneMatch = scoreResultList.stream().noneMatch(s -> aiopsItem.equals(s.getScoreType().getAiopsItem()));

            if (noneMatch) {
                ScoreResult.ScoreItem<AiopsExamItemScore> item = new ScoreResult.ScoreItem<>(0, "", LocalDateTime.now());
                AiopsExamItemScore aeis = new AiopsExamItemScore();
                aeis.setAiopsItem(aiopsItem);
                item.setScoreType(aeis);
                scoreResultList.add(item);
            }
        });
        return scoreResultList;
    }

    private int getHighLevelDeviceCount(Long eid, List<String> aiopsItemIdList) {

        AiopsExamRecord aer = aiopsExamService.getRecentlyAiopsExamRecord(eid);
        if (Objects.isNull(aer)) {
            return 0;
        }
        List<AiopsExamItemInstanceScore> aeiisList = aiopsExamMapper
                .selectAiopsExamRecordInstance(Stream.of(aer.getId()).collect(Collectors.toList()));

        return (int) aeiisList
                .stream()
                .filter(i -> aiopsItemIdList.contains(i.getAiopsItemId()))
                .filter(i -> "HIGH".equals(i.getLevelCode())).count();
    }

    private int getWarningDeviceCount(List<String> aiopsItemIdList) {
        String sql = "SELECT  count(distinct deviceId) as cnt FROM servicecloud.warning WHERE status = 'unsolved' AND warninglevel = 'FATAL' AND deviceId IN ('"
                + String.join("','", aiopsItemIdList) + "')";
        List<Map<String, Object>> retMap = bigDataUtil.srQuery(sql);
        if (CollectionUtils.isEmpty(retMap)) {
            return 0;
        }
        Object cnt = retMap.get(0).getOrDefault("cnt", 0);
        return IntegerUtil.objectToInteger(cnt);
    }

    @Override
    public BaseResponse getTenantDeviceCnt(TenantDeviceCntReq tenantDeviceCntReq) {
        List<TenantDeviceCnt> tenantDeviceCntList = deviceMapper.getDeviceCntByDeviceType(tenantDeviceCntReq);
        return BaseResponse.ok(tenantDeviceCntList);
    }

    private List<CollectWarning> getCollectWarningList(String deviceId, List<CollectWarning> collectWarningList) {
        if (CollectionUtils.isEmpty(collectWarningList)) {
            return collectWarningList;
        }
        // 雲備援是etl上報數據 所以沒有deviceId 只有做預警 所以这边离开保持原有得预警项
        if (StringUtils.isBlank(deviceId)) {
            return collectWarningList;
        }


        AiopsKitDevice ad = deviceMapper.selectDeviceByDeviceId(deviceId);
        Long eid = ad.getEid();
        List<Long> acwIdList = collectWarningList.stream().map(CollectWarning::getId).collect(Collectors.toList());
        List<CollectWarning> acwList = collectWarningMapper.selectCollectWarningByIds(acwIdList);
        List<String> warningCodeList = acwList.stream().map(CollectWarning::getWarningCode).collect(Collectors.toList());

        Map<String, Object> param = new HashMap<>();
        param.put("warningCodeList", warningCodeList);
        param.put("scopeId", eid);
        List<CollectWarning> collectWarningSetList = collectWarningMapper.getCollectWarningSetList(param);

        if (CollectionUtils.isEmpty(collectWarningSetList)) {
            return collectWarningList;
        }
//        for (CollectWarning collectWarning : collectWarningSetList) {
//            AiopsTenantWarning atw = deviceV2Mapper.selectAcwTenantEnableByAcwId(collectWarning.getId(), eid);
//            if (Objects.nonNull(atw)) {
//                collectWarning.setWarningEnable(atw.getIsWarningEnable());
//            }
//        }

        Map<String, CollectWarning> codeMap = collectWarningSetList.stream()
                .collect(Collectors.toMap(CollectWarning::getWarningCode, Function.identity(), (v1, v2) -> v1));
        // 如果找到有租户自定义，则使用租户自定义
        return acwList.stream()
                .map(acw -> codeMap.getOrDefault(acw.getWarningCode(), acw))
                .collect(Collectors.toList());
    }

    private Map<Long, Integer> getWarningCount(Set<Long> aiIdList) {
        String aiIdString = aiIdList.stream().map(aiId -> aiId + "").collect(Collectors.joining(","));
        String sql = "select count(1) cnt,aiid from servicecloud.warning where aiid in (" + aiIdString + ")  group by aiid";
        List<Map<String, Object>> maps = bigDataUtil.srQuery(sql);
        Map<Long, Integer> aiIdMap = maps.stream().collect(Collectors.toMap(i -> LongUtil.objectToLong(i.get("aiid")),
                i -> IntegerUtil.objectToInteger(i.get("cnt"))));
        return aiIdMap;
    }

    private List<AiopsAuthStatus> getAuthList() {
        return Lists.newArrayList(AiopsAuthStatus.AUTHED, AiopsAuthStatus.UNAUTH, AiopsAuthStatus.NONE);
    }

    private Set<String> getCustomiseDeviceSet(Long eid) {
        return Sets.newHashSet(DEFAULT_SCOPE_ID, LongUtil.safeToString(eid));
    }

    @Override
    public Map<Long, Boolean> getTenantWarningEnable(List<Long> acwIdList, Long eid) {
        return deviceMapper.selectAcwTenantEnable(acwIdList, eid)
                .stream()
                .collect(Collectors.toMap(
                        AiopsTenantWarning::getAcwId,
                        AiopsTenantWarning::getIsWarningEnable
                ));
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public BaseResponse fixSourceAcwIdNull(Long eid) {
        List<CollectWarning> needFixData = deviceMapper.selectNeedFixData(eid);
        if (CollectionUtils.isEmpty(needFixData)) {
            return BaseResponse.ok();
        }
        List<CollectWarning> collectWarnings = deviceMapper.selectFixData(eid);
        Map<String, Long> codeMap = collectWarnings.stream().collect(Collectors.toMap(CollectWarning::getWarningCode, CollectWarning::getId));
        needFixData
                .stream().filter(i -> codeMap.containsKey(i.getWarningCode()))
                .forEach(i -> {
                    Long sourceAcwId = codeMap.get(i.getWarningCode());
                    deviceMapper.updateNeedFixData(sourceAcwId, i.getId());

                });
        return BaseResponse.ok();
    }

    @Override
    public BaseResponse getAcwIsEnable(Long acwId, Long eid) {
        Map<String, Object> param = new HashMap<>();
        param.put("id", acwId);
        CollectWarning collectWarning = collectWarningMapper.getCollectWarning(param);
        return BaseResponse.ok(collectWarningMapper.selectAcwIsEnable(acwId, eid, collectWarning.getWarningCode()));
    }

    @Override
    public void deleteWarning(String deviceId) {
        AiopsKitDevice aiopsKitDevice = deviceMapper.selectDeviceByDeviceId(deviceId);

        //获取所有预警项数据
        Long id = aiopsKitDevice.getId();
        List<DeviceCollectDetail> deviceCollectDetailList = deviceMapper.getDeviceCollectDetailList(id, null);
        //然后删除预警数据adcdId, accId, deviceId, scopeId, collectWarningList
        deviceCollectDetailList.forEach(item -> {

            long adcdId = item.getId();
            long accId = item.getAccId();
            String deviceId1 = item.getDeviceId();
            String scopeId = item.getScopeId();
            List<CollectWarning> collectWarningList = item.getCollectWarningList();
            //删除预警项目数据
            deleteCollectDetailForDevice(adcdId, accId, deviceId1, scopeId, collectWarningList);
        });
    }

    @Override
    public BaseResponse getAwcByEidAndAiopsItemAndAsset(Long eid, String aiopsItem, Boolean related, String deviceNameOrId, int pageNum, int pageSize, Long aiopsInstance) {
        Map<String, Object> param = new HashMap<>();

        List<String> aiopsItemList = Stream.of(aiopsItem).collect(Collectors.toList());
        param.put("aiopsItemList", aiopsItemList);
        param.put("eid", eid);
        param.put("aiopsAuthStatusList", Lists.newArrayList(
                AiopsAuthStatus.AUTHED, AiopsAuthStatus.UNAUTH, AiopsAuthStatus.NONE));
        param.put("deviceNameOrId", deviceNameOrId);
        param.put("id", aiopsInstance);
        List<MergeStatisticsDetailInfo> mergeStatisticsDetailInfos =
                instanceMapper.selectMergeStatisticsDetailByMap(param);
        Map<String, Object> result = new HashMap<>();
        if (mergeStatisticsDetailInfos.isEmpty()) {
            result.put("total", 0);
            result.put("list", Collections.emptyList());
            return BaseResponse.ok(result);
        }

        List<String> categoryNumber = instanceMapper.selectCategoryNamesByAiopsItems(aiopsItemList);

        // 所有 aiId
        Set<String> allAiIds = mergeStatisticsDetailInfos.stream()
                .map(info -> info.getAiopsInstanceId().toString())
                .collect(Collectors.toSet());

        // 建立 aiId 關聯 Map（是否在任一張表中出現過）
        Map<String, Boolean> aiIdRelationMap = new HashMap<>();

        for (String table : categoryNumber) {
            if (allAiIds.isEmpty()) continue;

            String sql = String.format("SELECT DISTINCT aiId FROM %s.%s WHERE aiId IN (%s)",
                    bigDataUtil.getSrDbName(),
                    table,
                    allAiIds.stream().map(id -> "'" + id + "'").collect(Collectors.joining(","))
            );

            List<Map<String, Object>> resultList = bigDataUtil.query(sql);

            if (resultList != null) {
                for (Map<String, Object> row : resultList) {
                    String aiId = String.valueOf(row.get("aiId"));
                    aiIdRelationMap.put(aiId, true);
                }
            }
        }

        // 過濾與加上 isRelated 標記
        List<MergeStatisticsDetailInfo> filteredList = new ArrayList<>();
        for (MergeStatisticsDetailInfo info : mergeStatisticsDetailInfos) {
            String aiId = info.getAiopsInstanceId().toString();
            boolean hasRelation = aiIdRelationMap.getOrDefault(aiId, false);

            info.setIsRelated(hasRelation ? 1 : 0);

            if (related == null || related.equals(hasRelation)) {
                filteredList.add(info);
            }
        }

        // 分頁
        int totalCount = filteredList.size();
        int fromIndex = Math.min((pageNum - 1) * pageSize, totalCount);
        int toIndex = Math.min(fromIndex + pageSize, totalCount);
        List<MergeStatisticsDetailInfo> pageList = filteredList.subList(fromIndex, toIndex);

        result.put("total", totalCount);
        result.put("list", pageList);
        return BaseResponse.ok(result);
    }
}
