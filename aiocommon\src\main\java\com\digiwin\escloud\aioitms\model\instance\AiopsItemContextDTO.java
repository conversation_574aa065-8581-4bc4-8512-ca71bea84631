package com.digiwin.escloud.aioitms.model.instance;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class AiopsItemContextDTO {
    private Long aiId;
    private String aiopsItem;
    private String aiopsItemId;
    /**
     * TODO: 以下三个属性暂时不传入值
     */
    @ApiModelProperty("是否添加实例收集项到设备(若为true，则sourceDeviceId必填)")
    private Boolean isAddInstanceCollectToDevice;
    @ApiModelProperty("是否映射实例到设备(若为true，则sourceDeviceId必填)")
    private Boolean isMapInstancesToDevices;
    @ApiModelProperty("来源设备Id")
    private String sourceDeviceId;
}
