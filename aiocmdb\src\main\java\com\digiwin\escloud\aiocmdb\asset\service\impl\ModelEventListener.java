package com.digiwin.escloud.aiocmdb.asset.service.impl;

import com.digiwin.escloud.aiocmdb.asset.service.IAssetCategoryService;
import com.digiwin.escloud.aiocmdb.model.model.ModelModifiedEvent;
import com.digiwin.escloud.aiocmdb.util.CommonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.util.stream.Collectors;
import java.util.stream.Stream;

@Component
@Slf4j
public class ModelEventListener {

    @Autowired
    private IAssetCategoryService assetCategoryService;
    @Autowired
    private CommonUtils commonUtils;

    @Async
    @EventListener
    public void handleModelModification(ModelModifiedEvent modelEvent) {
        log.info("Received model modification event for modelCode: [{}]. Triggering display field reset.",modelEvent.getModelCode());
        String modelCode = modelEvent.getModelCode();
        try {
            //根据配置自动建表
            commonUtils.autoCreateTable(Stream.of(modelEvent.getModelCode()).collect(Collectors.toList()));
            // 在独立的线程中异步执行耗时操作
            assetCategoryService.populateModelShowFields(modelCode,modelEvent.getSid());
            log.info("Successfully reset display fields for modelCode: [{}].", modelCode);
        } catch (Exception e) {
            log.error("Failed to reset display fields for modelCode: [{}].",modelCode, e);
        }
    }
}
