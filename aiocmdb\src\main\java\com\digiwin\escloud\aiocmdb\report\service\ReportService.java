package com.digiwin.escloud.aiocmdb.report.service;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.digiwin.escloud.aiocmdb.assetmaintenance.bigdata.BigDataUtil;
import com.digiwin.escloud.aiocmdb.assetmaintenance.bigdata.DataService;
import com.digiwin.escloud.aiocmdb.field.model.Field;
import com.digiwin.escloud.aiocmdb.field.model.FieldTypeEnum;
import com.digiwin.escloud.aiocmdb.field.service.FieldService;
import com.digiwin.escloud.aiocmdb.freemarker.FreemarkerService;
import com.digiwin.escloud.aiocmdb.mail.IMailSendService;
import com.digiwin.escloud.aiocmdb.report.dao.ReportDao;
import com.digiwin.escloud.aiocmdb.report.model.*;
import com.digiwin.escloud.aiocmdb.report.model.dto.AssetObject;
import com.digiwin.escloud.aiocmdb.report.model.dto.ReportMaintenanceUpdateDTO;
import com.digiwin.escloud.aiocmdb.util.FreemarkerTplData;
import com.digiwin.escloud.aioitms.model.bigdata.OpType;
import com.digiwin.escloud.aioitms.model.bigdata.StarRocksEntity;
import com.digiwin.escloud.common.controller.ParamCheckHelp;
import com.digiwin.escloud.common.feign.AioUserFeignClient;
import com.digiwin.escloud.common.model.ResponseCode;
import com.digiwin.escloud.common.response.BaseResponse;
import com.digiwin.escloud.common.response.PageInfo;
import com.digiwin.escloud.common.util.*;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.Lists;
import com.google.gson.*;
import com.jayway.jsonpath.Criteria;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

import static com.digiwin.escloud.common.util.auto.MessageUtils.ZH_CN_STANDARD;
import static com.digiwin.escloud.common.util.auto.MessageUtils.ZH_TW_STANDARD;

@Service
@Slf4j
public class ReportService implements IReportService, ParamCheckHelp {
    @Value("${digiwin.cmdb.connectarea}")
    private String connectArea;
    @Value("${mis.root:https://aieom.digiwincloud.com/}")
    private String misRoot;
    @Value("${aiops.root:https://aiops.digiwincloud.com/}")
    private String aiopsRoot;
    @Autowired
    private ReportDao reportDao;
    @Autowired
    private IMailSendService mailSendService;
    /*@Autowired
    private MongoTemplate mongoTemplate;*/
    @Autowired
    private DataService dataService;
    @Autowired
    FieldService fieldService;
    @Resource
    private AioUserFeignClient aioUserFeignClient;
    @Autowired
    private FreemarkerService tplService;

    @Autowired
    BigDataUtil bigDataUtil;

    @Override
    public List<ReportReceiver> getReceivers(String serviceCode, String moduleCode, int page, int size) {
        int start = (page - 1) * size;
        HashMap<String, Object> map = new HashMap<>();
        map.put("sid", RequestUtil.getHeaderSid());
        map.put("serviceCode", serviceCode);
        map.put("moduleCode", moduleCode);
        map.put("start", start);
        map.put("size", size);
        map.put("eidList", getServiceEidList(reportDao.getReceiversEidList()));
        return reportDao.getReceivers(map);
    }

    @Override
    public int getReceiversCount(String serviceCode, String moduleCode) {
        HashMap<String, Object> map = new HashMap<>();
        map.put("sid", RequestUtil.getHeaderSid());
        map.put("serviceCode", serviceCode);
        map.put("moduleCode", moduleCode);
        map.put("eidList", getServiceEidList(reportDao.getReceiversEidList()));
        return reportDao.getReceiversCount(map);
    }

    @Override
    public BaseResponse getReceiversByRmId(Long rmId) {
        //region 参数检查

        Optional<BaseResponse> optResponse = checkParamIsEmpty(rmId, "rmId");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        //endregion

        ReportMaintenance rm = reportDao.selectReportMaintenanceById(rmId);
        if (rm == null) {
            return BaseResponse.ok();
        }
        String receivers = rm.getReceivers();
        if (StringUtils.isEmpty(receivers)) {
            return BaseResponse.ok();
        }
        long sid = rm.getSid();
        String[] receiversArray = receivers.toLowerCase().replace(",", ";")
                .replace("\n", ";").split(";");
        int length = receiversArray.length;
        List<ReportReceiver> resultList = new ArrayList<>(length);
        Map<String, List<ReportReceiver>> foundReceiverListMap = new HashMap<>();
        Set<String> emptyRRSet = new HashSet<>(length);
        Map<String, Object> map = new HashMap<>(2);
        map.put("sid", RequestUtil.getHeaderSidOrDefault(sid));
        map.put("serviceCode", rm.getServiceCode());
        for (int i = 0; i < length; i++) {
            String partMail = receiversArray[i];
            if (StringUtils.isBlank(partMail)) {
                continue;
            }
            List<ReportReceiver> rrList = foundReceiverListMap.get(partMail);
            if (rrList == null) {
                map.put("partMail", partMail);
                rrList = reportDao.selectReportReceiversByPartMap(map);
                foundReceiverListMap.put(partMail, rrList);
            }
            if (CollectionUtils.isEmpty(rrList)) {
                //返回未知
                if (!emptyRRSet.contains(partMail)) {
                    emptyRRSet.add(partMail);
                    resultList.add(new ReportReceiver("", partMail, true));
                }
                continue;
            }
            StringBuilder sbGuestMails = new StringBuilder(partMail);
            if (rrList.size() == 1) {
                ReportReceiver foundRR = rrList.get(0);
                String foundMail = foundRR.getReceiverMail();
                if (StringUtils.isBlank(foundMail)) {
                    //返回未知
                    if (!emptyRRSet.contains(partMail)) {
                        emptyRRSet.add(partMail);
                        resultList.add(new ReportReceiver("", partMail, true));
                    }
                }
                foundMail = foundMail.toLowerCase().toLowerCase().replace(",", ";")
                        .replace("\n", ";");
                //只有一笔要判断一下有没有命中或命中后面的信箱
                boolean found = foundMail.equals(partMail);
                for (int j = i + 1; j < length; j++) {
                    String nextMail = receiversArray[j];
                    sbGuestMails.append(";");
                    sbGuestMails.append(nextMail);
                    if (StringUtils.isBlank(nextMail)) {
                        if (foundMail.equals(nextMail)) {
                            found = true;
                            i = j;
                            continue;
                        }
                        sbGuestMails.append(";");
                    }
                    if (foundMail.startsWith(sbGuestMails.toString())) {
                        found = true;
                        i = j;
                        continue;
                    }
                    break;
                }
                if (found) {
                    resultList.addAll(rrList);
                } else {
                    if (!emptyRRSet.contains(partMail)) {
                        emptyRRSet.add(partMail);
                        resultList.add(new ReportReceiver("", partMail, true));
                    }
                }
                continue;
            }
            Map<String, ReportReceiver> rrMap = rrList.stream().filter(x -> !StringUtils.isEmpty(x.getReceiverMail()))
                    .peek(x -> x.setCheckMail(x.getReceiverMail().toLowerCase()))
                    .collect(Collectors.toMap(ReportReceiver::getCheckMail, x -> x, (x, y) -> y));
            //先指定必定命中的那一个
            ReportReceiver lastFoundRR = rrMap.get(partMail);
            //找出可能的组合
            for (int j = i + 1; j < length; j++) {
                String nextMail = receiversArray[j];
                sbGuestMails.append(";");
                sbGuestMails.append(nextMail);
                ReportReceiver foundRR = rrMap.get(sbGuestMails.toString());
                if (foundRR == null) {
                    //只要下一个拼接后未命中，就可以退出(因为再拼接也不可能命中了)
                    break;
                }
                lastFoundRR = foundRR;
                i = j;
            }
            resultList.add(lastFoundRR);
        }
        return BaseResponse.ok(resultList);
    }

    @Override
    public List<ReportReceiverModuleBase> getModules() {
        if (LongUtil.isNotEmpty(RequestUtil.getHeaderServiceProviderSid())) {
            //目前只能获取漏扫模块数据
            return reportDao.getModules()
                    .stream().filter(i -> "vulnerability".equals(i.getModuleCode())).collect(Collectors.toList());
        }
        return reportDao.getModules();
    }

    @Override
    public boolean saveReceiver(ReportReceiver reportReceiver) {
        if (reportReceiver.getId() == 0) {
            reportReceiver.setId(SnowFlake.getInstance().newId());
        }
        reportReceiver.setSid(RequestUtil.getHeaderSid());
        boolean result = reportDao.saveReceiver(reportReceiver) > 0;
        if (result) {
            reportDao.deleteReceiverModules(reportReceiver.getId());

            if (!CollectionUtils.isEmpty(reportReceiver.getModules())) {
                HashMap<String, Object> map = new HashMap<>();
                map.put("rrId", reportReceiver.getId());
                map.put("list", reportReceiver.getModules());
                reportDao.saveReceiverModules(map);
            }
        }
        return result;
    }

    @Override
    public boolean deleteReceiver(long id) {
        boolean result = reportDao.deleteReceiver(id) > 0;
        if (result) {
            reportDao.deleteReceiverModules(id);
        }
        return result;
    }

    @Override
    public List<ReportMaintenance> getReports(String serviceCode, Integer reportStatus, Integer misStatus, String beginTime, String endTime, int page, int size) {
        int start = (page - 1) * size;
        HashMap<String, Object> map = new HashMap<>();
        map.put("sid", RequestUtil.getHeaderSid());
        map.put("serviceCode", serviceCode);
        map.put("reportStatus", reportStatus);
        map.put("misStatus", misStatus);
        map.put("beginTime", beginTime);
        map.put("endTime", endTime);
        map.put("start", start);
        map.put("size", size);

        List<ReportMaintenance> reportMaintenanceList = reportDao.getReports(map);
        reportMaintenanceList.forEach(report -> {
            HashMap<String, Object> fileMap = new HashMap<>();
            fileMap.put("reportId", report.getId());
            fileMap.put("fileType", report.getReportType());
            List<ReportFile> files = reportDao.getFiles(fileMap);

            files.forEach(file -> {
                String url = String.format(
                        "%saiogateway/aiocmdb/api/report/%s/file?reportId=%d&fileId=%d",
                        misRoot,
                        report.getServiceCode(),
                        report.getId(),
                        file.getId()
                );
                file.setUrl(url);
            });

            report.setFiles(files);
        });
        return reportMaintenanceList;
    }

    @Override
    public int getReportsCount(String serviceCode, Integer reportStatus, Integer misStatus, String beginTime, String endTime) {
        HashMap<String, Object> map = new HashMap<>();
        map.put("sid", RequestUtil.getHeaderSid());
        map.put("serviceCode", serviceCode);
        map.put("reportStatus", reportStatus);
        map.put("misStatus", misStatus);
        map.put("beginTime", beginTime);
        map.put("endTime", endTime);
        return reportDao.getReportsCount(map);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BaseResponse sendReport(ReportMaintenance rm) {
        // 設置寄送時間
        rm.setSendTime(new Date());
        BaseResponse res = new BaseResponse();

        // 檢查接收人是否為空
        if (StringUtils.isEmpty(rm.getReceivers())) {
            res.setCode(ResponseCode.INTERNAL_ERROR.toString());
            res.setErrMsg("the receivers is empty");
            return res;
        }

        // 處理接收人
        String receivers = processReceivers(rm.getReceivers());
        rm.setReceivers(receivers);

        // 保存報告
        reportDao.saveReportMaintenance(rm);

        // 處理附件
        String attachmentHtml = processAttachments(rm);

        // 生成郵件主題和內容
        Boolean isCN = "CN".equals(connectArea);
        List<String> reportTime = Arrays.asList(rm.getReportTime().split("-"));
        String reportName = getReportName(rm.getReportType(), isCN);
        String subject = String.format("%s_%s-%s_%s(%s)",
                reportName,
                reportTime.get(0),
                reportTime.get(1),
                rm.getCustomerName(),
                isCN ? "系统发送请勿回信" : "系統發送請勿回信"
        );

        // 處理查詢模板參數
        Map<String, Object> mailParams = prepareMailParameters(rm, attachmentHtml);

        // 取得模板內容
        String content = getMailContent(mailParams, "AssetMaintenanceReportMailContent.ftl", isCN);

        // 非同步發送郵件
        sendMailAsync(rm, subject, content, isCN);

        //確認發送執行完後更新報告狀態
        reportDao.updateReportMaintenance(rm.getId(), 2);
        //更新MIS端發送狀態
        reportDao.updateMISStatus(rm.getId(), 1, null, null);

        res.setCode(ResponseCode.SUCCESS.toString());
        return res;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BaseResponse downloadReport(ReportTemp rt) {
        long id = SnowFlake.getInstance().newId();
        long sid = RequestUtil.getHeaderSid();
        rt.setId(id);
        rt.setSid(sid);
        BaseResponse res = new BaseResponse();
        reportDao.saveReportTemp(rt);
        rt.getReportTempDevices().stream().forEach(o -> {
            o.setId(SnowFlake.getInstance().newId());
            o.setReportTempId(id);
        });
        reportDao.saveReportTempDevice(rt.getReportTempDevices());
        res.setCode(ResponseCode.SUCCESS.toString());
        res.setData(id);
        return res;
    }


    @Override
    public JsonArray GetMaintenancestateReportData(String serviceCode, String reportTempId, String modelCode, String type) {
        Gson gson = new Gson();
        JsonArray jsonArray = new JsonArray();
        try {
            long reportTempIdlong = Long.parseLong(reportTempId);
            //获取报表的设备列表
            ReportTemp reportTemp = reportDao.getReportTemp(reportTempIdlong, modelCode);
            if (reportTemp == null || reportTemp.getReportTempDevices() == null || reportTemp.getReportTempDevices().size() <= 0)
                return new JsonArray();

            //获取设备当年的维护记录列表
            List<String> deviceList = new ArrayList<String>();
            for (ReportTempDevice deviceTemp : reportTemp.getReportTempDevices()) {
                deviceList.add(deviceTemp.getDeviceId());
            }

            //获取查询年月
            String yearMonth = reportTemp.getReportTime();
            String yearMonthStart = reportTemp.getReportTime();
            if (type.equals("year"))
                yearMonthStart = reportTemp.getReportTime().substring(0, 5) + "01";
            /*Criteria c = Criteria.where("deviceId").in(deviceList).and("serviceCode").is(serviceCode).and("maintenanceYearMonth").gte(yearMonthStart).lte(yearMonth);
            List<Object> deviceDetailList = mongoTemplate.find(new Query(c), Object.class, "rs_mt_" + modelCode);*/


            List<Map<String, Object>> deviceDetailList = dataService.getMaintenanceReportList(modelCode, RequestUtil.getHeaderEid(), "", deviceList, "", yearMonthStart, yearMonth);
            if (deviceDetailList == null || deviceDetailList.size() <= 0) return new JsonArray();

            JsonArray jsonDeviceSourceArray = gson.fromJson(gson.toJson(deviceDetailList), JsonArray.class);
            JsonArray jsonDeviceArray = new JsonArray();
            analysisJson(serviceCode, jsonDeviceArray, jsonDeviceSourceArray, true);
            analysisJson(serviceCode, jsonArray, jsonDeviceArray, false);

            //return gson.toJson(jsonArray);
            return jsonArray;

        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return new JsonArray();
    }


    @Override
    public JsonArray GetMaintenancestateReportDataByDeviceId(String serviceCode, String deviceId, String modelCode, String yearMonth, String type) {
        Gson gson = new Gson();
        JsonArray jsonArray = new JsonArray();
        try {
            //获取查询年月
            String yearMonthStart = yearMonth;
            if (type.equals("year"))
                yearMonthStart = yearMonth.substring(0, 5) + "01";
            /*Criteria c = Criteria.where("deviceId").is(deviceId).and("serviceCode").is(serviceCode).and("maintenanceYearMonth").gte(yearMonthStart).lte(yearMonth);
            ;
            List<Object> deviceDetailList = mongoTemplate.find(new Query(c), Object.class, "rs_mt_" + modelCode);*/

            List<Map<String, Object>> deviceDetailList = dataService.getMaintenanceReportList(modelCode, RequestUtil.getHeaderEid(), deviceId, new ArrayList<>(), "", yearMonthStart, yearMonth);

            if (deviceDetailList == null || deviceDetailList.size() <= 0) return new JsonArray();

            JsonArray jsonDeviceSourceArray = gson.fromJson(gson.toJson(deviceDetailList), JsonArray.class);
            JsonArray jsonDeviceArray = new JsonArray();
            analysisJson(serviceCode, jsonDeviceArray, jsonDeviceSourceArray, true);
            analysisJson(serviceCode, jsonArray, jsonDeviceArray, false);

            return jsonArray;
            // return gson.toJson(jsonArray);

        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return new JsonArray();
    }

    @Override
    public JsonArray GetMaintenancestateReportDataByAllDevice(String serviceCode, String modelCode, String yearMonth, String item) {
        Gson gson = new Gson();
        JsonArray jsonArray = new JsonArray();
        JsonArray jsonDirectArray = new JsonArray();
        try {
            //获取查询年月
            String yearMonthStart = yearMonth;

            Criteria c1 = Criteria.where("serviceCode").is(serviceCode);
            Criteria c = Criteria.where("serviceCode").is(serviceCode).and("maintenanceYearMonth").is(yearMonth);
            List<Map<String, Object>> deviceDetailList = null;
            if (modelCode.equals("Software_Hardware") || modelCode.equals("BackupSchedule"))
//                deviceDetailList =  mongoTemplate.find(new Query(c1), Object.class, "rs_" + modelCode);
                deviceDetailList = dataService.getAssetList(modelCode, RequestUtil.getHeaderEid(), "", "", "", "", "", "", "", "", "", "", 0, 0);
            else
                deviceDetailList = dataService.getMaintenanceReportList(modelCode, RequestUtil.getHeaderEid(), "", new ArrayList<>(), "", yearMonthStart, yearMonthStart);

//            deviceDetailList =  mongoTemplate.find(new Query(c), Object.class, "rs_mt_" + modelCode);

            if (deviceDetailList == null || deviceDetailList.size() <= 0) return new JsonArray();

            JsonArray jsonDeviceSourceArray = gson.fromJson(gson.toJson(deviceDetailList), JsonArray.class);
            JsonArray jsonDeviceArray = new JsonArray();
            analysisJson(serviceCode, jsonDeviceArray, jsonDeviceSourceArray, true);
            analysisJson(serviceCode, jsonArray, jsonDeviceArray, false);

            if (!StringUtils.isEmpty(item)) {
                for (int i = 0; i < jsonArray.size(); i++) {
                    JsonObject deviceTemp = jsonArray.get(i).getAsJsonObject();
                    JsonElement element = deviceTemp.get(item);
                    if (element != null && element.isJsonArray()) {
                        JsonArray jsonTempArray = element.getAsJsonArray();
                        for (int j = 0; j < jsonTempArray.size(); j++) {
                            JsonObject jsonTempObject = new JsonObject();
                            comJson(jsonTempObject, jsonTempArray.get(j).getAsJsonObject(), "");
                            jsonTempObject.add("deviceId", deviceTemp.get("deviceId"));
                            jsonTempObject.add("maintenanceYearMonth", deviceTemp.get("maintenanceYearMonth"));
                            jsonDirectArray.add(jsonTempObject);
                        }
                    }
                }
                return jsonDirectArray;
            }

            return jsonArray;
            // return gson.toJson(jsonArray);

        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return new JsonArray();
    }

    private void analysisJson(String serviceCode, JsonArray jsonArray, JsonArray jsonDeviceArray, boolean needRelation) {
        Gson gson = new Gson();
        for (int k = 0; k < jsonDeviceArray.size(); k++) {
            JsonObject jsonTemp = new JsonObject();
            JsonObject jsonTempSource = jsonDeviceArray.get(k).getAsJsonObject();
            if (needRelation && jsonDeviceArray.get(k).getAsJsonObject().get("relation") != null) {
                JsonArray jsonRelationSourceArray = jsonDeviceArray.get(k).getAsJsonObject().get("relation").getAsJsonArray();
                if (jsonRelationSourceArray != null && jsonRelationSourceArray.size() > 0) {
                    for (int j = 0; j < jsonRelationSourceArray.size(); j++) {
                        JsonObject jsonRelationSource = jsonRelationSourceArray.get(j).getAsJsonObject();
                        //修复漏洞
                        if (Objects.isNull(jsonRelationSource)) {
                            continue; // 如果jsonRelationSource为null，跳过当前循环
                        }
                        if (jsonRelationSource != null && jsonRelationSource.get("relationModel") != null && jsonRelationSource.get("relationModel").getAsString().equals("BackupSchedule"))
                            continue;
                        JsonArray jsonRelationItemSourceArray = jsonRelationSource.get("relationItems").getAsJsonArray();
                        if (jsonRelationSourceArray == null || jsonRelationSourceArray.size() <= 0) continue;
                        JsonArray jsonRelationValueArray = new JsonArray();
                        for (int m = 0; m < jsonRelationItemSourceArray.size(); m++) {
                            Criteria c1 = Criteria.where("deviceId").is(jsonRelationItemSourceArray.get(m).getAsJsonObject().get("deviceId").getAsString())
                                    .and("serviceCode").is(serviceCode)
                                    .and("maintenanceYearMonth").is(jsonTempSource.get("maintenanceYearMonth").getAsString());
                            List<Map<String, Object>> relationItem = dataService.getMaintenanceReportList(jsonRelationSource.get("relationModel").getAsString(),
                                    RequestUtil.getHeaderEid(),
                                    jsonRelationItemSourceArray.get(m).getAsJsonObject().get("deviceId").getAsString(), new ArrayList<>(), "",
                                    jsonTempSource.get("maintenanceYearMonth").getAsString(),
                                    jsonTempSource.get("maintenanceYearMonth").getAsString());
//                                    mongoTemplate.find(new Query(c1), Object.class, "rs_mt_" + jsonRelationSource.get("relationModel").getAsString());
                            if (relationItem != null && relationItem.size() > 0)
                                jsonRelationValueArray.add(gson.fromJson(gson.toJson(relationItem.get(0)), JsonObject.class));
                        }
                        jsonTempSource.add("relation_" + jsonRelationSource.get("relationModel").getAsString(), jsonRelationValueArray);

                    }
                    jsonTempSource.remove("relation");
                }
            }
            comJson(jsonTemp, jsonTempSource, "");
            //comJsonArray(jsonArray, jsonTemp);
            jsonArray.add(jsonTemp);
        }
    }

    private void getReleationItems(JsonObject jsonTempSource) {
        Gson gson = new Gson();
        JsonArray jsonRelationSourceArray = jsonTempSource.getAsJsonObject().get("relation").getAsJsonArray();
        if (jsonRelationSourceArray == null || jsonRelationSourceArray.size() <= 0) return;
        for (int j = 0; j < jsonRelationSourceArray.size(); j++) {
            JsonObject jsonRelationSource = jsonRelationSourceArray.get(j).getAsJsonObject();
            if (Objects.isNull(jsonRelationSource)) {
                continue; // 如果jsonRelationSource为null，跳过当前循环
            }
            if (jsonRelationSource != null && jsonRelationSource.get("relationModel") != null && jsonRelationSource.get("relationModel").getAsString().equals("BackupSchedule"))
                continue;
            JsonArray jsonRelationItemSourceArray = jsonRelationSource.get("relationItems").getAsJsonArray();
            if (jsonRelationSourceArray == null || jsonRelationSourceArray.size() <= 0) continue;
            JsonArray jsonRelationValueArray = new JsonArray();
            for (int m = 0; m < jsonRelationItemSourceArray.size(); m++) {
                Criteria c1 = Criteria.where("deviceId").is(jsonRelationItemSourceArray.get(m).getAsJsonObject().get("deviceId").getAsString())
                        .and("maintenanceYearMonth").is(jsonTempSource.get("maintenanceYearMonth").getAsString());
//                List<Object> relationItem = mongoTemplate.find(new Query(c1), Object.class, "rs_mt_" + jsonRelationSource.get("relationModel").getAsString());
                List<Map<String, Object>> relationItem = dataService.getMaintenanceReportList(jsonRelationSource.get("relationModel").getAsString(),
                        RequestUtil.getHeaderEid(),
                        jsonRelationItemSourceArray.get(m).getAsJsonObject().get("deviceId").getAsString(), new ArrayList<>(), "",
                        jsonTempSource.get("maintenanceYearMonth").getAsString(),
                        jsonTempSource.get("maintenanceYearMonth").getAsString());
                if (relationItem != null && relationItem.size() > 0)
                    jsonRelationValueArray.add(gson.fromJson(gson.toJson(relationItem.get(0)), JsonObject.class));
            }
            jsonTempSource.add("relation_" + jsonRelationSource.get("relationModel").getAsString(), jsonRelationValueArray);
        }
    }

    @Override
    public JsonArray GetGetMaintenancestateReportDataByArrayItem(String serviceCode, String reportTempId, String modelCode, String type, String item) {
        JsonArray jsonDirectArray = new JsonArray();
        Gson gson = new Gson();
        JsonArray jsonArray = new JsonArray();
        try {
            long reportTempIdlong = Long.parseLong(reportTempId);
            //获取报表的设备列表
            ReportTemp reportTemp = reportDao.getReportTemp(reportTempIdlong, modelCode);
            if (reportTemp == null || reportTemp.getReportTempDevices() == null || reportTemp.getReportTempDevices().size() <= 0)
                return new JsonArray();

            //获取设备当年的维护记录列表
            List<String> deviceList = new ArrayList<String>();
            for (ReportTempDevice deviceTemp : reportTemp.getReportTempDevices()) {
                deviceList.add(deviceTemp.getDeviceId());
            }

            //获取查询年月
            String yearMonth = reportTemp.getReportTime();
            String yearMonthStart = reportTemp.getReportTime();
            if (type.equals("year"))
                yearMonthStart = reportTemp.getReportTime().substring(0, 5) + "01";//reportTemp.getReportTime();

            Criteria c = Criteria.where("deviceId").in(deviceList).and("serviceCode").is(serviceCode).and("maintenanceYearMonth").gte(yearMonthStart).lte(yearMonth);
//            List<Object> deviceDetailList = mongoTemplate.find(new Query(c), Object.class, "rs_mt_" + modelCode);
            List<Map<String, Object>> deviceDetailList = dataService.getMaintenanceReportList(modelCode,
                    RequestUtil.getHeaderEid(),
                    "", deviceList, "",
                    yearMonthStart,
                    yearMonth);
            if (deviceDetailList == null || deviceDetailList.size() <= 0) return new JsonArray();

            JsonArray jsonDeviceSourceArray = gson.fromJson(gson.toJson(deviceDetailList), JsonArray.class);
            JsonArray jsonDeviceArray = new JsonArray();
            analysisJson(serviceCode, jsonDeviceArray, jsonDeviceSourceArray, true);
            analysisJson(serviceCode, jsonArray, jsonDeviceArray, false);
            for (int i = 0; i < jsonArray.size(); i++) {
                JsonObject deviceTemp = jsonArray.get(i).getAsJsonObject();
                JsonElement element = deviceTemp.get(item);
                if (element != null && element.isJsonArray()) {
                    JsonArray jsonTempArray = element.getAsJsonArray();
                    for (int j = 0; j < jsonTempArray.size(); j++) {
                        JsonObject jsonTempObject = new JsonObject();
                        comJson(jsonTempObject, jsonTempArray.get(j).getAsJsonObject(), "");
                        jsonTempObject.add("deviceId", deviceTemp.get("deviceId"));
                        jsonTempObject.add("maintenanceYearMonth", deviceTemp.get("maintenanceYearMonth"));
                        jsonDirectArray.add(jsonTempObject);
                    }
                }
            }
            return jsonDirectArray;
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return new JsonArray();
    }

    @Override
    public JsonArray GetGetMaintenancestateReportDataByDeviceIdByArrayItem(String serviceCode, String deviceId, String modelCode, String yearMonth, String type, String item) {
        JsonArray jsonDirectArray = new JsonArray();
        Gson gson = new Gson();
        JsonArray jsonArray = new JsonArray();
        try {
            //获取查询年月
            String yearMonthStart = yearMonth;
            if (type.equals("year"))
                yearMonthStart = yearMonth.substring(0, 5) + "01";

            Criteria c = Criteria.where("deviceId").is(deviceId).and("serviceCode").is(serviceCode).and("maintenanceYearMonth").gte(yearMonthStart).lte(yearMonth);
            List<Map<String, Object>> deviceDetailList = dataService.getMaintenanceReportList(modelCode,
                    RequestUtil.getHeaderEid(),
                    deviceId, new ArrayList<>(), "",
                    yearMonthStart,
                    yearMonth);
            ;//mongoTemplate.find(new Query(c), Object.class, "rs_mt_" + modelCode);
            if (deviceDetailList == null || deviceDetailList.size() <= 0) return new JsonArray();

            JsonArray jsonDeviceSourceArray = gson.fromJson(gson.toJson(deviceDetailList), JsonArray.class);
            JsonArray jsonDeviceArray = new JsonArray();
            analysisJson(serviceCode, jsonDeviceArray, jsonDeviceSourceArray, true);
            analysisJson(serviceCode, jsonArray, jsonDeviceArray, false);
            for (int i = 0; i < jsonArray.size(); i++) {
                JsonObject deviceTemp = jsonArray.get(i).getAsJsonObject();
                JsonElement element = deviceTemp.get(item);
                if (element != null && element.isJsonArray()) {
                    JsonArray jsonTempArray = element.getAsJsonArray();
                    for (int j = 0; j < jsonTempArray.size(); j++) {
                        JsonObject jsonTempObject = new JsonObject();
                        comJson(jsonTempObject, jsonTempArray.get(j).getAsJsonObject(), "");
                        jsonTempObject.add("deviceId", deviceTemp.get("deviceId"));
                        jsonTempObject.add("maintenanceYearMonth", deviceTemp.get("maintenanceYearMonth"));
                        jsonDirectArray.add(jsonTempObject);
                    }
                }
            }
            return jsonDirectArray;
            // return gson.toJson(jsonArray);

        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return new JsonArray();
    }


    @Override
    public JsonArray GetGetMaintenancestateReportDataByRelationArrayItem(String serviceCode, String reportTempId, String modelCode, String type, String item) {
        JsonArray jsonDirectArray = new JsonArray();
        Gson gson = new Gson();
        JsonArray jsonArray = new JsonArray();
        try {
            long reportTempIdlong = Long.parseLong(reportTempId);
            //获取报表的设备列表
            ReportTemp reportTemp = reportDao.getReportTemp(reportTempIdlong, modelCode);
            if (reportTemp == null || reportTemp.getReportTempDevices() == null || reportTemp.getReportTempDevices().size() <= 0)
                return new JsonArray();

            //获取设备当年的维护记录列表
            List<String> deviceList = new ArrayList<String>();
            for (ReportTempDevice deviceTemp : reportTemp.getReportTempDevices()) {
                deviceList.add(deviceTemp.getDeviceId());
            }

            //获取查询年月
            String yearMonth = reportTemp.getReportTime();
            String yearMonthStart = reportTemp.getReportTime();
            if (type.equals("year"))
                yearMonthStart = reportTemp.getReportTime().substring(0, 5) + "01";//reportTemp.getReportTime();

            Criteria c = Criteria.where("deviceId").in(deviceList).and("serviceCode").is(serviceCode).and("maintenanceYearMonth").gte(yearMonthStart).lte(yearMonth);
            List<Map<String, Object>> deviceDetailList = dataService.getMaintenanceReportList(modelCode,
                    RequestUtil.getHeaderEid(),
                    "", deviceList, "",
                    yearMonthStart,
                    yearMonth);//mongoTemplate.find(new Query(c), Object.class, "rs_mt_" + modelCode);
            if (deviceDetailList == null || deviceDetailList.size() <= 0) return new JsonArray();

            JsonArray jsonDeviceSourceArray = gson.fromJson(gson.toJson(deviceDetailList), JsonArray.class);
            JsonArray jsonDeviceArray = new JsonArray();
            analysisJson(serviceCode, jsonDeviceArray, jsonDeviceSourceArray, true);
            analysisJson(serviceCode, jsonArray, jsonDeviceArray, false);
            String[] p = item.split("__");
            if (p == null || p.length <= 0) return new JsonArray();
            JsonArray jsonRelationArray = new JsonArray();
            for (int i = 0; i < jsonArray.size(); i++) {
                JsonObject deviceTemp = jsonArray.get(i).getAsJsonObject();
                JsonElement element = deviceTemp.get(p[0]);//item
                JsonArray jsonTempArray = element.getAsJsonArray();
                for (int j = 0; j < jsonTempArray.size(); j++) {
                    JsonObject jsonTempObject = new JsonObject();
                    comJson(jsonTempObject, jsonTempArray.get(j).getAsJsonObject(), "");
                    jsonTempObject.add("rs_deviceId", deviceTemp.get("deviceId"));
                    jsonTempObject.add("rs_maintenanceYearMonth", deviceTemp.get("maintenanceYearMonth"));
                    jsonRelationArray.add(jsonTempObject);
                }
            }

            JsonArray jsonRelationDeviceTempArray = new JsonArray();
            JsonArray jsonRelationDeviceArray = new JsonArray();
            analysisJson(serviceCode, jsonRelationDeviceTempArray, jsonRelationArray, true);
            analysisJson(serviceCode, jsonRelationDeviceArray, jsonRelationDeviceTempArray, false);


            if (p.length > 1 && !StringUtils.isEmpty(p[1])) {
                for (int i = 0; i < jsonRelationDeviceArray.size(); i++) {
                    JsonObject deviceTemp = jsonRelationDeviceArray.get(i).getAsJsonObject();
                    JsonElement element = deviceTemp.get(p[1]);
                    if (element != null && element.isJsonArray()) {
                        JsonArray jsonTempArray = element.getAsJsonArray();
                        for (int j = 0; j < jsonTempArray.size(); j++) {
                            JsonObject jsonTempObject = new JsonObject();
                            comJson(jsonTempObject, jsonTempArray.get(j).getAsJsonObject(), "");
                            jsonTempObject.add("rs_deviceId", deviceTemp.get("rs_deviceId"));
                            jsonTempObject.add("rs_maintenanceYearMonth", deviceTemp.get("rs_maintenanceYearMonth"));
                            jsonTempObject.add("deviceId", deviceTemp.get("deviceId"));
                            jsonTempObject.add("maintenanceYearMonth", deviceTemp.get("maintenanceYearMonth"));
                            jsonDirectArray.add(jsonTempObject);
                        }
                    }
                }
                return jsonDirectArray;
            } else
                return jsonRelationDeviceArray;
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return new JsonArray();
    }

    @Override
    public JsonArray getMaintenancestateReportDataByDeviceIdByRelationArrayItem(String serviceCode, String deviceId, String modelCode, String yearMonth, String type, String item) {
        JsonArray jsonDirectArray = new JsonArray();
        Gson gson = new Gson();
        JsonArray jsonArray = new JsonArray();
        try {
            //获取查询年月
            String yearMonthStart = yearMonth;
            if (type.equals("year"))
                yearMonthStart = yearMonth.substring(0, 5) + "01";

            Criteria c = Criteria.where("deviceId").is(deviceId).and("serviceCode").is(serviceCode).and("maintenanceYearMonth").gte(yearMonthStart).lte(yearMonth);
            List<Map<String, Object>> deviceDetailList = dataService.getMaintenanceReportList(modelCode,
                    RequestUtil.getHeaderEid(),
                    deviceId, new ArrayList<>(), "",
                    yearMonthStart,
                    yearMonth);//mongoTemplate.find(new Query(c), Object.class, "rs_mt_" + modelCode);
            if (deviceDetailList == null || deviceDetailList.size() <= 0) return new JsonArray();

            JsonArray jsonDeviceSourceArray = gson.fromJson(gson.toJson(deviceDetailList), JsonArray.class);
            JsonArray jsonDeviceArray = new JsonArray();
            analysisJson(serviceCode, jsonDeviceArray, jsonDeviceSourceArray, true);
            analysisJson(serviceCode, jsonArray, jsonDeviceArray, false);
            String[] p = item.split("__");
            if (p == null || p.length <= 0) return new JsonArray();
            JsonArray jsonRelationArray = new JsonArray();
            for (int i = 0; i < jsonArray.size(); i++) {
                JsonObject deviceTemp = jsonArray.get(i).getAsJsonObject();
                JsonElement element = deviceTemp.get(p[0]);//item
                JsonArray jsonTempArray = element.getAsJsonArray();
                for (int j = 0; j < jsonTempArray.size(); j++) {
                    JsonObject jsonTempObject = new JsonObject();
                    comJson(jsonTempObject, jsonTempArray.get(j).getAsJsonObject(), "");
                    jsonTempObject.add("rs_deviceId", deviceTemp.get("deviceId"));
                    jsonTempObject.add("rs_maintenanceYearMonth", deviceTemp.get("maintenanceYearMonth"));
                    jsonRelationArray.add(jsonTempObject);
                }
            }

            JsonArray jsonRelationDeviceTempArray = new JsonArray();
            JsonArray jsonRelationDeviceArray = new JsonArray();
            analysisJson(serviceCode, jsonRelationDeviceTempArray, jsonRelationArray, true);
            analysisJson(serviceCode, jsonRelationDeviceArray, jsonRelationDeviceTempArray, false);


            if (p.length > 1 && !StringUtils.isEmpty(p[1])) {
                for (int i = 0; i < jsonRelationDeviceArray.size(); i++) {
                    JsonObject deviceTemp = jsonRelationDeviceArray.get(i).getAsJsonObject();
                    JsonElement element = deviceTemp.get(p[1]);
                    if (element != null && element.isJsonArray()) {
                        JsonArray jsonTempArray = element.getAsJsonArray();
                        for (int j = 0; j < jsonTempArray.size(); j++) {
                            JsonObject jsonTempObject = new JsonObject();
                            comJson(jsonTempObject, jsonTempArray.get(j).getAsJsonObject(), "");
                            jsonTempObject.add("rs_deviceId", deviceTemp.get("rs_deviceId"));
                            jsonTempObject.add("rs_maintenanceYearMonth", deviceTemp.get("rs_maintenanceYearMonth"));
                            jsonTempObject.add("deviceId", deviceTemp.get("deviceId"));
                            jsonTempObject.add("maintenanceYearMonth", deviceTemp.get("maintenanceYearMonth"));
                            jsonDirectArray.add(jsonTempObject);
                        }
                    }
                }
                return jsonDirectArray;
            } else
                return jsonRelationDeviceArray;

            // return gson.toJson(jsonArray);

        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return new JsonArray();
    }

    private void comJson(JsonObject jsonDectObject, JsonObject jsonSourceObject, String fieldPrix) {
        for (String field : jsonSourceObject.keySet()) {
            JsonElement jsonElement = jsonSourceObject.get(field);
            if (jsonElement.isJsonObject()) {
                comJson(jsonDectObject, jsonElement.getAsJsonObject(), fieldPrix + (fieldPrix.equals("") ? "" : "_") + field);
            } else {
                Field fieldInfo = fieldService.getFieldDetail(field);
                if (fieldInfo == null || !fieldInfo.getFieldType().toUpperCase().equals("ENUM"))
                    jsonDectObject.add(fieldPrix + (fieldPrix.equals("") ? "" : "_") + field, jsonElement);
                else {
                    try {
                        JsonParser jsonParser = new JsonParser();
                        //FieldTypeEnum fieldEnum = fieldInfo.getFieldTypeEnumList().stream()
                        //        .filter(o -> o.getEnumCode().equals(jsonElement.getAsString()))
                        //        .collect(Collectors.toList()).stream().findFirst().get();
                        //漏洞修复。
                        FieldTypeEnum fieldEnum = fieldInfo.getFieldTypeEnumList().stream()
                                .filter(o -> o.getEnumCode().equals(jsonElement.getAsString()))
                                .findFirst()
                                .map(Optional::of)
                                .orElse(Optional.empty())
                                .orElse(null);
                        jsonDectObject.add(fieldPrix + (fieldPrix.equals("") ? "" : "_") + field, fieldEnum == null ? jsonElement : jsonParser.parse(fieldEnum.getEnumName()));
                    } catch (Exception ex) {
                        jsonDectObject.add(fieldPrix + (fieldPrix.equals("") ? "" : "_") + field, jsonElement);
                    }
                }
            }
        }
    }

    private void comJsonArray(JsonArray jsonArray, JsonObject jsonTemp) {
        for (String field : jsonTemp.keySet()) {
            JsonElement jsonElement = jsonTemp.get(field);
            if (!field.equals("relation") && jsonElement.isJsonArray()) {
                JsonArray jsonTempArray = jsonElement.getAsJsonArray();
                for (int i = 0; i < jsonTempArray.size(); i++) {
                    JsonObject jsonNewTemp = new JsonObject();
                    JsonObject jsonTempItem = jsonTempArray.get(i).getAsJsonObject();
                    for (String fieldtemp : jsonTemp.keySet()) {
                        if (fieldtemp.equals(field)) continue;
                        JsonElement jsonElementtemp = jsonTemp.get(fieldtemp);
                        jsonNewTemp.add(fieldtemp, jsonElementtemp);
                    }
                    for (String fieldtemp : jsonTempItem.keySet()) {
                        JsonElement jsonElementtemp = jsonTempItem.get(fieldtemp);
                        Field fieldInfo = fieldService.getFieldDetail(fieldtemp);
                        if (fieldInfo == null || !fieldInfo.getFieldType().toUpperCase().equals("ENUM"))
                            jsonNewTemp.add(field + "_" + fieldtemp, jsonElementtemp);
                        else {
                            try {
                                JsonParser jsonParser = new JsonParser();
                                //FieldTypeEnum fieldEnum = fieldInfo.getFieldTypeEnumList()
                                //        .stream().filter(o -> o.getEnumCode()
                                //        .equals(jsonElementtemp.getAsString()))
                                //        .collect(Collectors.toList()).stream().findFirst().get();
                                // 漏洞修复。
                                Optional<FieldTypeEnum> optionalFieldEnum = fieldInfo.getFieldTypeEnumList()
                                        .stream()
                                        .filter(o -> o.getEnumCode().equals(jsonElementtemp.getAsString()))
                                        .findFirst();
                                if (optionalFieldEnum.isPresent()) {
                                    FieldTypeEnum fieldEnum = optionalFieldEnum.get();
                                    jsonNewTemp.add(field + "_" + fieldtemp, fieldEnum == null ? jsonElementtemp : jsonParser.parse(fieldEnum.getEnumName()));
                                } else {
                                    jsonNewTemp.add(field + "_" + fieldtemp, jsonElementtemp);
                                }
                            } catch (Exception ex) {
                                jsonNewTemp.add(field + "_" + fieldtemp, jsonElementtemp);
                            }
                        }
                    }
                    jsonArray.add(jsonNewTemp);
                }
            }
        }
    }

    @Override
    public List<Map<String, Object>> GetHostMaintenancestateReportByDeviceId(String modelCode, String deviceId, String yearMonth, String type, String item, String serviceCode, String eid) {
        try {
            //获取查询年月
            String yearMonthStart = yearMonth;
            if (type.equals("year"))
                yearMonthStart = yearMonth.substring(0, 5) + "01";

            List<Map<String, Object>> deviceDetailList = new ArrayList<>();
            switch (item) {
                case "RP_HOST_BASEINFO":
                    deviceDetailList = dataService.getHostReport_baseInfo(modelCode, item,
                            eid,
                            deviceId, new ArrayList<>(), "",
                            yearMonthStart,
                            yearMonth);
                    break;
                case "RP_HOST_DISKINFO":
                    deviceDetailList = dataService.getHostReport_diskInfo(modelCode, item,
                            eid,
                            deviceId, new ArrayList<>(), "",
                            yearMonthStart,
                            yearMonth);
                    break;
                case "RP_HOST_YEAR":
                    deviceDetailList = dataService.getHostReport_year(modelCode, "RP_HOST_BASEINFO",
                            eid,
                            deviceId, new ArrayList<>(), "",
                            yearMonthStart,
                            yearMonth);
                    break;
                case "RP_HOST_DATA_FILE_SQL":
                    deviceDetailList = dataService.getHostReport_data_file_sql(modelCode, "RP_HOST_BASEINFO",
                            eid,
                            deviceId, new ArrayList<>(), "",
                            yearMonthStart,
                            yearMonth);
                    break;
                case "RP_HOST_UPS":
                    deviceDetailList = dataService.getHostReport_ups_sql(modelCode, item,
                            eid,
                            deviceId, new ArrayList<>(), "",
                            yearMonthStart,
                            yearMonth);
                    break;
                case "RP_HOST_FOLDER":
                    deviceDetailList = dataService.getHostReport_folder_sql(modelCode, item,
                            eid,
                            deviceId, new ArrayList<>(), "",
                            yearMonthStart,
                            yearMonth);
                    break;
                default:
                    deviceDetailList = new ArrayList<>();
                    break;
            }

            return deviceDetailList;
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return new ArrayList();

    }

    @Override
    public List<Map<String, Object>> GetHostMaintenancestateReport(String reportTempId, String modelCode, String type, String item, String serviceCode, String eid) {
        try {
            long reportTempIdlong = Long.parseLong(reportTempId);
            //获取报表的设备列表
            ReportTemp reportTemp = reportDao.getReportTemp(reportTempIdlong, modelCode);
            if (reportTemp == null || reportTemp.getReportTempDevices() == null || reportTemp.getReportTempDevices().size() <= 0)
                return new ArrayList<>();

            //获取设备当年的维护记录列表
            List<String> deviceList = new ArrayList<String>();
            for (ReportTempDevice deviceTemp : reportTemp.getReportTempDevices()) {
                deviceList.add(deviceTemp.getDeviceId());
            }

            //获取查询年月
            String yearMonth = reportTemp.getReportTime();
            String yearMonthStart = reportTemp.getReportTime();
            if (type.equals("year"))
                yearMonthStart = reportTemp.getReportTime().substring(0, 5) + "01";//reportTemp.getReportTime();

//            Criteria c = Criteria.where("deviceId").in(deviceList).and("serviceCode").is(serviceCode).and("maintenanceYearMonth").gte(yearMonthStart).lte(yearMonth);
//            List<Object> deviceDetailList = mongoTemplate.find(new Query(c), Object.class, "rs_mt_" + modelCode);
            List<Map<String, Object>> deviceDetailList = new ArrayList<>();
            switch (item) {
                case "RP_HOST_BASEINFO":
                    deviceDetailList = dataService.getHostReport_baseInfo(modelCode, item,
                            eid,
                            "", deviceList, "",
                            yearMonthStart,
                            yearMonth);
                    break;
                case "RP_HOST_DISKINFO":
                    deviceDetailList = dataService.getHostReport_diskInfo(modelCode, item,
                            eid,
                            "", deviceList, "",
                            yearMonthStart,
                            yearMonth);
                    break;
                case "RP_HOST_YEAR":
                    deviceDetailList = dataService.getHostReport_year(modelCode, "RP_HOST_BASEINFO",
                            eid,
                            "", deviceList, "",
                            yearMonthStart,
                            yearMonth);
                    break;
                case "RP_HOST_DATA_FILE_SQL":
                    deviceDetailList = dataService.getHostReport_data_file_sql(modelCode, "RP_HOST_BASEINFO",
                            eid,
                            "", deviceList, "",
                            yearMonthStart,
                            yearMonth);
                    break;
                case "RP_HOST_UPS":
                    deviceDetailList = dataService.getHostReport_ups_sql(modelCode, item,
                            eid,
                            "", deviceList, "",
                            yearMonthStart,
                            yearMonth);
                    break;
                case "RP_HOST_FOLDER":
                    deviceDetailList = dataService.getHostReport_folder_sql(modelCode, item,
                            eid,
                            "", new ArrayList<>(), "",
                            yearMonthStart,
                            yearMonth);
                    break;
                default:
                    deviceDetailList = new ArrayList<>();
                    break;
            }

            return deviceDetailList;
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return new ArrayList();

    }


    @Override
    public List<Map<String, Object>> GetNasMaintenancestateReportByDeviceId(String modelCode, String deviceId, String yearMonth, String type, String item, String serviceCode, String eid) {
        try {
            //获取查询年月
            String yearMonthStart = yearMonth;
            if (type.equals("year"))
                yearMonthStart = yearMonth.substring(0, 5) + "01";

            List<Map<String, Object>> deviceDetailList = new ArrayList<>();
            switch (item) {
                case "RP_NAS_BASEINFO":
                    deviceDetailList = dataService.getNasReport_baseInfo(modelCode, item,
                            eid,
                            deviceId, new ArrayList<>(), "",
                            yearMonthStart,
                            yearMonth);
                    break;
                case "RP_NAS_SPACE":
                    deviceDetailList = dataService.getNasReport_space(modelCode, item,
                            eid,
                            deviceId, new ArrayList<>(), "",
                            yearMonthStart,
                            yearMonth);
                    break;
                case "RP_NAS_USB":
                    deviceDetailList = dataService.getNasReport_usb(modelCode, item,
                            eid,
                            deviceId, new ArrayList<>(), "",
                            yearMonthStart,
                            yearMonth);
                    break;
                default:
                    deviceDetailList = new ArrayList<>();
                    break;
            }

            return deviceDetailList;
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return new ArrayList();

    }

    @Override
    public List<Map<String, Object>> GetNasMaintenancestateReport(String reportTempId, String modelCode, String type, String item, String serviceCode, String eid) {
        try {
            long reportTempIdlong = Long.parseLong(reportTempId);
            //获取报表的设备列表
            ReportTemp reportTemp = reportDao.getReportTemp(reportTempIdlong, modelCode);
            if (reportTemp == null || reportTemp.getReportTempDevices() == null || reportTemp.getReportTempDevices().size() <= 0)
                return new ArrayList<>();

            //获取设备当年的维护记录列表
            List<String> deviceList = new ArrayList<String>();
            for (ReportTempDevice deviceTemp : reportTemp.getReportTempDevices()) {
                deviceList.add(deviceTemp.getDeviceId());
            }

            //获取查询年月
            String yearMonth = reportTemp.getReportTime();
            String yearMonthStart = reportTemp.getReportTime();
            if (type.equals("year"))
                yearMonthStart = reportTemp.getReportTime().substring(0, 5) + "01";//reportTemp.getReportTime();

            List<Map<String, Object>> deviceDetailList = new ArrayList<>();
            switch (item) {
                case "RP_NAS_BASEINFO":
                    deviceDetailList = dataService.getNasReport_baseInfo(modelCode, item,
                            eid,
                            "", deviceList, "",
                            yearMonthStart,
                            yearMonth);
                    break;
                case "RP_NAS_SPACE":
                    deviceDetailList = dataService.getNasReport_space(modelCode, item,
                            eid,
                            "", deviceList, "",
                            yearMonthStart,
                            yearMonth);
                    break;
                case "RP_NAS_USB":
                    deviceDetailList = dataService.getNasReport_usb(modelCode, item,
                            eid,
                            "", deviceList, "",
                            yearMonthStart,
                            yearMonth);
                    break;
                default:
                    deviceDetailList = new ArrayList<>();
                    break;
            }

            return deviceDetailList;
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return new ArrayList();

    }


    @Override
    public List<Map<String, Object>> GetFirewallMaintenancestateReportByDeviceId(String modelCode, String deviceId, String yearMonth, String type, String item, String serviceCode, String eid) {
        try {
            //获取查询年月
            String yearMonthStart = yearMonth;
            if (type.equals("year"))
                yearMonthStart = yearMonth.substring(0, 5) + "01";

            List<Map<String, Object>> deviceDetailList = new ArrayList<>();
            switch (item) {
                case "RP_FIREWALL":
                    deviceDetailList = dataService.getFirewallReport_baseInfo(modelCode, item,
                            eid,
                            deviceId, new ArrayList<>(), "",
                            yearMonthStart,
                            yearMonth);
                    break;
                default:
                    deviceDetailList = new ArrayList<>();
                    break;
            }

            return deviceDetailList;
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return new ArrayList();

    }

    @Override
    public List<Map<String, Object>> GetFirewallMaintenancestateReport(String reportTempId, String modelCode, String type, String item, String serviceCode, String eid) {
        try {
            long reportTempIdlong = Long.parseLong(reportTempId);
            //获取报表的设备列表
            ReportTemp reportTemp = reportDao.getReportTemp(reportTempIdlong, modelCode);
            if (reportTemp == null || reportTemp.getReportTempDevices() == null || reportTemp.getReportTempDevices().size() <= 0)
                return new ArrayList<>();

            //获取设备当年的维护记录列表
            List<String> deviceList = new ArrayList<String>();
            for (ReportTempDevice deviceTemp : reportTemp.getReportTempDevices()) {
                deviceList.add(deviceTemp.getDeviceId());
            }

            //获取查询年月
            String yearMonth = reportTemp.getReportTime();
            String yearMonthStart = reportTemp.getReportTime();
            if (type.equals("year"))
                yearMonthStart = reportTemp.getReportTime().substring(0, 5) + "01";//reportTemp.getReportTime();

            List<Map<String, Object>> deviceDetailList = new ArrayList<>();
            switch (item) {
                case "RP_FIREWALL":
                    deviceDetailList = dataService.getFirewallReport_baseInfo(modelCode, item,
                            eid,
                            "", deviceList, "",
                            yearMonthStart,
                            yearMonth);
                    break;
                default:
                    deviceDetailList = new ArrayList<>();
                    break;
            }

            return deviceDetailList;
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return new ArrayList();

    }

    @Override
    public List<Map<String, Object>> GetMailServerMaintenancestateReportByDeviceId(String modelCode, String deviceId, String yearMonth, String type, String item, String serviceCode, String eid) {
        try {
            //获取查询年月
            String yearMonthStart = yearMonth;
            if (type.equals("year"))
                yearMonthStart = yearMonth.substring(0, 5) + "01";

            List<Map<String, Object>> deviceDetailList = new ArrayList<>();
            switch (item) {
                case "RP_MAILSERVER":
                    deviceDetailList = dataService.getMailServerReport(modelCode, item,
                            eid,
                            deviceId, new ArrayList<>(), "",
                            yearMonthStart,
                            yearMonth);
                    break;
                case "RP_MAILSERVER_SPACE":
                    deviceDetailList = dataService.getMailServerReport_space(modelCode, item,
                            eid,
                            deviceId, new ArrayList<>(), "",
                            yearMonthStart,
                            yearMonth);
                    break;
                default:
                    deviceDetailList = new ArrayList<>();
                    break;
            }

            return deviceDetailList;
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return new ArrayList();

    }

    @Override
    public List<Map<String, Object>> GetMailServerMaintenancestateReport(String reportTempId, String modelCode, String type, String item, String serviceCode, String eid) {
        try {
            long reportTempIdlong = Long.parseLong(reportTempId);
            //获取报表的设备列表
            ReportTemp reportTemp = reportDao.getReportTemp(reportTempIdlong, modelCode);
            if (reportTemp == null || reportTemp.getReportTempDevices() == null || reportTemp.getReportTempDevices().size() <= 0)
                return new ArrayList<>();

            //获取设备当年的维护记录列表
            List<String> deviceList = new ArrayList<String>();
            for (ReportTempDevice deviceTemp : reportTemp.getReportTempDevices()) {
                deviceList.add(deviceTemp.getDeviceId());
            }

            //获取查询年月
            String yearMonth = reportTemp.getReportTime();
            String yearMonthStart = reportTemp.getReportTime();
            if (type.equals("year"))
                yearMonthStart = reportTemp.getReportTime().substring(0, 5) + "01";//reportTemp.getReportTime();

            List<Map<String, Object>> deviceDetailList = new ArrayList<>();
            switch (item) {
                case "RP_MAILSERVER":
                    deviceDetailList = dataService.getMailServerReport(modelCode, item,
                            eid,
                            "", deviceList, "",
                            yearMonthStart,
                            yearMonth);
                    break;
                case "RP_MAILSERVER_SPACE":
                    deviceDetailList = dataService.getMailServerReport_space(modelCode, item,
                            eid,
                            "", deviceList, "",
                            yearMonthStart,
                            yearMonth);
                    break;
                default:
                    deviceDetailList = new ArrayList<>();
                    break;
            }

            return deviceDetailList;
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return new ArrayList();

    }


    @Override
    public List<Map<String, Object>> GetClientMaintenancestateReportByDeviceId(String modelCode, String deviceId, String yearMonth, String type, String item, String serviceCode, String eid) {
        try {
            //获取查询年月
            /*String yearMonthStart = yearMonth;
            if (type.equals("year"))
                yearMonthStart = yearMonth.substring(0, 5) + "01";
*/
            List<Map<String, Object>> deviceDetailList = new ArrayList<>();
            switch (item) {
                case "RP_CLIENT":
                    deviceDetailList = dataService.getClientReport(modelCode, item,
                            eid,
                            deviceId, new ArrayList<>(), "",
                            "",
                            "");
                    break;
                case "RP_CLIENT_DISKINFO":
                    deviceDetailList = dataService.getClientReport_disk(modelCode, item,
                            eid,
                            deviceId, new ArrayList<>(), "",
                            "",
                            "");
                    break;
                default:
                    deviceDetailList = new ArrayList<>();
                    break;
            }

            return deviceDetailList;
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return new ArrayList();

    }

    @Override
    public List<Map<String, Object>> GetClientMaintenancestateReport(String reportTempId, String modelCode, String type, String item, String serviceCode, String eid) {
        try {
            long reportTempIdlong = Long.parseLong(reportTempId);
            //获取报表的设备列表
            ReportTemp reportTemp = reportDao.getReportTemp(reportTempIdlong, modelCode);
            if (reportTemp == null || reportTemp.getReportTempDevices() == null || reportTemp.getReportTempDevices().size() <= 0)
                return new ArrayList<>();

            //获取设备当年的维护记录列表
            List<String> deviceList = new ArrayList<String>();
            for (ReportTempDevice deviceTemp : reportTemp.getReportTempDevices()) {
                deviceList.add(deviceTemp.getDeviceId());
            }

            //获取查询年月
//            String yearMonth = reportTemp.getReportTime();
//            String yearMonthStart = reportTemp.getReportTime();
//            if (type.equals("year"))
//                yearMonthStart = reportTemp.getReportTime().substring(0, 5) + "01";//reportTemp.getReportTime();

            List<Map<String, Object>> deviceDetailList = new ArrayList<>();
            switch (item) {
                case "RP_CLIENT":
                    deviceDetailList = dataService.getClientReport(modelCode, item,
                            eid,
                            "", deviceList, "",
                            "",
                            "");
                    break;
                case "RP_CLIENT_DISKINFO":
                    deviceDetailList = dataService.getClientReport_disk(modelCode, item,
                            eid,
                            "", deviceList, "",
                            "",
                            "");
                    break;
                default:
                    deviceDetailList = new ArrayList<>();
                    break;
            }

            return deviceDetailList;
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return new ArrayList();

    }


    @Override
    public List<Map<String, Object>> GetBackupScheduleMaintenancestateReport(String yearMonth, String modelCode, String type, String item, String serviceCode, String eid) {
        try {
            List<Map<String, Object>> deviceDetailList = new ArrayList<>();
            switch (item) {
                case "RP_BACKUPSCHEDULE":
                    deviceDetailList = dataService.getBackupScheduleReport(modelCode, item,
                            eid,
                            "", new ArrayList<>(), "",
                            "",
                            "");
                    break;
                default:
                    deviceDetailList = new ArrayList<>();
                    break;
            }

            return deviceDetailList;
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return new ArrayList();

    }


    @Override
    public List<Map<String, Object>> GetSoftware_HardwareMaintenancestateReportByDeviceId(String modelCode, String deviceId, String yearMonth, String type, String item, String serviceCode, String eid) {
        try {
            //获取查询年月
            String yearMonthStart = yearMonth;
            if (type.equals("year"))
                yearMonthStart = yearMonth.substring(0, 5) + "01";

            List<Map<String, Object>> deviceDetailList = new ArrayList<>();
            switch (item) {
                case "RP_SOFTWARE":
                    deviceDetailList = dataService.getSoftwareReport(modelCode, item,
                            eid,
                            deviceId, new ArrayList<>(), "",
                            yearMonthStart,
                            yearMonth);
                    break;
                case "RP_HARDWARE":
                    deviceDetailList = dataService.getHardwareReport(modelCode, item,
                            eid,
                            deviceId, new ArrayList<>(), "",
                            yearMonthStart,
                            yearMonth);
                    break;
                default:
                    deviceDetailList = new ArrayList<>();
                    break;
            }

            return deviceDetailList;
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return new ArrayList();

    }

    @Override
    public List<Map<String, Object>> GetSoftware_HardwareMaintenancestateReport(String reportTempId, String modelCode, String type, String item, String serviceCode, String eid) {
        try {
            long reportTempIdlong = Long.parseLong(reportTempId);
            //获取报表的设备列表
            ReportTemp reportTemp = reportDao.getReportTemp(reportTempIdlong, modelCode);
            if (reportTemp == null || reportTemp.getReportTempDevices() == null || reportTemp.getReportTempDevices().size() <= 0)
                return new ArrayList<>();

            //获取设备当年的维护记录列表
            List<String> deviceList = new ArrayList<String>();
            for (ReportTempDevice deviceTemp : reportTemp.getReportTempDevices()) {
                deviceList.add(deviceTemp.getDeviceId());
            }

            List<Map<String, Object>> deviceDetailList = new ArrayList<>();
            switch (item) {
                case "RP_SOFTWARE":
                    deviceDetailList = dataService.getSoftwareReport(modelCode, item,
                            eid,
                            "", deviceList, "",
                            "",
                            "");
                    break;
                case "RP_HARDWARE":
                    deviceDetailList = dataService.getHardwareReport(modelCode, item,
                            eid,
                            "", deviceList, "",
                            "",
                            "");
                    break;
                default:
                    deviceDetailList = new ArrayList<>();
                    break;
            }

            return deviceDetailList;
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return new ArrayList();

    }

    @Override
    public List<Map<String, Object>> GetEsxiMaintenancestateReportByDeviceId(String modelCode, String deviceId, String yearMonth, String type, String item, String serviceCode, String eid) {
        try {
            //获取查询年月
            String yearMonthStart = yearMonth;
            if (type.equals("year"))
                yearMonthStart = yearMonth.substring(0, 5) + "01";

            List<Map<String, Object>> deviceDetailList = new ArrayList<>();
            List<String> relateDeviceList = new ArrayList<>();
            if (item.startsWith("RELATION")) //RELATION:RS_MT_ESXI:ESXI_HOST:RP_ESXI_HOST
            {
                String[] arr = item.split(":");
                if (arr == null || arr[0] == null || arr[1] == null || arr[2] == null || arr[3] == null) { //格式不正确
                    return new ArrayList();
                }
                String relateModel = arr[2];
                //查询关联设备
                Map<String, Object> mainAssetMap = dataService.getMaintenanceDetail(arr[1], 0, 0, "", yearMonth, deviceId, "");
                if (mainAssetMap != null) {
                    List list = JSONObject.parseObject(mainAssetMap.get("relation") != null ? mainAssetMap.get("relation").toString() : null, List.class, Feature.OrderedField);
                    if (CollectionUtils.isEmpty(list)) {
                        return new ArrayList();
                    } else {
                        //循环关联的模型
                        for (int i = 0; i < list.size(); i++) {
                            LinkedHashMap<String, Object> map = JSONObject.parseObject(JSONObject.toJSONString(list.get(i)), LinkedHashMap.class);
                            for (String key : map.keySet()) {
                                if (key.equals("relationModel") && relateModel.equals(map.get(key).toString())) {
                                    log.info("找到关联模型了");
                                    List relationItems = JSONObject.parseObject(JSONObject.toJSONString(map.get("relationItems")), List.class);
                                    if (CollectionUtils.isEmpty(relationItems)) {
                                        return new ArrayList();
                                    }
                                    log.info("关联模型里还存在关联设备");
                                    for (int j = 0; j < relationItems.size(); j++) {
                                        LinkedHashMap<String, Object> relationItemMap = JSONObject.parseObject(JSONObject.toJSONString(relationItems.get(j)), LinkedHashMap.class);
                                        for (String relationItemKey : relationItemMap.keySet()) {
                                            if (relationItemKey.equals("deviceId")) {
                                                relateDeviceList.add(relationItemMap.get("deviceId").toString());
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
            switch (item) {
                case "RP_ESXI":
                    deviceDetailList = dataService.getEsxiReport(modelCode, "RP_ESXI",
                            eid,
                            deviceId, new ArrayList<>(), "",
                            yearMonthStart,
                            yearMonth);
                    break;
                case "RELATION:ESXI:Esxi_Host:RP_ESXI_HOST":
                    if (!CollectionUtils.isEmpty(relateDeviceList)) {
                        deviceDetailList = dataService.getEsxiHostReport(modelCode, "RP_ESXI_HOST",
                                eid,
                                "", relateDeviceList, "",
                                yearMonthStart,
                                yearMonth);
                    }
                    break;
                case "RELATION:ESXI:Esxi_Storage:RP_ESXI_STORAGE":
                    if (!CollectionUtils.isEmpty(relateDeviceList)) {
                        deviceDetailList = dataService.getEsxiStorageReport(modelCode, "RP_ESXI_STORAGE",
                                eid,
                                "", relateDeviceList, "",
                                yearMonthStart,
                                yearMonth);
                    }
                    break;
                case "RELATION:ESXI:Esxi_Host:RP_ESXI_HOST_SPACE":
                    if (!CollectionUtils.isEmpty(relateDeviceList)) {
                        deviceDetailList = dataService.getEsxiHostSpaceReport(modelCode, "RP_ESXI_HOST_SPACE",
                                eid,
                                "", relateDeviceList, "",
                                yearMonthStart,
                                yearMonth);
                    }
                    break;
                case "RELATION:ESXI:Esxi_Storage:RP_ESXI_STORAGE_LUN":
                    if (!CollectionUtils.isEmpty(relateDeviceList)) {
                        deviceDetailList = dataService.getEsxiStorageLunReport(modelCode, "RP_ESXI_STORAGE_LUN",
                                eid,
                                "", relateDeviceList, "",
                                yearMonthStart,
                                yearMonth);
                    }
                    break;
                default:
                    deviceDetailList = new ArrayList<>();
                    break;
            }

            return deviceDetailList;
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return new ArrayList();

    }


    @Override
    public List<Map<String, Object>> GetEsxiMaintenancestateReport(String reportTempId, String modelCode, String type, String item, String serviceCode, String eid) {
        try {
            long reportTempIdlong = Long.parseLong(reportTempId);
            //获取报表的设备列表
            ReportTemp reportTemp = reportDao.getReportTemp(reportTempIdlong, modelCode);
            if (reportTemp == null || reportTemp.getReportTempDevices() == null || reportTemp.getReportTempDevices().size() <= 0)
                return new ArrayList<>();

            //获取设备当年的维护记录列表
            List<String> deviceList = new ArrayList<String>();
            for (ReportTempDevice deviceTemp : reportTemp.getReportTempDevices()) {
                deviceList.add(deviceTemp.getDeviceId());
            }

            //获取查询年月
            String yearMonth = reportTemp.getReportTime();
            String yearMonthStart = reportTemp.getReportTime();
            if (type.equals("year"))
                yearMonthStart = reportTemp.getReportTime().substring(0, 5) + "01";//reportTemp.getReportTime();

            List<Map<String, Object>> deviceDetailList = new ArrayList<>();
            List<String> relateDeviceList = new ArrayList<>();
            if (item.startsWith("RELATION")) //RELATION:RS_MT_ESXI:ESXI_HOST:RP_ESXI_HOST
            {
                String[] arr = item.split(":");
                if (arr == null || arr[0] == null || arr[1] == null || arr[2] == null || arr[3] == null) { //格式不正确
                    return new ArrayList();
                }
                String relateModel = arr[2];
                //查询关联设备
                List<Map<String, Object>> mainAssetMapList = dataService.getMaintenanceList(arr[1], 0L, "", deviceList, "", 0, 0);
                if (!CollectionUtils.isEmpty(mainAssetMapList)) {
                    mainAssetMapList.forEach(mainAssetMap -> {
                        List list = JSONObject.parseObject(mainAssetMap.get("relation") != null ? mainAssetMap.get("relation").toString() : null, List.class, Feature.OrderedField);
                        if (!CollectionUtils.isEmpty(list)) {
                            //循环关联的模型
                            for (int i = 0; i < list.size(); i++) {
                                LinkedHashMap<String, Object> map = JSONObject.parseObject(JSONObject.toJSONString(list.get(i)), LinkedHashMap.class);
                                for (String key : map.keySet()) {
                                    if (key.equals("relationModel") && relateModel.equals(map.get(key).toString())) {
                                        log.info("找到关联模型了");
                                        List relationItems = JSONObject.parseObject(JSONObject.toJSONString(map.get("relationItems")), List.class);
                                        if (!CollectionUtils.isEmpty(relationItems)) {
                                            log.info("关联模型里还存在关联设备");
                                            for (int j = 0; j < relationItems.size(); j++) {
                                                LinkedHashMap<String, Object> relationItemMap = JSONObject.parseObject(JSONObject.toJSONString(relationItems.get(j)), LinkedHashMap.class);
                                                for (String relationItemKey : relationItemMap.keySet()) {
                                                    if (relationItemKey.equals("deviceId")) {
                                                        relateDeviceList.add(relationItemMap.get("deviceId").toString());
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    });
                }
            }
            switch (item) {
                case "RP_ESXI":
                    if (!CollectionUtils.isEmpty(deviceList)) {
                        deviceDetailList = dataService.getEsxiReport(modelCode, "RP_ESXI",
                                eid,
                                "", deviceList, "",
                                yearMonthStart,
                                yearMonth);
                    }
                    break;
                case "RELATION:ESXI:Esxi_Host:RP_ESXI_HOST":
                    if (!CollectionUtils.isEmpty(relateDeviceList)) {
                        deviceDetailList = dataService.getEsxiHostReport(modelCode, "RP_ESXI_HOST",
                                eid,
                                "", relateDeviceList, "",
                                yearMonthStart,
                                yearMonth);
                    }
                    break;
                case "RELATION:ESXI:Esxi_Storage:RP_ESXI_STORAGE":
                    if (!CollectionUtils.isEmpty(relateDeviceList)) {
                        deviceDetailList = dataService.getEsxiStorageReport(modelCode, "RP_ESXI_STORAGE",
                                eid,
                                "", relateDeviceList, "",
                                yearMonthStart,
                                yearMonth);
                    }
                    break;
                case "RELATION:ESXI:Esxi_Host:RP_ESXI_HOST_SPACE":
                    if (!CollectionUtils.isEmpty(relateDeviceList)) {
                        deviceDetailList = dataService.getEsxiHostSpaceReport(modelCode, "RP_ESXI_HOST_SPACE",
                                eid,
                                "", relateDeviceList, "",
                                yearMonthStart,
                                yearMonth);
                    }
                    break;
                case "RELATION:ESXI:Esxi_Storage:RP_ESXI_STORAGE_LUN":
                    if (!CollectionUtils.isEmpty(relateDeviceList)) {
                        deviceDetailList = dataService.getEsxiStorageLunReport(modelCode, "RP_ESXI_STORAGE_LUN",
                                eid,
                                "", relateDeviceList, "",
                                yearMonthStart,
                                yearMonth);
                    }
                    break;
                default:
                    deviceDetailList = new ArrayList<>();
                    break;
            }

            return deviceDetailList;
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return new ArrayList();

    }

    @Transactional
    @Override
    public int updateReportMaintenance(ReportMaintenanceUpdateDTO dto) {
        Long id = dto.getId();
        Integer status = dto.getStatus();
        if (status.equals(0)) {
            dto.setStatus(reportDao.getMaintenanceStatus(dto.getId()).get(1)); //取消作廢因第一筆會是作廢狀態，故需取第二筆資料
        }
        reportDao.insertReportMaintenanceLog(dto);
        return reportDao.updateReportMaintenance(id, dto.getStatus());
    }

    @Override
    public List<ReportMaintenanceLog> getReportLogs(Long reportMaintenanceId) {
        return reportDao.getReportLogs(reportMaintenanceId);
    }

    public List<Long> getServiceEidList(List<Long> sourceEidList) {
        if (RequestUtil.getHeaderServiceProviderSid() == 0 || !"AIEOM_SERVICE".equals(RequestUtil.getHeaderPlatformType())) {
            return new ArrayList<>();
        }
        if (CollectionUtils.isEmpty(sourceEidList)) {
            return Lists.newArrayList(-1L);
        }
        BaseResponse br = aioUserFeignClient.getSupplierAiopsModule(RequestUtil.getHeaderServiceProviderSid(), RequestUtil.getHeaderSid(), sourceEidList);
        if (br.checkIsSuccess()) {
            List<Map<String, Object>> data = (List<Map<String, Object>>) br.getData();
            List<Long> eidList = data.stream().map(map -> LongUtil.objectToLong(map.get("eid"))).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(eidList)) {
                return eidList;
            }
        }
        return Lists.newArrayList(-1L);
    }

    @Override
    public BaseResponse saveAssetMaintenanceReport(ReportMaintenance reportMaintenance) {
        if (Objects.isNull(reportMaintenance)) {
            return BaseResponse.error(ResponseCode.REPORT_ASSET_MAINTENANCE_PARAM_IS_EMPTY);
        }

        Long id = SnowFlake.getInstance().newId();
        Long sid = RequestUtil.getHeaderSid();
        reportMaintenance.setId(id);
        reportMaintenance.setSid(sid);
        reportMaintenance.setReportStatus(1); //因 1.39 暫無生成報告階段，故狀態預設"評估中"
        reportMaintenance.setDeviceNum(reportMaintenance.getDeviceMappings().size());
        reportMaintenance.setGenerateTime(new Date());

        // 添加設備mapping表數據ID
        reportMaintenance.getDeviceMappings().forEach(reportDevice -> {
            reportDevice.setId(SnowFlake.getInstance().newId());
        });

        Integer affectedReportRows = reportDao.saveAssetMaintenanceReport(reportMaintenance);
        Integer affectedAssetRows = reportDao.saveMaintenanceAsset(id, reportMaintenance.getDeviceMappings());

        if (IntegerUtil.isEmpty(affectedReportRows)) {
            return BaseResponse.error(ResponseCode.REPORT_ASSET_MAINTENANCE_SAVE_ERROR);
        }

        if (IntegerUtil.isEmpty(affectedAssetRows)) {
            return BaseResponse.error(ResponseCode.MAINTENANCE_ASSET_LIST_SAVE_ERROR);
        }

        return BaseResponse.ok(id);
    }

    @Override
    public BaseResponse getMaintenanceDeviceList(Long reportAssetMaintenanceId) {
        if (LongUtil.isEmpty(reportAssetMaintenanceId)) {
            return BaseResponse.error(ResponseCode.MAINTENANCE_ASSET_LIST_PARAM_IS_EMPTY);
        }

        List<ReportDeviceMapping> reportDeviceMappingList = reportDao.getMaintenanceAsset(reportAssetMaintenanceId);

        if (CollectionUtils.isEmpty(reportDeviceMappingList)) {
            return BaseResponse.error(ResponseCode.MAINTENANCE_ASSET_LIST_IS_EMPTY);
        }

        List<Map<String, Object>> result = reportDeviceMappingList.stream()
                .collect(Collectors.groupingBy(ReportDeviceMapping::getModelCode))
                .entrySet().stream()
                .map(entry -> {
                    Map<String, Object> map = new HashMap<>();
                    map.put("modelCode", entry.getKey());
                    map.put("deviceList", entry.getValue());
                    return map;
                })
                .collect(Collectors.toList());

        return BaseResponse.ok(result);
    }

    @Override
    public BaseResponse createMaintenancereadLog(ReportMaintenanceReadLog reportMaintenanceReadLog) {
        if (Objects.isNull(reportMaintenanceReadLog)) {
            return BaseResponse.error(ResponseCode.REPORT_ASSET_MAINTENANCE_READ_LOG_DATA_IS_EMPTY);
        }

        reportMaintenanceReadLog.setId(SnowFlake.getInstance().newId());
        reportMaintenanceReadLog.setReadTime(new Date());
        if (reportDao.createMaintenanceReadLog(reportMaintenanceReadLog) <= 0) {
            return BaseResponse.error(ResponseCode.REPORT_ASSET_MAINTENANCE_READ_LOG_SAVE_ERROR);
        }
        return BaseResponse.ok(reportMaintenanceReadLog);
    }

    @Override
    public BaseResponse getMaintenancereadLog(Long assetMaintenanceId, Integer pageNum, Integer pageSize) {
        if (LongUtil.isEmpty(assetMaintenanceId)) {
            return BaseResponse.error(ResponseCode.PARAM_IS_EMPTY);
        }

        PageInfo info = new PageInfo<>();
        Integer total = reportDao.getMaintenanceReadLogCount(assetMaintenanceId);
        info.setTotal(total);
        info.setPages((int) Math.ceil((double) total / pageSize));
        info.setPageNum(pageNum);
        info.setPageSize(pageSize);
        info.setList(reportDao.getMaintenanceReadLog(assetMaintenanceId, (pageNum - 1) * pageSize, pageSize));
        return BaseResponse.ok(info);
    }

    @Override
    public BaseResponse updateReportMISStatus(Long assetMaintenanceId, Integer misStatus, String sender) {
        Integer affectRow = reportDao.updateMISStatus(assetMaintenanceId, misStatus, sender, new Date());
        if (affectRow <= 0) {
            BaseResponse.error(ResponseCode.UPDATE_FAILD, affectRow);
        }
        return BaseResponse.ok(affectRow);
    }

    @Override
    public void downloadMaintenanceReportFile(String serviceCode, String reportId, String fileId, HttpServletResponse response) {
        try {
            String redirectUrl = misRoot + "#/404";

            Optional<ReportFile> reportFileOpt = Optional.ofNullable(reportDao.getFileByReportIdAndFileId(serviceCode, reportId, fileId));

            if (reportFileOpt.isPresent()) {
                redirectUrl = reportFileOpt.get().getUrl();
            }

            response.sendRedirect(redirectUrl);
        } catch (Exception e) {
            log.error("DownloadMaintenanceReportFile Error", e);
        }
    }

    private String processReceivers(String receiversInput) {
        return Arrays.stream(receiversInput.split(";"))
                .filter(k -> !StringUtils.isEmpty(k))
                .distinct()
                .collect(Collectors.joining(","));
    }

    private String processAttachments(ReportMaintenance rm) {
        StringBuilder attachmentHtml = new StringBuilder();

        Optional.ofNullable(rm.getFiles()).ifPresent(files -> {
            if (!CollectionUtil.isEmpty(files)) {
                files.forEach(reportFile -> {
                    // 設置文件屬性
                    reportFile.setId(SnowFlake.getInstance().newId());
                    reportFile.setSid(rm.getSid());
                    reportFile.setRmId(rm.getId());
                    reportFile.setFileType(rm.getReportType());
                    reportDao.saveReportFile(reportFile);

                    // 生成下載URL
                    String url = String.format(
                            "%saiogateway/aiocmdb/api/report/%s/file?reportId=%d&fileId=%d",
                            misRoot,
                            rm.getServiceCode(),
                            rm.getId(),
                            reportFile.getId()
                    );

                    // 添加下載鏈接
                    attachmentHtml.append(String.format("<a href=\"%s\">%s</a>&nbsp;&nbsp;", url, reportFile.getName()));
                });
            }
        });

        return attachmentHtml.toString();
    }

    private Map<String, Object> prepareMailParameters(ReportMaintenance rm, String attachmentHtml) {
        String[] reportTime = rm.getReportTime().split("-");
        String year = reportTime[0];
        String month = reportTime[1];
        boolean isCN = "CN".equals(connectArea);
        String reportName = getReportName(rm.getReportType(), isCN);

        Map<String, Object> params = convertToMap(rm);
        params.put("attachment", attachmentHtml);
        params.put("reportName", reportName);
        params.put("year", year);
        params.put("month", month);
        params.put("reportURLName", reportName);
        params.put("platFormUrl", misRoot + "#/report/asset-report/list");

        return params;
    }

    private void sendMailAsync(ReportMaintenance rm, String subject, String content, Boolean isCN) {
        List<String> receiverList = Arrays.stream(rm.getReceivers().split(",")).distinct().collect(Collectors.toList());

        CompletableFuture.runAsync(() ->
                mailSendService.sendCmdbReportMail(
                        rm.getServiceCode(),
                        receiverList,
                        subject,
                        content,
                        isCN ? "zh-CN" : "zh-TW"
                )
        );
    }

    private String getReportName(Integer reportType, Boolean isCN) {
        switch (reportType) {
            case 1:
                return isCN ? "企业运维软硬件资产报告" : "企業運維軟硬件資產報告";
            case 2:
                return isCN ? "企业运维终端运维报告" : "企業運維終端運維報告";
            default:
                return isCN ? "企业运维资产维护报告" : "企業運維資產維護報告";
        }
    }

    private String getMailContent(Map<String, Object> map, String templateName, Boolean isCN) {
        try {
            FreemarkerTplData tplData = new FreemarkerTplData(isCN ? ZH_CN_STANDARD : ZH_TW_STANDARD, map);
            return tplService.getByTemplateName(templateName, tplData);
        } catch (Exception e) {
            log.error("GetMailContent Error", e);
            return "";
        }
    }

    private <T> Map<String, Object> convertToMap(T data) {
        Map<String, Object> map = new HashMap<>();
        try {
            java.lang.reflect.Field[] fields = data.getClass().getDeclaredFields();
            for (java.lang.reflect.Field field : fields) {
                field.setAccessible(true);
                map.put(field.getName(), field.get(data));
            }
        } catch (IllegalAccessException e) {
            e.printStackTrace();
        }
        return map;
    }

    @Override
    public BaseResponse selectAssetReportRecord(String eid, String startReportDate, String endReportDate, String assetCategory, String userName,
                                                String startReportGenerateTime, String endReportGenerateTime, String serviceCodeORCustomerName, int pageNum, int pageSize) {
        PageHelper.startPage(pageNum, pageSize);
        List<AssetReportRecord> assetReportRecords = reportDao.selectAssetReportRecord(eid, startReportDate, endReportDate, assetCategory, userName,
                startReportGenerateTime, endReportGenerateTime, serviceCodeORCustomerName);
        PageInfo<AssetReportRecord> pageInfo = new PageInfo<>(assetReportRecords);
        return BaseResponse.ok(pageInfo);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BaseResponse deleteAssetReportRecordById(Long id, String reportType) {
        int deleted = reportDao.deleteAssetReportRecordById(id, reportType);
        String sql = String.format("DELETE FROM %s.%s WHERE reportId = %s",
                bigDataUtil.getSrDbName(),
                reportType,
                id);
        bigDataUtil.srSave(sql);
        return BaseResponse.ok(deleted);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BaseResponse insertAssetReportRecord(AssetReportRecord record) {
        ObjectMapper objectMapper = new ObjectMapper();
        List<AssetObject> categoryList;
        int inserted = 0;
        record.setId(SnowFlake.getInstance().newId());
        record.setReportGenerateTime(String.valueOf(LocalDateTime.now()));

        // 1. 取得當年月字串
        String yearMonth = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMM"));
        // 2. 定義 recordNumber 前綴
        String prefix = "ISMS-04-030";
        // 3. 查詢該 eid + 年月 最大 recordNumber
        String maxRecordNumber = reportDao.queryMaxRecordNumberByEid(record.getEid(), prefix + "-" + yearMonth + "-%");
        // 4. 解析 maxRecordNumber 流水號，並 +1，若空，流水號設 1
        int nextSerial = 1;
        if (maxRecordNumber != null && !maxRecordNumber.isEmpty()) {
            // 取流水號字串，格式: ISMS-04-030-202507-001 (流水號從第20位開始)
            String serialStr = maxRecordNumber.substring(19);
            try {
                nextSerial = Integer.parseInt(serialStr) + 1;
            } catch (NumberFormatException e) {
                nextSerial = 1; // 如果解析失敗就重置為1
            }
        }
        // 5. 格式化流水號，補三位數零
        String serialFormatted = String.format("%03d", nextSerial);
        // 6. 組合新的 recordNumber
        String newRecordNumber = prefix + "-" + yearMonth + "-" + serialFormatted;
        record.setRecordNumber(newRecordNumber);

        reportDao.insertAssetReportRecord(record);
        try {
            String json = record.getAssetObject();
            if (json == null || json.trim().isEmpty()) {
                return BaseResponse.error(ResponseCode.ASSET_OBJECT_IS_NULL);
            }
            categoryList = objectMapper.readValue(json, new TypeReference<List<AssetObject>>() {
            });

            List<Map<String, Object>> allResults = new ArrayList<>();
            for (AssetObject item : categoryList) {
                String category = item.getCategoryNumber();
                List<Long> ids = item.getAssetIdList();
                String sql;
                if (ids != null && !ids.isEmpty()) {
                    String idListStr = ids.stream()
                            .map(String::valueOf)
                            .collect(Collectors.joining(","));
                    sql = String.format("SELECT * FROM %s.%s WHERE eid = %s AND assetId IN (%s)",
                            bigDataUtil.getSrDbName(),
                            category,
                            record.getEid(),
                            idListStr
                    );
                } else {
                    // 沒傳 assetIdList → 查全部該 eid 的資料
                    sql = String.format("SELECT * FROM %s.%s WHERE eid = %s",
                            bigDataUtil.getSrDbName(),
                            category,
                            record.getEid()
                    );
                }
                    List<Map<String, Object>> query = bigDataUtil.starrocksQuery(sql);
                    if (query != null && !query.isEmpty()) {
                        allResults.addAll(query);
                    }
            }
            StarRocksEntity starRocksEntity = new StarRocksEntity();
            starRocksEntity.setDatabase(bigDataUtil.getSrDbName());
            starRocksEntity.setTable(record.getReportType());
            String sqlFieldNames = String.format("DESCRIBE %s.%s",
                    bigDataUtil.getSrDbName(),
                    record.getReportType()
            );
            List<Map<String, Object>> list = bigDataUtil.starrocksQuery(sqlFieldNames);
            // 將欄位名稱提取成 String[]
            String[] fieldNames = list.stream()
                    .map(m -> (String) m.get("Field"))
                    .toArray(String[]::new);
            // 設定到 starRocksEntity
            starRocksEntity.setFieldNames(fieldNames);

            // rows
            List<LinkedHashMap<String, Object>> rows = new ArrayList<>();
            if ("AssetInventory".equals(record.getReportType())) {
                for (Map<String, Object> result : allResults) {
                    LinkedHashMap<String, Object> row = new LinkedHashMap<>();
                    row.put("id", SnowFlake.getInstance().newId());
                    row.put("reportId", record.getId());
                    row.put("assetId", result.getOrDefault("assetId", null));
                    row.put("deviceId", result.getOrDefault("deviceId", null));
                    row.put("eid", record.getEid());
                    row.put("collectedTime", System.currentTimeMillis());
                    row.put("aiopsItem", result.getOrDefault("aiopsItem", null));
                    row.put("aiopsItemId", result.getOrDefault("aiopsItemId", null));
                    row.put("aiId", result.getOrDefault("aiId", null));
                    row.put("flumeTimestamp", System.currentTimeMillis());
                    row.put("assetCode", result.getOrDefault("assetCode", null));
                    row.put("assetName", result.getOrDefault("assetName", null));
                    row.put("useStatus", result.getOrDefault("useStatus", null));
                    row.put("assetValue", result.getOrDefault("assetValue", null));
                    row.put("manufacturer", result.getOrDefault("manufacturer", null));
                    row.put("modelNumber", result.getOrDefault("modelNumber", null));
                    row.put("custodian", result.getOrDefault("custodian", null));
                    row.put("assetCategory", result.getOrDefault("assetCategory", null));
//                    row.put("ciaScore", result.getOrDefault("ciaScore", null));
//                    row.put("ciaScoreDetails", result.getOrDefault("ciaScoreDetails", null));
//                    row.put("confidentialityValue", result.getOrDefault("confidentialityValue", null));
//                    row.put("integrityValue", result.getOrDefault("integrityValue", null));
//                    row.put("availabilityValue", result.getOrDefault("availabilityValue", null));
                    rows.add(row);
                }
            } else if ("SoftwareInventory".equals(record.getReportType())) {
                for (Map<String, Object> result : allResults) {
                    LinkedHashMap<String, Object> row = new LinkedHashMap<>();
                    row.put("id", SnowFlake.getInstance().newId());
                    row.put("reportId", record.getId());
                    row.put("assetId", result.getOrDefault("assetId", null));
                    row.put("deviceId", result.getOrDefault("deviceId", null));
                    row.put("eid", record.getEid());
                    row.put("collectedTime", System.currentTimeMillis());
                    row.put("aiopsItem", result.getOrDefault("aiopsItem", null));
                    row.put("aiopsItemId", result.getOrDefault("aiopsItemId", null));
                    row.put("aiId", result.getOrDefault("aiId", null));
                    row.put("flumeTimestamp", System.currentTimeMillis());
                    row.put("assetCode", result.getOrDefault("assetCode", null));
                    row.put("assetName", result.getOrDefault("assetName", null));
                    row.put("versionNumber", result.getOrDefault("versionNumber", null));
                    row.put("type", result.getOrDefault("type", null));
                    row.put("installDate", result.getOrDefault("installDate", null));
                    row.put("licenseExpiryDate", result.getOrDefault("licenseExpiryDate", null));
                    row.put("count", record.getAssetCount());
                    row.put("custodialUnit", result.getOrDefault("custodialUnit", null));
                    row.put("custodian", result.getOrDefault("custodian", null));
                    row.put("user", result.getOrDefault("user", null));
                    rows.add(row);
                }
            }
            starRocksEntity.setRows(rows);
            bigDataUtil.srStreamLoadThrowsException(starRocksEntity);
            inserted = reportDao.updateReportStatus(record.getId(), "GENERATED");
        } catch (Exception e) {
            log.error("解析 assetObject 發生錯誤", e);
        }
        return BaseResponse.ok(inserted);
    }

    @Override
    public BaseResponse selectAssetReportRecordById(Long id){
        return BaseResponse.ok(reportDao.selectAssetReportRecordById(id));
    }
}
