package com.digiwin.escloud.issueservice.v3.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.digiwin.escloud.common.feign.AioIssueFeignClient;
import com.digiwin.escloud.common.model.ResponseCode;
import com.digiwin.escloud.common.util.CollectionUtil;
import com.digiwin.escloud.common.util.IntegerUtil;
import com.digiwin.escloud.common.util.RequestUtil;
import com.digiwin.escloud.common.util.SnowFlake;
import com.digiwin.escloud.integration.api.iam.req.tenant.TenantVO;
import com.digiwin.escloud.integration.api.iam.req.user.IamAuthoredUser;
import com.digiwin.escloud.integration.service.GmcService;
import com.digiwin.escloud.integration.service.IamService;
import com.digiwin.escloud.issueservice.cache.IssueSwitchCache;
import com.digiwin.escloud.issueservice.constant.Constants;
import com.digiwin.escloud.issueservice.dao.IIssueDao;
import com.digiwin.escloud.issueservice.dao.IUserDao;
import com.digiwin.escloud.issueservice.dao.Impl.IssueSyncDao;
import com.digiwin.escloud.issueservice.model.*;
import com.digiwin.escloud.issueservice.services.*;
import com.digiwin.escloud.issueservice.services.Impl.KcfService;
import com.digiwin.escloud.issueservice.statemode.Incomplete;
import com.digiwin.escloud.issueservice.statemode.IssueContext;
import com.digiwin.escloud.issueservice.t.integration.fuguan.mq.issue.IssueFGProducer;
import com.digiwin.escloud.issueservice.t.integration.fuguan.rule.issue.Prerequisite;
import com.digiwin.escloud.issueservice.t.issuedetail.dao.IssueProcessMapper;
import com.digiwin.escloud.issueservice.t.issuesubmit.dao.IssueSubmitMapper;
import com.digiwin.escloud.issueservice.t.issuesubmit.service.IssueSubmitService;
import com.digiwin.escloud.issueservice.t.model.cases.Cases;
import com.digiwin.escloud.issueservice.t.model.cases.CasesEmail;
import com.digiwin.escloud.issueservice.t.model.cases.Module;
import com.digiwin.escloud.issueservice.t.model.cases.constants.SubmitWay;
import com.digiwin.escloud.issueservice.t.model.cases.dto.CasesFormDTO;
import com.digiwin.escloud.issueservice.t.model.common.BaseResponse;
import com.digiwin.escloud.issueservice.t.model.common.ResponseStatus;
import com.digiwin.escloud.issueservice.t.utils.CasesProcessUtils;
import com.digiwin.escloud.issueservice.t.utils.DateUtils;
import com.digiwin.escloud.issueservice.utils.MailUtils;
import com.digiwin.escloud.common.util.auto.MessageUtils;
import com.digiwin.escloud.issueservice.v3.dao.IIssueDaoV3;
import com.digiwin.escloud.issueservice.v3.dao.IIssueDetailDaoV3;
import com.digiwin.escloud.issueservice.v3.service.IIssueServiceV3;
import com.digiwin.escloud.messagelibrary.Model.MessageDestination;
import com.digiwin.escloud.messagelibrary.ProducerBaseInterface;
import com.digiwin.escloud.userapi.model.customer.ServiceStaffCond;
import com.digiwin.escloud.userapi.model.user.UserDetailInfo;
import com.digiwin.escloud.userapi.service.UserServiceFeignClient;
import com.google.gson.Gson;
import com.mongodb.BasicDBObject;
import com.mongodb.DBObject;
import com.mongodb.gridfs.GridFSDBFile;
import com.mongodb.gridfs.GridFSFile;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.apache.tomcat.util.http.fileupload.ByteArrayOutputStream;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.gridfs.GridFsTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.web.client.RestTemplate;

import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.time.LocalTime;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static org.apache.tomcat.util.http.fileupload.IOUtils.copy;

//import com.github.pagehelper.util.StringUtil;
//import org.springframework.util.StringUtils;

/**
 * Created by Administrator on 2018-01-03.
 */
@Service
public class IssueServiceV3 extends ProducerBaseInterface<Boolean, Issue> implements IIssueServiceV3 {
    @Value("${digiwin.visitor.public.user.service.code}")
    private String visitorServiceCode;
    @Value("${digiwin.issue.connectarea}")
    private String connectArea;
    @Value("${digiwin.aio.dbname:aio-db}")
    private String aioDBName;
    @Value("${digiwin.issueservice.defaultSid:241199971893824}")
    Long defaultSid;
    @Autowired
    private CommonMailService commonMailService;
    @Autowired
    private MailUtils mailUtils;
    @Autowired
    private IssueProcessMapper issueProcessMapper;

    @Autowired
    private IssueSyncDao issueSyncDao;
    @Autowired
    private IssueFGProducer fg187Mq;
    @Autowired
    private CasesProcessUtils casesProcessUtils;
    @Autowired
    private MessageUtils messageUtils;
    @Autowired
    IssueSubmitService issueSubmitService;
    @Autowired
    private Prerequisite post187Prerequisite;
    private static final String ToSubmiterBackMailStr = "您通过鼎捷云管家提交的问题已被退回！案件单详细信息如下：</p>\n" +
            "\t\t\t<table class=\"issuetable\" align=left style=\"border: solid 1px #dad7d7; width:860px;text-indent: 0em;margin-left: 2.5em;font-size: 14px;\">\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t\t\t<td width=75 height=40 style=\"padding-left:10px\"><b>产品线</b></td>\n" +
            "\t\t\t\t\t<td width=200>%s</td>\n" +
            "\t\t\t\t\t<td width=75><b>客服代号</b></td>\n" +
            "\t\t\t\t\t<td width=200>%s</td>\n" +
            "\t\t\t\t\t<td width=75><b>合约等级</b></td>\n" +
            "\t\t\t\t\t<td style=\"border-right:0px;\">%s</td>\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t\t\t<td width=75 height=40 style=\"padding-left:10px\"><b>案件单号</b></td>\n" +
            "\t\t\t\t\t<td width=200>%s</td>\n" +
            "\t\t\t\t\t<td width=75><b>客户名称</b></td>\n" +
            "\t\t\t\t\t<td width=200>%s</td>\n" +
            "\t\t\t\t\t<td width=75><b>接单时间</b></td>\n" +
            "\t\t\t\t\t<td style=\"border-right:0px;\">%s</td>\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t\t\t<td height=40  style=\"padding-left:10px\"><b>联系人</b></td>\n" +
            "\t\t\t\t\t<td>%s</td>\n" +
            "\t\t\t\t\t<td><b>E-Mail</b></td>\n" +
            "\t\t\t\t\t<td>%s</td>\n" +
            "\t\t\t\t\t<td><b>电话</b></td>\n" +
            "\t\t\t\t\t<td style=\"border-right:0px;\">%s</td>\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t\t\t<td height=40  style=\"padding-left:10px\"><b>附件</b></td>\n" +
            "\t\t\t\t\t<td colspan=\"5\"   style=\"border-right:0px;\">%s</td>\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t\t\t<td style=\"border-bottom:#dad7d7 dotted  0px; vertical-align: text-top; padding-left:10px;\"><b>问题描述</b></td>\n" +
            "\t\t\t\t\t<td colspan=\"5\" style=\"border-right:0px; border-bottom:0px;\"><pre  style=\"width:740px;border: none;font-family: sans-serif;word-wrap: break-word; white-space: pre-wrap;\">%s</pre></td>\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t</table>";
    private static final String ToSubmiterBackMailStr_TW = "您通過鼎新服務雲提交的問題已被退回！案件單詳細資訊如下：</p>\n" +
            "\t\t\t<table class=\"issuetable\" align=left style=\"border: solid 1px #dad7d7; width:860px;text-indent: 0em;margin-left: 2.5em;font-size: 14px;\">\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t\t\t<td width=75 height=40 style=\"padding-left:10px\"><b>産品線</b></td>\n" +
            "\t\t\t\t\t<td width=200>%s</td>\n" +
            "\t\t\t\t\t<td width=75><b>客服代號</b></td>\n" +
            "\t\t\t\t\t<td width=200 >%s</td>\n" +
            "\t\t\t\t\t<td width=75><b>合約等級</b></td>\n" +
            "\t\t\t\t\t<td style=\"border-right:0px;\">%s</td>\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t\t\t<td width=75 height=40 style=\"padding-left:10px\"><b>案件代號</b></td>\n" +
            "\t\t\t\t\t<td width=200>%s</td>\n" +
            "\t\t\t\t\t<td width=75><b>客戶名稱</b></td>\n" +
            "\t\t\t\t\t<td width=200>%s</td>\n" +
            "\t\t\t\t\t<td width=75><b>填單時間</b></td>\n" +
            "\t\t\t\t\t<td style=\"border-right:0px;\">%s</td>\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t\t\t<td height=40  style=\"padding-left:10px\"><b>連絡人</b></td>\n" +
            "\t\t\t\t\t<td>%s</td>\n" +
            "\t\t\t\t\t<td><b>E-Mail</b></td>\n" +
            "\t\t\t\t\t<td>%s</td>\n" +
            "\t\t\t\t\t<td><b>電話</b></td>\n" +
            "\t\t\t\t\t<td style=\"border-right:0px;\">%s</td>\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t\t\t<td height=40  style=\"padding-left:10px\"><b>附件</b></td>\n" +
            "\t\t\t\t\t<td colspan=\"5\" style=\"border-right:0px;\">%s</td>\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t\t\t<td style=\"border-bottom:#dad7d7 dotted  0px; vertical-align: text-top; padding-left:10px;\"><b>問題描述</b></td>\n" +
            "\t\t\t\t\t<td colspan=\"5\" style=\" border-right:0px; border-bottom:0px;\"><pre  style=\"width:740px;border: none;font-family: sans-serif;word-wrap: break-word; white-space: pre-wrap;\">%s</pre></td>\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t</table>";
    private static final String ToSubmiterMailStrByConfirm = "您有新的案件待审核，请您及时登录鼎新服务云进行审核！案件单详细资讯如下：</p>\n" +
            "\t\t\t<table class=\"issuetable\" align=left style=\"border: solid 1px #dad7d7; width:860px;text-indent: 0em;margin-left: 2.5em;font-size: 14px;\">\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t\t\t<td width=75 height=40 style=\"padding-left:10px\"><b>产品线</b></td>\n" +
            "\t\t\t\t\t<td width=200>%s</td>\n" +
            "\t\t\t\t\t<td width=75><b>客服代号</b></td>\n" +
            "\t\t\t\t\t<td width=200>%s</td>\n" +
            "\t\t\t\t\t<td width=75><b>合约等级</b></td>\n" +
            "\t\t\t\t\t<td style=\"border-right:0px;\">%s</td>\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t\t\t<td width=75 height=40 style=\"padding-left:10px\"><b>案件单号</b></td>\n" +
            "\t\t\t\t\t<td width=200>%s</td>\n" +
            "\t\t\t\t\t<td width=75><b>客户名称</b></td>\n" +
            "\t\t\t\t\t<td width=200>%s</td>\n" +
            "\t\t\t\t\t<td width=75><b>接单时间</b></td>\n" +
            "\t\t\t\t\t<td style=\"border-right:0px;\">%s</td>\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t\t\t<td height=40  style=\"padding-left:10px\"><b>联系人</b></td>\n" +
            "\t\t\t\t\t<td>%s</td>\n" +
            "\t\t\t\t\t<td><b>E-Mail</b></td>\n" +
            "\t\t\t\t\t<td>%s</td>\n" +
            "\t\t\t\t\t<td><b>电话</b></td>\n" +
            "\t\t\t\t\t<td style=\"border-right:0px;\">%s</td>\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t\t\t<td height=40  style=\"padding-left:10px\"><b>附件</b></td>\n" +
            "\t\t\t\t\t<td colspan=\"5\"   style=\"border-right:0px;\">%s</td>\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t\t\t<td style=\"border-bottom:#dad7d7 dotted  0px; vertical-align: text-top; padding-left:10px;\"><b>问题描述</b></td>\n" +
            "\t\t\t\t\t<td colspan=\"5\" style=\"border-right:0px; border-bottom:0px;\"><pre  style=\"width:740px;border: none;font-family: sans-serif;word-wrap: break-word; white-space: pre-wrap;\">%s</pre></td>\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t</table>";
    private static final String ToSubmiterMailStrByConfirm_TW = "您有新的案件待審核，請您及時登入鼎新服務雲進行審核！案件單詳細資訊如下：</p>\n" +
            "\t\t\t<table class=\"issuetable\" align=left style=\"border: solid 1px #dad7d7; width:860px;text-indent: 0em;margin-left: 2.5em;font-size: 14px;\">\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t\t\t<td width=75 height=40 style=\"padding-left:10px\"><b>産品線</b></td>\n" +
            "\t\t\t\t\t<td width=200>%s</td>\n" +
            "\t\t\t\t\t<td width=75><b>客服代號</b></td>\n" +
            "\t\t\t\t\t<td width=200 >%s</td>\n" +
            "\t\t\t\t\t<td width=75><b>合約等級</b></td>\n" +
            "\t\t\t\t\t<td style=\"border-right:0px;\">%s</td>\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t\t\t<td width=75 height=40 style=\"padding-left:10px\"><b>案件代號</b></td>\n" +
            "\t\t\t\t\t<td width=200>%s</td>\n" +
            "\t\t\t\t\t<td width=75><b>客戶名稱</b></td>\n" +
            "\t\t\t\t\t<td width=200>%s</td>\n" +
            "\t\t\t\t\t<td width=75><b>填單時間</b></td>\n" +
            "\t\t\t\t\t<td style=\"border-right:0px;\">%s</td>\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t\t\t<td height=40  style=\"padding-left:10px\"><b>連絡人</b></td>\n" +
            "\t\t\t\t\t<td>%s</td>\n" +
            "\t\t\t\t\t<td><b>E-Mail</b></td>\n" +
            "\t\t\t\t\t<td>%s</td>\n" +
            "\t\t\t\t\t<td><b>電話</b></td>\n" +
            "\t\t\t\t\t<td style=\"border-right:0px;\">%s</td>\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t\t\t<td height=40  style=\"padding-left:10px\"><b>附件</b></td>\n" +
            "\t\t\t\t\t<td colspan=\"5\" style=\"border-right:0px;\">%s</td>\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t\t\t<td style=\"border-bottom:#dad7d7 dotted  0px; vertical-align: text-top; padding-left:10px;\"><b>問題描述</b></td>\n" +
            "\t\t\t\t\t<td colspan=\"5\" style=\" border-right:0px; border-bottom:0px;\"><pre  style=\"width:740px;border: none;font-family: sans-serif;word-wrap: break-word; white-space: pre-wrap;\">%s</pre></td>\n" +
            "\t\t\t\t<tr>\n" +
            "\t\t\t</table>";

    private static Log log = LogFactory.getLog(IssueServiceV3.class);
    @Autowired
    RestTemplate restTemplate;
    @Autowired
    GridFsTemplate gridFsTemplate;
    @Autowired
    private IIssueDaoV3 issueDao;
    @Autowired
    private IIssueDao oldIssueDao;
    @Autowired
    private IssueSwitchCache issueSwitchCache;
    @Autowired
    private IIssueDetailDaoV3 issueDetailDao;

    @Autowired
    private ICustomerService customerService;
    @Autowired
    private IUserService userService;
    @Autowired
    private IProductService productService;
    @Autowired
    private IIssueNoticeService issueNoticeService;
    @Autowired
    private IPromotionService promotionService;
    @Value("${attachmentfilePathPrefix}")
    private String attachmentfilePathPrefix;
    @Value("${issueAttachAddress}")
    private String issueAttachAddress;
    @Value("${digiwin.issue.defaultlanguage}")
    private String defaultLanguage;

    @Value("${digiwin.issue.servertimezone:08:00}")
    private String servertimezone;
    @Value("${api.bigdata.url}")
    private String bigDataUrl;
    @Autowired
    IUserDao userDao;
    @Autowired
    private ITagService tagService;
    @Autowired
    private GmcService gmcService;
    @Autowired
    private KcfService kcfService;
    @Autowired
    private AioIssueFeignClient aioIssueClient;
    @Autowired
    private UserServiceFeignClient userServiceFeignClient;
    @Autowired
    private IssueSubmitMapper issuesMapper;
    @Autowired
    private IamService iamService;
    @Override
    protected Map<MessageDestination, String[]> generateMessage(Issue issue, Boolean success) {
        Map<MessageDestination, String[]> messgae = new HashMap<MessageDestination, String[]>();
        if (success) {
            String[] mx = {PrepareIssueMail(issue)};
            messgae.put(MessageDestination.MAILMESSAGEDESTINATION, mx);
        }
        return messgae;
    }

    @Override
    protected Boolean realBusiness(Issue issue) {
        return true;
    }

    @Override
    protected void exceptionHandler(Exception e) {

    }

    private boolean CustomerIssueConfirmSetting(Issue issue) {

        return customerService.GetCustomerIssueConfirmSetting(issue.getServiceCode(), issue.getProductCode());
    }

    private String GetCustomerIssueConfirmer(Issue issue) {

        return customerService.GetCustomerIssueConfirmer(issue.getServiceCode(), issue.getProductCode());
    }

    @Override
    public boolean checkConfirm(Issue issue) {
        //易聊不需走这套逻辑
        boolean isSIMIssue = issue.getSubmitWay().startsWith("SIM_") || issue.getSubmitWay().startsWith("EASY_TALK");

        if (isSIMIssue) {
            return false;
        }
        if (!isSIMIssue) {
            boolean isConfirm = CustomerIssueConfirmSetting(issue);
            if (isConfirm) {
                String confirmIds = GetCustomerIssueConfirmer(issue);
                if (StringUtils.isBlank(confirmIds)) {
                    return false;
                }
            } else {
                return false;
            }

        }

        return true;
    }

    @Override
    public String getServiceCodeByTenantId(String tenantId){
        List<TenantVO> list = iamService.getTenantSimpleInfo(RequestUtil.getHeaderToken(),Arrays.asList(tenantId));
        if(list == null || list.size() == 0) {
            return "";
        }
        return list.get(0).getCustomerId();
    }
    /**
     * 案件提交
     *
     * @param issue
     * @return
     */
    @Override
    public int SubmitIssue(Issue issue) {
        int res = 0;
        //long issueId = 0;
        DateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String date = sdf.format(new Date());
        //1.生成案件单
        try {
            issue.setIssueStatus(IssueStatus.Incomplete.toString());
            if (issue.isTrial()) {
                issue.setSyncStatus(IssueStatus.Trial.toString());
            } else {
                if (CheckContractIsTrial(issue.getServiceCode(), issue.getProductCode())) {
                    issue.setSyncStatus(IssueStatus.Trial.toString());
                } else {
                    //再案件信息完善之前，同步状态为"待完善"
                    issue.setSyncStatus(IssueStatus.Incomplete.toString());
                }
            }

            //如果是SIM，允许前端设置提交时间
            String submitWay = issue.getSubmitWay();
            String submitTime = issue.getSubmitTime();
            if (submitWay == null || submitTime == null || submitTime.trim().isEmpty() || !submitWay.startsWith("SIM_") || !submitWay.startsWith("EASY_TALK")) {
                issue.setSubmitTime(date);
            }
            if (StringUtils.isEmpty(issue.getServiceRegion())) {
                issue.setServiceRegion(connectArea);
            }
            issueDao.InsertIssue(issue);

            //新增所選的預警編號.
            issueDao.batchInsertWarningId(issue.getIssueId(), issue.getCrmId(), issue.getWarningIdCollection());
            //新增跟這個 ISSUE 有關的项目任务
            issueDao.batchInsertSourceMap(issue.getIssueId(), issue.getCrmId(), issue.getIssueSourceMapList());
        } catch (Exception ex) {
            res = 1;
            log.error(ex);
            ex.printStackTrace();
            return res;
        }
//        //2.发送邮件给客户
//        SendMailToSubmiter(issue);
//        //3.完善案件资料，并通知客服
//        CompleteIssueInfo(issueId, issue);
//        //4.保存附件
//        SaveAttachments(issueId, issue);

        return res;
    }

    @Override
    public IssueSubmitResponse submitTIssue(Issue issue,boolean closeMailToSubmiter) {
        IssueSubmitResponse issueSubmitResponse = new IssueSubmitResponse();
        //1.生成案件单
        CasesFormDTO cases = esIssueToTIssue(issue);

        cases.setAccount(Arrays.asList(dealTService(issue,issue.getProductCode())));
        try {
            BaseResponse baseResponse = issueSubmitService.submitCaseToJiaofu("",issue.getUserId(),cases, null);
            if(baseResponse.getStatus() == ResponseStatus.OK.getCode()){
                Cases resultCases = (Cases) baseResponse.getResponse();
                Long issueId = resultCases.getIssueId();
                issue.setIssueId(issueId);
                issue.setCrmId(resultCases.getCrmId());
                issue.setSubmitTime(resultCases.getSubmitTime());
                //保存附件
                SaveAttachments(issueId, 0L, issue);

                //保存處理進度,可能是立案并结案，可能是立案加回复内容
                SaveIssueProgress(issue);

                //如果是新版易聊来的立案并结案的案件，需要将单身存起来 并发送结案邮件
                if (IssueStatus.Closed.toString().equals(issue.getIssueStatus())) {
                    //更新summary
                    updateSummaryV3(issue);
                    //立案并结案 邮件 结案邮件通知提交联系人邮箱（有勾选通知客户）
                    if (closeMailToSubmiter) {
                        try {
                            SendCloseMailToSubmiter(issue);
                        } catch (Exception ex) {
                            ex.printStackTrace();
                        }
                    }
                }
                issueSubmitResponse.setCode("0");
                issueSubmitResponse.setData(Long.toString(issueId));
            }else {
                issueSubmitResponse.setCode("1");
                issueSubmitResponse.setData("-1");
                issueSubmitResponse.setErrMsg(baseResponse.getStatusDescription());
            }
        }catch (Exception ex) {
            log.error("submitTIssue error:" + ex.toString());
        }

        return issueSubmitResponse;
    }

    private CasesFormDTO esIssueToTIssue(Issue issue) {
        CasesFormDTO cases = new CasesFormDTO();
        cases.setServiceRegion(issue.getServiceRegion());
        cases.setServiceCode(issue.getServiceCode());
        cases.setProductCode(issue.getProductCode());
        cases.setProductCategory(issue.getProductCategory());
        cases.setEmergency(issue.isEmergency());
        cases.setLiveProcess(issue.isLiveProcess());
        cases.setEnvironment(issue.getEnvironment());
        cases.setEnt(issue.getEnt());
        cases.setSite(issue.getSite());
        cases.setQuestionTitle(issue.getIssueDescription());
        cases.setIssueDescription(issue.getIssueDescription());
        cases.setSubmitedId(issue.getUserId());
        cases.setContractNo(issue.getContractNo());
        cases.setProjectNo(issue.getProjectNo());
        cases.setIssueStatus(issue.getIssueStatus());
        cases.setSubmitWay(issue.getSubmitWay());
        UserContact userContact = issue.getUserContact();
        if(userContact != null){
            cases.setUsername(userContact.getName());
            cases.setPhone(userContact.getPhone01());
            cases.setEmail(userContact.getEmail());
            cases.setUserId(userContact.getUserId());
            cases.setContactId(userContact.getId());
        }

        IssueCasedetail issueCasedetail = issue.getIssueCasedetail();
        if(issueCasedetail != null){
            cases.setIssueClassification(issueCasedetail.getIssueClassification());
            cases.setProgramCode(issueCasedetail.getProgramCode());
            cases.setErpSystemCode(issueCasedetail.getErpSystemCode());
            cases.setEstimatedWorkHours(issueCasedetail.getEstimatedWorkHours());
            cases.setExpectedCompletionDate(issueCasedetail.getExpectedCompletionDate());
            cases.setRequPlanCompletionDate(issueCasedetail.getRequPlanCompletionDate());
            cases.setIssueLevel(StringUtils.isNotBlank(issueCasedetail.getIssueLevel()) ? Integer.valueOf(issueCasedetail.getIssueLevel()) : 0);
            cases.setCoefficientId(issueCasedetail.getCoefficientId());
        }
        return cases;
    }

    /**
     * 分配T的客服
     * @param issue
     * @param productCode
     */
    private String dealTService(Issue issue, String productCode) {
        if(StringUtils.isNotBlank(issue.getServiceId())){
            return issue.getServiceId();
        }
        ServiceStaffCond cond = new ServiceStaffCond();
        IssueCasedetail issueCasedetail = issue.getIssueCasedetail();
        cond.setCustomerServiceCode(issue.getServiceCode());
        cond.setProductCode(issue.getProductCode());
        cond.setErpSystemCode(issueCasedetail.getErpSystemCode());
        if (!StringUtils.isEmpty(issueCasedetail.getErpSystemCode())) {

            Module module = issuesMapper.getModuleByModuleCode(productCode, issueCasedetail.getErpSystemCode().toUpperCase());
            if (!ObjectUtils.isEmpty(module)) {
                cond.setArea(module.getArea());
            }else{
                cond.setArea("");
            }
        }

        List<String> windowStaffs = userServiceFeignClient.getServiceStaff(cond);

        if (!CollectionUtils.isEmpty(windowStaffs))
        {
            //取窗口人员
            String windowWorkno = windowStaffs.get(0);
            //当窗口人员存在的时候，看看有没有设置代理人
            String agentWorkNo = issueProcessMapper.getAgentWorkNo(windowWorkno);

            UserDetailInfo windowUser;
            try{
                windowUser = userServiceFeignClient.getUserDetailByWorkNo(StringUtils.isNotEmpty(agentWorkNo) ? agentWorkNo : windowWorkno);
                if(windowUser != null){
                    return windowUser.getUserId();
                }
            } catch (RuntimeException e) {
                log.error("调用userservice获取问题处理窗口人员信息出错, 请联系管理员." + e.toString());
            }
        }else {
            List<String> list = issueProcessMapper.getDefaultProcess(productCode);
            if(CollectionUtil.isNotEmpty(list)){
                try{
                    UserDetailInfo defaultProcess = userServiceFeignClient.getUserDetailByWorkNo(list.get(0));
                    if(defaultProcess != null){
                        return defaultProcess.getUserId();
                    }
                } catch (RuntimeException e) {
                    log.error("调用userservice获取问题处理窗口人员信息出错, 请联系管理员." + e.toString());
                }
            }
        }
        return "";
    }
//    private void UpdateIssueCrmId(long issueId){
//        String crmId = "ES" + String.format("%010", issueId);
//        issueDao.UpdateIssueCrmId(issueId, crmId);
//    }

    /**
     * 2021-11-23 huly：体验客户提单 都不同步到crm
     *
     * @param serviceCode
     * @param productCode
     * @return
     */
    public boolean needSyncCrm(String serviceCode, String productCode) {

        if (oldIssueDao.checkCrmCustomer(serviceCode, productCode)) {
            return false;
        } else {
            return true;
        }
    }

    @Async
    @Override
    public void SubmitIssueAsync(Issue issue, String issueStatus, boolean closeMailToSubmiter) {
        //20191220 huly:案件提交后，判断是否走审核流程。
        boolean isConfirm = false;
        //易聊不需走这套逻辑
        boolean isSIMIssue = issue.getSubmitWay().startsWith("SIM_") || issue.getSubmitWay().startsWith("EASY_TALK") || IssueSubmitMode.CCInEasyTalk.toString().equals(issue.getSubmitWay());

        if (!isSIMIssue) {
            isConfirm = CustomerIssueConfirmSetting(issue);
        }
        Long issueId = issue.getIssueId();

        //isConfirm=true，走审核
        if (isConfirm) {
            //3.完善案件资料，并通知客服
            CompleteIssueInfo(issueId, issue, issueStatus, isConfirm);
            //4.保存附件
            SaveAttachments(issueId, 0L, issue);

            //增加子单头
            saveCasedetail(issue);
            //5.保存處理進度
            SaveIssueProgress(issue);

            //6.修改案件状态为可同步状态(或已提交状态)
            UpdateSyncIssueStatus(issue, isConfirm);

            /*//易聊提交的案件，不发Mail给客服與客戶
            if (isSIMIssue) {
                return;
            }*/
            //7.发送邮件给客户
            try {
                //20200703電話立案所提交的案件，不發郵件給客戶
                if (!issue.getSubmitWay().equals("CTI_Phone")) {
                    SendMailToSubmiterByConfirm(issue, isConfirm);
                }
            } catch (Exception ex) {
                ex.printStackTrace();
            }

            //7.发送邮件和通知给审核人
            try {
                SendNoticeToConfirmer(issue);
            } catch (Exception ex) {
                ex.printStackTrace();
            }
        } else {

            //3.完善案件资料，并通知客服
            CompleteIssueInfo(issueId, issue, issueStatus, isConfirm);

            //4.保存附件
            SaveAttachments(issueId, 0L, issue);

            //增加子单头
            saveCasedetail(issue);
            //5.保存處理進度
            SaveIssueProgress(issue);

            //6.修改案件状态为可同步状态(或已提交状态)
            UpdateSyncIssueStatus(issue, isConfirm);

            //更新总工时
            issueDetailDao.updateDetailIssueTotalWorkHours(issueId);

            //立案并结案时，需要更新汇总时间
            updateSummaryV3(issue);

            sendMail(issueStatus, issue, isConfirm, closeMailToSubmiter, isSIMIssue);
            //如果是ISV产品线，需要立ISV的案件
            checkISV(issue);
        }
        //更新缓存历史记录，用户级别：客户、产品 必传userId，productCode不用传
        //产品级别：处理人、模组/应用、问题分类 必传productCode，userId不用传
        saveCache(issue);
    }

    private void checkISV(Issue issue){
        try{
            //1.判定是否为ISV产品线
            if(checkISVProductCode(issue.getProductCode()).booleanValue() == Boolean.TRUE && !ObjectUtils.isEmpty(issue.getIssueCasedetail())){
                //2.判断ISV租户是否存在于服务云
//                Long sid = getISVTenantInServiceCloud(issue.getIssueCasedetail().getErpSystemCode());
                String servicerId = "";//ISV租户id
                String servicerName = "";//ISV租户名称
                Long sid = 0L;
                String erpSystemCode = issue.getIssueCasedetail().getErpSystemCode();
                log.info("erpSystemCode:" + erpSystemCode);
                if(StringUtils.isEmpty(erpSystemCode)){
                    saveISVLog(issue.getIssueId(),issue.getUserId(),"erpSystemCode is null");
                    return;
                }
                try{
                    Map<String, Object> itemDetail = gmcService.getItemDetail(erpSystemCode);
                    if (ObjectUtils.isEmpty(itemDetail)) {
                        log.info("getItemDetail is null" + erpSystemCode);
                        saveISVLog(issue.getIssueId(),issue.getUserId(),"getItemDetail is null:" + erpSystemCode);
                        return;
                    }
                    if(!ObjectUtils.isEmpty(itemDetail.get("servicerId"))){
                        servicerId = itemDetail.get("servicerId").toString();
                    }
                    if(!ObjectUtils.isEmpty(itemDetail.get("servicerName"))){
                        servicerName = itemDetail.get("servicerName").toString();
                    }
                }catch (Exception e)
                {
                    saveISVLog(issue.getIssueId(),issue.getUserId(),"checkISV:getItemDetail exception");
                    return;
                }

                if(StringUtils.isEmpty(servicerId)){
                    saveISVLog(issue.getIssueId(),issue.getUserId(),"checkISV:servicerId is null");
                    return;
                }
                sid = issueDao.getSid(servicerId);
                log.info("servicerId:" + servicerId);
                log.info("sid:" + sid);
                if(sid == null){
                    saveISVLog(issue.getIssueId(),issue.getUserId(),"submitToISV:ISV tenant not exist in servicecloud:sid is null");
                    //4.sid不存在于服务云，邮件通知案件的鼎捷负责人
                    sendMailForISVTenantNotInServiceCloud(issue,servicerId,servicerName);
                    return;
                }
                if(sid != 0L){
                    //3.存在于服务云，提交案件到ISV
                    log.info("submitToISV start");
                    String aioIssueCode = submitToISV(issue, sid);
                    log.info("aioIssueCode:" + aioIssueCode);
                    //更新服务云案件
                    updateDigiwinIssue(issue.getIssueId(),aioIssueCode,issue,issue.getUserId());

                }
            }
        }catch (Exception ex) {
            log.info("checkISV:issueId:"+issue.getIssueId());


        }
    }

    public void saveISVLog(long issueId,String userId,String exception){
        issueDao.saveISVLog(issueId, userId, exception);
    }

    /**
     * 鼎捷邮件，单头状态是VN(ISV处理中)，单身操作类型是TurnToISV，记录issue_source_map
     * @param aioIssueCode
     */
    public void updateDigiwinIssue(long issueId,String aioIssueCode,Issue issue,String userId){
        if(StringUtils.isEmpty(aioIssueCode)){
            saveISVLog(issueId,userId,"aioIssueCode is null:" + aioIssueCode);
            return;
        }
        issueDao.updateIssueStatusBySubmit(issueId, IssueStatus.ISVProcessing.toString());
        saveISVIssueProgress(issue);
        saveIssueSourceMap(issueId,issue.getCrmId(),aioIssueCode);
    }
    public void saveIssueSourceMap(long issueId,String crmId,String aioIssueCode) {
        try {
            issueDao.saveIssueSourceMap(issueId, crmId, aioIssueCode, IssueSourceType.ISV.toString(), IssueSourceType.ISV.toString(), "", "");
        } catch (Exception ex) {
            log.info("saveIssueSourceMap error:issueId:"+issueId + " ;aioIssueCode: " + aioIssueCode);
        }
    }
    public void saveISVIssueProgress(Issue issue) {
        try {
            IssueProgress progress = new IssueProgress();
            String date = issue.getSubmitTime();

            progress.setProcessTime(date);
            String syncStatus = getIssueProcessSyncStatusV3(issue.getProductCode(), issue.getServiceCode(), issue.getIssueStatus());
            progress.setSyncStatus(syncStatus);
            progress.setProcessType(IssueProcessType.TurnToISV.toString());
            progress.setReplyType(ReplyType.A.toString());
            progress.setProcessor(issue.getServiceStaff());//操作人是客服,也是立案时匹配到的服务任务
            progress.setSequeceNum(issueDao.getMaxOrder(issue.getIssueId()));
            progress.setCurrentStatus(IssueStatus.ISVProcessing.toString());
            progress.setOpenToSubmit(false);
            if("CN".equals(issue.getServiceRegion())){
                progress.setDescription("转ISV");
            }else {
                progress.setDescription("轉ISV");
            }

            issueDao.InsertIssueProgress(issue.getIssueId(), issue.getCrmId(), progress);
        } catch (Exception ex) {
            log.info("saveISVIssueProgress error:"+ex.toString());
        }
    }

    public String submitToISV(Issue issue,Long sid){
        String aioIssueCode = "";//ISV案件的issueCode
        try {
            String flag = issueDao.getFlag(sid);
            log.info("flag:"+flag);
            String serviceCode = (flag + issue.getServiceCode()).trim();
            log.info("serviceCode:"+serviceCode);
            Long eid = issueDao.getEid(serviceCode,sid);
            log.info("eid:"+eid);
            if(eid == null){
                saveISVLog(issue.getIssueId(),issue.getUserId(),"eid is null");
                return aioIssueCode;
            }
            long userSid = 0L;
            if(sid != null){
                try{
                    String tenantId = issueDao.getTenantIdByServiceCode(sid, serviceCode);
                    if (org.springframework.util.StringUtils.isEmpty(tenantId)) {
                        saveISVLog(issue.getIssueId(),issue.getUserId(),"tenantId not exist");
                        return aioIssueCode;
                    }
                    IamAuthoredUser iamAuthoredUser = kcfService.getIamAuthoredUser(issue.getUserId(),tenantId);
                    userSid = iamAuthoredUser.getSid();
                }catch (Exception e){
                    saveISVLog(issue.getIssueId(),issue.getUserId(),"获取iam用户信息异常");
                    return aioIssueCode;
                }

                com.digiwin.escloud.aioissue.model.Issue aioIssue = new com.digiwin.escloud.aioissue.model.Issue();
                aioIssue.setSid(sid);
                aioIssue.setEid(eid);
                aioIssue.setServiceCode(serviceCode);
                aioIssue.setProductCode(issue.getIssueCasedetail().getErpSystemCode());
                aioIssue.setIssueDescription(issue.getIssueDescription());
                aioIssue.setSubmitWay(SubmitWay.DIGIWIN.toString());
                aioIssue.setIssueSubmitMode(SubmitWay.DIGIWIN.toString());
                aioIssue.setServiceRegion(issue.getServiceRegion());
                aioIssue.setMachineRegion(issue.getMachineRegion());
                aioIssue.setIssueType(IssueType.Issue.toString());
                aioIssue.setUserSid(userSid);
                aioIssue.setWarningItemCollection(Arrays.asList(issue.getCrmId()));
                aioIssue.setSourceType(IssueSourceType.ISV.toString());
                UserContact userContact = issue.getUserContact() ;
                if(userContact != null){
                    com.digiwin.escloud.aiouser.model.usercontact.UserContact aioUserContact = new com.digiwin.escloud.aiouser.model.usercontact.UserContact();
                    aioUserContact.setUserId(userSid);
                    aioUserContact.setEmail(userContact.getEmail());
                    aioUserContact.setName(userContact.getName());
                    aioUserContact.setPhone(userContact.getPhone01());
                    aioUserContact.setServiceCode(serviceCode);
                    aioIssue.setUserContact(aioUserContact);
                }

                log.info("aioIssueClient.submitIssue:" + JSON.toJSONString(aioIssue));
                com.digiwin.escloud.common.response.BaseResponse<String> response = aioIssueClient.submitIssue(JSON.toJSONString(aioIssue),null,sid,eid);
                if(ResponseCode.SUCCESS.getCode().equals(response.getCode())){
                    aioIssueCode = response.getData();
                }else{
                    saveISVLog(issue.getIssueId(),issue.getUserId(),"aioIssueClient.submitIssue 失败");
                }
            }
        }
        catch (Exception ex) {
            log.info("submitToISV error:"+ex.toString());
        }
        return aioIssueCode;
    }

    private Boolean checkISVProductCode(String productCode){
        return issueDao.checkISVProductCode(productCode);
    }

    public Long getISVTenantInServiceCloud(String erpSystemCode){
        Long sid = 0L;
        try{
            String supplierCode = getSupplierCode(erpSystemCode);
            if(StringUtils.isNotBlank(supplierCode)){
                return issueDao.getSid(supplierCode);
            }
        }catch (Exception ex) {
            log.error("getISVTenantInServiceCloud:",ex);
        }
        return sid;

    }

    private String getSupplierCode(String erpSystemCode){
        String supplierCode = "";
        try {
            Map<String, Object> itemDetail = gmcService.getItemDetail(erpSystemCode);
            if (ObjectUtils.isEmpty(itemDetail)) {
                log.error("getItemDetail is null" + erpSystemCode);
                return supplierCode;
            }
            if(!ObjectUtils.isEmpty(itemDetail.get("servicerId"))){
                supplierCode = itemDetail.get("servicerId").toString();
            }
        } catch (Exception ex) {
            log.error("getItemDetail:",ex);
        }
        return supplierCode;
    }

   private void saveCache(Issue issue) {
        //1 用户级别：客户、产品、联系方式 必传userId，productCode不用传
        saveHistorySelect(issue.getUserId(), issue.getServiceCode(), IssueSelectType.CUSTOMER.toString(), "");
//        saveHistorySelect(issue.getUserId(), issue.getProductCode(), IssueSelectType.PRODUCT.toString(), "");
        updateLastUserContact(issue.getUserId(), IssueSelectType.CONTACT.toString(), issue.getUserContact());
        //2.产品级别：处理人、模组/应用、问题分类 必传productCode
        saveHistorySelect(issue.getUserId(), issue.getServiceStaff(), IssueSelectType.HANDLER.toString(), issue.getProductCode());
        saveHistorySelect(issue.getUserId(), issue.getIssueCasedetail().getErpSystemCode(), IssueSelectType.ERPSYSTEMCODE.toString(), issue.getProductCode());
        saveHistorySelect(issue.getUserId(), issue.getIssueCasedetail().getIssueClassification(), IssueSelectType.ISSUECLASSFICATION.toString(), issue.getProductCode());
    }

    /**
     * 立案并结案时，end_min  reply_min 都是0，这里不需要更新入口
     * @param issue
     */
    public void updateSummaryV3(Issue issue) {
        if (IssueStatus.Closed.toString().equals(issue.getIssueStatus())) {
            casesProcessUtils.saveIssueSummaryV3(issue.getUserId(),issue.getIssueId(), IssueProcessType.Close.toString(), issue.getSubmitTime());
            casesProcessUtils.saveIssueSummaryV3(issue.getUserId(),issue.getIssueId(), IssueProcessType.AgreeClose.toString(), issue.getSubmitTime());
        }
    }

    /**
     * 构造禅道bug单子
     *
     * @param issueId
     * @param userId
     * @param issue
     * @return
     */
    public ProductBug bug(long issueId, String crmId, String userId, IssueProgress issueProgress, Issue issue, String customerProductRemarkInfo) {
        ProductBug bug = new ProductBug();
        bug.setBugType("1");
        bug.setServiceSource("CN".equals(connectArea) ? "2" : "1");
        bug.setServiceID(crmId);
        bug.setServiceCreateDate(issue.getSubmitTime());
        bug.setProduct(issueDetailDao.getErpSystemCode(issueId));
        bug.setProductLine(issue.getProductCode());
        bug.setTitle(!StringUtils.isEmpty(issue.getIssueDescription()) && issue.getIssueDescription().length() > 245 ? issue.getIssueDescription().substring(0, 245) : issue.getIssueDescription());
        bug.setSeverity(3);
        bug.setPri(1);
        bug.setRelation(!StringUtils.isEmpty(customerProductRemarkInfo)  ? customerProductRemarkInfo : "");
        if (issue.isEmergency()) {
            bug.setPri(1);
        }
        bug.setSteps(StringUtils.isEmpty(issueProgress.getDescription()) ? issue.getIssueDescription() : issueProgress.getDescription());
        try {
            List<IssueAttachmentFileV3> list = issueDao.getIssueAttachmentsFileV3(String.valueOf(issueId), 0L);
            if (!CollectionUtils.isEmpty(list)) {
                List<ProductFile> files = new ArrayList<>();
                list.stream().forEach(o -> {
                    ProductFile file = new ProductFile();
                    file.setFileId(o.getFileId());
                    file.setFileName(o.getFileName());
                    file.setFileType(o.getFileType());
                    file.setUrl(o.getUrl());

                    files.add(file);
                });

                bug.setFiles(files);
            }
        } catch (Exception e) {
            log.error(e.toString());
        }

        bug.setStatus(ProductStatus.active.toString());
        StaffUserInfo staffUserInfo = userService.GetServiceStaffInfoByUserId(userId);
        if (!ObjectUtils.isEmpty(staffUserInfo)) {
            bug.setCreateBy(staffUserInfo.getFullName() + "(" + staffUserInfo.getItCode() + ")");
        }
        if (!ObjectUtils.isEmpty(issue.getUserContact())) {
            bug.setConnectBy(issue.getUserContact().getName() + " || " + issue.getUserContact().getPhone01() + " || " + issue.getUserContact().getEmail());
        }
        //取得客户名称
        CustomerServiceInfo customerServiceInfo = customerService.GetCustomerServiceInfo(issue.getServiceCode(),issue.getProductCode());
        if (!ObjectUtils.isEmpty(customerServiceInfo)) {
            bug.setCustomerName(issue.getServiceCode()+"-"+customerServiceInfo.getCustomerName());
        }
        return bug;
    }

    /**
     * 构造禅道bug单子
     *
     * @param issueId
     * @param userId
     * @param issue
     * @return
     */
    public ProductStory story(long issueId, String crmId, String userId, IssueProgress issueProgress, Issue issue, String customerProductRemarkInfo) {
        ProductStory story = new ProductStory();
        story.setProduct(issueDetailDao.getErpSystemCode(issueId));
        story.setProductLine(issue.getProductCode());
        story.setSource("CN".equals(connectArea) ? "2" : "1");
        story.setNeedNotReview("N");
        story.setTitle(!StringUtils.isEmpty(issue.getIssueDescription()) && issue.getIssueDescription().length() > 245 ? issue.getIssueDescription().substring(0, 245) : issue.getIssueDescription());
        story.setPri(3);
        story.setRelation(!StringUtils.isEmpty(customerProductRemarkInfo)  ? customerProductRemarkInfo : "");
        if (issue.isEmergency()) {
            story.setPri(1);
        }
        story.setSpec(StringUtils.isEmpty(issueProgress.getDescription()) ? issue.getIssueDescription() : issueProgress.getDescription());
        story.setVerify(issueProgress.getVerify());
        try {
            List<IssueAttachmentFileV3> list = issueDao.getIssueAttachmentsFileV3(String.valueOf(issueId), 0L);
            if (!CollectionUtils.isEmpty(list)) {
                List<ProductFile> files = new ArrayList<>();
                list.stream().forEach(o -> {
                    ProductFile file = new ProductFile();
                    file.setFileId(o.getFileId());
                    file.setFileName(o.getFileName());
                    file.setFileType(o.getFileType());
                    file.setUrl(o.getUrl());

                    files.add(file);
                });

                story.setFiles(files);
            }
        } catch (Exception e) {
            log.error(e.toString());
        }
        story.setServiceSource("CN".equals(connectArea) ? "2" : "1");
        story.setServiceID(crmId);
        story.setServiceCreateDate(issue.getSubmitTime());
        StaffUserInfo staffUserInfo = userService.GetServiceStaffInfoByUserId(userId);
        if (!ObjectUtils.isEmpty(staffUserInfo)) {
            story.setCreateBy(staffUserInfo.getFullName() + "(" + staffUserInfo.getItCode() + ")");
        }
        if (!ObjectUtils.isEmpty(issue.getUserContact())) {
            story.setConnectBy(issue.getUserContact().getName() + " || " + issue.getUserContact().getTelephone() + " || " + issue.getUserContact().getEmail());
        }
        //取得客户名称
        CustomerServiceInfo customerServiceInfo = customerService.GetCustomerServiceInfo(issue.getServiceCode(),issue.getProductCode());
        if (!ObjectUtils.isEmpty(customerServiceInfo)) {
            story.setCustomerName(issue.getServiceCode()+"-"+customerServiceInfo.getCustomerName());
        }
        return story;
    }

    private void sendMail(String issueStatus, Issue issue, boolean isConfirm, boolean closeMailToSubmiter, boolean isSIMIssue) {
        if (IssueStatus.Closed.toString().equals(issueStatus)) {
            //立案并结案 邮件 结案邮件通知提交联系人邮箱（有勾选通知客户）
            if (closeMailToSubmiter) {
                try {
                    SendCloseMailToSubmiter(issue);
                } catch (Exception ex) {
                    ex.printStackTrace();
                }
            }
        } else {
            //立即 立案，提交产中
            if (!isSIMIssue) {
                //7.发送邮件给客户,易聊提交案件不提交给联系人
                try {
                    SendMailToSubmiterByConfirm(issue, isConfirm);
                } catch (Exception ex) {
                    ex.printStackTrace();
                }
            }


            //8.发送邮件和通知给客服
            try {
                SendNoticeToCC(issue);
            } catch (Exception ex) {
                ex.printStackTrace();
            }
        }
    }


    @Override
    public String getCrmIdByIssueId(long issueId) {
        return issueDao.getCrmIdByIssueId(issueId);
    }

    @Override
    public String getIssueIdByCrmId(String crmId) {
        long issueId = issueDao.getIssueIdByCrmId(crmId);
        return String.format("%d", issueId);
    }

    @Override
    public String getProductId(long issueId, String sourceType, String productIssueClassification) {
        return issueDao.getProductId(issueId, sourceType, productIssueClassification);
    }

    /**
     * 更新单头同步状态
     *
     * @param issue
     * @param isConfirm
     */
    private void UpdateSyncIssueStatus(Issue issue, boolean isConfirm) {
        //2021-11-23 huly：访客和体验客户提单 都不同步到crm
        String syncStatus = SyncStatus.DontNeedSync.toString();
        if (!visitorServiceCode.equals(issue.getServiceCode())) {
            if (issue.isTrial() || CheckContractIsTrial(issue.getServiceCode(), issue.getProductCode())) {
                syncStatus = SyncStatus.Trial.toString();
            } else if (isConfirm) {
                syncStatus = SyncStatus.DontNeedSync.toString();
            } else if("163".equals(issue.getProductCode()) || "15".equals(issue.getProductCode())) {
                CustomerServiceInfo customerServiceInfo = new CustomerServiceInfo();
                customerServiceInfo = customerService.GetCustomerServiceInfo(issue.getServiceCode(), issue.getProductCode());
                if (customerServiceInfo != null) {
                    if(customerServiceInfo.isTrial() && "3".equals(customerServiceInfo.getContractSource())){
                        syncStatus = SyncStatus.DontNeedSync.toString();
                    } else {
                        syncStatus = SyncStatus.UnSync.toString();
                    }
                } else {
                    syncStatus = SyncStatus.DontNeedSync.toString();
                }
            } else {
                IssueSwitch issueSwitch = issueSwitchCache.selectIssueSwitch(issue.getProductCode());

                if (issueSwitch != null && !StringUtils.isEmpty(issueSwitch.getSyncCrmMoment())) {
                    //有设置开关的情况下，在判断是否是体验客户，是体验客户，直接不同步，是crm客户，在判断开会里面的同步时机
                    if (oldIssueDao.checkCrmCustomer(issue.getServiceCode(), issue.getProductCode())) {
                        if (issueSwitch != null && !StringUtils.isEmpty(issueSwitch.getSyncCrmMoment())) {
                            if (SyncMoment.Submit.toString().equals(issueSwitch.getSyncCrmMoment())) {
                                syncStatus = SyncStatus.UnSync.toString();
                            } else if (SyncMoment.Close.toString().equals(issueSwitch.getSyncCrmMoment())) {
                                syncStatus = SyncStatus.DontNeedSync.toString();
                            }
                        }
                    }

                }
            }

        }

        issue.setSyncStatus(syncStatus);
        issueDao.UpdateSyncStatusByIssueId(issue.getIssueId(), syncStatus);
    }

    private boolean CheckContractIsTrial(String ServiceCode, String ProductCode) {
        //檢查該產品線是否為試用
        CustomerServiceInfo customerServiceInfo = new CustomerServiceInfo();
        //获取客户的维护合约信息
        customerServiceInfo = customerService.GetCustomerServiceInfo(ServiceCode, ProductCode);
        if (customerServiceInfo != null) {
            if (customerServiceInfo.isTrial()) {
                return true;
            }
        }
        return false;
    }


    @Async
    public void SendFollowUpMail(Issue issue, String desc) {
        //发送邮件给客户
        /*try {
            SendFollowUpMailToSubmiter(issue,desc);
        } catch (Exception ex) {
            ex.printStackTrace();
        }*/

        //发送邮件和通知给客服
        try {
            SendFollowUpNoticeToCC(issue, desc);
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    private void SendFollowUpMailToSubmiter(Issue issue, String desc) {
        try {
            String[] mx = {PrepareIssueFollowUpMail(issue, desc)};
            simpleSend(MessageDestination.MAILMESSAGEDESTINATION, mx);
        } catch (Exception ex) {
            ex.printStackTrace();
            log.error(ex);
        }
    }

    private void SendFollowUpNoticeToCC(Issue issue, String desc) {
        try {
            String[] mx = {PrepareIssueFollowUpMailToCC(issue, desc)};
            simpleSend(MessageDestination.MAILMESSAGEDESTINATION, mx);
            List<WebsocketMessageModel> models = new ArrayList<WebsocketMessageModel>();
            models.add(new WebsocketMessageModel("/iqchange/" + issue.getServiceStaff(),
                    (issue.getServiceCode() == null ? "无客服代号" : issue.getServiceCode())
                            + "     " +
                            (issue.getIssueDescription() == null ? "无案件描述" : (issue.getIssueDescription().length() < 20 ? issue.getIssueDescription() : issue.getIssueDescription().substring(0, 19)))));
            Gson gson = new Gson();
            String[] wx = {gson.toJson(models)};
            simpleSend(MessageDestination.WEBSOCKETMESSAGEDESTINATION, wx);
        } catch (Exception ex) {
            ex.printStackTrace();
            log.error(ex);
        }
    }

    //    private List<String> GetAdditionalExplanationReceivers() {
//
//    }
    //保存call-center之資訊
    private void SaveIssueAdditionalExplanationDetail(Issue issue) {
        try {
            if (issue.getIssueId() != 0 && issue.getIssueDescription() != null) {
                issueDetailDao.insertAdditionalExplanationDetailIssue(issue);
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    private void saveCasedetailNew(Issue issue, boolean isSIMIssue) {
        try {
            IssueCasedetail issueCasedetail = issue.getIssueCasedetail();
            if (issueCasedetail != null) {
                issueCasedetail.setIssueId(Long.toString(issue.getIssueId()));
                /*//易聊提交的案件，需要有单身，并且必须填写问题分类，因此issue_casedetail的syncstatus必须等于T
                if (isSIMIssue) {
                    issueCasedetail.setSyncStatus("T");
                } else {
                    issueCasedetail.setSyncStatus("Z");
                }*/
                //2010-1-15 huly跟偉勝沟通后确认只存T
                issueCasedetail.setSyncStatus("Z");
                issueDetailDao.insertDetailIssue(issueCasedetail);
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    private void saveCasedetail(Issue issue) {
        try {
            IssueCasedetail issueCasedetail = issue.getIssueCasedetail();
            if (issueCasedetail != null) {
                issueCasedetail.setIssueId(Long.toString(issue.getIssueId()));
                /*//易聊提交的案件，需要有单身，并且必须填写问题分类，因此issue_casedetail的syncstatus必须等于T
                if (isSIMIssue) {
                    issueCasedetail.setSyncStatus("T");
                } else {
                    issueCasedetail.setSyncStatus("Z");
                }*/
                //2010-1-15 huly跟偉勝沟通后确认只存T
                issueCasedetail.setSyncStatus(getIssueProcessSyncStatusV3(issue.getProductCode(), issue.getServiceCode(), issue.getIssueStatus()));
                issueDetailDao.insertDetailIssue(issueCasedetail);
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    /**
     * 判断子单头、单身的同步状态
     *
     * @param productCode
     * @param serviceCode
     * @param currentIssueStatus
     * @return
     */
    public String getIssueProcessSyncStatusV3(String productCode, String serviceCode, String currentIssueStatus) {
        String syncStatus = SyncStatus.DontNeedSync.toString();//默认
        //1 如果是访客，直接不同步
        if (visitorServiceCode.equals(serviceCode)) {
            return syncStatus;
        } else {
            IssueSwitch issueSwitch = issueSwitchCache.selectIssueSwitch(productCode);

            if (issueSwitch != null && !StringUtils.isEmpty(issueSwitch.getSyncCrmMoment())) {
                //有设置开关的情况下，在判断是否是体验客户，是体验客户，直接不同步，是crm客户，在判断开会里面的同步时机
                if (oldIssueDao.checkCrmCustomer(serviceCode, productCode)) {
                    if (SyncMoment.Submit.toString().equals(issueSwitch.getSyncCrmMoment())) { //提单同步，比如大陆E10
                        syncStatus = SyncStatus.EditUnSync.toString();
                    } else if (SyncMoment.Close.toString().equals(issueSwitch.getSyncCrmMoment())) { //结案（交付结案）时同步,比如东南亚T
                        if (IssueStatus.Closed.toString().equals(currentIssueStatus)) {
                            syncStatus = SyncStatus.EditUnSync.toString();
                        } else {
                            syncStatus = SyncStatus.DontNeedSync.toString();
                        }
                    }
                }
            }
        }

        return syncStatus;
    }

    private void SaveIssueProgress(Issue issue) {
        try {
            List<IssueProgress> issueProgresses = issue.getIssueProgresses();
            if (issueProgresses == null || issueProgresses.size() <= 0) {
                return;
            }
            String date = StringUtils.isNotBlank(issue.getSubmitTime())?issue.getSubmitTime() : DateUtils.getCurrentTime();
            //2021-11-23 huly：访客和体验客户提单 都不同步到crm

            String syncStatus = getIssueProcessSyncStatusV3(issue.getProductCode(), issue.getServiceCode(), issue.getIssueStatus());

            for (IssueProgress progress : issueProgresses) {
                progress.setProcessTime(date);
                progress.setSyncStatus(syncStatus);
                //2021-12-30 易聊聊天立案 如果是立案并结案 台湾的结案单身同步状态为空 大陆不变
                if ("TW".equals(connectArea)) {
                    if (IssueProcessType.Close.toString().equals(progress.getProcessType())) {
                        progress.setSyncStatus("");
                    }
                }
                if("CN".equals(issue.getServiceRegion())){
                    if("Accept Issue".equals(progress.getDescription())){
                        progress.setDescription("问题受理中");
                        progress.setCurrentStatus(IssueStatus.Submited.toString());
                    }else if("Resolved Issue".equals(progress.getDescription())){
                        progress.setDescription("问题已解决");
                    }else if("Close Issue".equals(progress.getDescription())){
                        progress.setDescription("结案");
                        progress.setCurrentStatus(IssueStatus.Closed.toString());
                    }else if("Cancel Close Issue".equals(progress.getDescription())){
                        progress.setDescription("取消结案");
                        progress.setCurrentStatus(IssueStatus.Processing.toString());
                    }else {

                    }
                }
                issueDao.InsertIssueProgress(issue.getIssueId(), issue.getCrmId(), progress);
                //新版易聊来的T的案件 需要同步
                if("CN".equals(connectArea) && Constants.getTProductCodes().contains(issue.getProductCode())){
                    fg187Mq.send(issue.getIssueId(), progress.getSequeceNum());
                    Runnable runnable = () -> casesProcessUtils.sendToWorkday(issue.getIssueId(),progress.getSequeceNum(),0);
                    ExecutorService executorService = Executors.newSingleThreadExecutor();
                    try {
                        executorService.execute(runnable);
                    } catch (Exception ex) {
                        log.error("sendToWorkday", ex);
                    } finally {
                        executorService.shutdown();
                    }
                }

            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    /**
     * 发送邮件给客服人员 抄送给其他客服、客户、CC
     *
     * @param issue
     */
    private void SendMailToCustomerService_T(Issue issue) {
        try {
            String[] mx = {PrepareIssueMailToCustomerService_T(issue)};
            simpleSend(MessageDestination.MAILMESSAGEDESTINATION, mx);
            List<WebsocketMessageModel> models = new ArrayList<WebsocketMessageModel>();
            models.add(new WebsocketMessageModel("/iqchange/" + issue.getServiceStaff(),
                    (issue.getServiceCode() == null ? "无客服代号" : issue.getServiceCode())
                            + "     " +
                            (issue.getIssueDescription() == null ? "无案件描述" : (issue.getIssueDescription().length() < 20 ? issue.getIssueDescription() : issue.getIssueDescription().substring(0, 19)))));
            Gson gson = new Gson();
            String[] wx = {gson.toJson(models)};
            simpleSend(MessageDestination.WEBSOCKETMESSAGEDESTINATION, wx);
        } catch (Exception ex) {
            ex.printStackTrace();
            log.error(ex);
        }
    }

    public void SendCloseMailToSubmiter(Issue issue) {
        try {
            String[] mx = {PrepareCloseMailToSubmiter(issue)};
            simpleSend(MessageDestination.MAILMESSAGEDESTINATION, mx);
        } catch (Exception ex) {
            ex.printStackTrace();
            log.error(ex);
        }
    }

    public void SendCloseMailToSubmiterCheck(Issue issue) {
        try {
            String[] mx = {PrepareCloseMailToSubmiterCheck(issue)};
            simpleSend(MessageDestination.MAILMESSAGEDESTINATION, mx);
        } catch (Exception ex) {
            ex.printStackTrace();
            log.error(ex);
        }
    }

    public void SendAutoCloseMailToSubmiter(CasesEmail casesEmail, List<String> receiver, List<String> ccMailList, String language) {
        try {
            String[] mx = {PrepareAutoCloseMailToSubmiter(casesEmail, receiver, ccMailList, language)};
            simpleSend(MessageDestination.MAILMESSAGEDESTINATION, mx);
        } catch (Exception ex) {
            ex.printStackTrace();
            log.error(ex);
        }
    }

    /**
     * 发送邮件给案件提交人
     *
     * @param issue
     */
    //@Async
    private void SendMailToSubmiterByConfirm(Issue issue, boolean isConfirm) {
        try {
            String[] mx = {PrepareIssueMailByConfirm(issue, isConfirm)};
            simpleSend(MessageDestination.MAILMESSAGEDESTINATION, mx);
        } catch (Exception ex) {
            ex.printStackTrace();
            log.error(ex);
        }
    }

    /**
     * 发送退回邮件给案件提交人
     *
     * @param issue
     */
    //@Async
    private void SendBackMailToSubmiter(Issue issue) {
        try {
            String[] mx = {PrepareIssueBackMail(issue)};
            simpleSend(MessageDestination.MAILMESSAGEDESTINATION, mx);
        } catch (Exception ex) {
            ex.printStackTrace();
            log.error(ex);
        }
    }

    /**
     * 发送邮件给案件提交人
     *
     * @param issue
     */
    //@Async
    private void SendMailToSubmiter(Issue issue) {
        try {
            String[] mx = {PrepareIssueMail(issue)};
            simpleSend(MessageDestination.MAILMESSAGEDESTINATION, mx);
        } catch (Exception ex) {
            ex.printStackTrace();
            log.error(ex);
        }
    }

    /**
     * for 越南T 大陆T的案件使用
     *
     * @param issueId
     * @param issue
     */
    //@Async
    private void CompleteIssueInfoNew(long issueId, Issue issue, String issueStatus, boolean isConfirm) {
        try {
            if (isConfirm) {
                issue.setIssueStatus(IssueStatus.Confirming.toString());
            } else {
                if (org.apache.commons.lang.StringUtils.isNotBlank(issueStatus) && issue.getSubmitWay().startsWith("SIM_")) {
                    issue.setIssueStatus(issueStatus);//已結案
                } else {
                    issue.setIssueStatus(IssueStatus.Submited.toString());
                }

            }

//            } else if (issue.getIssueType().equals(IssueType.UpdateRequest.toString())){
//                List<String> userRole=userService.GetUserRole(issue.getUserId());
//                if (userRole.contains(UserRole.mis.toString()))
//                    issue.setIssueStatus(IssueStatus.Submited.toString());
//                else
//                    issue.setIssueStatus(IssueStatus.Confirming.toString());
//            }
            //3.2 获取案件提交人联系方式Id
            long userContactId = userDao.GetUserContractId(issue.getUserContact());
            //目前不直接掉用其他微服务来取得，因为逻辑相对简单，跨微服务调用容易失败
//            long userContactId = -1;
//            //嘗試3次
//            int tryCount = 3;
//            do {
//                System.out.print(issueId + " try get user contact id try count:" + tryCount);
//                try {
//                    userContactId = userService.GetUserContactId(issue.getUserContact());
//                } catch (Exception ex) {
//                    ex.printStackTrace();
//                }
//                tryCount--;
//            } while (userContactId < 0 && tryCount > 0);
            if (userContactId < 0) {
                System.out.print(issueId + " get user contact id fail!");
            }

            //3.2 更新客服信息
            if (!isConfirm) {
                if (issue.getServiceStaff() == null || issue.getServiceStaff().isEmpty()) {
                    try {
                        String area = "";
                        if (!StringUtils.isEmpty(issue.getIssueCasedetail().getErpSystemCode())) {
                            Module module = issueProcessMapper.getModuleByModuleCode(issue.getProductCode(), issue.getIssueCasedetail().getErpSystemCode());
                            if (!ObjectUtils.isEmpty(module)) {
                                area = module.getArea();
                            }
                        }

                        List<String> list = userService.GetServiceStaffInfoForT(
                                issue.getServiceCode(), issue.getProductCode() == null ? "" : issue.getProductCode()
                                , issue.getIssueCasedetail().getErpSystemCode(), area
                        );
                        if (!CollectionUtils.isEmpty(list)) {
                            String workno = list.get(0);
                            StaffUserInfo staffUserInfo = userService.GetServiceStaffInfoByWorkNo(workno);
                            if (staffUserInfo != null) {
                                issue.setServiceStaff(staffUserInfo.getUserId());
                                issue.setDepartment(staffUserInfo.getDepartment());

                            }
                            //得到群抄送人mail
                            String worknos = "";
                            for (String temp : list) {
                                if (temp.equals(workno)) {
                                    continue;
                                }
                                worknos += temp + ",";
                            }
                            if (!StringUtils.isEmpty(worknos)) {
                                List<String> cc = userService.GetUserEmailList(worknos.substring(0, worknos.length() - 1));
                                if (!CollectionUtils.isEmpty(cc)) {
                                    String ccmail = "";
                                    for (String temp : cc) {

                                        ccmail += temp + ";";
                                    }
                                    issue.setCcGroupUser(ccmail.substring(0, ccmail.length() - 1));
                                }
                            }

                        }
                        /*System.out.print(serviceStaffGetResponse);
                        if (serviceStaffGetResponse != null && serviceStaffGetResponse.getCode().equals("0")) {
                            staffUserInfo = serviceStaffGetResponse.getStaffUserInfo();
                            //staffUserInfo = userService.GetServiceStaffInfo(issue.getServiceCode(), issue.getProductCode() == null ? "" : issue.getProductCode());
                            //设置客户群抄收人
                            issue.setCcGroupUser(serviceStaffGetResponse.getMailCC());
                            if (staffUserInfo != null) {
                                issue.setServiceStaff(staffUserInfo.getUserId());
                                issue.setDepartment(staffUserInfo.getDepartment());
                                //重新设置客户群抄收人
                                if("100".equals(issue.getProductCode()) || "06".equals(issue.getProductCode())){
                                    issue.setCcGroupUser(staffUserInfo.getService_cc_staff_emails());
                                }
                            }


                            //设置是否没有责任人员
                            issue.setNoResponsiblePersion(serviceStaffGetResponse.isNoResponsiblePerson());
                        }*/
                    } catch (Exception ex) {
                        ex.printStackTrace();
                    }
                }
            }

            //3.3 更新产品版本
            try {
                if (issue.getProductVersion() == null || issue.getProductVersion().isEmpty()) {
                    issueDao.UpdateIssueProductVersion(issue);
                }
            } catch (Exception ex) {
                ex.printStackTrace();
            }

//            if (staffUserInfo.getUserId().isEmpty()){
//
//            }else {
            //组织crmId
            String crmId;
            String warningId = Optional.ofNullable(issue.getWarningId()).orElse("");
            String sourceType = Optional.ofNullable(issue.getSourceType()).orElse("");
            if (issue.getSubmitWay().startsWith("SIM_")) {
                crmId = "ESM" + String.format("%09d", issueId);
            }else {

                if (warningId.length() > 0) {
                    if (issue.getSubmitWay().equals(IssueSubmitMode.ITMS.toString())) {
                        //科維预警立案(客户)
                        crmId = IssueCodeRoleStart.ESC.toString() + String.format("%09d", issueId);
                    }else if (issue.getSubmitWay().equals(IssueSubmitMode.ITMS_Service.toString())) {
                        //科維预警立案(客服)
                        crmId = IssueCodeRoleStart.ESD.toString() + String.format("%09d", issueId);
                    }else{
                        //科維预警立案(客户)
                        crmId = IssueCodeRoleStart.ESC.toString() + String.format("%09d", issueId);
                    }

                }else if("EDREvent".equals(sourceType)){
                    if (issue.getSubmitWay().startsWith(IssueSubmitMode.AIOSSM.toString())) {
                        //企業運維立案(客服)
                        crmId = IssueCodeRoleStart.ESD.toString() + String.format("%09d", issueId);
                    } else if (issue.getSubmitWay().equals(IssueSubmitMode.AIEOM.toString())) {
                        //企業運維立案(客戶)
                        crmId = IssueCodeRoleStart.ESC.toString() + String.format("%09d", issueId);
                    } else{
                        //科維预警立案(客户)
                        crmId = IssueCodeRoleStart.ESC.toString() + String.format("%09d", issueId);
                    }
                }
                else {
                    if (issue.getIssueSubmitMode() != null) {
                        if (issue.getIssueSubmitMode().equals(IssueSubmitMode.CallMe.toString()))
                            crmId = "MSG" + String.format("%09d", issueId);
                        else if (issue.getIssueSubmitMode().equals(IssueSubmitMode.CouponUse.toString()))
                            crmId = "CON" + String.format("%09d", issueId);
                        else
                            crmId = "ES" + String.format("%010d", issueId);
                    } else
                        crmId = "ES" + String.format("%010d", issueId);
                }

            }
            issue.setCrmId(crmId);
            issueDao.UpdateIssue(issueId, crmId, issue.getServiceStaff(), issue.getDepartment(), issue.getIssueStatus(), userContactId);
            //不在这里调整案件同步状态，避免案件还未完善就被同步

            //檢查是否為EDREvent
            String EDREventIssue = issueDao.checkIssueIsEdrEvent(issue.getCrmId());
            if(!StringUtils.isEmpty(EDREventIssue)){
                this.updateEventNoticeStatus(issue);
            }else {
                //如果有預警編號,  則更新預警編號的狀態
                this.updateWarningNoticeStatus(issue);
            }
            System.out.print("update EsClientService Warning Notice status:" + issueId);

            //SendNoticeToCC(issue);
            if (isConfirm) {
                // 查询审核人
                String confirmIds = GetCustomerIssueConfirmer(issue);
                SaveIssueCreateLog(issueId, issue.getCrmId(), issue.getProductCode(), issue.getCrmId(), issue.getSubmitTime(), confirmIds, issue.getIssueStatus(), issue.getServiceRegion());
            } else {
                SaveIssueCreateLog(issueId, issue.getCrmId(), issue.getProductCode(), issue.getCrmId(), issue.getSubmitTime(), issue.getServiceStaff(), issue.getIssueStatus(), issue.getServiceRegion());
            }
//            }
        } catch (Exception ex) {
            ex.printStackTrace();
            log.error(ex);
        }
    }

    /**
     * 完善案件资料，并通知客服
     *
     * @param issueId
     * @param issue
     */
    //@Async
    private void CompleteIssueInfo(long issueId, Issue issue, String issueStatus, boolean isConfirm) {
        try {
            //2021-09-15 huly 状态模式
            if (!StringUtils.isEmpty(issueStatus)) {
                issue.setIssueStatus(issueStatus);
            } else {
                IssueContext context = new IssueContext(issue.getProductCode(), issue.getServiceCode());
                new Incomplete().completeIssueInfo(context);
                issue.setIssueStatus(context.getIssueStatus());
            }

            //3.2 获取案件提交人联系方式Id
            long userContactId = 0L;
            if (issue.getUserContact() != null && issue.getUserContact().getId() == 0) {
                userContactId = userDao.GetUserContractId(issue.getUserContact());
                if (userContactId < 0) {
                    System.out.print(issueId + " get user contact id fail!");
                }
            } else {
                userContactId = issue.getUserContact().getId();
            }
            //3.2 20250217调整至此 原因分配客服的时候 需要传模组参数：如果是雅典娜来的案件，传过来的模组不一定是CRM的模组，需要根据mars_product.erpSystemCodeSource的配置来决定找到CRM对应的模组
            if(IssueSubmitMode.ATHENA.toString().equals(issue.getIssueSubmitMode()) && ObjectUtil.isNotNull(issue.getIssueCasedetail()) && StringUtils.isNotEmpty(issue.getIssueCasedetail().getErpSystemCode())){
                String dapSystemCode = issue.getIssueCasedetail().getErpSystemCode();
                String productCode = issue.getProductCode();
                int erpSystemCodeSource = issueDao.getERPSystemCodeSource(productCode);
                if(erpSystemCodeSource == 1){
                    String tempDapSystemCode = issueDao.getERPSystemCodeByDapCode(productCode,dapSystemCode);
                    issue.getIssueCasedetail().setErpSystemCode(StringUtils.isEmpty(tempDapSystemCode) ? issue.getIssueCasedetail().getErpSystemCode() : tempDapSystemCode);
                }
            }
            //3.3 更新客服信息
            //易聊客服立案会传处理人
            if (StringUtils.isEmpty(issue.getServiceId())) {
                if (!isConfirm) {
                    ServiceStaffGetResponse serviceStaffGetResponse = null;
                    if (issue.getServiceStaff() == null || issue.getServiceStaff().isEmpty()) {
                        try {
                            StaffUserInfo staffUserInfo = new StaffUserInfo();

                            String productCode = issue.getProductCode() == null ? "" : issue.getProductCode();

                            //3567 朱昭熹:142產品線案件分派邏輯調整，修改完成后，hotfix立即发版
                            boolean hasTipTopProductCode = Boolean.FALSE;
                            if (connectArea.equals("TW")) {
                                if ("142".equals(issue.getProductCode())) {
                                    //檢查有沒有買過 06 或 100 的產品線, 如果有, 則用 06 去查找
                                    boolean has06ProductCode = issueProcessMapper.getTipTopProductCode(issue.getServiceCode(), "06") > 0;
                                    boolean has100ProductCode = issueProcessMapper.getTipTopProductCode(issue.getServiceCode(), "100") > 0;

                                    //1059 朱昭熹:142產品線案件分派邏輯調整--20210526 add 調整規則
                                    if (has06ProductCode && has100ProductCode) {
                                        //兩個都有, 就用 100
                                        productCode = "100";
                                        hasTipTopProductCode = Boolean.TRUE;
                                    } else if (has06ProductCode) {
                                        //一個有一個沒有(合約等級不為 G), 就用那個產品線
                                        productCode = "06";
                                        hasTipTopProductCode = Boolean.TRUE;
                                    } else if (has100ProductCode) {
                                        //一個有一個沒有(合約等級不為 G), 就用那個產品線
                                        productCode = "100";
                                        hasTipTopProductCode = Boolean.TRUE;
                                    } else {
                                        //兩個都為 G 級, 就用原本的 142
                                        productCode = "142";
                                    }
                                }
                            }
                            String moduleCode = (issue.getIssueCasedetail() != null && !StringUtils.isEmpty(issue.getIssueCasedetail().getErpSystemCode())) ? issue.getIssueCasedetail().getErpSystemCode() : "";
                            serviceStaffGetResponse = userService.GetServiceStaffInfo(
                                    issue.getUserId(), issue.getServiceCode(), productCode
                                    , issue.getServiceRegion() == null ? "" : issue.getServiceRegion(), issue.getIssueDescription(), issue.getIssueClassificationNo() == null ? "" : issue.getIssueClassificationNo()
                                    , issue.getSubmitWay() == null ? "" : issue.getSubmitWay(), moduleCode,issue.getUserId() == null?"":issue.getUserId()
                            );

                            System.out.print(serviceStaffGetResponse);
                            if (serviceStaffGetResponse != null && serviceStaffGetResponse.getCode().equals("0")) {
                                staffUserInfo = serviceStaffGetResponse.getStaffUserInfo();
                                //staffUserInfo = userService.GetServiceStaffInfo(issue.getServiceCode(), issue.getProductCode() == null ? "" : issue.getProductCode());
                                //设置客户群抄收人
                                issue.setCcGroupUser(serviceStaffGetResponse.getMailCC());
                                if (staffUserInfo != null) {
                                    issue.setServiceStaff(staffUserInfo.getUserId());
                                    issue.setDepartment(staffUserInfo.getDepartment());

                                    //重新设置客户群抄收人
                                    if ("100".equals(issue.getProductCode()) || "06".equals(issue.getProductCode()) || hasTipTopProductCode) {
                                        issue.setCcGroupUser(staffUserInfo.getService_cc_staff_emails());
                                    }
                                }
                                //设置是否没有责任人员
                                issue.setNoResponsiblePersion(serviceStaffGetResponse.isNoResponsiblePerson());
                            }

                        } catch (Exception ex) {
                            ex.printStackTrace();
                        }
                    }
                }
            } else {

                StaffUserInfo staffUserInfo = userService.GetServiceStaffInfoByUserId(issue.getServiceId());
                issue.setServiceStaff(issue.getServiceId());
                issue.setDepartment(staffUserInfo.getDepartment());
            }

            //如果处理人是自己，直接状态变更为处理中
            if (!IssueStatus.Closed.toString().equals(issue.getIssueStatus()) && !StringUtils.isEmpty(issue.getServiceStaff()) && !StringUtils.isEmpty(issue.getUserId())) {
                if (issue.getServiceStaff().equals(issue.getUserId())) {
                    issue.setIssueStatus(IssueStatus.Processing.toString());
                }
            }
            //3.3 更新产品版本
            try {
                if (issue.getProductVersion() == null || issue.getProductVersion().isEmpty()) {
                    issueDao.UpdateIssueProductVersion(issue);
                }
            } catch (Exception ex) {
                ex.printStackTrace();
            }
            String crmId = genarateCrmId(issueId, issue);

            issue.setCrmId(crmId);
            issueDao.UpdateIssue(issueId, crmId, issue.getServiceStaff(), issue.getDepartment(), issue.getIssueStatus(), userContactId);
            //不在这里调整案件同步状态，避免案件还未完善就被同步

            //如果有預警編號,  則更新預警編號的狀態
            if (!IssueSubmitMode.ATHENA.toString().equals(issue.getIssueSubmitMode())) {
                this.updateWarningNoticeStatus(issue);
            }
            issueDao.updateIssueSourceMap(issue.getIssueId(), issue.getCrmId());
            System.out.print("update EsClientService Warning Notice status:" + issueId);

            if (isConfirm) {
                // 查询审核人
                String confirmIds = GetCustomerIssueConfirmer(issue);
                SaveIssueCreateLog(issueId, issue.getCrmId(), issue.getProductCode(), issue.getUserId(), issue.getSubmitTime(), confirmIds, issue.getIssueStatus(), issue.getServiceRegion());
            } else {
                SaveIssueCreateLog(issueId, issue.getCrmId(), issue.getProductCode(), issue.getUserId(), issue.getSubmitTime(), issue.getServiceStaff(), issue.getIssueStatus(), issue.getServiceRegion());
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            log.error(ex);
        }
    }

    private String genarateCrmId(long issueId, Issue issue) {
        //组织crmId
        String crmId;
        if (!StringUtils.isEmpty(issue.getSubmitWay())) {
            if (issue.getSubmitWay().startsWith("SIM_")) {
                crmId = IssueCodeRoleStart.ESM.toString() + String.format("%09d", issueId);
            }
            else {
                String warningId = Optional.ofNullable(issue.getWarningId()).orElse("");
                if (warningId.length() > 0) {
                     if (issue.getSubmitWay().equals(IssueSubmitMode.ITMS.toString())) {
                         //科維预警立案(客户)
                         crmId = IssueCodeRoleStart.ESC.toString() + String.format("%09d", issueId);
                     } else if (issue.getSubmitWay().equals(IssueSubmitMode.ITMS_Service.toString())) {
                         //科維预警立案(客服)
                         crmId = IssueCodeRoleStart.ESD.toString() + String.format("%09d", issueId);
                     } else if (issue.getSubmitWay().startsWith(IssueSubmitMode.AIOSSM.toString())) {
                         //\企業運維立案(客服)
                         crmId = IssueCodeRoleStart.ESD.toString() + String.format("%09d", issueId);
                     }else if (issue.getSubmitWay().equals(IssueSubmitMode.AIEOM.toString())) {
                         //\企業運維立案(客戶)
                         crmId = IssueCodeRoleStart.ESC.toString() + String.format("%09d", issueId);
                     } else {
                         //科維预警立案(客户)
                         crmId = IssueCodeRoleStart.ESC.toString() + String.format("%09d", issueId);
                     }
                } else {
                    if (issue.getSubmitWay().startsWith("EASY_TALK"))
                        crmId = IssueCodeRoleStart.ESV.toString() + String.format("%09d", issueId);
                    else if (issue.getSubmitWay().startsWith("CTI_Phone"))
                        crmId = IssueCodeRoleStart.EST.toString() + String.format("%09d", issueId);
                    else if (issue.getSubmitWay().equals(IssueSubmitMode.CallMe.toString()))
                        crmId = IssueCodeRoleStart.MSG.toString() + String.format("%09d", issueId);
                    else if (issue.getSubmitWay().equals(IssueSubmitMode.CouponUse.toString()))
                        crmId = IssueCodeRoleStart.CON.toString() + String.format("%09d", issueId);
                    else if (issue.getSubmitWay().equals(IssueSubmitMode.Chat.toString()))
                        crmId = IssueCodeRoleStart.EM.toString() + String.format("%010d", issueId);
                    else if (issue.getSubmitWay().equals(IssueSubmitMode.CCInEasyTalk.toString()))
                        crmId = IssueCodeRoleStart.EMC.toString() + String.format("%09d", issueId);
                    /*else if (issue.getSubmitWay().equals(IssueSubmitMode.AIOSSM.toString()))
                        crmId = IssueCodeRoleStart.EMC.toString() + String.format("%09d", issueId);
                    else if (issue.getSubmitWay().equals(IssueSubmitMode.AIEOM.toString()))
                        crmId = IssueCodeRoleStart.EMC.toString() + String.format("%09d", issueId);*/
                    else if (issue.getSubmitWay().equals(IssueSubmitMode.ATHENA.toString()))
                        crmId = IssueCodeRoleStart.EA.toString() + String.format("%010d", issueId);
                    else
                        crmId = "ES" + String.format("%010d", issueId);
                }
            }


        } else
            crmId = "ES" + String.format("%010d", issueId);
        return crmId;
    }

    @Override
    public void UpdateSyncStatusByIssueId(long issueId, String syncStatus) {
        issueDao.UpdateSyncStatusByIssueId(issueId, syncStatus);
    }

    public void UpdateProcessSyncStatusByIssueId(long issueId, String syncStatus) {
        issueDao.UpdateProcessSyncStatusByIssueId(issueId, syncStatus);
    }

    /**
     * 发送邮件和通知给审核人
     *
     * @param issue
     */
    private void SendNoticeToConfirmer(Issue issue) {
        try {
            String confirmIds = GetCustomerIssueConfirmer(issue);

            if (StringUtils.isEmpty(confirmIds)) {
                return;
            }
            String[] mx = {PrepareIssueMailToConfirmer(issue, confirmIds)};
            simpleSend(MessageDestination.MAILMESSAGEDESTINATION, mx);
            List<WebsocketMessageModel> models = new ArrayList<WebsocketMessageModel>();
            models.add(new WebsocketMessageModel("/iqchange/" + issue.getServiceStaff(),
                    (issue.getServiceCode() == null ? "无客服代号" : issue.getServiceCode())
                            + "     " +
                            (issue.getIssueDescription() == null ? "无案件描述" : (issue.getIssueDescription().length() < 20 ? issue.getIssueDescription() : issue.getIssueDescription().substring(0, 19)))));
            Gson gson = new Gson();
            String[] wx = {gson.toJson(models)};
            simpleSend(MessageDestination.WEBSOCKETMESSAGEDESTINATION, wx);
        } catch (Exception ex) {
            ex.printStackTrace();
            log.error(ex);
        }
    }

    /**
     * 发送邮件和通知给客服CC。抄送客户
     *
     * @param issue
     */
    private void SendNoticeToCCANDCustomer(Issue issue) {
        try {
            String[] mx = {PrepareIssueMailToCCANDCustomer(issue)};
            simpleSend(MessageDestination.MAILMESSAGEDESTINATION, mx);
            List<WebsocketMessageModel> models = new ArrayList<WebsocketMessageModel>();
            models.add(new WebsocketMessageModel("/iqchange/" + issue.getServiceStaff(),
                    (issue.getServiceCode() == null ? "无客服代号" : issue.getServiceCode())
                            + "     " +
                            (issue.getIssueDescription() == null ? "无案件描述" : (issue.getIssueDescription().length() < 20 ? issue.getIssueDescription() : issue.getIssueDescription().substring(0, 19)))));
            Gson gson = new Gson();
            String[] wx = {gson.toJson(models)};
            simpleSend(MessageDestination.WEBSOCKETMESSAGEDESTINATION, wx);
        } catch (Exception ex) {
            ex.printStackTrace();
            log.error(ex);
        }
    }

    /**
     * 发送邮件和通知给客服CC
     *
     * @param issue
     */
    private void SendNoticeToCC(Issue issue) {
        try {
            String[] mx = {PrepareIssueMailToCC(issue)};
            simpleSend(MessageDestination.MAILMESSAGEDESTINATION, mx);
            List<WebsocketMessageModel> models = new ArrayList<WebsocketMessageModel>();
            models.add(new WebsocketMessageModel("/iqchange/" + issue.getServiceStaff(),
                    (issue.getServiceCode() == null ? "无客服代号" : issue.getServiceCode())
                            + "     " +
                            (issue.getIssueDescription() == null ? "无案件描述" : (issue.getIssueDescription().length() < 20 ? issue.getIssueDescription() : issue.getIssueDescription().substring(0, 19)))));
            Gson gson = new Gson();
            String[] wx = {gson.toJson(models)};
            simpleSend(MessageDestination.WEBSOCKETMESSAGEDESTINATION, wx);
        } catch (Exception ex) {
            ex.printStackTrace();
            log.error(ex);
        }
    }

    private void sendMailForISVTenantNotInServiceCloud(Issue issue,String servicerId,String servicerName) {
        try {
            String[] mx = {PrepareIssueMailToCCForISVTenantNotInServiceCloud(issue,servicerId,servicerName)};
            simpleSend(MessageDestination.MAILMESSAGEDESTINATION, mx);
            List<WebsocketMessageModel> models = new ArrayList<WebsocketMessageModel>();
            models.add(new WebsocketMessageModel("/iqchange/" + issue.getServiceStaff(),
                    (issue.getServiceCode() == null ? "无客服代号" : issue.getServiceCode())
                            + "     " +
                            (issue.getIssueDescription() == null ? "无案件描述" : (issue.getIssueDescription().length() < 20 ? issue.getIssueDescription() : issue.getIssueDescription().substring(0, 19)))));
            Gson gson = new Gson();
            String[] wx = {gson.toJson(models)};
            simpleSend(MessageDestination.WEBSOCKETMESSAGEDESTINATION, wx);
        } catch (Exception ex) {
            ex.printStackTrace();
            log.error(ex);
        }
    }
    private String GetServiceID(long issueId) {
        try {
            Issue issue;
            issue = issueDao.SelectIssueByIssueId(String.valueOf(issueId));
            return issue.getServiceId();
        } catch (Exception ex) {
            ex.printStackTrace();
            log.error(ex);
            return "";
        }
    }

    private List<String> GetAdditionalExplanationReceivers(long issueId) {
        try {
            List<String> receivers = new ArrayList<String>();
            receivers = issueDao.SelectEmails(issueId);
            return receivers;
        } catch (Exception ex) {
            ex.printStackTrace();
            log.error(ex);
            return null;
        }
    }


    /**
     * 保存案件提交进度
     *
     * @param issueId
     * @param userId
     * @param date
     */
    private void SaveIssueCreateLog(long issueId, String crmId, String productCode, String userId, String date, String serviceStaff, String issueStatus, String serviceRegion) {
        try {
            IssueProgress issueProgress = new IssueProgress();
            issueProgress.setProcessType(IssueProcessType.Submit.toString());
            issueProgress.setProcessor(userId);
            issueProgress.setDescription("Submit Issue");
            if("CN".equals(serviceRegion)){
                issueProgress.setDescription("提交问题");
            }
            issueProgress.setReplyType(ReplyType.Q.toString());
            issueProgress.setProcessTime(date);
//            issueProgress.setSyncStatus("T");
            //云管家打通T服务云，需要存下面3个字段
            issueProgress.setCurrentStatus(issueStatus);
            issueProgress.setHandlerId(serviceStaff);
            issueProgress.setWorkno("");
            String syncStatus = "";
            issueDao.InsertIssueProgress(issueId, crmId, issueProgress);
        } catch (Exception ex) {
            ex.printStackTrace();
            log.error(ex);
        }
    }

    /**
     * 保存案件审核进度
     *
     * @param issueId
     * @param userId
     * @param date
     */
    private void SaveIssueConfirmLog(long issueId, String userId, String date) {
        try {
            IssueProgress issueProgress = new IssueProgress();
            issueProgress.setProcessType(IssueProcessType.Confirm.toString());
            issueProgress.setProcessor(userId);
            issueProgress.setDescription("Confirm Issue");
            issueProgress.setProcessTime(date);
            issueDao.InsertIssueProgress(issueId, "", issueProgress);
        } catch (Exception ex) {
            ex.printStackTrace();
            log.error(ex);
        }
    }

    /**
     * 保存案件附件
     *
     * @param issueId
     * @param issue
     */
    //@Async
    public void SaveAttachments(long issueId, long progressId, Issue issue) {
        try {
            StringBuilder sbLog = new StringBuilder();
            if (issue.getIssueAttachments() != null && issue.getIssueAttachments().size() > 0) {
                StringBuilder sbLog2 = new StringBuilder();
                for (IssueAttachment att : issue.getIssueAttachments()) {
                    //DgwFileUtils.writeToFile(att.getAttachment(),attachmentfilePathPrefix, "issue__" + issue.getIssueId()+ "_" + Integer.toString(att.getSequeceNum()) + att.getFileType());
                    int tryCount = 3;
                    sbLog.setLength(0);
                    GridFSFile gridFSFile = null;
                    Object id = null;
                    long chunkSize = -1;
                    int sequeceNum = att.getSequeceNum() + 1;
                    String fileType = att.getFileType();
                    String fileName = "issue__" + issueId + "__" + Integer.toString(sequeceNum) + fileType;
                    //v3版本 fileName 存扩展名到mongodb
                    if (!StringUtils.isEmpty(att.getFileName())) {
                        fileName = "issue__" + issueId + "__" + att.getFileName() + fileType;
                        if (progressId != 0L) {
                            fileName = "issue__" + issueId + "__" + progressId + "__" + att.getFileName() + fileType;
                        }
                    }
                    sbLog.append("issueId:");
                    sbLog.append(Long.toString(issueId));
                    sbLog.append(" sequenceNum:");
                    sbLog.append(Long.toString(sequeceNum));
                    sbLog.append(" do save attachment fileName:");
                    sbLog.append(fileName);
                    sbLog.append(" fileType:");
                    sbLog.append(fileType);
                    do {
                        sbLog2.setLength(0);
                        try {
                            InputStream inputStream = new ByteArrayInputStream(att.getAttachment());
                            DBObject metaData = new BasicDBObject();
                            metaData.put("issueId", issueId);
                            metaData.put("sequeceNum", sequeceNum);
                            metaData.put("fileType", fileType);
                            gridFSFile = gridFsTemplate.store(inputStream, fileName, fileType, metaData);
                        } catch (Exception ex) {
                            ex.printStackTrace();
                            log.error(ex);
                        }
                        if (gridFSFile != null) {
                            sbLog2.append(" chunkSize:");
                            sbLog2.append((chunkSize = gridFSFile.getChunkSize()));
                            sbLog2.append(" length:");
                            sbLog2.append(gridFSFile.getLength());
                            sbLog2.append(" fileId:");
                            sbLog2.append((id = gridFSFile.getId()));
                            //存到mysql
                            IssueAttachmentFileV3 issueAttachmentFileV3 = new IssueAttachmentFileV3();
                            issueAttachmentFileV3.setIssueId(issueId);
                            issueAttachmentFileV3.setProgressId(progressId);
                            issueAttachmentFileV3.setFileId(gridFSFile.getId().toString());
                            issueAttachmentFileV3.setFileName(fileName);
                            issueAttachmentFileV3.setFileType(fileType);
                            issueAttachmentFileV3.setUrl(issueAttachAddress + "/issueservicesyncapi/api/issues/attach/" + issueAttachmentFileV3.getFileId());

                            issueDao.saveAttachmentFile(issueAttachmentFileV3);
                        }
                        sbLog2.append(" tryCount:");
                        sbLog2.append(Integer.toString(tryCount));
                        System.out.print(sbLog.toString() + sbLog2.toString() + "\n");
                        tryCount--;
                        //gridFsTemplate.findOne(Query.query(Criteria.where("_id").is(fileId))))
                    } while ((gridFSFile == null || id == null || chunkSize == 0) && tryCount > 0);
                    if (tryCount < 0) {
                        System.out.print("Save attachment fail!");
                    }
                }
            } else {
                sbLog.append("issueId:");
                sbLog.append(Long.toString(issueId));
                sbLog.append(" not attachment!");
                sbLog.append("\n");
                System.out.print(sbLog.toString());
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            log.error(ex);
        }
    }

    /**
     * 保存案件补充说明附件
     *
     * @param issueId
     * @param issue
     */
    //@Async
    private void SaveAdditionalExplanationAttachments(long issueId, Issue issue, String dt) {
        try {
            if (issue.getIssueAttachments() != null && issue.getIssueAttachments().size() > 0) {
                for (IssueAttachment att : issue.getIssueAttachments()) {
                    //DgwFileUtils.writeToFile(att.getAttachment(),attachmentfilePathPrefix, "issue__" + issue.getIssueId()+ "_" + Integer.toString(att.getSequeceNum()) + att.getFileType());
                    try {
                        InputStream inputStream = new ByteArrayInputStream(att.getAttachment());
                        DBObject metaData = new BasicDBObject();
                        metaData.put("issueId", issue.getIssueId());
                        metaData.put("sequeceNum", att.getSequeceNum() + 1);
                        gridFsTemplate.store(inputStream,
                                "issue__" + issue.getIssueId() + "__" + dt + "__" + Integer.toString(att.getSequeceNum() + 1) + att.getFileType(),
                                att.getFileType(),
                                metaData);
                    } catch (Exception ex) {
                        ex.printStackTrace();
                        log.error(ex);
                    }
                }
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            log.error(ex);
        }
    }

    /**
     * 组织发送结案案件给提交人的邮件
     *
     * @return
     */
    public String PrepareAutoCloseMailToSubmiter(CasesEmail casesEmail, List<String> receiver, List<String> ccMailList, String language) {
        List<Mail> mails = new ArrayList<Mail>();
        try {

            Mail mail01 = new Mail();

            String languageStandard = messageUtils.getLanguage(language);
            log.info("邮件模板语言：" + languageStandard);
            String subject = "";
            subject = messageUtils.get("IssueCloseMailToSubmiter", languageStandard);

            mail01.setSubject(subject);


            String formatToSubmiterMailStr = commonMailService.readMailContent("autoCloseV3.html", language);
            String mailMsg = String.format(formatToSubmiterMailStr, casesEmail.getHtmlCases());

            mail01.setMessage(mailMsg);
            mail01.setReceivers(receiver);
            mail01.setCcs(ccMailList);
            mail01.setMailSourceType(MailSourceType.Issue);
            mail01.setUrl("");
            mail01.setSourceId(Long.toString(casesEmail.getIssueId()));
            mail01.setPriority(1);
            mail01.setLanguage(language);
            mails.add(mail01);

        } catch (Exception ex) {
            log.error(ex);
            ex.printStackTrace();
        }
        Gson gson = new Gson();
        return gson.toJson(mails);
    }

    /**
     * 组织发送结案案件给提交人的邮件
     *
     * @param issue
     * @return
     */
    private String PrepareCloseMailToSubmiter(Issue issue) {
        List<Mail> mails = new ArrayList<Mail>();
        try {
            //获得语言别
            String language = defaultLanguage;
            int userTimeZone = 0; //默认时区
            UserPersonalInfo userPersonalInfo = userService.GetUserPersonalInfo(issue.getUserContact().getUserId());//(issue.getUserId());
            if (userPersonalInfo != null && userPersonalInfo.getLanguage() != null && !userPersonalInfo.getLanguage().isEmpty()) {
                language = userPersonalInfo.getLanguage();
                userTimeZone = getTimeZoneMin(userPersonalInfo.getTimeZone());
            }
            //获取客户的维护合约等级
            CustomerServiceInfo customerServiceInfo = new CustomerServiceInfo();
            try {
                customerServiceInfo = customerService.GetCustomerServiceInfo(issue.getServiceCode(), issue.getProductCode());
            } catch (Exception ex) {
                customerServiceInfo = new CustomerServiceInfo();
            }

            //组织发给客服专员的邮件
            String formatToSubmiterMailStr = commonMailService.readMailContent("issueMailCloseToSubmit.html", language);

            Long issueId = issue.getIssueId();
            System.out.print("Before PrepareCloseMailToSubmiter.GetIssueAttachmentFiles " + issueId);
            //組織案件附件超鏈接
            String attachementStr = messageUtils.get("nothing", language);
            attachementStr = getAttachementStrV3(issueId, attachementStr);
            System.out.print("After PrepareCloseMailToSubmiter.GetIssueAttachmentFiles " + issueId);

//            if (issue.getIssueAttachments() != null && issue.getIssueAttachments().size() > 0) {
//                attachementStr = "该案件单含有附件";
//                if (language.equals("zh-TW")) {
//                    attachementStr = "該案件單含有附件";
//                }
//            }
            UserContact userContact = issue.getUserContact();
            String phone01 = userContact.getPhone01();
            String phone02 = userContact.getPhone02();
            String phone = phone01 == null || phone01.isEmpty() ? phone02 == null || phone02.isEmpty() ? "" : phone02 : phone01 + (phone02 == null || phone02.isEmpty() ? "" : "#" + phone02);
            String crmId = issue.getCrmId();
            String serviceCode = issue.getServiceCode();
            String customerName = customerServiceInfo.getCustomerName();
            String issueDescription = issue.getIssueDescription();
            String mailMsg = String.format(formatToSubmiterMailStr, customerServiceInfo.getProductName() + "[ " + issue.getProductCode() + " ]", serviceCode, customerServiceInfo.getContractStateDesc() == null ? "" : customerServiceInfo.getContractStateDesc(),
                    crmId, customerName, transferTimeZone(issue.getSubmitTime(), userTimeZone),
                    userContact.getName(), userContact.getEmail(), phone, attachementStr,
                    issueDescription);

            //1. 获取客户mail
            //组织发送给提交者的邮件
            List<String> receivers01 = new ArrayList<String>();
            receivers01.add(issue.getUserContact().getEmail());

            //add 添加额外的抄送人
            List<String> cc01 = new ArrayList<String>();
            if (issue.getCcUser() != null && !issue.getCcUser().trim().equals("")) {
                String ccUser = issue.getCcUser().trim();
                String[] list = ccUser.split(";");
                for (int i = 0; i < list.length; i++) {
                    cc01.add(list[i].trim());
                }
            }
            Mail mail01 = new Mail();
            String subject = "";
            String languageStandard = messageUtils.getLanguage(language);
            log.info("邮件模板语言：" + languageStandard);
            if (connectArea == null || connectArea.isEmpty() || connectArea.equals("CN")) {
                //【鼎捷服务云】您的问题已提交成功
                subject = messageUtils.get("IssueCloseMailToSubmiter", languageStandard);
            } else {
                //台湾区，要增加其他标题内容
                subject = mailUtils.getIssueMailSubject("IssueCloseMailToSubmiter", language, crmId, serviceCode + customerName, issueDescription, false);
            }

            mail01.setSubject(subject);
            mail01.setMessage(mailMsg);
            mail01.setReceivers(receivers01);
            mail01.setCcs(cc01);
            mail01.setMailSourceType(MailSourceType.Issue);
            mail01.setUrl("");
            mail01.setSourceId(Long.toString(issue.getIssueId()));
            mail01.setPriority(1);
            mail01.setLanguage(language);
            mails.add(mail01);

        } catch (Exception ex) {
            log.error(ex);
            ex.printStackTrace();
        }
        Gson gson = new Gson();
        return gson.toJson(mails);
    }

    /**
     * 组织发送结案案件给提交人验证的邮件
     *
     * @param issue
     * @return
     */
    private String PrepareCloseMailToSubmiterCheck(Issue issue) {
        List<Mail> mails = new ArrayList<Mail>();
        try {
            //获得语言别
            String language = defaultLanguage;
            int userTimeZone = 0; //默认时区
            UserPersonalInfo userPersonalInfo = userService.GetUserPersonalInfo(issue.getUserContact().getUserId());//(issue.getUserId());
            if (userPersonalInfo != null && userPersonalInfo.getLanguage() != null && !userPersonalInfo.getLanguage().isEmpty()) {
                language = userPersonalInfo.getLanguage();
                userTimeZone = getTimeZoneMin(userPersonalInfo.getTimeZone());
            }
            //获取客户的维护合约等级
            CustomerServiceInfo customerServiceInfo = new CustomerServiceInfo();
            try {
                customerServiceInfo = customerService.GetCustomerServiceInfo(issue.getServiceCode(), issue.getProductCode());
            } catch (Exception ex) {
                customerServiceInfo = new CustomerServiceInfo();
            }

            //组织发给客服专员的邮件
            String formatToSubmiterMailStr = commonMailService.readMailContent("issueMailCloseToSubmitCheck.html", language);

            Long issueId = issue.getIssueId();
            System.out.print("Before PrepareCloseMailToSubmiterCheck.GetIssueAttachmentFiles " + issueId);
            //組織案件附件超鏈接
            String attachementStr = messageUtils.get("nothing", language);
            attachementStr = getAttachementStrV3(issueId, attachementStr);
            System.out.print("After PrepareCloseMailToSubmiterCheck.GetIssueAttachmentFiles " + issueId);

//            if (issue.getIssueAttachments() != null && issue.getIssueAttachments().size() > 0) {
//                attachementStr = "该案件单含有附件";
//                if (language.equals("zh-TW")) {
//                    attachementStr = "該案件單含有附件";
//                }
//            }
            UserContact userContact = issue.getUserContact();
            String phone01 = userContact.getPhone01();
            String phone02 = userContact.getPhone02();
            String phone = phone01 == null || phone01.isEmpty() ? phone02 == null || phone02.isEmpty() ? "" : phone02 : phone01 + (phone02 == null || phone02.isEmpty() ? "" : "#" + phone02);
            String crmId = issue.getCrmId();
            String serviceCode = issue.getServiceCode();
            String customerName = customerServiceInfo.getCustomerName();
            String issueDescription = issue.getIssueDescription();
            String mailMsg = String.format(formatToSubmiterMailStr, customerServiceInfo.getProductName() + "[ " + issue.getProductCode() + " ]", serviceCode, customerServiceInfo.getContractStateDesc() == null ? "" : customerServiceInfo.getContractStateDesc(),
                    crmId, customerName, transferTimeZone(issue.getSubmitTime(), userTimeZone),
                    userContact.getName(), userContact.getEmail(), phone, attachementStr,
                    issueDescription);

            //1. 获取客户mail
            //组织发送给提交者的邮件
            List<String> receivers01 = new ArrayList<String>();
            receivers01.add(issue.getUserContact().getEmail());

            //add 添加额外的抄送人
            List<String> cc01 = new ArrayList<String>();
            if (issue.getCcUser() != null && !issue.getCcUser().trim().equals("")) {
                String ccUser = issue.getCcUser().trim();
                String[] list = ccUser.split(";");
                for (int i = 0; i < list.length; i++) {
                    cc01.add(list[i].trim());
                }
            }
            Mail mail01 = new Mail();
            String subject = "";
            String languageStandard = messageUtils.getLanguage(language);
            log.info("邮件模板语言：" + languageStandard);
            if (connectArea == null || connectArea.isEmpty() || connectArea.equals("CN")) {
                //【鼎捷服务云】您的问题已提交成功
                subject = messageUtils.get("IssueCloseMailToSubmiterCheck", languageStandard);
            } else {
                //台湾区，要增加其他标题内容
                subject = mailUtils.getIssueMailSubject("IssueCloseMailToSubmiterCheck", language, crmId, serviceCode + customerName, issueDescription, false);
            }

            mail01.setSubject(subject);
            mail01.setMessage(mailMsg);
            mail01.setReceivers(receivers01);
            mail01.setCcs(cc01);
            mail01.setMailSourceType(MailSourceType.Issue);
            mail01.setUrl("");
            mail01.setSourceId(Long.toString(issue.getIssueId()));
            mail01.setPriority(1);
            mail01.setLanguage(language);
            mails.add(mail01);

        } catch (Exception ex) {
            log.error(ex);
            ex.printStackTrace();
        }
        Gson gson = new Gson();
        return gson.toJson(mails);
    }

    /**
     * 组织发送给案件提交人的邮件,走审核时，发送给客户的邮件
     *
     * @param issue
     * @return
     */
    private String PrepareIssueMailByConfirm(Issue issue, boolean isConfirm) {
        List<Mail> mails = new ArrayList<Mail>();
        try {
            //获得语言别
            String language = defaultLanguage;
            int userTimeZone = 0; //默认时区
            UserPersonalInfo userPersonalInfo = userService.GetUserPersonalInfo(issue.getUserContact().getUserId());//(issue.getUserId());
            if (userPersonalInfo != null && userPersonalInfo.getLanguage() != null && !userPersonalInfo.getLanguage().isEmpty()) {
                language = userPersonalInfo.getLanguage();
                userTimeZone = getTimeZoneMin(userPersonalInfo.getTimeZone());
            }
            //获取客户的维护合约等级
            CustomerServiceInfo customerServiceInfo = new CustomerServiceInfo();
            try {
                customerServiceInfo = customerService.GetCustomerServiceInfo(issue.getServiceCode(), issue.getProductCode());
            } catch (Exception ex) {
                customerServiceInfo = new CustomerServiceInfo();
            }

            //组织发给客服专员的邮件
//            String attachementStr = "无";
//            String attachementStr = messageUtils.get("nothing",language);
            String formatToSubmiterMailStr = commonMailService.readMailContent("issueMailCustomerSumbit.html", language);
//            String formatToSubmiterMailStr = ToSubmiterMailStr;
//            if (language.equals("zh-cn")) {
//                formatToSubmiterMailStr = iMailService.readMailContent("issueMailCustomerSumbit.html","zh-CN");
//            } else if (language.equals("zh-TW")) {
//                attachementStr = "無";
////                formatToSubmiterMailStr = ToSubmiterMailStr_TW;
//                formatToSubmiterMailStr = iMailService.readMailContent("issueMailCustomerSumbit.html","zh-TW");
//            } else if (language.equals("zh-MALA")) {
//                attachementStr = "無";
//                formatToSubmiterMailStr = iMailService.readMailContent("issueMailCustomerSumbit.html","zh-MALA");
//            } else if (language.equals("zh-THAI")) {
//                attachementStr = "無";
//                formatToSubmiterMailStr = iMailService.readMailContent("issueMailCustomerSumbit.html","zh-THAI");
//            }

            Long issueId = issue.getIssueId();
            System.out.print("Before PrepareIssueMail.GetIssueAttachmentFiles " + issueId);
            //組織案件附件超鏈接
            String attachementStr = messageUtils.get("nothing", language);
            attachementStr = getAttachementStrV3(issueId, attachementStr);
            System.out.print("After PrepareIssueMail.GetIssueAttachmentFiles " + issueId);

//            if (issue.getIssueAttachments() != null && issue.getIssueAttachments().size() > 0) {
//                attachementStr = "该案件单含有附件";
//                if (language.equals("zh-TW")) {
//                    attachementStr = "該案件單含有附件";
//                }
//            }
            UserContact userContact = issue.getUserContact();
            String phone01 = userContact.getPhone01();
            String phone02 = userContact.getPhone02();
            String phone = phone01 == null || phone01.isEmpty() ? phone02 == null || phone02.isEmpty() ? "" : phone02 : phone01 + (phone02 == null || phone02.isEmpty() ? "" : "#" + phone02);
            String crmId = issue.getCrmId();
            String serviceCode = issue.getServiceCode();
            String customerName = customerServiceInfo.getCustomerName();
            String issueDescription = issue.getIssueDescription();
            String mailMsg = String.format(formatToSubmiterMailStr, customerServiceInfo.getProductName() + "[ " + issue.getProductCode() + " ]", serviceCode, customerServiceInfo.getContractStateDesc() == null ? "" : customerServiceInfo.getContractStateDesc(),
                    crmId, customerName, transferTimeZone(issue.getSubmitTime(), userTimeZone),
                    userContact.getName(), userContact.getEmail(), phone, attachementStr,
                    issueDescription);

            //1. 获取客户mail
            //组织发送给提交者的邮件
            List<String> receivers01 = new ArrayList<String>();
            receivers01.add(issue.getUserContact().getEmail());

            //add 添加额外的抄送人
            List<String> cc01 = new ArrayList<String>();
            if (issue.getCcUser() != null && !issue.getCcUser().trim().equals("")) {
                String ccUser = issue.getCcUser().trim();
                String[] list = ccUser.split(";");
                for (int i = 0; i < list.length; i++) {
                    cc01.add(list[i].trim());
                }
            }
            Mail mail01 = new Mail();
            String subject = "";
            String languageStandard = messageUtils.getLanguage(language);
            log.info("邮件模板语言：" + languageStandard);
            if (connectArea == null || connectArea.isEmpty() || connectArea.equals("CN")) {
                //【鼎捷服务云】您的问题已提交成功
                subject = messageUtils.get("IssueSubmitMailToSubmiter", languageStandard);
                if (issue.getIssueSubmitMode() != null && issue.getIssueSubmitMode().equals(IssueSubmitMode.CouponUse.toString())) {
                    subject = mailUtils.getCouponIssueMailSubject("CouponIssueSubmitMailToSubmiter", language, issue.getCouponRuleName() == null ? "" : issue.getCouponRuleName());
                }
            } else {
                //台湾区，要增加其他标题内容
                subject = mailUtils.getIssueMailSubject("IssueSubmitMailToSubmiter", language, crmId, serviceCode + customerName, issueDescription, false);
                if (issue.getIssueSubmitMode() != null) {
                    if (issue.getIssueSubmitMode().equals(IssueSubmitMode.CallMe.toString())) {
                        subject = mailUtils.getIssueMailSubject("CallMeIssueSubmitMailToSubmiter", language, crmId, serviceCode + customerName, issueDescription, false);
                    } else if (issue.getIssueSubmitMode().equals(IssueSubmitMode.CouponUse.toString())) {
                        subject = mailUtils.getCouponIssueMailSubject("CouponIssueSubmitMailToSubmiter", language, issue.getCouponRuleName() == null ? "" : issue.getCouponRuleName());
                    }
                }
            }
            //            if (connectArea == null || connectArea.isEmpty() || connectArea.equals("CN")) {
//                //【鼎捷服务云】您的问题已提交成功
//                subject = EsCloudMailSubject.IssueSubmitMailToSubmiter.toString();
//                if (issue.getIssueSubmitMode()!=null && issue.getIssueSubmitMode().equals(IssueSubmitMode.CouponUse.toString())) {
//                    subject = EsCloudMailSubject.CouponIssueSubmitMailToSubmiter.getCouponIssueMailSubject(language, issue.getCouponRuleName()==null?"":issue.getCouponRuleName());
//                }
//            } else if (connectArea.equals("TW")) {
//                //台湾区，要增加其他标题内容
//                subject = EsCloudMailSubject.IssueSubmitMailToSubmiter.getIssueMailSubject(language, crmId, serviceCode + customerName, issueDescription, false);
//                if (issue.getIssueSubmitMode()!=null) {
//                    if (issue.getIssueSubmitMode().equals(IssueSubmitMode.CallMe.toString())) {
//                        String issueCategoryDesc=IssueCategory.valueOf(issue.getIssueCategory()).getDescription();
//                        subject = EsCloudMailSubject.CallMeIssueSubmitMailToSubmiter.getIssueMailSubject(language, crmId, serviceCode + customerName, issueDescription, false);
//                    }
//                    else if (issue.getIssueSubmitMode().equals(IssueSubmitMode.CouponUse.toString())) {
//                        subject = EsCloudMailSubject.CouponIssueSubmitMailToSubmiter.getCouponIssueMailSubject(language, issue.getCouponRuleName()==null?"":issue.getCouponRuleName());
//                    }
//                }
//            }
            mail01.setSubject(subject);
            mail01.setMessage(mailMsg);
//            mail01.setMessage("您于" + issue.getSubmitTime() + "填写的问题已提交成功，客服人员会第一时间为您处理！");
//            if (language.equals("zh-TW")) {
//                mail01.setMessage("您於" + issue.getSubmitTime() + "填寫的問題已提交成功，客服人員會第一時間爲您處理！");
//            }

            mail01.setReceivers(receivers01);
            mail01.setCcs(cc01);
            mail01.setMailSourceType(MailSourceType.Issue);
            mail01.setUrl("");
            mail01.setSourceId(Long.toString(issue.getIssueId()));
            mail01.setPriority(1);
            mail01.setLanguage(language);
            mails.add(mail01);

        } catch (Exception ex) {
            log.error(ex);
            ex.printStackTrace();
        }
        Gson gson = new Gson();
        return gson.toJson(mails);
    }


    /**
     * 组织发送给案件提交人的退回邮件
     *
     * @param issue
     * @return
     */
    private String PrepareIssueBackMail(Issue issue) {
        List<Mail> mails = new ArrayList<Mail>();
        try {
            //获得语言别
            String language = defaultLanguage;
            int userTimeZone = 0; //默认时区
            UserPersonalInfo userPersonalInfo = userService.GetUserPersonalInfo(issue.getUserContact().getUserId());//(issue.getUserId());
            if (userPersonalInfo != null && userPersonalInfo.getLanguage() != null && !userPersonalInfo.getLanguage().isEmpty()) {
                language = userPersonalInfo.getLanguage();
                userTimeZone = getTimeZoneMin(userPersonalInfo.getTimeZone());
            }
            //获取客户的维护合约等级
            CustomerServiceInfo customerServiceInfo = new CustomerServiceInfo();
            try {
                customerServiceInfo = customerService.GetCustomerServiceInfo(issue.getServiceCode(), issue.getProductCode());
            } catch (Exception ex) {
                customerServiceInfo = new CustomerServiceInfo();
            }

            //组织发给客服专员的邮件
            String attachementStr = "无";
            String formatToSubmiterMailStr = ToSubmiterBackMailStr;
            if (language.equals("zh-TW")) {
                attachementStr = "無";
                formatToSubmiterMailStr = ToSubmiterBackMailStr_TW;
            }

            Long issueId = issue.getIssueId();

            System.out.print("Before PrepareIssueMail.GetIssueAttachmentFiles " + issueId);
            //組織案件附件超鏈接
            List<IssueAttachmentFile> attachFiles = GetIssueAttachmentFiles(Long.toString(issueId));
            if (attachFiles != null && attachFiles.size() > 0) {
                attachementStr = "";
                for (IssueAttachmentFile file : attachFiles) {
                    attachementStr = attachementStr + "  " + "<a href=\"" + issueAttachAddress + "/issueservicesyncapi/api/issues/attach/" + file.getFileId() + "/download\">" + file.getFileName() + "</a>";
                }
            }
            System.out.print("After PrepareIssueMail.GetIssueAttachmentFiles " + issueId);

//            if (issue.getIssueAttachments() != null && issue.getIssueAttachments().size() > 0) {
//                attachementStr = "该案件单含有附件";
//                if (language.equals("zh-TW")) {
//                    attachementStr = "該案件單含有附件";
//                }
//            }
            UserContact userContact = issue.getUserContact();
            String phone01 = userContact.getPhone01();
            String phone02 = userContact.getPhone02();
            String phone = phone01 == null || phone01.isEmpty() ? phone02 == null || phone02.isEmpty() ? "" : phone02 : phone01 + (phone02 == null || phone02.isEmpty() ? "" : "#" + phone02);
            String crmId = issue.getCrmId();
            String serviceCode = issue.getServiceCode();
            String customerName = customerServiceInfo.getCustomerName();
            String issueDescription = issue.getIssueDescription();
            String mailMsg = String.format(formatToSubmiterMailStr, customerServiceInfo.getProductName() + "[ " + issue.getProductCode() + " ]", serviceCode, customerServiceInfo.getContractStateDesc() == null ? "" : customerServiceInfo.getContractStateDesc(),
                    crmId, customerName, transferTimeZone(issue.getSubmitTime(), userTimeZone),
                    userContact.getName(), userContact.getEmail(), phone, attachementStr,
                    issueDescription);

            //1. 获取客户mail
            //组织发送给提交者的邮件
            List<String> receivers01 = new ArrayList<String>();
            receivers01.add(issue.getUserContact().getEmail());

            //add 添加额外的抄送人
            if (issue.getCcUser() != null && !issue.getCcUser().trim().equals("")) {
                String ccUser = issue.getCcUser().trim();
                String[] list = ccUser.split(";");
                for (int i = 0; i < list.length; i++) {
                    receivers01.add(list[i].trim());
                }
            }
            Mail mail01 = new Mail();
            String subject = "";
            if (connectArea == null || connectArea.isEmpty() || connectArea.equals("CN")) {
                //【鼎捷服务云】您的问题已提交成功
                subject = EsCloudMailSubject.BACK_TO_RESPONSOR.toString();
                if (issue.getIssueSubmitMode() != null && issue.getIssueSubmitMode().equals(IssueSubmitMode.CouponUse.toString())) {
                    subject = EsCloudMailSubject.CouponIssueSubmitMailToSubmiter.getCouponIssueMailSubject(language, issue.getCouponRuleName() == null ? "" : issue.getCouponRuleName());
                }
            } else if (connectArea.equals("TW")) {
                //台湾区，要增加其他标题内容
                subject = EsCloudMailSubject.BACK_TO_RESPONSOR.getIssueMailSubject(language, crmId, serviceCode + customerName, issueDescription, false);
                if (issue.getIssueSubmitMode() != null) {
                    if (issue.getIssueSubmitMode().equals(IssueSubmitMode.CallMe.toString())) {
                        String issueCategoryDesc = IssueCategory.valueOf(issue.getIssueCategory()).getDescription();
                        subject = EsCloudMailSubject.CallMeIssueSubmitMailToSubmiter.getIssueMailSubject(language, crmId, serviceCode + customerName, issueDescription, false);
                    } else if (issue.getIssueSubmitMode().equals(IssueSubmitMode.CouponUse.toString())) {
                        subject = EsCloudMailSubject.CouponIssueSubmitMailToSubmiter.getCouponIssueMailSubject(language, issue.getCouponRuleName() == null ? "" : issue.getCouponRuleName());
                    }
                }
            }
            mail01.setSubject(subject);
            mail01.setMessage(mailMsg);
//            mail01.setMessage("您于" + issue.getSubmitTime() + "填写的问题已提交成功，客服人员会第一时间为您处理！");
//            if (language.equals("zh-TW")) {
//                mail01.setMessage("您於" + issue.getSubmitTime() + "填寫的問題已提交成功，客服人員會第一時間爲您處理！");
//            }

            mail01.setReceivers(receivers01);

            mail01.setMailSourceType(MailSourceType.Issue);
            mail01.setUrl("");
            mail01.setSourceId(Long.toString(issue.getIssueId()));
            mail01.setPriority(1);
            mail01.setLanguage(language);
            mails.add(mail01);

        } catch (Exception ex) {
            log.error(ex);
            ex.printStackTrace();
        }
        Gson gson = new Gson();
        return gson.toJson(mails);
    }

    /**
     * 组织发送给案件提交人的邮件
     *
     * @param issue
     * @return
     */
    private String PrepareIssueMail(Issue issue) {
        List<Mail> mails = new ArrayList<Mail>();
        try {
            //获得语言别
            String language = defaultLanguage;
            int userTimeZone = 0; //默认时区
            UserPersonalInfo userPersonalInfo = userService.GetUserPersonalInfo(issue.getUserContact().getUserId());//(issue.getUserId());
            if (userPersonalInfo != null && userPersonalInfo.getLanguage() != null && !userPersonalInfo.getLanguage().isEmpty()) {
                language = userPersonalInfo.getLanguage();
                userTimeZone = getTimeZoneMin(userPersonalInfo.getTimeZone());
            }
            //获取客户的维护合约等级
            CustomerServiceInfo customerServiceInfo = new CustomerServiceInfo();
            try {
                customerServiceInfo = customerService.GetCustomerServiceInfo(issue.getServiceCode(), issue.getProductCode());
            } catch (Exception ex) {
                customerServiceInfo = new CustomerServiceInfo();
            }

            //组织发给客服专员的邮件
//            String attachementStr = "无";
            String attachementStr = messageUtils.get("nothing", language);
            String formatToSubmiterMailStr = commonMailService.readMailContent("issueMailCustomerSumbit.html", language);
//            String formatToSubmiterMailStr = ToSubmiterMailStr;
//            if (language.equals("zh-cn")) {
//                formatToSubmiterMailStr = iMailService.readMailContent("issueMailCustomerSumbit.html","zh-CN");
//            } else if (language.equals("zh-TW")) {
//                attachementStr = "無";
//                //formatToSubmiterMailStr = ToSubmiterMailStr_TW;
//                formatToSubmiterMailStr = iMailService.readMailContent("issueMailCustomerSumbit.html","zh-TW");
//
//            } else if (language.equals("zh-MALA")) {
//                attachementStr = "無";
//                formatToSubmiterMailStr = iMailService.readMailContent("issueMailCustomerSumbit.html","zh-MALA");
//            } else if (language.equals("zh-THAI")) {
//                attachementStr = "無";
//                formatToSubmiterMailStr = iMailService.readMailContent("issueMailCustomerSumbit.html","zh-THAI");
//            }


            Long issueId = issue.getIssueId();

            System.out.print("Before PrepareIssueMail.GetIssueAttachmentFiles " + issueId);
            //組織案件附件超鏈接
            List<IssueAttachmentFile> attachFiles = GetIssueAttachmentFiles(Long.toString(issueId));
            if (attachFiles != null && attachFiles.size() > 0) {
                attachementStr = "";
                for (IssueAttachmentFile file : attachFiles) {
                    attachementStr = attachementStr + "  " + "<a href=\"" + issueAttachAddress + "/issueservicesyncapi/api/issues/attach/" + file.getFileId() + "/download\">" + file.getFileName() + "</a>";
                }
            }
            System.out.print("After PrepareIssueMail.GetIssueAttachmentFiles " + issueId);

//            if (issue.getIssueAttachments() != null && issue.getIssueAttachments().size() > 0) {
//                attachementStr = "该案件单含有附件";
//                if (language.equals("zh-TW")) {
//                    attachementStr = "該案件單含有附件";
//                }
//            }
            UserContact userContact = issue.getUserContact();
            String phone01 = userContact.getPhone01();
            String phone02 = userContact.getPhone02();
            String phone = phone01 == null || phone01.isEmpty() ? phone02 == null || phone02.isEmpty() ? "" : phone02 : phone01 + (phone02 == null || phone02.isEmpty() ? "" : "#" + phone02);
            String crmId = issue.getCrmId();
            String serviceCode = issue.getServiceCode();
            String customerName = customerServiceInfo.getCustomerName();
            String issueDescription = issue.getIssueDescription();
            String mailMsg = String.format(formatToSubmiterMailStr, customerServiceInfo.getProductName() + "[ " + issue.getProductCode() + " ]", serviceCode, customerServiceInfo.getContractStateDesc() == null ? "" : customerServiceInfo.getContractStateDesc(),
                    crmId, customerName, transferTimeZone(issue.getSubmitTime(), userTimeZone),
                    userContact.getName(), userContact.getEmail(), phone, attachementStr,
                    issueDescription);

            //1. 获取客户mail
            //组织发送给提交者的邮件
            List<String> receivers01 = new ArrayList<String>();
            receivers01.add(issue.getUserContact().getEmail());

            //add 添加额外的抄送人
            if (issue.getCcUser() != null && !issue.getCcUser().trim().equals("")) {
                String ccUser = issue.getCcUser().trim();
                String[] list = ccUser.split(";");
                for (int i = 0; i < list.length; i++) {
                    receivers01.add(list[i].trim());
                }
            }
            Mail mail01 = new Mail();
            String subject = "";
            if (connectArea == null || connectArea.isEmpty() || connectArea.equals("CN")) {
                //【鼎捷服务云】您的问题已提交成功
                if (issue.getIssueSubmitMode() != null && issue.getIssueSubmitMode().equals(IssueSubmitMode.CouponUse.toString())) {
                    subject = mailUtils.getCouponIssueMailSubject("CouponIssueSubmitMailToSubmiter", language, issue.getCouponRuleName() == null ? "" : issue.getCouponRuleName());
                }
            } else {
                //台湾区，要增加其他标题内容
                subject = mailUtils.getIssueMailSubject("IssueSubmitMailToSubmiter", language, crmId, serviceCode + customerName, issueDescription, false);
                if (issue.getIssueSubmitMode() != null) {
                    if (issue.getIssueSubmitMode().equals(IssueSubmitMode.CallMe.toString())) {
                        subject = mailUtils.getIssueMailSubject("CallMeIssueSubmitMailToSubmiter", language, crmId, serviceCode + customerName, issueDescription, false);
                    } else if (issue.getIssueSubmitMode().equals(IssueSubmitMode.CouponUse.toString())) {
                        subject = mailUtils.getCouponIssueMailSubject("CouponIssueSubmitMailToSubmiter", language, issue.getCouponRuleName() == null ? "" : issue.getCouponRuleName());
                    }
                }
            }
//            if (connectArea == null || connectArea.isEmpty() || connectArea.equals("CN")) {
//                //【鼎捷服务云】您的问题已提交成功
//                subject = EsCloudMailSubject.IssueSubmitMailToSubmiter.toString();
//                if (issue.getIssueSubmitMode()!=null && issue.getIssueSubmitMode().equals(IssueSubmitMode.CouponUse.toString())) {
//                    subject = EsCloudMailSubject.CouponIssueSubmitMailToSubmiter.getCouponIssueMailSubject(language, issue.getCouponRuleName()==null?"":issue.getCouponRuleName());
//                }
//            } else if (connectArea.equals("TW")) {
//                //台湾区，要增加其他标题内容
//                subject = EsCloudMailSubject.IssueSubmitMailToSubmiter.getIssueMailSubject(language, crmId, serviceCode + customerName, issueDescription, false);
//                if (issue.getIssueSubmitMode()!=null) {
//                    if (issue.getIssueSubmitMode().equals(IssueSubmitMode.CallMe.toString())) {
//                        String issueCategoryDesc=IssueCategory.valueOf(issue.getIssueCategory()).getDescription();
//                        subject = EsCloudMailSubject.CallMeIssueSubmitMailToSubmiter.getIssueMailSubject(language, crmId, serviceCode + customerName, issueDescription, false);
//                    }
//                    else if (issue.getIssueSubmitMode().equals(IssueSubmitMode.CouponUse.toString())) {
//                        subject = EsCloudMailSubject.CouponIssueSubmitMailToSubmiter.getCouponIssueMailSubject(language, issue.getCouponRuleName()==null?"":issue.getCouponRuleName());
//                    }
//                }
//            }
            mail01.setSubject(subject);
            mail01.setMessage(mailMsg);
//            mail01.setMessage("您于" + issue.getSubmitTime() + "填写的问题已提交成功，客服人员会第一时间为您处理！");
//            if (language.equals("zh-TW")) {
//                mail01.setMessage("您於" + issue.getSubmitTime() + "填寫的問題已提交成功，客服人員會第一時間爲您處理！");
//            }

            mail01.setReceivers(receivers01);

            mail01.setMailSourceType(MailSourceType.Issue);
            mail01.setUrl("");
            mail01.setSourceId(Long.toString(issue.getIssueId()));
            mail01.setPriority(1);
            mail01.setLanguage(language);
            mails.add(mail01);

        } catch (Exception ex) {
            log.error(ex);
            ex.printStackTrace();
        }
        Gson gson = new Gson();
        return gson.toJson(mails);
    }

    /**
     * 组织发送给审核人的邮件
     *
     * @param issue
     * @return
     */
    private String PrepareIssueMailToConfirmer(Issue issue, String confirmIds) {
        List<Mail> mails = new ArrayList<Mail>();
        try {

            //2. 获取mis的mail
            List<String> receivers02 = userService.GetCustomerIssueConfirmerEmail(confirmIds);

            System.out.print("confirmer email: " + receivers02);
            if (CollectionUtils.isEmpty(receivers02)) {
                return "";
            }


            //获得语言别
            String language = defaultLanguage;
            //台湾区，客服不管语系是什么，都是默认语系(繁體)
            boolean isTWArea = connectArea != null && connectArea.equals("TW");

            //获取客户的维护合约等级
            CustomerServiceInfo customerServiceInfo = new CustomerServiceInfo();
            try {
                customerServiceInfo = customerService.GetCustomerServiceInfo(issue.getServiceCode(), issue.getProductCode());
            } catch (Exception ex) {
                customerServiceInfo = new CustomerServiceInfo();
            }

            //组织发给客服专员的邮件
            String attachementStr = "无";
            String formatToCCMailStr = ToSubmiterMailStrByConfirm;
            if (language.equals("zh-TW")) {
                attachementStr = "無";
                formatToCCMailStr = ToSubmiterMailStrByConfirm_TW;
            }

            Long issueId = issue.getIssueId();

            System.out.print("Before PrepareIssueMailToCC.GetIssueAttachmentFiles " + issueId);
            //組織案件附件超鏈接
            boolean hasAttachment = false;
            List<IssueAttachmentFile> attachFiles = GetIssueAttachmentFiles(Long.toString(issueId));
            if (attachFiles != null && attachFiles.size() > 0) {
                attachementStr = "";
                hasAttachment = true;
                for (IssueAttachmentFile file : attachFiles) {
                    attachementStr = attachementStr + "  " + "<a href=\"" + issueAttachAddress + "/issueservicesyncapi/api/issues/attach/" + file.getFileId() + "/download\">" + file.getFileName() + "</a>";
                }
            }

            System.out.print("Before PrepareIssueMailToCC.GetIssueAttachmentFiles " + issueId);
//            if (issue.getIssueAttachments() != null && issue.getIssueAttachments().size() > 0) {
//                attachementStr = "该案件单含有附件";
//                if (language.equals("zh-TW")) {
//                    attachementStr = "該案件單含有附件";
//                }
//            }
//            String mailMsg = String.format(ToCCMailStr, "", issue.getSubmitTime(), issue.getServiceCode(), issue.getProductCode(),
//                    issue.getProductVersion(), issue.getUserContact().getName(), issue.getUserContact().getEmail(), issue.getUserContact().getPhone01(), attachementStr,
//                    issue.getIssueDescription());
//            String mailMsg = String.format(formatToCCMailStr, "", issue.getSubmitTime(), issue.getServiceCode(), issue.getProductCode(),
//                    issue.getProductVersion(), issue.getUserContact().getName(), issue.getUserContact().getEmail(), issue.getUserContact().getPhone01(), attachementStr,
//                    issue.getIssueDescription());
            UserContact userContact = issue.getUserContact();
            String phone01 = userContact.getPhone01();
            String phone02 = userContact.getPhone02();
            String phone = phone01 == null || phone01.isEmpty() ? phone02 == null || phone02.isEmpty() ? "" : phone02 : phone01 + (phone02 == null || phone02.isEmpty() ? "" : "#" + phone02);
            String crmId = issue.getCrmId();
            String serviceCode = issue.getServiceCode();
            String customerName = customerServiceInfo.getCustomerName();
            String issueDescription = issue.getIssueDescription();
            String mailMsg = String.format(formatToCCMailStr, customerServiceInfo.getProductName() + "[ " + issue.getProductCode() + " ]", serviceCode, customerServiceInfo.getContractStateDesc() == null ? "" : customerServiceInfo.getContractStateDesc(),
                    crmId, customerName, issue.getSubmitTime(),
                    userContact.getName(), userContact.getEmail(), phone, attachementStr,
                    issueDescription);

            Mail mail02 = new Mail();
            String subject = "";
            //台湾区，要增加其他标题内容
            if (isTWArea) {
                subject = EsCloudMailSubject.IssueSubmitMailToConfirmer.getIssueMailSubject(language, crmId, serviceCode + customerName, issueDescription, issue.isNoResponsiblePersion(), hasAttachment);
                if (issue.getIssueSubmitMode() != null) {
                    if (issue.getIssueSubmitMode().equals(IssueSubmitMode.CallMe.toString())) {
                        String issueCategoryDesc = IssueCategory.valueOf(issue.getIssueCategory()).getDescription();
                        subject = EsCloudMailSubject.CallMeIssueSubmitMailToCC.getIssueMailSubject(language, crmId, serviceCode + customerName, issueDescription, issue.isNoResponsiblePersion(), hasAttachment);
                    } else if (issue.getIssueSubmitMode().equals(IssueSubmitMode.CouponUse.toString())) {
                        subject = EsCloudMailSubject.CouponIssueSubmitMailToCC.getCouponIssueMailSubject(language, serviceCode + customerName, issue.getCouponRuleName() == null ? "" : issue.getCouponRuleName());
                    }
                }
            } else {
                //【鼎捷服务云】您有新案件
                subject = EsCloudMailSubject.IssueSubmitMailToConfirmer.getIssueMailSubject(language, hasAttachment);
                if (issue.getIssueSubmitMode() != null && issue.getIssueSubmitMode().equals(IssueSubmitMode.CouponUse.toString())) {
                    subject = EsCloudMailSubject.CouponIssueSubmitMailToCC.getCouponIssueMailSubject(language, serviceCode + customerName, issue.getCouponRuleName() == null ? "" : issue.getCouponRuleName());
                }
            }
            mail02.setSubject(subject);
            mail02.setMessage(mailMsg);
            mail02.setReceivers(receivers02);
            mail02.setMailSourceType(MailSourceType.Issue);
            mail02.setUrl("");
            mail02.setSourceId(Long.toString(issue.getIssueId()));
            mail02.setPriority(1);
            mail02.setLanguage(language);
            mails.add(mail02);
        } catch (Exception ex) {
            ex.printStackTrace();
            log.error(ex);
        }
        Gson gson = new Gson();
        return gson.toJson(mails);
    }

    /**
     * 发送邮件给客服人员 抄送给其他客服、客户、CC
     *
     * @param issue
     * @return
     */
    private String PrepareIssueMailToCustomerService_T(Issue issue) {
        List<Mail> mails = new ArrayList<Mail>();
        try {
            //2. 获取客服mail
            List<String> receivers02 = new ArrayList<String>();
            StaffUserInfo staffUserInfo = userService.GetServiceStaffInfoByUserId(issue.getServiceStaff());//(issue.getUserId());
            receivers02.add(staffUserInfo.getEmail());
            System.out.print("In PrepareIssueMailToCustomerService_T");
            System.out.print(staffUserInfo.getEmail());
            System.out.print("In Staff TimeZone");
            System.out.print(staffUserInfo.getTimeZone());


            //获得语言别
            String language = defaultLanguage;
            //台湾区，客服不管语系是什么，都是默认语系(繁體)
            int userTimeZone = 0; //默认时区
            if (staffUserInfo != null && staffUserInfo.getLanguage() != null && !staffUserInfo.getLanguage().isEmpty()) {
                language = staffUserInfo.getLanguage();
            }
            //获得客服时区
            if (staffUserInfo != null && staffUserInfo.getTimeZone() != null) {
                userTimeZone = getTimeZoneMin(staffUserInfo.getTimeZone());
            }
            //获取客户的维护合约等级
            CustomerServiceInfo customerServiceInfo = new CustomerServiceInfo();
            try {
                customerServiceInfo = customerService.GetCustomerServiceInfo(issue.getServiceCode(), issue.getProductCode());
            } catch (Exception ex) {
                customerServiceInfo = new CustomerServiceInfo();
            }

            //组织发给客服专员的邮件
//            String attachementStr = "无";
            String attachementStr = messageUtils.get("nothing", language);
//            String formatToCCMailStr = ToCCMailStr;
            String formatToCCMailStr = commonMailService.readMailContent("issueMailCustomerServiceReceivesNewT.html", language);
//            if (language.equals("zh-CN")) {
//                formatToCCMailStr = commonMailService.readMailContent("issueMailCustomerServiceReceivesNew.html","zh-CN");
//            } else if (language.equals("zh-TW")) {
//                attachementStr = "無";
////                formatToCCMailStr = ToCCMailStr_TW;
//                formatToCCMailStr = commonMailService.readMailContent("issueMailCustomerServiceReceivesNew.html","zh-TW");
//            } else if (language.equals("zh-MALA")) {
//                attachementStr = "無";
//                formatToCCMailStr = commonMailService.readMailContent("issueMailCustomerServiceReceivesNew.html","zh-MALA");
//            } else if (language.equals("zh-THAI")) {
//                attachementStr = "無";
//                formatToCCMailStr = commonMailService.readMailContent("issueMailCustomerServiceReceivesNew.html","zh-THAI");
//            }

            Long issueId = issue.getIssueId();

            System.out.print("Before PrepareIssueMailToCustomerService_T.GetIssueAttachmentFiles " + issueId);
            //組織案件附件超鏈接
            boolean hasAttachment = false;
            List<IssueAttachmentFile> attachFiles = GetIssueAttachmentFiles(Long.toString(issueId));
            if (attachFiles != null && attachFiles.size() > 0) {
                attachementStr = "";
                hasAttachment = true;
                for (IssueAttachmentFile file : attachFiles) {
                    attachementStr = attachementStr + "  " + "<a href=\"" + issueAttachAddress + "/issueservicesyncapi/api/issues/attach/" + file.getFileId() + "/download\">" + file.getFileName() + "</a>";
                }
            }

            System.out.print("Before PrepareIssueMailToCustomerService_T.GetIssueAttachmentFiles " + issueId);
//            if (issue.getIssueAttachments() != null && issue.getIssueAttachments().size() > 0) {
//                attachementStr = "该案件单含有附件";
//                if (language.equals("zh-TW")) {
//                    attachementStr = "該案件單含有附件";
//                }
//            }
//            String mailMsg = String.format(ToCCMailStr, "", issue.getSubmitTime(), issue.getServiceCode(), issue.getProductCode(),
//                    issue.getProductVersion(), issue.getUserContact().getName(), issue.getUserContact().getEmail(), issue.getUserContact().getPhone01(), attachementStr,
//                    issue.getIssueDescription());
//            String mailMsg = String.format(formatToCCMailStr, "", issue.getSubmitTime(), issue.getServiceCode(), issue.getProductCode(),
//                    issue.getProductVersion(), issue.getUserContact().getName(), issue.getUserContact().getEmail(), issue.getUserContact().getPhone01(), attachementStr,
//                    issue.getIssueDescription());
            UserContact userContact = issue.getUserContact();
            String phone01 = userContact.getPhone01();
            String phone02 = userContact.getPhone02();
            String phone = phone01 == null || phone01.isEmpty() ? phone02 == null || phone02.isEmpty() ? "" : phone02 : phone01 + (phone02 == null || phone02.isEmpty() ? "" : "#" + phone02);
            String crmId = issue.getCrmId();
            String serviceCode = issue.getServiceCode();
            String customerName = customerServiceInfo.getCustomerName();
            String issueDescription = issue.getIssueDescription();
            String mailMsg = String.format(formatToCCMailStr, customerServiceInfo.getProductName() + "[ " + issue.getProductCode() + " ]", serviceCode, customerServiceInfo.getContractStateDesc() == null ? "" : customerServiceInfo.getContractStateDesc(),
                    crmId, customerName, transferTimeZone(issue.getSubmitTime(), userTimeZone).substring(0, 19),
                    userContact.getName(), userContact.getEmail(), phone, issue.getCcUser(), attachementStr,
                    issueDescription);

            Mail mail02 = new Mail();
            String subject = "";
            String languageStandard = messageUtils.getLanguage(language);
            if (org.apache.commons.lang3.StringUtils.equalsIgnoreCase(MessageUtils.ZH_CN_STANDARD, languageStandard)) {
                //【鼎捷服务云】您有新案件
                subject = mailUtils.getIssueMailSubject("IssueSubmitMailToCC", language, hasAttachment);
                if (issue.getIssueSubmitMode() != null && issue.getIssueSubmitMode().equals(IssueSubmitMode.CouponUse.toString())) {
                    subject = mailUtils.getCouponIssueMailSubject("CouponIssueSubmitMailToCC", language, serviceCode + customerName, issue.getCouponRuleName() == null ? "" : issue.getCouponRuleName());
                }
            } else {
                //台湾区，要增加其他标题内容
                subject = mailUtils.getIssueMailSubject("IssueSubmitMailToCC", language, crmId, serviceCode + customerName, issueDescription, issue.isNoResponsiblePersion(), hasAttachment);
                if (issue.getIssueSubmitMode() != null) {
                    if (issue.getIssueSubmitMode().equals(IssueSubmitMode.CallMe.toString())) {
                        subject = mailUtils.getIssueMailSubject("CallMeIssueSubmitMailToCC", language, crmId, serviceCode + customerName, issueDescription, issue.isNoResponsiblePersion(), hasAttachment);
                    } else if (issue.getIssueSubmitMode().equals(IssueSubmitMode.CouponUse.toString())) {
                        subject = mailUtils.getCouponIssueMailSubject("CouponIssueSubmitMailToCC", language, serviceCode + customerName, issue.getCouponRuleName() == null ? "" : issue.getCouponRuleName());
                    }
                }
            }

            //台湾区，要增加其他标题内容
//            if (isTWArea) {
//                subject = EsCloudMailSubject.IssueSubmitMailToCC.getIssueMailSubject(language, crmId, serviceCode + customerName, issueDescription, issue.isNoResponsiblePersion(), hasAttachment);
//                if (issue.getIssueSubmitMode()!=null) {
//                    if (issue.getIssueSubmitMode().equals(IssueSubmitMode.CallMe.toString())) {
//                        String issueCategoryDesc=IssueCategory.valueOf(issue.getIssueCategory()).getDescription();
//                        subject = EsCloudMailSubject.CallMeIssueSubmitMailToCC.getIssueMailSubject(language, crmId, serviceCode + customerName, issueDescription, issue.isNoResponsiblePersion(), hasAttachment);
//                    }
//                    else if (issue.getIssueSubmitMode().equals(IssueSubmitMode.CouponUse.toString())) {
//                        subject = EsCloudMailSubject.CouponIssueSubmitMailToCC.getCouponIssueMailSubject(language, serviceCode + customerName, issue.getCouponRuleName()==null?"":issue.getCouponRuleName());
//                    }
//                }
//            } else {
//                //【鼎捷服务云】您有新案件
//                subject = EsCloudMailSubject.IssueSubmitMailToCC.getIssueMailSubject(language, hasAttachment);
//                if (issue.getIssueSubmitMode()!=null && issue.getIssueSubmitMode().equals(IssueSubmitMode.CouponUse.toString())) {
//                    subject = EsCloudMailSubject.CouponIssueSubmitMailToCC.getCouponIssueMailSubject(language, serviceCode + customerName, issue.getCouponRuleName()==null?"":issue.getCouponRuleName());
//                }
//            }
            mail02.setSubject(subject);
            mail02.setMessage(mailMsg);
            mail02.setReceivers(receivers02);
            //t产品线增加邮件抄送人
            //設置用戶群抄收人
            if ("100".equals(issue.getProductCode()) || "06".equals(issue.getProductCode())) {
                HashSet<String> ccs = new HashSet<>();
                //T的其他顺序的客服要作为抄送人
                if (!StringUtils.isEmpty(issue.getCcGroupUser())) {
                    String[] ccsArray = issue.getCcGroupUser().split(";");
                    if (ccsArray != null) {
                        for (String ccUser : ccsArray) {
                            ccs.add(ccUser);
                        }
                    }
                }
                //T的案件需要增加CC的人作为抄送人
                if (!StringUtils.isEmpty(issue.getCcUser())) {
                    String[] ccsArray = issue.getCcUser().split(";");
                    if (ccsArray != null) {
                        for (String ccUser : ccsArray) {
                            ccs.add(ccUser);
                        }
                    }
                }
                //T的案件需要增加客户作为抄送人
                if (!StringUtils.isEmpty(userContact.getEmail())) {
                    ccs.add(userContact.getEmail());
                }
                List<String> ccMailList = new ArrayList<>(ccs);
                mail02.setCcs(ccMailList);
            }

            mail02.setMailSourceType(MailSourceType.Issue);
            mail02.setUrl("");
            mail02.setSourceId(Long.toString(issue.getIssueId()));
            mail02.setPriority(1);
            mail02.setLanguage(language);
            mails.add(mail02);
        } catch (Exception ex) {
            ex.printStackTrace();
            log.error(ex);
        }
        Gson gson = new Gson();
        return gson.toJson(mails);
    }

    /**
     * 组织发送给客服CC的邮件,抄送给客户，审核后的邮件
     *
     * @param issue
     * @return
     */
    private String PrepareIssueMailToCCANDCustomer(Issue issue) {
        List<Mail> mails = new ArrayList<Mail>();
        try {
            //2. 获取客服mail
            List<String> receivers02 = new ArrayList<String>();
            StaffUserInfo staffUserInfo = userService.GetServiceStaffInfoByUserId(issue.getServiceStaff());//(issue.getUserId());
            receivers02.add(staffUserInfo.getEmail());
            System.out.print("In PrepareIssueMailToCC");
            System.out.print(staffUserInfo.getEmail());
            System.out.print("In Staff TimeZone");
            System.out.print(staffUserInfo.getTimeZone());

            //設置用戶群抄收人
            if (!"100".equals(issue.getProductCode()) && !"06".equals(issue.getProductCode())) {
                String ccGroup = issue.getCcGroupUser();
                if (ccGroup != null && !(ccGroup = ccGroup.trim()).isEmpty()) {
                    String[] list = ccGroup.split(";");
                    for (int i = 0; i < list.length; i++) {
                        receivers02.add(list[i].trim());
                    }
                }
            }


            //获得语言别
            String language = defaultLanguage;
            //台湾区，客服不管语系是什么，都是默认语系(繁體)
            int userTimeZone = 0; //默认时区
            if (staffUserInfo != null && staffUserInfo.getLanguage() != null && !staffUserInfo.getLanguage().isEmpty()) {
                language = staffUserInfo.getLanguage();
            }
            //获得客服时区
            if (staffUserInfo != null && staffUserInfo.getTimeZone() != null) {
                userTimeZone = getTimeZoneMin(staffUserInfo.getTimeZone());
            }
            //获取客户的维护合约等级
            CustomerServiceInfo customerServiceInfo = new CustomerServiceInfo();
            try {
                customerServiceInfo = customerService.GetCustomerServiceInfo(issue.getServiceCode(), issue.getProductCode());
            } catch (Exception ex) {
                customerServiceInfo = new CustomerServiceInfo();
            }

            //组织发给客服专员的邮件
//            String attachementStr = "无";
            String attachementStr = messageUtils.get("nothing", language);
//            String formatToCCMailStr = ToCCMailStr;
            String formatToCCMailStr = commonMailService.readMailContent("issueMailCustomerServiceReceivesNew.html", language);
//            if (language.equals("zh-CN")) {
//                formatToCCMailStr = commonMailService.readMailContent("issueMailCustomerServiceReceivesNew.html","zh-CN");
//            } else if (language.equals("zh-TW")) {
//                attachementStr = "無";
////                formatToCCMailStr = ToCCMailStr_TW;
//                formatToCCMailStr = commonMailService.readMailContent("issueMailCustomerServiceReceivesNew.html","zh-TW");
//            } else if (language.equals("zh-MALA")) {
//                attachementStr = "無";
//                formatToCCMailStr = commonMailService.readMailContent("issueMailCustomerServiceReceivesNew.html","zh-MALA");
//            } else if (language.equals("zh-THAI")) {
//                attachementStr = "無";
//                formatToCCMailStr = commonMailService.readMailContent("issueMailCustomerServiceReceivesNew.html","zh-THAI");
//            }

            Long issueId = issue.getIssueId();

            System.out.print("Before PrepareIssueMailToCC.GetIssueAttachmentFiles " + issueId);
            //組織案件附件超鏈接
            boolean hasAttachment = false;
            List<IssueAttachmentFile> attachFiles = GetIssueAttachmentFiles(Long.toString(issueId));
            if (attachFiles != null && attachFiles.size() > 0) {
                attachementStr = "";
                hasAttachment = true;
                for (IssueAttachmentFile file : attachFiles) {
                    attachementStr = attachementStr + "  " + "<a href=\"" + issueAttachAddress + "/issueservicesyncapi/api/issues/attach/" + file.getFileId() + "/download\">" + file.getFileName() + "</a>";
                }
            }

            System.out.print("Before PrepareIssueMailToCC.GetIssueAttachmentFiles " + issueId);
//            if (issue.getIssueAttachments() != null && issue.getIssueAttachments().size() > 0) {
//                attachementStr = "该案件单含有附件";
//                if (language.equals("zh-TW")) {
//                    attachementStr = "該案件單含有附件";
//                }
//            }
//            String mailMsg = String.format(ToCCMailStr, "", issue.getSubmitTime(), issue.getServiceCode(), issue.getProductCode(),
//                    issue.getProductVersion(), issue.getUserContact().getName(), issue.getUserContact().getEmail(), issue.getUserContact().getPhone01(), attachementStr,
//                    issue.getIssueDescription());
//            String mailMsg = String.format(formatToCCMailStr, "", issue.getSubmitTime(), issue.getServiceCode(), issue.getProductCode(),
//                    issue.getProductVersion(), issue.getUserContact().getName(), issue.getUserContact().getEmail(), issue.getUserContact().getPhone01(), attachementStr,
//                    issue.getIssueDescription());
            UserContact userContact = issue.getUserContact();
            String phone01 = userContact.getPhone01();
            String phone02 = userContact.getPhone02();
            String phone = phone01 == null || phone01.isEmpty() ? phone02 == null || phone02.isEmpty() ? "" : phone02 : phone01 + (phone02 == null || phone02.isEmpty() ? "" : "#" + phone02);
            String crmId = issue.getCrmId();
            String serviceCode = issue.getServiceCode();
            String customerName = customerServiceInfo.getCustomerName();
            String issueDescription = issue.getIssueDescription();
            String mailMsg = String.format(formatToCCMailStr, customerServiceInfo.getProductName() + "[ " + issue.getProductCode() + " ]", serviceCode, customerServiceInfo.getContractStateDesc() == null ? "" : customerServiceInfo.getContractStateDesc(),
                    crmId, customerName, transferTimeZone(issue.getSubmitTime(), userTimeZone).substring(0, 19),
                    userContact.getName(), userContact.getEmail(), phone, StringUtils.isEmpty(issue.getCcUser()) ? "" : issue.getCcUser(), attachementStr,
                    issueDescription);

            Mail mail02 = new Mail();
            String subject = "";
            String languageStandard = messageUtils.getLanguage(language);
            if (org.apache.commons.lang3.StringUtils.equalsIgnoreCase(MessageUtils.ZH_CN_STANDARD, languageStandard)) {
                //【鼎捷服务云】您有新案件
                subject = mailUtils.getIssueMailSubject("IssueSubmitMailToCC", language, hasAttachment);
                if (issue.getIssueSubmitMode() != null && issue.getIssueSubmitMode().equals(IssueSubmitMode.CouponUse.toString())) {
                    subject = mailUtils.getCouponIssueMailSubject("CouponIssueSubmitMailToCC", language, serviceCode + customerName, issue.getCouponRuleName() == null ? "" : issue.getCouponRuleName());
                }
            } else {
                //台湾区，要增加其他标题内容
                subject = mailUtils.getIssueMailSubject("IssueSubmitMailToCC", language, crmId, serviceCode + customerName, issueDescription, issue.isNoResponsiblePersion(), hasAttachment);
                if (issue.getIssueSubmitMode() != null) {
                    if (issue.getIssueSubmitMode().equals(IssueSubmitMode.CallMe.toString())) {
                        subject = mailUtils.getIssueMailSubject("CallMeIssueSubmitMailToCC", language, crmId, serviceCode + customerName, issueDescription, issue.isNoResponsiblePersion(), hasAttachment);
                    } else if (issue.getIssueSubmitMode().equals(IssueSubmitMode.CouponUse.toString())) {
                        subject = mailUtils.getCouponIssueMailSubject("CouponIssueSubmitMailToCC", language, serviceCode + customerName, issue.getCouponRuleName() == null ? "" : issue.getCouponRuleName());
                    }
                }
            }

            //台湾区，要增加其他标题内容
//            if (isTWArea) {
//                subject = EsCloudMailSubject.IssueSubmitMailToCC.getIssueMailSubject(language, crmId, serviceCode + customerName, issueDescription, issue.isNoResponsiblePersion(), hasAttachment);
//                if (issue.getIssueSubmitMode()!=null) {
//                    if (issue.getIssueSubmitMode().equals(IssueSubmitMode.CallMe.toString())) {
//                        String issueCategoryDesc=IssueCategory.valueOf(issue.getIssueCategory()).getDescription();
//                        subject = EsCloudMailSubject.CallMeIssueSubmitMailToCC.getIssueMailSubject(language, crmId, serviceCode + customerName, issueDescription, issue.isNoResponsiblePersion(), hasAttachment);
//                    }
//                    else if (issue.getIssueSubmitMode().equals(IssueSubmitMode.CouponUse.toString())) {
//                        subject = EsCloudMailSubject.CouponIssueSubmitMailToCC.getCouponIssueMailSubject(language, serviceCode + customerName, issue.getCouponRuleName()==null?"":issue.getCouponRuleName());
//                    }
//                }
//            } else {
//                //【鼎捷服务云】您有新案件
//                subject = EsCloudMailSubject.IssueSubmitMailToCC.getIssueMailSubject(language, hasAttachment);
//                if (issue.getIssueSubmitMode()!=null && issue.getIssueSubmitMode().equals(IssueSubmitMode.CouponUse.toString())) {
//                    subject = EsCloudMailSubject.CouponIssueSubmitMailToCC.getCouponIssueMailSubject(language, serviceCode + customerName, issue.getCouponRuleName()==null?"":issue.getCouponRuleName());
//                }
//            }
            mail02.setSubject(subject);
            mail02.setMessage(mailMsg);
            mail02.setReceivers(receivers02);
            //t产品线增加邮件抄送人
            //設置用戶群抄收人
            HashSet<String> ccs = new HashSet<String>();
            if (issue.getUserContact() != null && !StringUtils.isEmpty(issue.getUserContact().getEmail())) {
                ccs.add(issue.getUserContact().getEmail());
            }
            if ("100".equals(issue.getProductCode()) || "06".equals(issue.getProductCode())) {
                if (!StringUtils.isEmpty(issue.getCcGroupUser())) {
                    String[] ccsArray = issue.getCcGroupUser().split(";");
                    if (ccsArray != null) {
                        for (String ccUser : ccsArray) {
                            ccs.add(ccUser);
                        }
                    }
                }
            }
            mail02.setCcs(new ArrayList<>(ccs));
            mail02.setMailSourceType(MailSourceType.Issue);
            mail02.setUrl("");
            mail02.setSourceId(Long.toString(issue.getIssueId()));
            mail02.setPriority(1);
            mail02.setLanguage(language);
            mails.add(mail02);
        } catch (Exception ex) {
            ex.printStackTrace();
            log.error(ex);
        }
        Gson gson = new Gson();
        return gson.toJson(mails);
    }

    /**
     * 组织发送给客服CC的邮件
     *
     * @param issue
     * @return
     */
    private String PrepareIssueMailToCC(Issue issue) {
        List<Mail> mails = new ArrayList<Mail>();
        try {
            //2. 获取客服mail
            List<String> receivers02 = new ArrayList<String>();
            StaffUserInfo staffUserInfo = userService.GetServiceStaffInfoByUserId(issue.getServiceStaff());//(issue.getUserId());
            receivers02.add(staffUserInfo.getEmail());
            System.out.print("In PrepareIssueMailToCC");
            System.out.print(staffUserInfo.getEmail());
            System.out.print("In Staff TimeZone");
            System.out.print(staffUserInfo.getTimeZone());

            //設置用戶群抄收人
            if (!"100".equals(issue.getProductCode()) && !"06".equals(issue.getProductCode())) {
                String ccGroup = issue.getCcGroupUser();
                if (ccGroup != null && !(ccGroup = ccGroup.trim()).isEmpty()) {
                    String[] list = ccGroup.split(";");
                    for (int i = 0; i < list.length; i++) {
                        receivers02.add(list[i].trim());
                    }
                }
            }


            //获得语言别
            String language = defaultLanguage;
            //台湾区，客服不管语系是什么，都是默认语系(繁體)
            int userTimeZone = 0; //默认时区
            if (staffUserInfo != null && staffUserInfo.getLanguage() != null && !staffUserInfo.getLanguage().isEmpty()) {
                language = staffUserInfo.getLanguage();
            }
            //获得客服时区
            if (staffUserInfo != null && staffUserInfo.getTimeZone() != null) {
                userTimeZone = getTimeZoneMin(staffUserInfo.getTimeZone());
            }
            //获取客户的维护合约等级
            CustomerServiceInfo customerServiceInfo = new CustomerServiceInfo();
            try {
                customerServiceInfo = customerService.GetCustomerServiceInfo(issue.getServiceCode(), issue.getProductCode());
            } catch (Exception ex) {
                customerServiceInfo = new CustomerServiceInfo();
            }

            //组织发给客服专员的邮件
//            String attachementStr = "无";
            String attachementStr = messageUtils.get("nothing", language);
//            String formatToCCMailStr = ToCCMailStr;
            String formatToCCMailStr = commonMailService.readMailContent("issueMailCustomerServiceReceivesNew.html", language);
//            if (language.equals("zh-CN")) {
//                formatToCCMailStr = commonMailService.readMailContent("issueMailCustomerServiceReceivesNew.html","zh-CN");
//            } else if (language.equals("zh-TW")) {
//                attachementStr = "無";
////                formatToCCMailStr = ToCCMailStr_TW;
//                formatToCCMailStr = commonMailService.readMailContent("issueMailCustomerServiceReceivesNew.html","zh-TW");
//            } else if (language.equals("zh-MALA")) {
//                attachementStr = "無";
//                formatToCCMailStr = commonMailService.readMailContent("issueMailCustomerServiceReceivesNew.html","zh-MALA");
//            } else if (language.equals("zh-THAI")) {
//                attachementStr = "無";
//                formatToCCMailStr = commonMailService.readMailContent("issueMailCustomerServiceReceivesNew.html","zh-THAI");
//            }

            Long issueId = issue.getIssueId();

            System.out.print("Before PrepareIssueMailToCC.GetIssueAttachmentFiles " + issueId);
            //組織案件附件超鏈接
            boolean hasAttachment = false;
            List<IssueAttachmentFileV3> list = issueDao.getIssueAttachmentsFileV3(String.valueOf(issueId), 0L);
            if (!CollectionUtils.isEmpty(list)) {
                attachementStr = "";
                hasAttachment = true;
                for (IssueAttachmentFileV3 file : list) {
                    attachementStr = attachementStr + "  " + "<a href=\"" + file.getUrl() + "/\">" + file.getFileName() + "</a>";
                }
            }

            System.out.print("Before PrepareIssueMailToCC.GetIssueAttachmentFiles " + issueId);
//            if (issue.getIssueAttachments() != null && issue.getIssueAttachments().size() > 0) {
//                attachementStr = "该案件单含有附件";
//                if (language.equals("zh-TW")) {
//                    attachementStr = "該案件單含有附件";
//                }
//            }
//            String mailMsg = String.format(ToCCMailStr, "", issue.getSubmitTime(), issue.getServiceCode(), issue.getProductCode(),
//                    issue.getProductVersion(), issue.getUserContact().getName(), issue.getUserContact().getEmail(), issue.getUserContact().getPhone01(), attachementStr,
//                    issue.getIssueDescription());
//            String mailMsg = String.format(formatToCCMailStr, "", issue.getSubmitTime(), issue.getServiceCode(), issue.getProductCode(),
//                    issue.getProductVersion(), issue.getUserContact().getName(), issue.getUserContact().getEmail(), issue.getUserContact().getPhone01(), attachementStr,
//                    issue.getIssueDescription());
            UserContact userContact = issue.getUserContact();
            String phone01 = userContact.getPhone01();
            String phone02 = userContact.getPhone02();
            String phone = phone01 == null || phone01.isEmpty() ? phone02 == null || phone02.isEmpty() ? "" : phone02 : phone01 + (phone02 == null || phone02.isEmpty() ? "" : "#" + phone02);
            String crmId = issue.getCrmId();
            String serviceCode = issue.getServiceCode();
            String customerName = customerServiceInfo.getCustomerName();
            String issueDescription = issue.getIssueDescription();
            String mailMsg = String.format(formatToCCMailStr, customerServiceInfo.getProductName() + "[ " + issue.getProductCode() + " ]", serviceCode, customerServiceInfo.getContractStateDesc() == null ? "" : customerServiceInfo.getContractStateDesc(),
                    crmId, customerName, transferTimeZone(issue.getSubmitTime(), userTimeZone).substring(0, 19),
                    userContact.getName(), userContact.getEmail(), phone, StringUtils.isEmpty(issue.getCcUser()) ? "" : issue.getCcUser(), attachementStr,
                    issueDescription);

            Mail mail02 = new Mail();
            String subject = "";
            String languageStandard = messageUtils.getLanguage(language);
            if (org.apache.commons.lang3.StringUtils.equalsIgnoreCase(MessageUtils.ZH_CN_STANDARD, languageStandard)) {
                //【鼎捷服务云】您有新案件 标题调整，如果是紧急案件，需要改成【鼎捷服务云】【紧急】您有新案件
                subject = mailUtils.getIssueMailSubject(issue.isEmergency() ? "IssueSubmitEmergencyMailToCC" : "IssueSubmitMailToCC", language, hasAttachment);
                if (issue.getIssueSubmitMode() != null && issue.getIssueSubmitMode().equals(IssueSubmitMode.CouponUse.toString())) {
                    subject = mailUtils.getCouponIssueMailSubject("CouponIssueSubmitMailToCC", language, serviceCode + customerName, issue.getCouponRuleName() == null ? "" : issue.getCouponRuleName());
                }
                //2021-11-23 huly：访客和体验客户提单 都不同步到crm
                if (visitorServiceCode.equals(issue.getServiceCode())) {
                    subject = "【访客】" + subject;
                } else if (!oldIssueDao.checkCrmCustomer(issue.getServiceCode(), issue.getProductCode())) {
                    subject = "【体验客户】" + subject;
                } else {

                }
                if("147".equals(issue.getProductCode()) || "163".equals(issue.getProductCode()) || "15".equals(issue.getProductCode())) {
                    if (customerServiceInfo != null) {
                        if (customerServiceInfo.isTrial()) {
                            //試用客戶
                            subject = "【试用客户】"+ subject;
                        }
                    }
                }
            } else {
                //台湾区，要增加其他标题内容
                subject = mailUtils.getIssueMailSubject(issue.isEmergency() ? "IssueSubmitEmergencyMailToCC" : "IssueSubmitMailToCC", language, crmId, serviceCode + customerName, issueDescription, issue.isNoResponsiblePersion(), hasAttachment);
                if (issue.getIssueSubmitMode() != null) {
                    if (issue.getIssueSubmitMode().equals(IssueSubmitMode.CallMe.toString())) {
                        subject = mailUtils.getIssueMailSubject(issue.isEmergency() ? "CallMeIssueSubmitEmergencyMailToCC" : "CallMeIssueSubmitMailToCC", language, crmId, serviceCode + customerName, issueDescription, issue.isNoResponsiblePersion(), hasAttachment);
                    } else if (issue.getIssueSubmitMode().equals(IssueSubmitMode.CouponUse.toString())) {
                        subject = mailUtils.getCouponIssueMailSubject(issue.isEmergency() ? "CouponIssueSubmitEmergencyMailToCC" : "CouponIssueSubmitMailToCC", language, serviceCode + customerName, issue.getCouponRuleName() == null ? "" : issue.getCouponRuleName());
                    }
                }
                //2021-11-23 huly：访客和体验客户提单 都不同步到crm
                if (visitorServiceCode.equals(issue.getServiceCode())) {
                    subject = "【訪客】" + subject;
                } else if (!oldIssueDao.checkCrmCustomer(issue.getServiceCode(), issue.getProductCode())) {
                    subject = "【體驗客戶】" + subject;
                } else {

                }
                if("147".equals(issue.getProductCode()) || "163".equals(issue.getProductCode()) || "15".equals(issue.getProductCode())) {
                    if (customerServiceInfo != null) {
                        if (customerServiceInfo.isTrial()) {
                            //試用客戶
                            subject = "【試用客戶】"+ subject;
                        }
                    }
                }
            }

            mail02.setSubject(subject);
            mail02.setMessage(mailMsg);
            mail02.setReceivers(receivers02);
            //t产品线增加邮件抄送人
            //設置用戶群抄收人
            HashSet<String> ccs = new HashSet<String>();
            if ("100".equals(issue.getProductCode()) || "06".equals(issue.getProductCode())) {
                if (!StringUtils.isEmpty(issue.getCcGroupUser())) {
                    String[] ccsArray = issue.getCcGroupUser().split(";");
                    if (ccsArray != null) {
                        for (String ccUser : ccsArray) {
                            ccs.add(ccUser);
                        }
                    }
                }
            }
            mail02.setCcs(new ArrayList<>(ccs));
            mail02.setMailSourceType(MailSourceType.Issue);
            mail02.setUrl("");
            mail02.setSourceId(Long.toString(issue.getIssueId()));
            mail02.setPriority(1);
            mail02.setLanguage(language);
            mails.add(mail02);
        } catch (Exception ex) {
            ex.printStackTrace();
            log.error(ex);
        }
        Gson gson = new Gson();
        return gson.toJson(mails);
    }


    private String PrepareIssueMailToCCForISVTenantNotInServiceCloud(Issue issue, String servicerId, String servicerName) {
        List<Mail> mails = new ArrayList<Mail>();
        try {
            //2. 获取客服mail
            List<String> receivers02 = new ArrayList<String>();
            StaffUserInfo staffUserInfo = userService.GetServiceStaffInfoByUserId(issue.getServiceStaff());//(issue.getUserId());
            receivers02.add(staffUserInfo.getEmail());
            System.out.print("In PrepareIssueMailToCCForISVTenantNotInServiceCloud");
            System.out.print(staffUserInfo.getEmail());
            System.out.print("In Staff TimeZone");
            System.out.print(staffUserInfo.getTimeZone());


            //获得语言别
            String language = defaultLanguage;
            //台湾区，客服不管语系是什么，都是默认语系(繁體)
            int userTimeZone = 0; //默认时区
            if (staffUserInfo != null && staffUserInfo.getLanguage() != null && !staffUserInfo.getLanguage().isEmpty()) {
                language = staffUserInfo.getLanguage();
            }
            //获得客服时区
            if (staffUserInfo != null && staffUserInfo.getTimeZone() != null) {
                userTimeZone = getTimeZoneMin(staffUserInfo.getTimeZone());
            }
            //获取客户的维护合约等级
            CustomerServiceInfo customerServiceInfo = new CustomerServiceInfo();
            try {
                customerServiceInfo = customerService.GetCustomerServiceInfo(issue.getServiceCode(), issue.getProductCode());
            } catch (Exception ex) {
                customerServiceInfo = new CustomerServiceInfo();
            }

            //获取模组应用
            ErpSystemCodeData erpSystemCodeData = new ErpSystemCodeData();
            try {
                String moduleCode = (issue.getIssueCasedetail() != null && !StringUtils.isEmpty(issue.getIssueCasedetail().getErpSystemCode())) ? issue.getIssueCasedetail().getErpSystemCode() : "";
                if(StringUtils.isNotBlank(moduleCode)){
                    erpSystemCodeData = issueDetailDao.selectSystemCodeData(issue.getProductCode(),issue.getIssueCasedetail().getErpSystemCode());
                }
                if(ObjectUtils.isEmpty(erpSystemCodeData)) erpSystemCodeData = new ErpSystemCodeData();
            } catch (Exception ex) {
                erpSystemCodeData = new ErpSystemCodeData();
            }

            //组织发给客服专员的邮件
            String formatToCCMailStr = commonMailService.readMailContent("issueMailCustomerServiceReceivesForISV.html", language);

            String isvTenant= servicerId +"(" + servicerName + ")";
            String crmId = issue.getCrmId();
            String serviceCode = issue.getServiceCode();
            String customerName = customerServiceInfo.getCustomerName();
            String erpSystemCode = StringUtils.isNotBlank(erpSystemCodeData.getErpSystemCode()) ? erpSystemCodeData.getErpSystemCode() : "";
            String erpSystemName = StringUtils.isNotBlank(erpSystemCodeData.getErpSystemName()) ? erpSystemCodeData.getErpSystemName() : "";
            String issueDescription = issue.getIssueDescription();
            String mailMsg = String.format(formatToCCMailStr,isvTenant, crmId, serviceCode+"("+customerName+")",issue.getProductCode() + "-" + customerServiceInfo.getProductName()   ,
                    erpSystemCode + "-" + erpSystemName, transferTimeZone(issue.getSubmitTime(), userTimeZone).substring(0, 19),issueDescription);

            Mail mail02 = new Mail();
            String languageStandard = messageUtils.getLanguage(language);
            String subject = mailUtils.getIssueMailSubjectForISV("IssueSubmitMailToCCForISV", languageStandard, crmId, serviceCode,customerName);

            mail02.setSubject(subject);
            mail02.setMessage(mailMsg);
            mail02.setReceivers(receivers02);
            mail02.setCcs(new ArrayList<>());
            mail02.setMailSourceType(MailSourceType.Issue);
            mail02.setUrl("");
            mail02.setSourceId(Long.toString(issue.getIssueId()));
            mail02.setPriority(1);
            mail02.setLanguage(language);
            mails.add(mail02);
        } catch (Exception ex) {
            ex.printStackTrace();
            log.error(ex);
        }
        Gson gson = new Gson();
        return gson.toJson(mails);
    }
    /**
     * 组织发送跟催给案件提交人的邮件
     *
     * @param issue
     * @return
     */
    private String PrepareIssueFollowUpMail(Issue issue, String dsc) {
        List<Mail> mails = new ArrayList<Mail>();
        try {
            //获得语言别
            String language = defaultLanguage;
            int userTimeZone = 0; //默认时区
            UserPersonalInfo userPersonalInfo = userService.GetUserPersonalInfo(issue.getUserContact().getUserId());//(issue.getUserId());
            if (userPersonalInfo != null && userPersonalInfo.getLanguage() != null && !userPersonalInfo.getLanguage().isEmpty()) {
                language = userPersonalInfo.getLanguage();
                userTimeZone = getTimeZoneMin(userPersonalInfo.getTimeZone());
            }
            //获取客户的维护合约等级
            CustomerServiceInfo customerServiceInfo = new CustomerServiceInfo();
            try {
                customerServiceInfo = customerService.GetCustomerServiceInfo(issue.getServiceCode(), issue.getProductCode());
            } catch (Exception ex) {
                customerServiceInfo = new CustomerServiceInfo();
            }

            //组织发给客服专员的邮件
//            String attachementStr = "无";
//            String attachementStr = messageUtils.get("nothing",language);
//            String formatToSubmiterMailStr = ToSubmiterFollowUpMailStr;
            String formatToSubmiterMailStr = commonMailService.readMailContent("issueMailReminderCustomer.html", language);
//            if (language.equals("zh-CN")) {
//                formatToSubmiterMailStr = iMailService.readMailContent("issueMailReminderCustomer.html","zh-CN");
//            } else if (language.equals("zh-TW")) {
//                attachementStr = "無";
////                formatToSubmiterMailStr = ToSubmiterFollowUpMailStr_TW;
//                formatToSubmiterMailStr = iMailService.readMailContent("issueMailReminderCustomer.html","zh-TW");
//            } else if (language.equals("zh-MALA")) {
//                attachementStr = "無";
//                formatToSubmiterMailStr = iMailService.readMailContent("issueMailReminderCustomer.html","zh-MALA");
//            } else if (language.equals("zh-THAI")) {
//                attachementStr = "無";
//                formatToSubmiterMailStr = iMailService.readMailContent("issueMailReminderCustomer.html","zh-THAI");
//            }

            Long issueId = issue.getIssueId();

            System.out.print("Before PrepareIssueMail.GetIssueAttachmentFiles " + issueId);
            //組織案件附件超鏈接
            String attachementStr = messageUtils.get("nothing", language);
            attachementStr = getAttachementStrV3(issueId, attachementStr);

            System.out.print("After PrepareIssueMail.GetIssueAttachmentFiles " + issueId);

            UserContact userContact = issue.getUserContact();
            String phone01 = userContact.getPhone01();
            String phone02 = userContact.getPhone02();
            String phone = phone01 == null || phone01.isEmpty() ? phone02 == null || phone02.isEmpty() ? "" : phone02 : phone01 + (phone02 == null || phone02.isEmpty() ? "" : "#" + phone02);
            String crmId = issue.getCrmId();
            String serviceCode = issue.getServiceCode();
            String customerName = customerServiceInfo.getCustomerName();
            String issueDescription = issue.getIssueDescription();
            String submitTime = transferTimeZone(issue.getSubmitTime(), userTimeZone);
            if (issue.getSubmitTime().endsWith(".0"))
                submitTime = submitTime.substring(0, submitTime.length() - 2);
            String mailMsg = String.format(formatToSubmiterMailStr, customerServiceInfo.getProductName() + "[ " + issue.getProductCode() + " ]", serviceCode, customerServiceInfo.getContractStateDesc() == null ? "" : customerServiceInfo.getContractStateDesc(),
                    crmId, customerName, submitTime,
                    userContact.getName(), userContact.getEmail(), phone, attachementStr,
                    issueDescription, dsc);

            //1. 获取客户mail
            //组织发送给提交者的邮件
            List<String> receivers01 = new ArrayList<String>();
            receivers01.add(issue.getUserContact().getEmail());
            //add 添加额外的抄送人
            if (issue.getCcUser() != null && !issue.getCcUser().trim().equals("")) {
                String ccUser = issue.getCcUser().trim();
                String[] list = ccUser.split(";");
                for (int i = 0; i < list.length; i++) {
                    receivers01.add(list[i].trim());
                }
            }
            Mail mail01 = new Mail();
            String subject = messageUtils.get("IssueSubmitFollowUpMailToSubmiter", language);
//            subject = EsCloudMailSubject.IssueSubmitFollowUpMailToSubmiter.toString();
            mail01.setSubject(subject);
            mail01.setMessage(mailMsg);

            mail01.setReceivers(receivers01);

            mail01.setMailSourceType(MailSourceType.Issue);
            mail01.setUrl("");
            mail01.setSourceId(Long.toString(issue.getIssueId()));
            mail01.setPriority(1);
            mail01.setLanguage(language);
            mails.add(mail01);

        } catch (Exception ex) {
            log.error(ex);
            ex.printStackTrace();
        }
        Gson gson = new Gson();
        return gson.toJson(mails);
    }

    /**
     * 从mysql查单头的案件
     *
     * @param issueId
     * @param attachementStr
     * @return
     */
    private String getAttachementStrV3(long issueId, String attachementStr) {
        List<IssueAttachmentFileV3> list = issueDao.getIssueAttachmentsFileV3(String.valueOf(issueId), 0L);
        if (!CollectionUtils.isEmpty(list)) {
            attachementStr = "";
            for (IssueAttachmentFileV3 file : list) {
                attachementStr = attachementStr + "  " + "<a href=\"" + file.getUrl() + "/\">" + file.getFileName() + "</a>";
            }
        }
        return attachementStr;
    }

    /**
     * 组织发送给客服CC的邮件
     *
     * @param issue
     * @return
     */
    private String PrepareIssueFollowUpMailToCC(Issue issue, String dsc) {
        List<Mail> mails = new ArrayList<Mail>();
        try {
            //2. 获取客服mail
            String serviceId = GetServiceID(issue.getIssueId());
            List<String> receivers02 = new ArrayList<String>();
            StaffUserInfo staffUserInfo = userService.GetServiceStaffInfoByUserId(serviceId);//(issue.getUserId());
            receivers02.add(staffUserInfo.getEmail());

            //設置用戶群抄收人
//            String ccGroup = issue.getCcGroupUser();
//            if (ccGroup != null && !(ccGroup = ccGroup.trim()).isEmpty()) {
//                String[] list = ccGroup.split(";");
//                for (int i = 0; i < list.length; i++) {
//                    receivers02.add(list[i].trim());
//                }
//            }

            //获得语言别
            String language = defaultLanguage;
            //台湾区，客服不管语系是什么，都是默认语系(繁體)
            int userTimeZone = 0;//默认时区
            if (staffUserInfo != null && staffUserInfo.getLanguage() != null && !staffUserInfo.getLanguage().isEmpty()) {
                language = staffUserInfo.getLanguage();
            }
            //获得客服时区
            if (staffUserInfo != null && staffUserInfo.getTimeZone() != null) {
                userTimeZone = getTimeZoneMin(staffUserInfo.getTimeZone());
            }
            //获取客户的维护合约等级
            CustomerServiceInfo customerServiceInfo = new CustomerServiceInfo();
            try {
                customerServiceInfo = customerService.GetCustomerServiceInfo(issue.getServiceCode(), issue.getProductCode());
            } catch (Exception ex) {
                customerServiceInfo = new CustomerServiceInfo();
            }

            //组织发给客服专员的邮件
//            String attachementStr = "无";
//            String attachementStr = messageUtils.get("nothing",language);
//            String formatToCCMailStr = ToCCFollowUpMailStr;
            String formatToCCMailStr = commonMailService.readMailContent("issueMailReminderCustomerService.html", language);
//            if (language.equals("zh-CN")) {
//                formatToCCMailStr = iMailService.readMailContent("issueMailReminderCustomerService.html","zh-CN");
//            } else if (language.equals("zh-TW")) {
//                attachementStr = "無";
//                //formatToCCMailStr = ToCCFollowUpMailStr_TW;
//                formatToCCMailStr = iMailService.readMailContent("issueMailReminderCustomerService.html","zh-TW");
//            } else if (language.equals("zh-MALA")) {
//                attachementStr = "無";
//                formatToCCMailStr = iMailService.readMailContent("issueMailReminderCustomerService.html","zh-MALA");
//            } else if (language.equals("zh-THAI")) {
//                attachementStr = "無";
//                formatToCCMailStr = iMailService.readMailContent("issueMailReminderCustomerService.html","zh-THAI");
//            }

            Long issueId = issue.getIssueId();

            System.out.print("Before PrepareIssueMailToCC.GetIssueAttachmentFiles " + issueId);
            //組織案件附件超鏈接
            String attachementStr = messageUtils.get("nothing", language);
            attachementStr = getAttachementStrV3(issueId, attachementStr);

            System.out.print("Before PrepareIssueMailToCC.GetIssueAttachmentFiles " + issueId);

            UserContact userContact = issue.getUserContact();
            String phone01 = userContact.getPhone01();
            String phone02 = userContact.getPhone02();
            String phone = phone01 == null || phone01.isEmpty() ? phone02 == null || phone02.isEmpty() ? "" : phone02 : phone01 + (phone02 == null || phone02.isEmpty() ? "" : "#" + phone02);
            String crmId = issue.getCrmId();
            String serviceCode = issue.getServiceCode();
            String customerName = customerServiceInfo.getCustomerName();
            String issueDescription = issue.getIssueDescription();
            String submitTime = transferTimeZone(issue.getSubmitTime(), userTimeZone);
            if (issue.getSubmitTime().endsWith(".0"))
                submitTime = submitTime.substring(0, submitTime.length() - 2);
            String mailMsg = String.format(formatToCCMailStr, customerServiceInfo.getProductName() + "[ " + issue.getProductCode() + " ]", serviceCode, customerServiceInfo.getContractStateDesc() == null ? "" : customerServiceInfo.getContractStateDesc(),
                    crmId, customerName, submitTime,
                    userContact.getName(), userContact.getEmail(), phone, attachementStr,
                    issueDescription, dsc);

            Mail mail02 = new Mail();
            String subject = messageUtils.get("IssueSubmitFollowUpMailToCC", language);
//            subject = EsCloudMailSubject.IssueSubmitFollowUpMailToCC.toString();

            mail02.setSubject(subject);
            mail02.setMessage(mailMsg);
            mail02.setReceivers(receivers02);
            mail02.setMailSourceType(MailSourceType.Issue);
            mail02.setUrl("");
            mail02.setSourceId(Long.toString(issue.getIssueId()));
            mail02.setPriority(1);
            mail02.setLanguage(language);
            mails.add(mail02);
        } catch (Exception ex) {
            ex.printStackTrace();
            log.error(ex);
        }
        Gson gson = new Gson();
        return gson.toJson(mails);
    }


    /**
     * 获取案件的处理进度
     *
     * @param issueId 案件Id
     * @return
     */
    @Override
    public Issue GetIssueProgress(String issueId, String userId, boolean isOpenToUser) {
        Issue issue = issueDao.SelectIssueByIssueId(issueId);
        if (issue != null){ //huly: 修复漏洞/bug 增加非空判断
            IssueCasedetail issueCasedetail = issueDao.SelectIssueCasedetail(issueId);
            if("CN".equals(connectArea) && Constants.getTProductCodes().contains(issue.getProductCode()) && issueCasedetail != null){
                //返回关联的案件单号
                /*List<String> subCrmIds = issueProcessMapper.querySubCrmIds(issue.getCrmId());
                String parentCrmId = issueProcessMapper.queryParentCrmId(issue.getCrmId());*/
                List<RelateIssue> relateIssues = issueProcessMapper.queryRelateIssue(issue.getCrmId());
                issueCasedetail.setRelateIssue(relateIssues);
                if(CollectionUtils.isEmpty(relateIssues)){
                    issue.setCanSubmitSubIssue(true);
                }
                //判断是否显示抛转187相关的按钮
                if(post187Prerequisite.needToSend(Long.valueOf(issueId), "N")){
                    issue.setShowSyncTo187Status(true);
                }
            }
            if(issue.isShowSyncTo187Status()){
                issue.setIssueProgresses(issueDao.SelectIssueProgressForT(issueId, userId, isOpenToUser));//里面包含单身附件.187同步是否成功
            }else {
                issue.setIssueProgresses(issueDao.SelectIssueProgress(issueId, userId, isOpenToUser));//里面包含单身附件
            }
            issue.setIssueCasedetail(issueCasedetail);

            issue.setIssueAdditionalExplanation(issueDao.SelectIssueAdditionalExplanation(issueId));
            issue.setIssueSummary(issueDao.getIssueSummary(issueId));
            issue.setIssueAttachmentsFileV3(issueDao.getIssueAttachmentsFileV3(issueId, 0L));//获取单头附件
            issue.setIssueKbshare(issueDao.SelectIssueKbshare(issueId));
            com.digiwin.escloud.issueservice.model.BaseResponse baseResponse = tagService.getTagMapping(issueId, "issue");
            if (baseResponse.getStatus() == 200) {
                issue.setTags((List) baseResponse.getResponse());
            }
        }

        return issue;
    }

//    private SimpleResponse EvaluationGrade(String evaluationgradestr) {
//        return restTemplate.postForObject("http://evaluationservice/evaluationgrade", evaluationgradestr, SimpleResponse.class);
//    }


    /**
     * 审核案件
     *
     * @param issueId
     * @param userId
     * @return
     */
    @Override
    public boolean ConfirmIssue(long issueId, String userId, String desc, String deptId) {
        boolean result = SetIssueStatus(issueId, "", IssueStatus.Submited, IssueStatus.Confirming,
                IssueProcessType.Confirm, userId, desc, deptId, false);
        if (result) {
            //审核通过,同步状态更新为N；退回，同步状态更新为
            issueDao.UpdateSyncStatusByIssueId(issueId, SyncStatus.UnSync.toString());
            //7.发送邮件和通知给客服
            try {
                Issue issue = GetIssueProgress(String.valueOf(issueId), userId, true);
                try {
                    StaffUserInfo staffUserInfo = new StaffUserInfo();
                    String moduleCode = (issue.getIssueCasedetail() != null && !StringUtils.isEmpty(issue.getIssueCasedetail().getErpSystemCode())) ? issue.getIssueCasedetail().getErpSystemCode() : "";
                    ServiceStaffGetResponse serviceStaffGetResponse = userService.GetServiceStaffInfo(
                            issue.getUserId(), issue.getServiceCode(), issue.getProductCode() == null ? "" : issue.getProductCode()
                            , issue.getServiceRegion() == null ? "" : issue.getServiceRegion(), issue.getIssueDescription(), issue.getIssueClassificationNo() == null ? "" : issue.getIssueClassificationNo()
                            , issue.getSubmitWay() == null ? "" : issue.getSubmitWay(), moduleCode,issue.getUserId() == null?"":issue.getUserId()
                    );
                    System.out.print(serviceStaffGetResponse);
                    if (serviceStaffGetResponse != null && serviceStaffGetResponse.getCode().equals("0")) {
                        staffUserInfo = serviceStaffGetResponse.getStaffUserInfo();
                        //staffUserInfo = userService.GetServiceStaffInfo(issue.getServiceCode(), issue.getProductCode() == null ? "" : issue.getProductCode());
                        //设置客户群抄收人
                        issue.setCcGroupUser(serviceStaffGetResponse.getMailCC());
                        if (staffUserInfo != null) {
                            issue.setServiceStaff(staffUserInfo.getUserId());
                            issue.setDepartment(staffUserInfo.getDepartment());
                            /*//重新设置客户群抄收人
                            if("100".equals(issue.getProductCode()) || "06".equals(issue.getProductCode())){
                                issue.setCcGroupUser(staffUserInfo.getService_cc_staff_emails());
                            }*/
                        }
                        //设置是否没有责任人员
                        issue.setNoResponsiblePersion(serviceStaffGetResponse.isNoResponsiblePerson());
                    }
                } catch (Exception ex) {
                    ex.printStackTrace();
                }
                issueDao.UpdateIssue(issueId, issue.getCrmId(), issue.getServiceStaff(), issue.getDepartment(), IssueStatus.Submited.toString(), Integer.parseInt(issue.getUserContactId()));
                //云管家打通T服务云，需要存下面3个字段
                IssueProgress issueProgress = new IssueProgress();
                issueProgress.setIssueId(issueId);
                issueProgress.setHandlerId(issue.getServiceStaff());
                issueProgress.setCurrentStatus(IssueStatus.Submited.toString());
                issueProgress.setProcessType(IssueProcessType.Confirm.toString());
                issueProgress.setSequeceNum(issueDao.getMaxOrder(issue.getIssueId()));
                issueDao.UpdateIssueProgress(issueProgress);
                SendNoticeToCCANDCustomer(issue);
            } catch (Exception ex) {
                ex.printStackTrace();
            }
        }

        return result;
    }

    /**
     * 设置案件状态，并保存案件处理进度
     *
     * @param issueId
     * @param crmId
     * @param issueStatus
     * @param userId
     * @param desc
     */
    private boolean SetIssueStatus(long issueId, String crmId, IssueStatus issueStatus, IssueStatus oldIssueStatus,
                                   IssueProcessType issueProcessType, String userId, String desc,
                                   String deptId, boolean issueProgressNeedSync) {
        boolean res = false;
        try {
            int success = 0;
            //如果走审核的话，是mis人员在操作，sql中不需要拼上userid、部门的条件
            if (IssueStatus.Confirming.toString().equals(oldIssueStatus.toString())) {
                success = issueDao.UpdateIssueStatusByStaff(issueId, issueStatus.toString(), oldIssueStatus.toString(), deptId, userId);
            } else {
                success = issueDao.UpdateIssueStatus(issueId, issueStatus.toString(), oldIssueStatus.toString(), deptId, userId);
            }
            if (success > 0) {
                SaveIssueProgress(issueId, crmId, issueProcessType, userId, desc, issueProgressNeedSync);
                //云管家打通T服务云，需要存下面3个字段
                Issue issueTmp = GetIssueProgress(String.valueOf(issueId), userId, true);
                IssueProgress issueProgress = new IssueProgress();
                issueProgress.setIssueId(issueId);
                issueProgress.setCurrentStatus(issueStatus.toString());
                issueProgress.setProcessType(issueProcessType.toString());
                issueProgress.setSequeceNum(issueDao.getMaxOrder(issueId));
                issueDao.UpdateIssueProgress(issueProgress);
                res = true;
            } else
                res = false;
        } catch (Exception ex) {
            res = false;
            log.error(ex);
            ex.printStackTrace();
        }
        return res;
    }

    private boolean SaveIssueProgress(long issueId, String crmId, IssueProcessType issueProcessType, String userId,
                                      String desc, boolean issueProgressNeedSync) {
        try {
            DateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            IssueProgress issueProgress = new IssueProgress();
            issueProgress.setProcessType(issueProcessType.toString());
            issueProgress.setProcessor(userId);
            issueProgress.setDescription(desc);
            issueProgress.setProcessTime(sdf.format(new Date()));
            issueProgress.setProcessHours(0);
            if (issueProgressNeedSync) {
                issueProgress.setSyncStatus("T");
            }
            issueProgress.setSequeceNum(issueDao.getMaxOrder(issueId));
            return issueDao.InsertIssueProgress(issueId, crmId, issueProgress) > 0;
        } catch (Exception ex) {
            log.error(ex);
            ex.printStackTrace();
            return false;
        }
    }


    /**
     * 退回
     *
     * @param issueId
     * @param userId
     * @param desc
     * @return
     */
    @Override
    public boolean SendBackIssue(long issueId, String userId, String desc, String deptId) {
        boolean result = SetIssueStatus(issueId, "", IssueStatus.Returned, IssueStatus.Confirming,
                IssueProcessType.SendBack, userId, desc, deptId, false);
        if (result) {
            //审核通过,同步状态更新为N；退回，同步状态更新为
            issueDao.UpdateSyncStatusByIssueId(issueId, SyncStatus.DontNeedSync.toString());
            //7.发送邮件给客户，案件被退回
            try {
                Issue issue = GetIssueProgress(String.valueOf(issueId), userId, true);

                //云管家打通T服务云，需要存下面3个字段
                IssueProgress issueProgress = new IssueProgress();
                issueProgress.setIssueId(issueId);
                issueProgress.setHandlerId("");//退回时 ，但是的handlerid为空
                issueProgress.setCurrentStatus(IssueStatus.Returned.toString());
                issueProgress.setProcessType(IssueProcessType.SendBack.toString());
                issueProgress.setSequeceNum(issueDao.getMaxOrder(issue.getIssueId()));
                issueDao.UpdateIssueProgress(issueProgress);

                SendBackMailToSubmiter(issue);
            } catch (Exception ex) {
                ex.printStackTrace();
            }

        }
        return result;
    }

    /**
     * 评价案件
     *
     * @param issueId
     * @param userId
     * @param desc
     * @param deptId
     * @return
     */
    @Override
    public boolean EvaluateIssue(long issueId, String userId, String desc, String deptId) {
        return SetIssueStatus(issueId, "", IssueStatus.Evaluated, IssueStatus.Closed,
                IssueProcessType.Evaluate, userId, desc, deptId, false);
    }


    @Override
    public List<Map<String, String>> getWebIssueClassification(String serviceRegion, String customerServiceCode, String productCode) {
        return issueDao.getWebIssueClassification(serviceRegion, customerServiceCode, productCode);
    }

    @Override
    public List<ServiceProductCount> getServiceProductTarget(String serviceRegion, String staffId, String refreshDate) {
        return issueDao.getServiceProductTarget(serviceRegion, staffId, refreshDate);
    }


    /**
     * 查看图片
     *
     * @param fileId
     * @return
     */
    @Override
    public GridFSDBFile GetIssueAttachFile(String fileId) {
        return gridFsTemplate.findOne(Query.query(Criteria.where("_id").is(fileId)));
    }

    /**
     * 获取案件附件文件列表
     *
     * @param issueId
     * @return
     */
    @Override
    public List<IssueAttachmentFile> GetIssueAttachmentFiles(String issueId) {
        List<IssueAttachmentFile> issueAttachmentFiles = new ArrayList<IssueAttachmentFile>();
        try {
            String regex = "^issue__" + issueId + "__";
            System.out.print("find attachmentFiles regex:" + regex);
            List<GridFSDBFile> gridFSDBFiles = gridFsTemplate.find(Query.query(Criteria.where("filename").regex(regex)));
            StringBuilder sbLog = new StringBuilder();
            if (gridFSDBFiles != null && gridFSDBFiles.size() > 0) {
                for (GridFSDBFile file : gridFSDBFiles) {
                    sbLog.setLength(0);
                    IssueAttachmentFile issueAttachmentFile = new IssueAttachmentFile();
                    issueAttachmentFile.setFileId(file.getId().toString());
                    issueAttachmentFile.setFileName(file.getFilename());
                    issueAttachmentFile.setFileType(file.getMetaData().get("fileType") != null ? file.getMetaData().get("fileType").toString() : file.getFilename().substring(file.getFilename().lastIndexOf(".")));
                    issueAttachmentFile.setUrl(issueAttachAddress + "/issueservicesyncapi/api/issues/attach/"+file.getId().toString());
                    issueAttachmentFiles.add(issueAttachmentFile);
                    sbLog.append("found ");
                    sbLog.append(issueAttachmentFile.getFileName());
                    sbLog.append(" fileSize:");
                    sbLog.append(file.getLength());
                    sbLog.append(" chunkSize:");
                    sbLog.append(file.getChunkSize());
                    sbLog.append("\n");
                    System.out.print(sbLog.toString());
                }
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return issueAttachmentFiles;
    }


    @Override
    public void dealTFile(){
        //将T产品线2024-01-01开始的案件以及2024-01-01未结案的案件查出来
        try{
            List<Long> issueIds = issueProcessMapper.queryTIssues();
            if(CollectionUtil.isNotEmpty(issueIds)){
                log.info("本次处理T附件，开始时间：" +  LocalTime.now());
                issueIds.forEach(k->{
                    List<IssueAttachmentFile> files = GetIssueAttachmentFiles(String.valueOf(k));
                    if(CollectionUtil.isNotEmpty(files)){
                        files.forEach(file->{
                            //存到mysql
                            IssueAttachmentFileV3 issueAttachmentFileV3 = new IssueAttachmentFileV3();
                            issueAttachmentFileV3.setIssueId(k);
                            issueAttachmentFileV3.setProgressId(0L);
                            issueAttachmentFileV3.setFileId(file.getFileId());
                            issueAttachmentFileV3.setFileName(file.getFileName());
                            issueAttachmentFileV3.setFileType(file.getFileType());
                            issueAttachmentFileV3.setUrl(file.getUrl());

                            try{
                                issueDao.saveAttachmentFile(issueAttachmentFileV3);
                            }catch (Exception e){
                                log.error(k +" :dealTFile error:"+ e.toString());
                            }

                        });
                    }

                });
                log.info("本次处理T附件完成，完成时间：" + LocalTime.now());
            }
        }catch (Exception e){
            log.error("dealTFile error："+ e.toString());
        }

    }
    /**
     * 获取案件附件
     *
     * @param issueId
     * @return
     */
    @Override
    public List<IssueAttachment> GetIssueAttachments(String issueId) {
        List<IssueAttachment> issueAttachments = new ArrayList<IssueAttachment>();
        try {
            List<GridFSDBFile> gridFSDBFiles = gridFsTemplate.find(Query.query(Criteria.where("filename").regex("^issue__" + issueId + "__")));
            if (gridFSDBFiles != null && gridFSDBFiles.size() > 0) {
                int i = 1;
                for (GridFSDBFile file : gridFSDBFiles) {
                    ByteArrayOutputStream output = new ByteArrayOutputStream();
                    try {
                        copy(file.getInputStream(), output);
                        IssueAttachment issueAttachment = new IssueAttachment();
                        issueAttachment.setSequeceNum(i);
                        issueAttachment.setAttachment(output.toByteArray());
                        String fileName = file.getFilename();
                        int lastDotIndex;
                        //处理案件后缀，优先从fileName抓取
                        if (org.apache.commons.lang.StringUtils.isNotBlank(fileName) &&
                                (lastDotIndex = fileName.lastIndexOf(".")) > -1) {
                            issueAttachment.setFileType(fileName.substring(lastDotIndex, fileName.length()));
                        } else {
                            //兼容旧逻辑
                            issueAttachment.setFileType(file.getContentType());
                        }
//                        issueAttachment.setFileId(file.getId().toString());
//                        issueAttachment.setFileName(file.getFilename());
                        issueAttachments.add(issueAttachment);
                        i++;
                    } catch (Exception e) {
                        e.printStackTrace();
                    } finally {
                        output = null;
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return issueAttachments;
    }


    @Override
    public List<String> getMachineRegionByStaffId(String staffId) {
        return issueDao.getMachineRegionByStaffId(staffId);
    }

    private int getTimeZoneMin(String utimezone) {
        //将时区转成分钟
        int TimeZoneH = 0;
        int TimeZoneM = 0;
        int userTimeZone = 0;
        if (StringUtils.isNotBlank(utimezone)) { //huly: 修复漏洞/bug  StringUtils.isNotBlank
            try {
                String[] list = utimezone.split(":");
                if (list.length > 1) {
                    TimeZoneH = Integer.valueOf(list[0]);
                    TimeZoneM = Integer.valueOf(list[1]);
                    userTimeZone = TimeZoneH * 60 + TimeZoneM;
                }
            } catch (Exception e) {
                e.printStackTrace();

            }
        }
        return userTimeZone;

    }

    /**
     * 转换时区
     *
     * @param srcTime
     * @param userTimezone
     * @return
     */
    @Override
    public String transferTimeZone(String srcTime, int userTimezone) {
        int serverTimezone = getTimeZoneMin(servertimezone); //server时区转成分钟
        if (serverTimezone == userTimezone || userTimezone == 0) {
            return srcTime;
        } else {
            try {
                int timezone = serverTimezone - userTimezone;
                DateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                Date date = sdf.parse(srcTime);

                Calendar calendar = Calendar.getInstance();
                calendar.setTime(date);
                calendar.set(Calendar.MINUTE,
                        calendar.get(Calendar.MINUTE) - timezone); // 加減時區
                return new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(calendar.getTime());

            } catch (Exception e) {
                e.printStackTrace();
                return srcTime;
            }
        }


    }

    /*將call-centeer內容標示為已讀
     * */
    @Override
    public int UpdateAdditionalExplanationReadType(String issueId) {
        return issueDao.UpdateAdditionalExplanationReadType(issueId);
    }

    /*储存子单头表issue_summary */
    @Override
    public int saveIssueSummary(String issueId, String type, String processTime) {
        IssueSummary is = issueDao.getIssueSummary(issueId);
        int result = 0;
        if (is != null) {
            //已存在則修改更新
            result = issueDao.updateIssueSummary(issueId, type, processTime);
            //判断已解决若未有已接单则更新已接单时间
            if (result == 1 && type.equals("resolved") && is.getAcceptTime() == null) {
                issueDao.updateIssueSummary(issueId, "accept", processTime);
            }
            //判断結案若未有已解決则更新已解決时间
            if (result == 1 && type.equals("closed") && is.getResolvedTime() == null) {
                issueDao.updateIssueSummary(issueId, "resolved", processTime);
            }
        } else {
            //不存在则新增一笔至子单头表
            result = issueDao.insertIssueSummary(issueId, type, processTime);
            if (result == 1 && type.equals("resolved")) {
                issueDao.updateIssueSummary(issueId, "accept", processTime);
            }
            //判断結案若未有已解決则更新已解決时间
            if (result == 1 && type.equals("closed")) {
                issueDao.updateIssueSummary(issueId, "accept", processTime);
                issueDao.updateIssueSummary(issueId, "resolved", processTime);
            }
        }
        return result;
    }

    /*查询子单头表issue_summary */
    @Override
    public IssueSummary queryIssueSummary(String issueId) {
        return issueDao.getIssueSummary(issueId);
    }


    @Override
    public boolean CheckCustomerContract(String serviceCode, String productCode) {
        try {
            CustomerServiceInfo customerServiceInfo = new CustomerServiceInfo();
            //获取客户的维护合约信息
            customerServiceInfo = customerService.GetCustomerServiceInfo(serviceCode, productCode);
            //判別試用狀態與合約日期
            if (customerServiceInfo != null) {
                //判別合約日期是否到期，是則表示無合約不可提交，否則判別是否為試用，是則繼續，否則表示為正式用戶
                if (customerServiceInfo.getContractStartDate() != null && !"".equals(customerServiceInfo.getContractStartDate()) && customerServiceInfo.getContractExprityDate() != null && !"".equals(customerServiceInfo.getContractExprityDate())) {
                    Date nowDate = new Date();
                    SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMdd");
                    String nowDateStr = dateFormat.format(nowDate);
                    if (nowDateStr.compareTo(customerServiceInfo.getContractStartDate()) >= 0 && nowDateStr.compareTo(customerServiceInfo.getContractExprityDate()) <= 0) {
                        //合約未到期
                        return true;
                    } else {
                        //合約到期
                        log.info("客戶合約到期，serviceCode:" + serviceCode + "  productCode:" + productCode);
                        return false;
                    }
                } else {
                    //無合約截止日期，則表示無合約或合約到期
                    log.info("找不到客戶合約日期訊息，serviceCode:" + serviceCode + "  productCode:" + productCode);
                    return false;
                }
            } else {
                log.info("找不到客戶合約訊息，serviceCode:" + serviceCode + "  productCode:" + productCode);
                return false;
            }
        } catch (Exception ex) {
            log.error(ex);
            return false;
        }
    }

    /**
     * 存儲預警項編號
     *
     * @param issue
     */
    @Override
    public void batchInsertWarningId(Issue issue) {
        issueDao.batchInsertWarningId(issue.getIssueId(), issue.getCrmId(), issue.getWarningIdCollection());
    }


    /**
     * 更新預警項
     *
     * @param issue
     */
    @Override
    public void updateWarningNoticeStatus(Issue issue) {
        //更新預警編號, 與立案編號的關係
        issueDao.updateWarningIdGroup(issue.getIssueId(), issue.getCrmId());

        if(issue.getSubmitWay().startsWith(IssueSubmitMode.AIOSSM.toString()) || issue.getSubmitWay().equals(IssueSubmitMode.AIEOM.toString())) {
            //處理跟這個 ISSUE 有關的 預警編號.
            List<String> data = Optional.ofNullable(issueDao.getWarningIdByCrmId(issue.getCrmId())).orElse(new ArrayList<>());
            data.forEach(warningkey -> this.updateWarningNoticeStatus_bigData(warningkey, issue.getIssueStatus(), issue.getCrmId()));
        } else if(issue.getSubmitWay().equals(IssueSubmitMode.ITMS_Service.toString()) || issue.getSubmitWay().equals(IssueSubmitMode.ITMS.toString())){
            //兼容舊版
            this.updateWarningNoticeStatus(issue.getWarningId(), issue.getIssueStatus(), issue.getCrmId());

            //處理跟這個 ISSUE 有關的 預警編號.
            List<String> data = Optional.ofNullable(issueDao.getWarningIdByCrmId(issue.getCrmId())).orElse(new ArrayList<>());
            data.forEach(warningId -> this.updateWarningNoticeStatus(warningId, issue.getIssueStatus(), issue.getCrmId()));
        }

    }

    /**
     * 更新預警項
     *
     * @param crmId
     */
    public void updateWarningNoticeStatus(String crmId) {
        Optional.ofNullable(issueSyncDao.SelectIssue(crmId)).ifPresent(issue -> this.updateWarningNoticeStatus(issue));
    }

    /**
     * 更新預警項
     *
     * @param warningId
     * @param issueStatus
     */
    private void updateWarningNoticeStatus(String warningId, String issueStatus, String crmId) {
        warningId = Optional.ofNullable(warningId).orElse("");
        if (warningId.length() == 0) {
            return;
        } else {
            String url = "http://esclientservice/warning/machine/notice/process/status";
            Map<String, String> m = new HashMap<>();
            m.put("warningNotificationId", warningId);

            if (IssueStatus.Closed.isSame(issueStatus)) {
                m.put("processStatus", "closeIssue");
            } else {
                m.put("processStatus", "createIssue");
            }
            m.put("remark", "");
            m.put("crmId", crmId);
            restTemplate.patchForObject(url, Arrays.asList(m), Void.class);
        }
    }

    /**
     * 更新預警項
     * @param warningItemkey
     * @param issueStatus
     * @param crmId
     */
    private void updateWarningNoticeStatus_bigData(String warningItemkey, String issueStatus, String crmId) {
        warningItemkey = Optional.ofNullable(warningItemkey).orElse("");
        if (warningItemkey.length() == 0) {
            return;
        } else {
            //"https://ddp-dataapi.digiwincloud.com.cn/hbase/save";
            String url = bigDataUrl+"hbase/save";

            HashMap<String, Object> map = new HashMap<>();
            map.put("tableName", "warning");
            map.put("rowKey", warningItemkey);
            map.put("cf", "info");
            HashMap<String, Object> dataMap = new HashMap<>();
            if (IssueStatus.Closed.isSame(issueStatus)) {
                dataMap.put("status", "solved");
            } else {
                dataMap.put("status", "createIssue");
            }
            dataMap.put("crmId", crmId);
            map.put("data", dataMap);
            RestTemplate restTemplate = new RestTemplate();
            restTemplate.postForObject(url, map, HashMap.class);
        }
    }

    /**
     * 更新事件
     *
     * @param issue
     */


    public void updateEventNoticeStatus(Issue issue) {
        //更新事件編號, 與立案編號的關係
        //處理跟這個 ISSUE 有關的 事件編號.
        List<String> data = Optional.ofNullable(issueDao.getEdrEventIdByIssueId(issue.getIssueId())).orElse(new ArrayList<>());
        data.forEach(eventId_serverId -> this.updateEventNoticeStatus(eventId_serverId, issue.getIssueStatus(), issue.getCrmId(),issue.getServiceCode(),defaultSid));
    }

    /**
     * 更新事件
     * @param eventId_serverId
     * @param issueStatus
     * @param crmId
     */
    private void updateEventNoticeStatus(String eventId_serverId, String issueStatus, String crmId, String serviceCode, long sid) {
        eventId_serverId = Optional.ofNullable(eventId_serverId).orElse("");
        if (eventId_serverId.length() == 0) {
            return;
        } else {
            // 因立案可以是不同的客服代號，所以serverId不一定等於serviceCode對應的serverId，所以調整所傳的事件Id有包含其serviceId，並以_做區分(evendId_serviceId)
            // 藉由servcieCode找出serviceId
            String[] list = eventId_serverId.split("_");
            String eventId = "";
            String serverId ="";
            if(list.length>1)
            {
                eventId = list[0];
                serverId =  list[1];
            }
            // 藉由servcieCode找出serviceId
            //String serverId = issueDao.getServerId(aioDBName,serviceCode, sid);
            if(!StringUtils.isEmpty(serverId)) {
                //藉由serviceId、eventId找出id，若沒有就要產生id
                Long id = issueDao.getIdByEventId(aioDBName,serverId, eventId, sid);
                if (null == id) {
                    id = SnowFlake.getInstance().newId();
                }
                Map<String, Object> map = new HashMap<String, Object>();
                map.put("aioDBName", aioDBName);
                map.put("id", id);
                if (IssueStatus.Closed.isSame(issueStatus)) {
                    map.put("status", "solved");
                } else {
                    map.put("status", "createIssue");
                }
                map.put("sid", sid);
                map.put("serverId", serverId);
                map.put("eventId", eventId);
                map.put("crmId", crmId);

                issueDao.insertEventIssueStatus(map);
                issueDao.updateEventDetailIssueStatus(map);
            }
            else {
                log.error("serviceId is empty from eventId_serverId:"+eventId_serverId+" sid:"+sid);
            }
        }
    }
    /*@Override
    public boolean updateIssueService(String userId, String issueId, String serviceId,String productCode) {
        String issueSyncStatus = issueDao.getIssueSyncStatus(issueId);
        issueSyncStatus=SyncStatus.SyncSuccess.toString().equals(issueSyncStatus)||SyncStatus.WaitSync.toString().equals(issueSyncStatus)?SyncStatus.EditUnSync.toString():issueSyncStatus;
        StaffUserInfo staffUserInfo = userService.GetServiceStaffInfoByUserId(serviceId);
        boolean result = issueDao.updateIssueService(issueId, serviceId,issueSyncStatus, staffUserInfo.getDepartment(), productCode);

        if(result){
            try {
                Issue oldIssue = GetIssueProgress(issueId,userId);
                String[] mx = {PrepareIssueMailToNewCC(userId, oldIssue)};
                simpleSend(MessageDestination.MAILMESSAGEDESTINATION, mx);
            } catch (Exception ex) {
                log.error(ex.toString());
            }

        }
        return result;
    }*/

    /**
     * 组织发送给新客服CC的邮件（变更处理人）
     *
     * @param issue
     * @return
     */
    public String PrepareIssueMailToCCByISVTurn(Issue issue) {
        List<Mail> mails = new ArrayList<Mail>();
        try {
            //2. 获取客服mail
            List<String> receivers02 = new ArrayList<String>();
            StaffUserInfo staffUserInfo = userService.GetServiceStaffInfoByUserId(issue.getServiceId());//(issue.getUserId());
            receivers02.add(staffUserInfo.getEmail());
            System.out.print("In PrepareIssueMailToNewCC");
            System.out.print(staffUserInfo.getEmail());
            System.out.print("In Staff TimeZone");
            System.out.print(staffUserInfo.getTimeZone());

            //获得语言别
            String language = defaultLanguage;
            //台湾区，客服不管语系是什么，都是默认语系(繁體)
            int userTimeZone = 0; //默认时区
            if (staffUserInfo != null && staffUserInfo.getLanguage() != null && !staffUserInfo.getLanguage().isEmpty()) {
                language = staffUserInfo.getLanguage();
            }
            //获得客服时区
            if (staffUserInfo != null && staffUserInfo.getTimeZone() != null) {
                userTimeZone = getTimeZoneMin(staffUserInfo.getTimeZone());
            }
            //获取客户的维护合约等级
            CustomerServiceInfo customerServiceInfo = new CustomerServiceInfo();
            try {
                customerServiceInfo = customerService.GetCustomerServiceInfo(issue.getServiceCode(), issue.getProductCode());
            } catch (Exception ex) {
                customerServiceInfo = new CustomerServiceInfo();
            }

            //组织发给客服专员的邮件
//            String attachementStr = "无";
//            String attachementStr = messageUtils.get("nothing",language);
//            String formatToCCMailStr = ToCCMailStr;
            String formatToCCMailStr = commonMailService.readMailContent("issueMailCustomerServiceReceivesNew.html", language);

            Long issueId = issue.getIssueId();

            System.out.print("Before PrepareIssueMailToNewCC.GetIssueAttachmentFiles " + issueId);
            //組織案件附件超鏈接
            String attachementStr = messageUtils.get("nothing", language);
            boolean hasAttachment = false;
            List<IssueAttachmentFileV3> list = issueDao.getIssueAttachmentsFileV3(String.valueOf(issueId), 0L);
            if (!CollectionUtils.isEmpty(list)) {
                attachementStr = "";
                hasAttachment = true;
                for (IssueAttachmentFileV3 file : list) {
                    attachementStr = attachementStr + "  " + "<a href=\"" + file.getUrl() + "/\">" + file.getFileName() + "</a>";
                }
            }

            System.out.print("Before PrepareIssueMailToNewCC.GetIssueAttachmentFiles " + issueId);
            UserContact userContact = issue.getUserContact();
            String phone01 = userContact.getPhone01();
            String phone02 = userContact.getPhone02();
            String phone = phone01 == null || phone01.isEmpty() ? phone02 == null || phone02.isEmpty() ? "" : phone02 : phone01 + (phone02 == null || phone02.isEmpty() ? "" : "#" + phone02);
            String crmId = issue.getCrmId();
            String serviceCode = issue.getServiceCode();
            String customerName = customerServiceInfo.getCustomerName();
            String issueDescription = issue.getIssueDescription();
            String mailMsg = String.format(formatToCCMailStr, customerServiceInfo.getProductName() + "[ " + issue.getProductCode() + " ]", serviceCode, customerServiceInfo.getContractStateDesc() == null ? "" : customerServiceInfo.getContractStateDesc(),
                    crmId, customerName, transferTimeZone(issue.getSubmitTime(), userTimeZone).substring(0, 19),
                    userContact.getName(), userContact.getEmail(), phone, StringUtils.isEmpty(issue.getCcUser()) ? "" : issue.getCcUser(), attachementStr,
                    issueDescription);

            Mail mail02 = new Mail();
            String subject = "";
            String languageStandard = messageUtils.getLanguage(language);
            if (org.apache.commons.lang3.StringUtils.equalsIgnoreCase(MessageUtils.ZH_CN_STANDARD, languageStandard)) {
                //【鼎捷服务云】您有新案件
                subject = "【ISV转鼎捷】" + mailUtils.getIssueMailSubject("IssueSubmitMailToCC", language, hasAttachment);

            } else {
                //台湾区，要增加其他标题内容
                subject = "【ISV轉鼎捷】" + mailUtils.getIssueMailSubject("IssueSubmitMailToCC", language, crmId, serviceCode + customerName, issueDescription, issue.isNoResponsiblePersion(), hasAttachment);
            }

            mail02.setSubject(subject);
            mail02.setMessage(mailMsg);
            mail02.setReceivers(receivers02);
            mail02.setMailSourceType(MailSourceType.Issue);
            mail02.setUrl("");
            mail02.setSourceId(Long.toString(issue.getIssueId()));
            mail02.setPriority(1);
            mail02.setLanguage(language);
            mails.add(mail02);
        } catch (Exception ex) {
            ex.printStackTrace();
            log.error(ex);
        }
        Gson gson = new Gson();
        return gson.toJson(mails);
    }
    /**
     * 组织发送给新客服CC的邮件（变更处理人）
     *
     * @param issue
     * @return
     */
    public String PrepareIssueMailToNewCC(String userId, Issue issue) {
        List<Mail> mails = new ArrayList<Mail>();
        try {
            //2. 获取客服mail
            List<String> receivers02 = new ArrayList<String>();
            StaffUserInfo staffUserInfo = userService.GetServiceStaffInfoByUserId(issue.getServiceId());//(issue.getUserId());
            receivers02.add(staffUserInfo.getEmail());
            System.out.print("In PrepareIssueMailToNewCC");
            System.out.print(staffUserInfo.getEmail());
            System.out.print("In Staff TimeZone");
            System.out.print(staffUserInfo.getTimeZone());

            StaffUserInfo oldProcessor = userService.GetServiceStaffInfoByUserId(userId);//旧的处理人
            //获得语言别
            String language = defaultLanguage;
            //台湾区，客服不管语系是什么，都是默认语系(繁體)
            int userTimeZone = 0; //默认时区
            if (staffUserInfo != null && staffUserInfo.getLanguage() != null && !staffUserInfo.getLanguage().isEmpty()) {
                language = staffUserInfo.getLanguage();
            }
            //获得客服时区
            if (staffUserInfo != null && staffUserInfo.getTimeZone() != null) {
                userTimeZone = getTimeZoneMin(staffUserInfo.getTimeZone());
            }
            //获取客户的维护合约等级
            CustomerServiceInfo customerServiceInfo = new CustomerServiceInfo();
            try {
                customerServiceInfo = customerService.GetCustomerServiceInfo(issue.getServiceCode(), issue.getProductCode());
            } catch (Exception ex) {
                customerServiceInfo = new CustomerServiceInfo();
            }

            //组织发给客服专员的邮件
//            String attachementStr = "无";
//            String attachementStr = messageUtils.get("nothing",language);
//            String formatToCCMailStr = ToCCMailStr;
            String formatToCCMailStr = commonMailService.readMailContent("issueMailCustomerServiceReceivesNew.html", language);

            Long issueId = issue.getIssueId();

            System.out.print("Before PrepareIssueMailToNewCC.GetIssueAttachmentFiles " + issueId);
            //組織案件附件超鏈接
            String attachementStr = messageUtils.get("nothing", language);
            boolean hasAttachment = false;
            List<IssueAttachmentFileV3> list = issueDao.getIssueAttachmentsFileV3(String.valueOf(issueId), 0L);
            if (!CollectionUtils.isEmpty(list)) {
                attachementStr = "";
                hasAttachment = true;
                for (IssueAttachmentFileV3 file : list) {
                    attachementStr = attachementStr + "  " + "<a href=\"" + file.getUrl() + "/\">" + file.getFileName() + "</a>";
                }
            }

            System.out.print("Before PrepareIssueMailToNewCC.GetIssueAttachmentFiles " + issueId);
            UserContact userContact = issue.getUserContact();
            String phone01 = userContact.getPhone01();
            String phone02 = userContact.getPhone02();
            String phone = phone01 == null || phone01.isEmpty() ? phone02 == null || phone02.isEmpty() ? "" : phone02 : phone01 + (phone02 == null || phone02.isEmpty() ? "" : "#" + phone02);
            String crmId = issue.getCrmId();
            String serviceCode = issue.getServiceCode();
            String customerName = customerServiceInfo.getCustomerName();
            String issueDescription = issue.getIssueDescription();
            String mailMsg = String.format(formatToCCMailStr, customerServiceInfo.getProductName() + "[ " + issue.getProductCode() + " ]", serviceCode, customerServiceInfo.getContractStateDesc() == null ? "" : customerServiceInfo.getContractStateDesc(),
                    crmId, customerName, transferTimeZone(issue.getSubmitTime(), userTimeZone).substring(0, 19),
                    userContact.getName(), userContact.getEmail(), phone, StringUtils.isEmpty(issue.getCcUser()) ? "" : issue.getCcUser(), attachementStr,
                    issueDescription);

            Mail mail02 = new Mail();
            String subject = "";
            String languageStandard = messageUtils.getLanguage(language);
            if (org.apache.commons.lang3.StringUtils.equalsIgnoreCase(MessageUtils.ZH_CN_STANDARD, languageStandard)) {
                //【鼎捷服务云】您有新案件
                subject = mailUtils.getIssueMailSubjectForChangeCC("IssueSubmitMailToCC", language, oldProcessor, hasAttachment);

            } else {
                //台湾区，要增加其他标题内容
                subject = mailUtils.getIssueMailSubjectForChangeCC("IssueSubmitMailToCC", language, oldProcessor, crmId, serviceCode + customerName, issueDescription, issue.isNoResponsiblePersion(), hasAttachment);
            }

            mail02.setSubject(subject);
            mail02.setMessage(mailMsg);
            mail02.setReceivers(receivers02);
            mail02.setMailSourceType(MailSourceType.Issue);
            mail02.setUrl("");
            mail02.setSourceId(Long.toString(issue.getIssueId()));
            mail02.setPriority(1);
            mail02.setLanguage(language);
            mails.add(mail02);
        } catch (Exception ex) {
            ex.printStackTrace();
            log.error(ex);
        }
        Gson gson = new Gson();
        return gson.toJson(mails);
    }
    /*@Override
    @Transactional
    public boolean cancelCloseIssue(String issueId, long progressId) {
        String issueSyncStatus = issueDao.getIssueSyncStatus(issueId);
        issueSyncStatus= SyncStatus.SyncSuccess.toString().equals(issueSyncStatus)|| SyncStatus.WaitSync.toString().equals(issueSyncStatus)? SyncStatus.EditUnSync.toString():issueSyncStatus;
        boolean resIssue = issueDao.cancelCloseIssue(issueId, issueSyncStatus);
        boolean resProgress=false;
        if (resIssue)
            resProgress=issueDao.updateCurrentStatus(progressId, IssueStatus.Processing.toString());
        return resIssue && resProgress;
    }*/

    /*@Override
    @Transactional
    public boolean cancelClose(long issueId, String crmId, String userId) {
        String issueSyncStatus = issueDao.getIssueSyncStatus(String.valueOf(issueId));
        issueSyncStatus= SyncStatus.SyncSuccess.toString().equals(issueSyncStatus)|| SyncStatus.WaitSync.toString().equals(issueSyncStatus)? SyncStatus.EditUnSync.toString():issueSyncStatus;
        boolean resIssue = issueDao.cancelCloseIssue(String.valueOf(issueId), issueSyncStatus);
        if (resIssue){
            IssueProgress issueProgress = new IssueProgress();

            DateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            issueProgress.setProcessTime(sdf.format(new Date()));
            issueProgress.setCrmId(crmId);
            issueProgress.setCurrentStatus(IssueStatus.Processing.toString());
            issueProgress.setReplyType(ReplyType.A.toString());
            issueProgress.setProcessType(IssueProcessType.CancelClose.toString());
            issueProgress.setDescription("Cancel Close Issue");
            issueProgress.setProcessor(userId);
            issueProgress.setSequeceNum(issueDao.getMaxOrder(issueId));
            return issueDao.InsertIssueProgress(issueId, issueProgress.getCrmId(), issueProgress) > 0;
        }
        return false;
    }*/

    @Override
    public boolean checkIssueIsSyncCrm(String serviceRegion, String productCode, String issueStatus) {
        Integer syncCRM = issueDao.checkIssueIsSyncCrm(serviceRegion, productCode, issueStatus);
        return syncCRM != null && syncCRM == 1;
    }

    /**
     * 由信箱找出UserId
     *
     * @param mail
     * @param serviceCode
     * @return
     */
    @Override
    public String findUserIdbyMail(String mail, String serviceCode) {
        String userId = issueDao.getUserIdbyMail(mail, serviceCode);
        return userId != null ? userId : "";
    }

    /**
     * 由客代找出公司電話
     *
     * @param serviceCode
     * @return
     */
    @Override
    public String findDefaultPhonebyServiceCode(String serviceCode) {
        String phone = issueDao.getDefaultPhonebyServiceCode(serviceCode);
        return phone != null ? phone : "";
    }

    /**
     * 由電話找出mail
     *
     * @param serviceCode
     * @param phone
     * @param extension
     * @param mobilePhone
     * @return
     */
    @Override
    public String findMailbyPhone(String serviceCode, String phone, String extension, String mobilePhone) {
        //整理電話格式，將特殊符號都移除
        String regEx = "[\\s\n`~!\\-@#$%^&*()+=|{}':;',\\[\\].<>/?~！@#￥%……&*（）——+|{}【】‘；：”“’。， 、？]";
        //可以在中括号内加上任何想要替换的字符，实际上是一个正規表达式
        String newString = "";//这里是将特殊字符换为newString字符串,""代表直接去掉

        //移除國際碼
        phone = phone.replaceAll("\\+81|\\+84|\\+86|\\+60|\\+62|\\+66|\\+886", "");
        //移除特殊符號
        Pattern p = Pattern.compile(regEx);
        Matcher m = p.matcher(phone);
        phone = m.replaceAll(newString);
        //phone = phone.replaceAll(regEx,newString);
        extension = extension.replaceAll("\\+81|\\+84|\\+86|\\+60|\\+62|\\+66|\\+886", "");//replace("+81","").replace("+84","").replace("+86","").replace("+60","").replace("+62","").replace("+66","").replace("+886","");
        Matcher e = p.matcher(extension);
        extension = e.replaceAll(newString);
        //extension = extension.replaceAll(regEx,newString);
        Matcher mp = p.matcher(mobilePhone);
        mobilePhone = mp.replaceAll(newString);
        //mobilePhone = mobilePhone.replaceAll(regEx,newString);

        String mail = issueDao.getMailbyPhone(serviceCode, phone, extension, mobilePhone);
        return mail != null ? mail : "";
    }

    /**
     * 获取案件列表
     *
     * @return
     */
    @Override
    public IssueSourceMapResult getIssueSourceMapList(String issueStatus, String sourceType, String sourceId, String userId,String content, Integer recentlyDays, String serviceCode) {

        Map<String, Object> map = new HashMap<>();
        map.put("issueStatus", issueStatus);
        map.put("sourceType", sourceType);
        map.put("sourceId", sourceId);
        map.put("userId", userId);
        map.put("connectArea", connectArea);
        map.put("content", content);
        if(recentlyDays != null && recentlyDays >0){
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            Calendar cal = Calendar.getInstance();
            Date dt = new Date();
            cal.setTime(dt);
            cal.add(Calendar.DATE, -1 * recentlyDays);
            String dateAfter = sdf.format(cal.getTime());
            map.put("recentlyDays", dateAfter);
        } else {
            map.put("recentlyDays", "");
        }
        map.put("serviceCode", StringUtils.isEmpty(serviceCode) ? "" : serviceCode);

        log.info(">>>>>========================查询案件开始======================<<<<<");
        List<Issue> issueList = issueDao.getIssueSourceMapList(map);
        long count = issueDao.getIssueSourceMapCount(map);

        IssueSourceMapResult issueSourceMapResult = new IssueSourceMapResult();
        issueSourceMapResult.setCount(count);
        issueSourceMapResult.setIssueList(issueList);
        log.info(">>>>>========================查询案件结束======================<<<<<");

        return issueSourceMapResult;
    }

    @Override
    public BaseResponse deleteIssueAttachmentFileV3(String fileId) {
        //1 删mysql表的文件记录
        int result = issueDao.deleteIssueAttachmentFileV3(fileId);
        if (result > 0) {
            //2 删mongodb里面的文件
            gridFsTemplate.delete(new Query(Criteria.where("_id").is(fileId)));
            return BaseResponse.ok();
        }
        return BaseResponse.error(ResponseStatus.DELETE_FAILD);
    }

    @Override
    public IssueSwitch getSwitch(String productCode) {
        return issueSwitchCache.selectIssueSwitch(productCode);
    }

    @Override
    public void removeSwitch(String productCode) {
        issueSwitchCache.clearCurrentIssueSwitch(productCode);
    }

    @Override
    public int checkErpSystemCode(String productCode, String erpSystemCode) {
        return issueDetailDao.checkErpSystemCode(productCode, erpSystemCode);
    }


    @Override
    public BaseResponse getHistorySelect(String userId, String type, String productCode) {
        log.info("userId:" + userId + " type:" + type + " productCode:" + productCode);
        if (StringUtils.isEmpty(type)) {
            return BaseResponse.error(ResponseStatus.PARAM_VERIFY);
        }
        return BaseResponse.ok(issueDao.getHistorySelect(userId, type, productCode));

    }

    @Override
    public BaseResponse saveHistorySelect(String userId, String code, String type, String productCode) {
        log.info("userId:" + userId + " code:" + code + " type:" + type + " productCode:" + productCode);
        try {
            //先查历史记录里有没有 有就更新日期，没有在考虑是否能新增
            boolean result = true;
            if (StringUtils.isEmpty(code)) {
                return BaseResponse.ok();
            }
            if (issueDao.getHistory(userId, code, type, productCode) > 0) {
                result = issueDao.updateHistory(userId, code, type, productCode) > 0;
            } else {
                List<IssueSelect> list = issueDao.getHistorySelect(userId, type, productCode);
                if (!CollectionUtils.isEmpty(list)) {
                    if (list.size() == 8) {
                        //删除
                        result = issueDao.deleteHistory(userId, type, productCode) > 0;
                    }
                }
                //新增
                result = result && issueDao.insertHistory(userId, code, type, productCode) > 0;
            }
            if (result) {
                return BaseResponse.ok();
            }
        } catch (Exception e) {
            log.error(e.toString());
        }
        return BaseResponse.error(ResponseStatus.INTERNAL_SERVER_ERROR);
    }

    @Override
    public BaseResponse updateLastUserContact(String userId, String type, UserContact userContact) {
        log.info("userId:" + userId + " type:" + type);
        if (userContact == null) {
            return BaseResponse.error(ResponseStatus.PARAM_VERIFY);
        }
        boolean result = false;
        String code = "";
        //联系方式id为空的时候，需要更新到联系方式表，有就不用更新到联系方式表了
        if (userContact.getId() == 0) {
            code = String.valueOf(userDao.GetUserContractId(userContact));
        } else {
            code = String.valueOf(userContact.getId());
        }
        UserContact oldUserContact = issueDao.getLastUserContact(userId, type);
        if (oldUserContact == null) {
            result = issueDao.insertLastUserContact(userId, code, type) > 0;
        } else {
            result = issueDao.updateLastUserContact(userId, code, type) > 0;
        }
        if (result) {
            return BaseResponse.ok();
        } else {
            return BaseResponse.error(ResponseStatus.UPDATE_FAILD);
        }
    }


    @Override
    public BaseResponse getLastUserContact(String userId, String type) {
        log.info("userId:" + userId + " type:" + type);
        return BaseResponse.ok(issueDao.getLastUserContact(userId, type));
    }

    @Override
    public BaseResponse getDefaultUserContact(String userId, String type) {
        if (issueDao.getLastUserContact(userId, type) != null) {
            return BaseResponse.ok(issueDao.getLastUserContact(userId, type));
        } else {
            UserContact userContact = issueDao.getDefaultUserContact(userId);
            if (userContact == null) {
                userContact = issueDao.getNewUserContact(userId);
                if (userContact == null) {
                    userContact = issueDao.getUserInfoUserContact(userId);
                    if (userContact == null) {
                        userContact = issueDao.getRegistUserContact(userId);
                        if (userContact == null) {
                            return BaseResponse.ok(userContact);
                        } else {
                            return BaseResponse.error(ResponseStatus.DATA_IS_NOT_FIND);
                        }
                    } else {
                        return BaseResponse.ok(userContact);
                    }
                } else {
                    return BaseResponse.ok(userContact);
                }
            } else {
                return BaseResponse.ok(userContact);
            }
        }
    }

    @Override
    public BaseResponse getCustomizeSearch(String userId, boolean isSearchPublic) {
        // 抓取該userId之單頭與對應單身
        //isSearchPublic為判別是否抓公用的條件
        return BaseResponse.ok(issueDao.getCustomizeSearchList(userId, isSearchPublic));
    }

    @Override
    public BaseResponse newCustomizeSearch(String userId, CustomizeSearchInfo customizeSearchInfo) {
        // 檢查該titlw是否有重複，在該userId下
        //新增單頭再新增單身
        if (!StringUtils.isEmpty(issueDao.checkCustomizeSearchTitle(userId, customizeSearchInfo.getTitle(),customizeSearchInfo.getId()))) {
            // 表示名稱有重複，所以不允取新增
            return BaseResponse.error(ResponseStatus.REPEAT_TITLE);
        } else {
            int id =issueDao.addCustomizeSearch(userId, customizeSearchInfo);
            if(id>0){
                customizeSearchInfo.setId(Integer.toString(id));
                if(!CollectionUtils.isEmpty(customizeSearchInfo.getCustomizeSearchAttribute())){
                    issueDao.addCustomizeSearchAttribute(userId, customizeSearchInfo);
                }
                return BaseResponse.ok(id);
            } else {
                return BaseResponse.error(ResponseStatus.INSERT_FAILD);
            }
        }
    }

    @Override
    public BaseResponse editCustomizeSearch(String userId, CustomizeSearchInfo customizeSearchInfo) {
        //檢查是否有該ID
        //檢查該titlw是否有重複
        //刪除單身再新增單身
        //更新單頭的title
        if (StringUtils.isEmpty(issueDao.checkCustomizeSearchId(userId, customizeSearchInfo.getId()))) {
            // 無ID，表示無該資料
            return BaseResponse.error(ResponseStatus.NO_ID);
        }else if(!StringUtils.isEmpty(issueDao.checkCustomizeSearchTitle(userId, customizeSearchInfo.getTitle(),customizeSearchInfo.getId()))) {
            // 表示名稱有重複，所以不允取新增
            return BaseResponse.error(ResponseStatus.REPEAT_TITLE);
        } else {
            issueDao.deleteCustomizeSearchAttribute(userId,customizeSearchInfo.getId());
            if(!CollectionUtils.isEmpty(customizeSearchInfo.getCustomizeSearchAttribute())) {
                issueDao.addCustomizeSearchAttribute(userId, customizeSearchInfo);
            }
            issueDao.updateCustomizeSearchTitle(userId, customizeSearchInfo);
            return BaseResponse.ok(customizeSearchInfo.getId());
        }
    }

    @Override
    public BaseResponse deleteCustomizeSearch(String userId,String customizeSearchId) {
        // 檢查id是否存在
        //刪除單身再刪除單頭
        if (StringUtils.isEmpty(issueDao.checkCustomizeSearchId(userId,customizeSearchId))) {
            // 無ID，表示無該資料
            return BaseResponse.error(ResponseStatus.NO_ID);
        }else {
            issueDao.deleteCustomizeSearchAttribute(userId,customizeSearchId);
            issueDao.deleteCustomizeSearch(userId,customizeSearchId);
            return BaseResponse.ok(customizeSearchId);
        }
    }
    @Override
    public BaseResponse getIssueGridDefaultField() {
        try {
            return BaseResponse.ok(issueDao.getIssueGridDefaultField());
        } catch (Exception e) {
            log.error("getIssueGridDefaultField error ", e);
        }
        return BaseResponse.error(ResponseStatus.QUERY_VERIFY);
    }
    @Override
    public BaseResponse getIssueGridCustomizedField(String userId) {
        try {
            //查看是否有自定义若无则查默认接口
            IssueGridCustomized issueGridCustomized=new IssueGridCustomized();
            issueGridCustomized.setUserId(userId);
            if(IntegerUtil.isNotEmpty(issueDao.hasIssueGridCustomized(userId))) {
                issueGridCustomized.setIssueGridFieldList(issueDao.getIssueGridCustomizedField(userId));
            }else{
                issueGridCustomized.setIssueGridFieldList(issueDao.getIssueGridDefaultField());
            }
            return BaseResponse.ok(issueGridCustomized);
        } catch (Exception e) {
            log.error("getIssueGridCustomizedField error ", e);
        }
        return BaseResponse.error(ResponseStatus.QUERY_VERIFY);
    }
    @Override
    @Transactional
    public BaseResponse saveIssueGridCustomizedField(IssueGridCustomized issueGridCustomized) {
        try {
            if(StringUtils.isEmpty(issueGridCustomized.getUserId())){
                return BaseResponse.error(ResponseStatus.USER_ID_EMPTY);
            }
            if(CollectionUtils.isEmpty(issueGridCustomized.getIssueGridFieldList())) {
                return BaseResponse.error(ResponseStatus.ISSUE_GRID_CUSTOMIZED_EMPTY);
            }
            List<IssueGridField> issueGridFieldList=issueGridCustomized.getIssueGridFieldList();
            issueGridFieldList.forEach(field -> {
                if(IntegerUtil.isNotEmpty(issueDao.checkIssueGridCustomizedField(issueGridCustomized.getUserId(),field.getFieldCode()))){
                    //已存在就更新
                    issueDao.saveIssueGridCustomizedField(issueGridCustomized.getUserId(),field);
                }else{
                    issueDao.insertIssueGridCustomizedField(issueGridCustomized.getUserId(),field);
                }

            });

            return BaseResponse.ok(true);
        } catch (Exception e) {
            log.error("saveIssueGridCustomizedField error ", e);
        }
        return BaseResponse.error(ResponseStatus.INSERT_FAILD);
    }
    @Override
    public BaseResponse getIssueFilterField() {
        try {
            List<IssueFilterField> issueFilterFieldList=issueDao.getIssueFilterFieldList();
            issueFilterFieldList.forEach(field -> {
                if(CollectionUtils.isEmpty(field.getFieldOperators())){
                    //不存在筛选项则给全部默认
                    field.setFieldOperators(issueDao.getIssueFilterOperatorList());
                }
            });
            return BaseResponse.ok(issueFilterFieldList);
        } catch (Exception e) {
            log.error("getIssueFilterField error ", e);
        }
        return BaseResponse.error(ResponseStatus.QUERY_VERIFY);
    }
    @Override
    public BaseResponse getIssueFilterOperator() {
        try {
            return BaseResponse.ok(issueDao.getIssueFilterOperatorList());
        } catch (Exception e) {
            log.error("getIssueFilterOperator error ", e);
        }
        return BaseResponse.error(ResponseStatus.QUERY_VERIFY);
    }
    @Override
    public BaseResponse getIssueFilterUserFieldList(String userId) {
        try {
            if(StringUtils.isEmpty(userId)){
                return BaseResponse.error(ResponseStatus.USER_ID_EMPTY);
            }
            //查看是否有用戶若无则查默认接口
            IssueFilterUserField issueFilterUserField=new IssueFilterUserField();
            issueFilterUserField.setUserId(userId);
            if(IntegerUtil.isNotEmpty(issueDao.hasIssueFilterUser(userId))) {
                issueFilterUserField.setIssueFilterFields(issueDao.getIssueFilterUserFieldList(userId));
            }else{
                issueFilterUserField.setIssueFilterFields(issueDao.getIssueFilterFieldList());
            }
            issueFilterUserField.getIssueFilterFields().forEach(field -> {
                if(CollectionUtils.isEmpty(field.getFieldOperators())){
                    //不存在筛选项则给全部默认
                    field.setFieldOperators(issueDao.getIssueFilterOperatorList());
                }
            });
            return BaseResponse.ok(issueFilterUserField);
        } catch (Exception e) {
            log.error("getIssueFilterUserFieldList error ", e);
        }
        return BaseResponse.error(ResponseStatus.QUERY_VERIFY);
    }
    @Override
    public BaseResponse getIssueFilterCustomized(String userId) {
        try {
            if(StringUtils.isEmpty(userId)){
                return BaseResponse.error(ResponseStatus.USER_ID_EMPTY);
            }
            return BaseResponse.ok(issueDao.getIssueFilterCustomize(userId));
        } catch (Exception e) {
            log.error("getIssueFiltercCustomize error ", e);
        }
        return BaseResponse.error(ResponseStatus.QUERY_VERIFY);
    }
    @Override
    @Transactional
    public BaseResponse saveIssueFilterUserField(IssueFilterUserField issueFilterUserField) {
        try {
            if(StringUtils.isEmpty(issueFilterUserField.getUserId())){
                return BaseResponse.error(ResponseStatus.USER_ID_EMPTY);
            }
            if(CollectionUtils.isEmpty(issueFilterUserField.getIssueFilterFields())) {
                return BaseResponse.error(ResponseStatus.ISSUE_FILTER_FIELD_EMPTY);
            }
            List<IssueFilterField> issueFilterFields=issueFilterUserField.getIssueFilterFields();
            issueFilterFields.forEach(field -> {
                if(IntegerUtil.isNotEmpty(issueDao.checkIssueFilterUserField(issueFilterUserField.getUserId(),field.getFieldCode()))){
                    //已存在就更新
                    issueDao.saveIssueFilterUserField(issueFilterUserField.getUserId(),field);
                }else{
                    issueDao.insertIssueFilterUserField(issueFilterUserField.getUserId(),field);
                }
            });
            return BaseResponse.ok(true);
        } catch (Exception e) {
            log.error("saveIssueFilterUserField error ", e);
        }
        return BaseResponse.error(ResponseStatus.INSERT_FAILD);
    }
    @Override
    public BaseResponse newFilterCustomized(String userId,IssueFilterCustomized issueFilterCustomized) {
        // 檢查該title是否有重複，在該userId下
        //新增單頭再新增單身
        try {
            if(StringUtils.isEmpty(userId)){
                return BaseResponse.error(ResponseStatus.USER_ID_EMPTY);
            }
            if (!StringUtils.isEmpty(issueDao.checkIssueFilterCustomizedTitle(userId, issueFilterCustomized.getTitle(),issueFilterCustomized.getId()))) {
                // 表示名稱有重複，所以不允取新增
                return BaseResponse.error(ResponseStatus.REPEAT_TITLE);
            } else {
                int id =issueDao.insertIssueFilterCustomized(userId,issueFilterCustomized);
                if(id>0){
                    issueFilterCustomized.setId(Integer.toString(id));
                    if(!CollectionUtils.isEmpty(issueFilterCustomized.getFilterCustomizedFields())){
                        issueDao.saveIssueFilterCustomizedField(userId,issueFilterCustomized);
                    }
                    return BaseResponse.ok(id);
                } else {
                    return BaseResponse.error(ResponseStatus.INSERT_FAILD);
                }
            }
        }catch (Exception e) {
            log.error("newFilterCustomized error ", e);
        }
        return BaseResponse.error(ResponseStatus.INSERT_FAILD);

    }

    @Override
    public BaseResponse saveFilterCustomized(String userId,IssueFilterCustomized issueFilterCustomized) {
        //檢查是否有該ID
        //檢查該titlw是否有重複
        //刪除單身再新增單身
        //更新單頭的title
        try {
            if (IntegerUtil.isEmpty(issueDao.checkIssueFiltercCustomize(userId, issueFilterCustomized.getId()))) {
                // 無ID，表示無該資料
                return BaseResponse.error(ResponseStatus.NO_ID);
            }else if(!StringUtils.isEmpty(issueDao.checkIssueFilterCustomizedTitle(userId, issueFilterCustomized.getTitle(),issueFilterCustomized.getId()))) {
                // 表示名稱有重複，所以不允取新增
                return BaseResponse.error(ResponseStatus.REPEAT_TITLE);
            } else {
                issueDao.deleteFilterCustomizedField(userId,issueFilterCustomized.getId());
                if(!CollectionUtils.isEmpty(issueFilterCustomized.getFilterCustomizedFields())) {
                    issueDao.saveIssueFilterCustomizedField(userId,issueFilterCustomized);
                }
                issueDao.saveIssueFilterCustomized(userId,issueFilterCustomized);
                return BaseResponse.ok(issueFilterCustomized.getId());
            }
        }catch (Exception e) {
            log.error("saveFilterCustomized error ", e);
        }
        return BaseResponse.error(ResponseStatus.UPDATE_FAILD);

    }

    @Override
    public BaseResponse deleteFileterCustomized(String userId,String customizeId) {
        // 檢查id是否存在
        //刪除單身再刪除單頭
        try {
            if (IntegerUtil.isEmpty(issueDao.checkIssueFiltercCustomize(userId,customizeId))) {
                // 無ID，表示無該資料
                return BaseResponse.error(ResponseStatus.NO_ID);
            }else {
                issueDao.deleteFilterCustomizedField(userId,customizeId);
                issueDao.deleteFilterCustomized(userId,customizeId);
                return BaseResponse.ok(customizeId);
            }
        }catch (Exception e) {
            log.error("deleteFileterCustomized error ", e);
        }
        return BaseResponse.error(ResponseStatus.DELETE_FAILD);

    }
    @Override
    public BaseResponse getIssueStatusInfoList(String language) {
        try {
            if(StringUtils.isEmpty(language)){
                return BaseResponse.ok(issueDao.getIssueStatusInfoList("zh-CN"));
            }
            return BaseResponse.ok(issueDao.getIssueStatusInfoList(language));
        } catch (Exception e) {
            log.error("getIssueStatusInfoList error ", e);
        }
        return BaseResponse.error(ResponseStatus.QUERY_VERIFY);
    }

    @Override
    public BaseResponse getIssueSubmitWayList(String language) {
        try {
            if(StringUtils.isEmpty(language)){
                return BaseResponse.ok(issueDao.getIssueSubmitWayList("zh-CN"));
            }
            return BaseResponse.ok(issueDao.getIssueSubmitWayList(language));
        } catch (Exception e) {
            log.error("getIssueSubmitWayList error ", e);
        }
        return BaseResponse.error(ResponseStatus.QUERY_VERIFY);
    }

    public List<Map<String, Object>> getFinalIssueProgressInfoByCrmIds(Map<String, Object> queryBody) {
        if (queryBody == null) {
            throw new IllegalArgumentException("queryBody is null");
        }
        int start = 0;
        String sizeStr = Optional.ofNullable(queryBody.get("pageSize")).orElse("20").toString();
        String pageStr = Optional.ofNullable(queryBody.get("pageNum")).orElse("1").toString();
        int page = Integer.parseInt(pageStr);
        int size = Integer.parseInt(sizeStr);
        if (page > 0) {
            start = (page - 1) * size;
        }
        queryBody.put("start", start);
        queryBody.put("size", size);
        List<Map<String, Object>> finalIssueProgressInfo = issueProcessMapper.getFinalIssueProgressInfo(queryBody);
        // 处理description字段中的换行符和HTML标签
        if (finalIssueProgressInfo != null && !finalIssueProgressInfo.isEmpty()) {
            for (Map<String, Object> item : finalIssueProgressInfo) {
                if (item.containsKey("description") && item.get("description") != null) {
                    String description = item.get("description").toString();
                    description = description.replace("\n", " ")
                                           .replace("<br>", " ")
                                           .replace("</br>", " ");
                    item.put("description", description);
                }
                if (item.containsKey("IssueDescription") && item.get("IssueDescription") != null) {
                    String issueDescription = item.get("IssueDescription").toString();
                    issueDescription = issueDescription.replace("\n", " ")
                            .replace("<br>", " ")
                            .replace("</br>", " ");
                    item.put("IssueDescription", issueDescription);
                }
            }
        }
        return finalIssueProgressInfo;
    }

    @Override
    public com.digiwin.escloud.common.response.BaseResponse<List<com.digiwin.escloud.aioissue.model.Issue>> getFinalIssueIssueStatus(List<String> issueCrmIdList) {
        List<com.digiwin.escloud.aioissue.model.Issue > issueProgresses = issueProcessMapper.selectFinalCrmIdAndIssueStatus(issueCrmIdList);
        return com.digiwin.escloud.common.response.BaseResponse.okT(issueProgresses);
    }

    public boolean checkMainMenuVisable(MainMenu main, String userId, String role){
        boolean checkIamRole = Boolean.FALSE.equals(main.getCheckIamRole()) || (StringUtils.isNotBlank(role) && Boolean.TRUE.equals(main.getCheckIamRole()) && Optional.ofNullable(issueDao.getMainRoles(main.getCode())).orElse(new ArrayList<>()).contains(role)); ////修复bug 调整equals值比较
        boolean checkProductCode = checkAuthProduct(userId,main);

        return checkIamRole && checkProductCode;
    }

    public boolean checkSecondMenuVisable(SecondMenu secondMenu,Map<String,Object> commonMap){
        if(StringUtils.isEmpty(secondMenu.getMenuCondition())){
            return true;
        }
        List<Map<String,String>> list = issueDao.getSqlDatas(dealCommonParam(commonMap,secondMenu.getMenuCondition()));
        if(CollectionUtil.isNotEmpty(list)){
            return true;
        }
        return false;
    }

    public boolean checkAuthProduct(String userId, MainMenu main){
        if(Boolean.FALSE.equals(main.getCheckProduct())){ //修复bug 调整equals值比较
            return true;
        }
        if(StringUtils.isEmpty(userId)){
            return false;
        }
        List<String> menuProducts = issueDao.getMainMenuProducts(main.getCode());
        if(CollectionUtils.isEmpty(menuProducts)){
            return true;
        }

        List<String> authProducts = issueDao.getAuthProducts(userId);
        if(CollectionUtils.isEmpty(authProducts)){
            return false;
        }
        Optional<String> any = authProducts.stream().filter(k->menuProducts.contains(k)).findFirst();
        if(any.isPresent()){
            return true;
        }else {
            return false;
        }
    }
    @Override
    public BaseResponse getEasyTalkMenuList(String userId, String role,String lang) {
        try {
            if(StringUtils.isEmpty(lang)){
                lang = "CN".equals(connectArea) ? "zh-CN" : "zh-TW";
            }
            List<MainMenu> mainMenu = Optional.ofNullable(issueDao.getMainMenus(lang)).orElse(new ArrayList<>());
            String finalLang = lang;
            Map<String, Object> map1 = new HashMap<String, Object>();
            map1.put("userId", userId);
            Map<String,Object> commonMap = Optional.ofNullable(issueDao.getCommonParamValue(map1)).orElse(new HashMap<>());

            return BaseResponse.ok(mainMenu.stream()
                    .filter(main ->checkMainMenuVisable(main,userId,role))
                    .map(main -> {
                        List<SecondMenu> secondMenus = Optional.ofNullable(issueDao.getSecondMenus(main.getCode(), finalLang)).orElse(new ArrayList<>());
                        //根据expandSql的值，去获取展开的节点数据，如果展开的节点数据为空，那就隐藏这个二级菜单;
                        List<SecondMenu> secondMenusResult = new ArrayList<>();
                        secondMenus.stream()
                            .filter(secondMenu -> checkSecondMenuVisable(secondMenu,commonMap))
                            .forEach(second->{
                                second.setName(dealCommonParam(commonMap,second.getName()));
                                if(StringUtils.isNotEmpty(second.getMenuParam())){
                                    second.setMenuParam(dealCommonParam(commonMap,second.getMenuParam()));
                                }
                                //如果需要展开 还需要有展开后的数据
                                if(second.isExpand()){
                                    if(StringUtils.isNotBlank(second.getExpandSql())){
                                        List<Map<String,String>> list = issueDao.getSqlDatas(dealCommonParam(commonMap,second.getExpandSql()));
                                        if(CollectionUtil.isNotEmpty(list)){
                                            second.setExpandNodes(list);
                                            secondMenusResult.add(second);
                                            issueStatusQuery(finalLang, second);
                                        }
                                    }
                                }else {
                                    //不需要展开时，直接添加
                                    second.setExpandNodes(new ArrayList<>());
                                    secondMenusResult.add(second);
                                    issueStatusQuery(finalLang, second);
                                }
                            });
                        main.setChildren(secondMenusResult);
                        return main;
                    })
                    .filter(k->CollectionUtil.isNotEmpty(k.getChildren()))  //再次过滤 二级菜单为空的主菜单，比如T的专案，如果一个二级菜单都没符合的，那一级菜单T的专案也要隐藏
                    .collect(Collectors.toList()));
        } catch (Exception e) {
            log.error("getEasyTalkMenuList error ", e);
        }
        return BaseResponse.error(ResponseStatus.QUERY_VERIFY);
    }

    private void issueStatusQuery(String finalLang, SecondMenu second) {
        //先获取自定义状态
        List<IssueStatusSet> issueStatusCustoms = issueDao.selectIssueStatusCustoms(second.getCode(), finalLang);
        if(CollectionUtils.isEmpty(issueStatusCustoms)){
            //未设定自定义状态，就去取通用状态
            issueStatusCustoms = issueDao.selectIssueStatusCommons(finalLang);
            second.setIssueStatusCustom(false);
        }else {
            second.setIssueStatusCustom(true);
        }
        second.setIssueStatusSet(issueStatusCustoms);
    }

    private String dealCommonParam(Map<String,Object> commonMap,String str){
        return str.replaceAll("#deptCode#",commonMap.get("departmentcode")!= null ? commonMap.get("departmentcode").toString() : "#deptCode#")
                .replaceAll("#acpCode#",commonMap.get("acpCode")!= null ? commonMap.get("acpCode").toString() : "#acpCode#")
                .replaceAll("#serviceCode#",commonMap.get("customerServicCode")!= null ? commonMap.get("customerServicCode").toString() : "#serviceCode#")
                .replaceAll("#userId#",commonMap.get("userId")!= null ? commonMap.get("userId").toString() : "#userId#");
    }
}
