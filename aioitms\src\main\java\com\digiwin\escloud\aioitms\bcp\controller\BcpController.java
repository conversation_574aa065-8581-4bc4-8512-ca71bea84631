package com.digiwin.escloud.aioitms.bcp.controller;

import com.digiwin.escloud.aioitms.bcp.model.BcpRequestParam;
import com.digiwin.escloud.aioitms.bcp.model.BcpSurveyRecord;
import com.digiwin.escloud.aioitms.bcp.model.BcpSurveyRecordSave;
import com.digiwin.escloud.aioitms.bcp.service.IBcpService;
import com.digiwin.escloud.common.model.ResponseCode;
import com.digiwin.escloud.common.response.BaseResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@Slf4j
@Api(value = "/bcp/survey", protocols = "HTTP", tags = {"BCP相關接口"}, description = "BCP相關接口")
@RestController
@RequestMapping("/bcp/survey")
public class BcpController {
    @Autowired
    IBcpService bcpService;

    @ApiOperation("BCP問卷資料")
    @GetMapping("/data")
    public BaseResponse getData(@ApiParam(value = "版本", required = true) @RequestParam(value = "version") String version,
                                @ApiParam(value = "語系", required = true) @RequestParam(value = "lang") String lang) {
        try {
            return bcpService.getData(version, lang);
        } catch (Exception ex) {
            log.error("Get BCP Data Error:", ex);
            return BaseResponse.error(ResponseCode.GET_BCP_QUESTIONNAIRE_ERROR);
        }
    }

    @ApiOperation("BCP查看自評")
    @GetMapping("/result/{surveyId}")
    public BaseResponse getResultBySurveyId(@ApiParam(value = "自評紀錄ID", required = true)
                                            @PathVariable String surveyId,
                                            @ApiParam(value = "語系", required = false)
                                            @RequestParam(value = "lang", required = false, defaultValue = "zh-TW") String lang) {
        try {
            return bcpService.getResultBySurveyId(surveyId, lang);
        } catch (Exception ex) {
            log.error("Get BCP Result Error:", ex);
            return BaseResponse.error(ResponseCode.GET_BCP_RESULT_ERROR);
        }
    }

    @ApiOperation("BCP自評紀錄")
    @PostMapping("/list")
    public BaseResponse getResultList(@RequestBody BcpRequestParam param) {
        try {
            return bcpService.getResultList(param);
        } catch (Exception ex) {
            log.error("Get BCP Result List Error:", ex);
            return BaseResponse.error(ResponseCode.GET_BCP_RESULT_LIST_ERROR);
        }
    }

    @ApiOperation("BCP客戶留資")
    @PostMapping("/record/save")
    public BaseResponse saveCustomerData(@RequestBody BcpSurveyRecord param) {
        try {
            return bcpService.saveCustomerData(param);
        } catch (Exception ex) {
            log.error("Save BCP Customer Data Error:", ex);
            return BaseResponse.error(ResponseCode.GET_CUSTOMER_DATA_ERROR);
        }
    }

    @ApiOperation("BCP保存自評")
    @PostMapping("/save")
    public BaseResponse saveRecord(@RequestBody BcpSurveyRecordSave param) {
        try {
            return bcpService.saveRecord(param);
        } catch (Exception ex) {
            log.error("Save BCP Result Error:", ex);
            return BaseResponse.error(ResponseCode.SAVE_BCP_RESULT_ERROR);
        }
    }
}
