package com.digiwin.escloud.common.util;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang.StringUtils;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * 字典实用类
 */
public final class MapUtil {

    /**
     * 获取第一层特定键字典结果
     * @param sourceMap 来源字典
     * @param key 特定键
     * @return 字典，找不到返回Optional.empty()
     */
    public static Optional<Map<String, Object>> getMap(Map<String, Object> sourceMap, String key) {
        if (CollectionUtils.isEmpty(sourceMap)) {
            return Optional.empty();
        }
        Object contentObj = sourceMap.get(key);
        if (!(contentObj instanceof Map)) {
            return Optional.empty();
        }
        Map<String, Object> resultMap = (Map<String, Object>) contentObj;
        if (CollectionUtils.isEmpty(resultMap)) {
            return Optional.empty();
        }
        return Optional.of(resultMap);
    }

    /**
     * 获取第一层特定键列表字典结果
     * @param sourceMap 来源字典
     * @param key 特定键
     * @return 列表字典，找不到返回Optional.empty()
     */
    public static Optional<List<Map<String, Object>>> getListMap(Map<String, Object> sourceMap, String key) {
        if (CollectionUtils.isEmpty(sourceMap)) {
            return Optional.empty();
        }
        Object contentObj = sourceMap.get(key);
        if (!(contentObj instanceof List)) {
            return Optional.empty();
        }
        List list = (List) contentObj;
        if (CollectionUtils.isEmpty(list)) {
            return Optional.empty();
        }
        if (!(list.get(0) instanceof Map)) {
            return Optional.empty();
        }
        List<Map<String, Object>> resultMap = (List<Map<String, Object>>) contentObj;
        if (CollectionUtils.isEmpty(resultMap)) {
            return Optional.empty();
        }
        return Optional.of(resultMap);
    }

    /**
     * 将通用列表转换成字典列表
     * @param list 通用列表
     * @return 字典列表
     */
    public static List<Map<String, Object>> convertToListMap(List<?> list) {
        if (CollectionUtils.isEmpty(list)) {
            return new ArrayList<>(0);
        }
        return list.stream().map(BeanUtil::object2Map).collect(Collectors.toList());
    }

    /**
     * 将对象转换成字典
     * @param obj 对象
     * @return 字典
     */
    public static Map<String, Object> objectToMap(Object obj) {
        return objectToMap(obj, new HashMap<>(0));
    }

    /**
     * 将对象转换成字典
     * @param obj 对象
     * @param defaultValue 默认值
     * @return 字典
     */
    public static Map<String, Object> objectToMap(Object obj, Map<String, Object> defaultValue) {
        return tryParseToMap(obj).orElse(defaultValue);
    }

    /**
     * 尝试将对象转换成字典
     * @param obj 对象
     * @return 字典，可选参数，转换失败将返回Optional.empty()
     */
    public static Optional<Map<String, Object>> tryParseToMap(Object obj) {
        try {
            if (!(obj instanceof Map)) {
                return Optional.empty();
            }
            return Optional.of((Map<String, Object>) obj);
        } catch (Exception ex) {
            return Optional.empty();
        }
    }

    /**
     * 转换字典对象值为字串值
     * @param map 对象值字典
     * @return 字串值字典
     */
    public static Map<String, String> transformObjectValueToString(Map<String, Object> map) {
        if (CollectionUtils.isEmpty(map)) {
            return new HashMap<>(0);
        }
        return map.entrySet().stream().collect(Collectors.toMap(Map.Entry::getKey,
                x -> Objects.toString(x.getValue(), "")));
    }

    /**
     * 获取依序键值排序的键值串接字串(默认串接用字串:底线(_))
     * @param map 字典
     * @return 键值串接字串
     */
    public static String getMapSeqString(Map<String, Object> map) {
        return getMapSeqString(map, "_");
    }

    /**
     * 获取依序键值排序的键值串接字串
     * @param map 字典
     * @param separator 串接用字串
     * @return 键值串接字串
     */
    public static String getMapSeqString(Map<String, Object> map, String separator) {
        if (CollectionUtils.isEmpty(map)) {
            return "";
        }
        //先进行key排序，之后依序根据键+串接用字串+值拼接成结果
        StringBuilder sbValue = new StringBuilder();
        map.keySet().stream().sorted().forEach(x -> {
            sbValue.append(x);
            sbValue.append(separator);
            sbValue.append(Objects.toString(map.get(x), ""));
            sbValue.append(separator);
        });
        //去除最后的串接用字串
        int length = sbValue.length();
        int sLength = separator.length();
        if (length >= sLength) {
            sbValue.delete(length - sLength, length);
        }
        return sbValue.toString();
    }

    /**
     * 依据路径获取字典值
     * @param sourceMap 来源字典
     * @param path 路径(使用点(.)间隔)
     * @return 是否成功取得(取不到返回Optional.empty())
     */
    public static Optional<Object> getMapDataByPath(Map<String, Object> sourceMap, String path) {
        if (CollectionUtils.isEmpty(sourceMap)) {
            return Optional.empty();
        }
        if (StringUtils.isBlank(path)) {
            return Optional.of(sourceMap);
        }
        String[] pathArray = path.split("\\.");
        int length = pathArray.length;
        int lastIndex = length-1;
        for (int i = 0; i < length; i++) {
            String currentPath = pathArray[i];
            if (!sourceMap.containsKey(currentPath)) {
                return Optional.empty();
            }
            if (i == lastIndex) {
                return Optional.ofNullable(sourceMap.get(currentPath));
            }
            sourceMap = (Map<String, Object>) sourceMap.get(currentPath);
        }
        return Optional.empty();
    }

    /**
     * 依据路径设置字典值
     * @param sourceMap 来源字典
     * @param path 路径
     * @param value 值
     */
    public static void setMapDataByPath(Map<String, Object> sourceMap, String path, Object value) {
        if (Objects.isNull(sourceMap)) {
            return;
        }
        if (StringUtils.isBlank(path)) {
            return;
        }
        String[] pathArray = path.split("\\.");
        int length = pathArray.length;
        int lastIndex = length - 1;
        for (int i = 0; i < length; i++) {
            String currentPath = pathArray[i];
            if (i == lastIndex) {
                sourceMap.put(currentPath, value);
                return;
            }
            Optional<Map<String, Object>> optCurrentMap = getMap(sourceMap, currentPath);
            Map<String, Object> currentMap;
            if (optCurrentMap.isPresent()) {
                currentMap = optCurrentMap.get();
            } else {
                currentMap = new HashMap<>();
                sourceMap.put(currentPath, currentMap);
            }
            sourceMap = currentMap;
        }
    }

    /**
     * 将通用列表转换成字典列表
     * @param list 通用列表
     * @return 字典列表
     */
    public static List<LinkedHashMap<String, Object>> convertToListLinkedHashMap(List<?> list) {
        if (CollectionUtils.isEmpty(list)) {
            return new ArrayList<>(0);
        }
        return list.stream().map(BeanUtil::object2LinkedHashMap).collect(Collectors.toList());
    }

    /**
     * 将通用列表转换成字典列表
     * @param sinkFieldsJson sr 表的橍位設定 json, 參考 etl_endgine.sinkFieldsJson 欄位
     * @param data 通用列表
     * @return 字典列表
     */
    public static List<LinkedHashMap<String, Object>> convertToListLinkedHashMap(String sinkFieldsJson, List<?> data) {

        // 整理 sr 欄位的順序
        JSONArray configArray = JSONArray.parseArray(sinkFieldsJson);
        List<String> configs = IntStream.range(0, configArray.size())
                .mapToObj(configArray::getJSONObject)
                .map(row -> row.getOrDefault("fieldCode", "").toString())
                .collect(Collectors.toList());

        // 轉換 data 資料
        List<JSONObject> mData = data.stream()
                .map(row -> (JSONObject) JSONObject.toJSON(row))
                .collect(Collectors.toList());

        // 依 sr 欄位的順序, 產生新的資料結構
        return mData.stream()
                .map(row -> {
                    LinkedHashMap<String, Object> newRow = new LinkedHashMap<>();
                    configs.forEach(configKey -> {
                        Object value = row.get(configKey);
                        if (value != null) {
                            // 有數據才放入
                            newRow.put(configKey, value);
                        }
                    });
                    return newRow;
                })
                .collect(Collectors.toList());
    }

    /**
     * 获取第一层特定键T结果
     * @param sourceMap 来源字典
     * @param key 特定键
     * @param <K> 键泛型
     * @param <V> 值泛型
     * @param <T> 返回值泛型
     * @return 找不到返回Optional.empty()
     */
    public static <K, V, T> Optional<T> getTByMap(Map<K, V> sourceMap, K key) {
        if (CollectionUtils.isEmpty(sourceMap)) {
            return Optional.empty();
        }
        V contentObj = sourceMap.get(key);
        try {
            return Optional.of((T) contentObj);
        } catch (Exception ignored) {
            return Optional.empty();
        }
    }
}
