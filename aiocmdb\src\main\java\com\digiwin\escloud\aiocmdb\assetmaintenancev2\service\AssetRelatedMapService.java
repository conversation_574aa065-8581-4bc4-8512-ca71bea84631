package com.digiwin.escloud.aiocmdb.assetmaintenancev2.service;

import cn.hutool.core.lang.Pair;
import com.digiwin.escloud.aiocmdb.asset.model.AssetRelatedCategoryClassification;
import com.digiwin.escloud.aiocmdb.assetmaintenancev2.model.AssetAttribute;
import com.digiwin.escloud.aiocmdb.assetmaintenancev2.model.AssetRelatedMap;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

public interface AssetRelatedMapService {

    Integer deleteRelatedAsset(String eid, long assetRelatedId);
    Map<String, Object> addRelatedAsset(List<AssetRelatedMap> assetRelatedMaps);
    Pair<Map<String, Object>, List<LinkedHashMap<String, Object>>> saveAssetAttribute(String modelCode, AssetAttribute assetAttribute);
    Optional<String> getNewAssetCode(String modelCode, String eid, AssetAttribute assetAttribute);
    List<AssetRelatedCategoryClassification> queryRelatedAssets(String eid, String primaryAssetId);
}
