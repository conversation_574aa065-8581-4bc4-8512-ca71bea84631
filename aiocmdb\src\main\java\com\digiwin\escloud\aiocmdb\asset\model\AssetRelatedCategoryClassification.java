package com.digiwin.escloud.aiocmdb.asset.model;

import cn.hutool.core.lang.Pair;
import com.digiwin.escloud.aiocmdb.assetmaintenancev2.model.AssociatedWithAssets;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.Data;

import java.util.*;


@Data
@JsonInclude(Include.NON_NULL)
public class AssetRelatedCategoryClassification {

    private String classificationName;
    private String classificationId;
    private String classificationIconUrl;
    private List<AssetCategorySimple> listAssetCategory; // 資產分類明細(mybatis會填入)

    // 以下為整理用
    private List<AssociatedWithAssets> listAssociatedWithAssets = new ArrayList<>(); //跟分類相關的資產編號
    private Map<String, AssociatedWithAssets> relateStoreDataMap = new HashMap<>();
    private List<AssociatedWithAssets> relateStoreData;

    public void groupRelateAssets(AssociatedWithAssets awa) {
        this.listAssetCategory.stream()
                .filter(row -> {
                    boolean rule1 = row.getModelCode().equals(awa.getAssociatedModelCode());
                    boolean rule2 = row.getModelCode().equals(awa.getAssociatedSinkName());
                    return rule1 && rule2;
                })
                .findFirst()
                .ifPresent(category -> {
                    relateStoreDataMap.put(awa.getAssociatedModelCode(),
                            new AssociatedWithAssets(
                                    awa.getAssociatedAssetId(),
                                    awa.getAssociatedModelCode(),
                                    awa.getAssociatedSinkName()
                            )
                    );
                    listAssociatedWithAssets.add(awa);
                });
    }
    
    public void addStoreData(String modelCode, List<?> data) {
        relateStoreDataMap.get(modelCode).addData(data);
    }
    
    public void finalProcessCategoryClassification() {
        relateStoreDataMap.forEach((modelCode, awa) -> {
            // 處理分類名稱
            Pair<String, String> categoryInfo = listAssetCategory.stream()
                    .filter(row -> row.getModelCode().equals(modelCode))
//                    .map(AssetCategorySimple::getCategoryName)
                    .map(row -> Pair.of(row.getCategoryName(), row.getCategoryIconUrl()))
                    .findFirst()
                    .orElse(Pair.of("", ""));

            String categoryName = categoryInfo.getKey();
            String categoryIconUrl = categoryInfo.getValue();

            categoryName = categoryName.isEmpty() ? modelCode : categoryName;
            awa.setCategoryName(categoryName);
            awa.setCategoryIconUrl(categoryIconUrl);
        });

        this.relateStoreData = new ArrayList<>(relateStoreDataMap.values());
    }

    public void clearUnnecessaryInfo() {
        if (!Objects.isNull(this.listAssociatedWithAssets)) {
            this.listAssociatedWithAssets.clear();
            this.listAssociatedWithAssets = null;
        }
        if (!Objects.isNull(this.listAssetCategory)) {
            this.listAssetCategory.clear();
            this.listAssetCategory = null;
        }
        if (!Objects.isNull(this.relateStoreDataMap)) {
            this.relateStoreDataMap.clear();
            this.relateStoreDataMap = null;
        }
    }
}
