package com.digiwin.escloud.aiocmdb.etl.service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.digiwin.escloud.common.constant.BigDataConstant;
import com.digiwin.escloud.common.exception.BizException;
import com.digiwin.escloud.common.model.ResponseCode;
import com.digiwin.escloud.common.response.BaseResponse;
import com.digiwin.escloud.aiocmdb.assetmaintenance.bigdata.BigDataUtil;
import com.digiwin.escloud.aiocmdb.etl.annotation.EtlStore;
import com.digiwin.escloud.aiocmdb.etl.dao.EtlMapper;
import com.digiwin.escloud.aiocmdb.etl.dto.EtlEngineSaveReqDTO;
import com.digiwin.escloud.aiocmdb.etl.model.EtlModelField;
import com.digiwin.escloud.aiocmdb.utils.EtlUtils;
import com.digiwin.escloud.etl.model.EtlEngine;
import com.digiwin.escloud.etl.model.EtlEngineTable;
import com.digiwin.escloud.etl.model.SinkType;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @Date 2024/11/25 14:53
 * @Created yanggld
 * @Description
 */
@Slf4j
@Service
@EtlStore(value = SinkType.STARROCKS)
public class StarRocksStoreService extends AbstractJdbcStoreService implements IEtlStoreService {

    public static final Long DEFAULT_TTL = 365L;
    public static final String DEFAULT_TTL_UNIT = "day";
    // 如果 COLUMN_KEY 为 PRI，则该列是主键，或是多列主键中的一列。
    public static final String PRI = "PRI";
    public static final int BUCKET_SIZE = 3;
    @Autowired
    private BigDataUtil bigDataUtil;
    @Autowired
    private EtlMapper etlMapper;

    @Override
    public BigDataUtil getBigDataUtil() {
        return this.bigDataUtil;
    }

    public String schemaSql(String schemaName, String schemaExtend) {
        schemaName = getSchemaName(schemaName);
        String sql = "CREATE DATABASE if not exists " + schemaName;
        if (StringUtils.isNotEmpty(schemaExtend)) {
            sql += " " + schemaExtend;
        }
        return sql;
    }

    @Override
    public String tableSql(EtlEngine etl) {
        String tableName = etl.getSinkName();
        String schema = etl.getSchemaName();
        String tablePk = etl.getSinkPk();
        String sinkFieldsJson = etl.getSinkFieldsJson();
        if (StringUtils.isEmpty(sinkFieldsJson)) {
            throw new RuntimeException("schema:" + schema + ",tableName:" + tableName + ",sinkFieldsJson is empty");
        }
        List<EtlModelField> etlModelFields = JSONArray.parseArray(sinkFieldsJson, EtlModelField.class);
        String sinkExtend = etl.getSinkExtend();
        long id = etl.getId();
        //处理前端没有传入sinkExtend字段，则从etl表中获取
        if (id > 0) {
            EtlEngine etlEngineById = etlMapper.getById(id);
            sinkExtend = etlEngineById.getSinkExtend();
        }
        String sinkTableTtl = etl.getSinkTableTtl();
        String sinkTableTtlField = etl.getSinkTableTtlField();
        String sinkTableTtlUnit = etl.getSinkTableTtlUnit();
        String sql = genSql(schema, tableName, tablePk, etlModelFields, sinkTableTtl, sinkTableTtlField, sinkTableTtlUnit, sinkExtend);
        return sql;
    }

    @Override
    protected String alterDdlSql(String schemaName, String sinkName, EtlEngineSaveReqDTO.EtlSinkOp sinkOp) {
        String op = sinkOp.getOp();
        schemaName = getSchemaName(schemaName);
        if ("u".equalsIgnoreCase(op)) {
            String afterColumn = sinkOp.getAfter().getAfterColumn();
            String comment = sinkOp.getAfter().getComment();
            String ddlSql = "alter table `" + schemaName + "`.`" + sinkName + "` MODIFY  column " + sinkOp.getBefore().getName() + " " + transType(sinkOp.getAfter().getType()) + " ";
            if (StringUtils.isNoneBlank(comment)) {
                ddlSql += " comment '" + comment + " ' ";
            }
//            if (StringUtils.isNoneBlank(afterColumn)) {
//                ddlSql += afterColumn;
//            }
            return ddlSql;
        } else if ("d".equalsIgnoreCase(op)) {
            return "alter table `" + schemaName + "`.`" + sinkName + "` drop column " + sinkOp.getBefore().getName();
        } else if ("c".equalsIgnoreCase(op)) {
            String afterColumn = sinkOp.getAfter().getAfterColumn();
            String comment = sinkOp.getAfter().getComment();
            String ddlSql = "alter table `" + schemaName + "`.`" + sinkName + "` add column " + sinkOp.getAfter().getName() + " " + transType(sinkOp.getAfter().getType()) + " ";
            if (StringUtils.isNoneBlank(comment)) {
                ddlSql += " comment '" + comment + " ' ";
            }
//            if (StringUtils.isNoneBlank(afterColumn)) {
//                ddlSql += afterColumn;
//            }
            return ddlSql;
        }
        return "";
    }

    private String getSchemaName(String schema) {
        if (StringUtils.isEmpty(schema) || schema.equalsIgnoreCase("default")) {
            schema = BigDataConstant.DEFAULT_SR_DB;
        }
        return schema;
    }

    private String genSql(String schema, String tableName, String tablePk, List<EtlModelField> etlModelFields, String sinkTableTtl, String sinkTableTtlField, String sinkTableTtlUnit, String sinkExtend) {
        if (CollectionUtils.isEmpty(etlModelFields)) {
            throw new RuntimeException("schema:" + schema + ",tableName:" + tableName + ",etlModelFields is empty");
        }
        schema = getSchemaName(schema);
        StringBuffer createTableSQL = new StringBuffer("create table if not exists `").append(schema).append("`.`").append(tableName).append("`(");
        List<String> fields = new ArrayList<>();
        for (int i = 0; i < etlModelFields.size(); i++) {
            EtlModelField etlModelField = etlModelFields.get(i);
            String field = etlModelField.getFieldCode();
            if (fields.contains(field)) {
                continue;
            }
            fields.add(field);
            String type = StringUtils.isEmpty(etlModelField.getFieldType()) ? "String" : etlModelField.getFieldType();
            type = transType(type);
            createTableSQL.append(field).append(" ").append(type);
            String aggType = etlModelField.getAggType();
            String defaultValue = etlModelField.getDefaultValue();
            if (!StringUtils.isEmpty(aggType)) {
                createTableSQL.append(" ").append(aggType);
            }
            if (!StringUtils.isEmpty(defaultValue)) {
                createTableSQL.append(" DEFAULT \"").append(defaultValue).append("\"");
            }
            if (i < etlModelFields.size() - 1) {
                createTableSQL.append(",");
            }
        }
        createTableSQL.append(")");
        // 引擎等参数
        if (StringUtils.isNoneBlank(sinkExtend)) {
            createTableSQL.append(sinkExtend);
        } else {
            if (!StringUtils.isBlank(tablePk)) {
                createTableSQL.append("primary KEY (").append(tablePk).append(") ");
            }
            if (!StringUtils.isBlank(sinkTableTtlField)) {
                createTableSQL.append("PARTITION BY RANGE (" + sinkTableTtlField + ")() ");
            }
            if (!StringUtils.isBlank(tablePk)) {
                createTableSQL.append("DISTRIBUTED BY HASH (").append(tablePk).append(") ");
            } else {
                createTableSQL.append("DISTRIBUTED BY RANDOM ");
            }
            createTableSQL.append("BUCKETS " + BUCKET_SIZE + " ");
            if (!StringUtils.isBlank(sinkTableTtlField)) {
                sinkTableTtl = StringUtils.isEmpty(sinkTableTtl) ? "365" : sinkTableTtl;
                sinkTableTtlUnit = StringUtils.isEmpty(sinkTableTtlUnit) ? "DAY" : sinkTableTtlUnit;
                createTableSQL.append("PROPERTIES(\n" +
                        "    \"dynamic_partition.time_unit\" = \"" + sinkTableTtlUnit + "\",\n" +
                        "    \"dynamic_partition.start\" = \"-" + sinkTableTtl + "\",\n" +
                        "    \"dynamic_partition.end\" = \"3\",\n" +
                        "    \"dynamic_partition.prefix\" = \"p\"\n" +
                        ");");
            }
        }
        return createTableSQL.toString();
    }

    public String transType(String type) {
        if (INT.equals(type)) {
            return "INT";
        } else if (BIGINT.equals(type)) {
            return "BIGINT";
        } else if (BIT.equals(type)) {
            return "STRING";
        } else if (CHAR.equals(type)) {
            return "CHAR(10)";
        } else if (VARCHAR.equals(type)) {
            return "STRING";
        } else if (TEXT.equals(type)) {
            return "STRING";
        } else if (DECIMAL.equals(type)) {
            return "DECIMAL(12,2)";
        } else if (BOOLEAN.equals(type)) {
            return "BOOLEAN";
        } else if (DATE.equals(type)) {
            return "DATE";
        } else if (TIME.equals(type)) {
            return "String";
        } else if (DATETIME.equals(type)) {
            return "DATETIME";
        } else if (ENUM.equals(type)) {
            return "STRING";
        } else if (RESOURCE.equals(type)) {
            return "STRING";
        }
        return type;
    }

    public String reTransType(String type) {
        if (INT.equalsIgnoreCase(type)) {
            return "INT";
        } else if (BIGINT.equalsIgnoreCase(type)) {
            return "BIGINT";
        } else if (StringUtils.startsWithIgnoreCase(type, "varchar")) {
            return "VARCHAR";
        } else if (StringUtils.startsWithIgnoreCase(type, CHAR)) {
            return "CHAR";
        } else if (StringUtils.startsWithIgnoreCase(type, DECIMAL)) {
            return "DECIMAL";
        } else if (BOOLEAN.equalsIgnoreCase(type)) {
            return "BOOLEAN";
        } else if (DATE.equalsIgnoreCase(type)) {
            return "DATE";
        } else if (DATETIME.equalsIgnoreCase(type)) {
            return "DATETIME";
        }
        return "VARCHAR";
    }


    @Override
    public BaseResponse createSink(EtlEngineSaveReqDTO etlEngine) {
        if (etlEngine != null && etlEngine.isSinkNameCreatedSelf() && etlEngine.isSinkSchemaCreatedSelf()) {
            String schemaDdl = getSchemaDdl(etlEngine);
            BaseResponse resp = bigDataUtil.srExecuteSql(schemaDdl);
            if (!resp.checkIsSuccess()) {
                return resp;
            }
            BaseResponse resp2 = bigDataUtil.srExecuteSql(etlEngine.getSinkDdl());
            if (!resp2.checkIsSuccess()) {
                return resp2;
            }
            // 查询是否创建存储
            List<EtlEngineTable> etlEngineTables = etlMapper.getEtlTableByEtlId(etlEngine.getId());
            if (CollectionUtils.isEmpty(etlEngineTables)) {
                int count = etlMapper.insertEtlTable(EtlEngineTable.builder(etlEngine));
                // 创建之后的回调
                if (etlEngine != null && !etlEngine.isSinkEnable()) {
                    etlMapper.enableEtlStatus(etlEngine.getId(), true);
                }
                return BaseResponse.ok(count);
            } else {
                return BaseResponse.error(ResponseCode.ETL_TABLE_IS_EXIST);
            }
        }
        return null;
    }

    @Override
    public Object updateSink(EtlEngineSaveReqDTO etlEngineSaveReqDTO) {
        long etlId = etlEngineSaveReqDTO.getId();
        // 如果etlEngine处于开启状态，先关闭
        EtlEngine etlEngine = etlMapper.getById(etlId);
        boolean isSinkEnable = true;
        if (etlEngine != null) {
            isSinkEnable = etlEngine.isSinkEnable();
        }
        BaseResponse response = updateSinkTable(etlEngineSaveReqDTO);
        if (!response.checkIsSuccess()) {
            throw new BizException(response);
        }
        // 如果关闭了etlEngine，则开启
        if (!isSinkEnable) {
            etlMapper.enableEtlStatus(etlId, true);
        }
        // 创建表结果存储
        int count = etlMapper.insertEtlTable(EtlEngineTable.builder(etlEngine));
        return count;
    }


    private BaseResponse updateSinkTable(EtlEngineSaveReqDTO etlEngineSaveReqDTO) {
        // 获取每个更新的语句ddl
        List<String> alterDdlList = new ArrayList<>();
        String changeOp = etlEngineSaveReqDTO.getChangeOp();
        if (StringUtils.isBlank(changeOp)) {
            return BaseResponse.ok();
        }
        List<EtlEngineSaveReqDTO.EtlSinkOp> changeOpList = JSONArray.parseArray(changeOp).toJavaList(EtlEngineSaveReqDTO.EtlSinkOp.class);
        String schemaName = etlEngineSaveReqDTO.getSchemaName();
        String sinkName = etlEngineSaveReqDTO.getSinkName();
        alterDdlList.addAll(getAlterDdl(changeOpList, schemaName, sinkName));
        // 执行更新
        for (String alterDdl : alterDdlList) {
            if (log.isDebugEnabled()) {
                log.debug("alter starrocks :{}", alterDdl);
            }
            BaseResponse resp = bigDataUtil.srExecuteSql(alterDdl);
            if (!resp.checkIsSuccess()) {
                return resp;
            }
        }
        return BaseResponse.ok(alterDdlList.size());
    }

    @Override
    public List<Map<String, Object>> getTableInfo(String schemaName, String sinkName) {
        String newSchemaName = EtlUtils.getSchemaName(schemaName, SinkType.STARROCKS.getName());
        String sql = "select COLUMN_NAME,COLUMN_TYPE,COLUMN_KEY from information_schema.columns where table_schema ='" + newSchemaName + "' and TABLE_NAME = '" + sinkName + "' ";
        List<Map<String, Object>> maps = bigDataUtil.starrocksQuery(sql);
        List<Map<String, Object>> result = maps.stream().map(sourceMap -> {
            Map<String, Object> map = new HashMap<>();
            map.put("fieldCode", sourceMap.get("COLUMN_NAME"));
            map.put("fieldType", reTransType(sourceMap.get("COLUMN_TYPE").toString()));
            map.put("COLUMN_KEY", sourceMap.get("COLUMN_KEY"));
            return map;
        }).collect(Collectors.toList());
        return result;
    }


    @Override
    public Boolean tableExists(String schemaName, String sinkName) {
        String newSchemaName = EtlUtils.getSchemaName(schemaName, SinkType.STARROCKS.getName());
        String sql = "select count(*) from " + newSchemaName + "." + sinkName;
        List<Map<String, Object>> query = bigDataUtil.starrocksQuery(sql);
        return !CollectionUtils.isEmpty(query);
    }

    @Override
    public String getTableTTL(String schemaName, String sinkName) {
        String newSchemaName = EtlUtils.getSchemaName(schemaName, SinkType.STARROCKS.getName());
        String sql = "select * from information_schema.tables_config where table_schema ='" + newSchemaName + "' and TABLE_NAME = '" + sinkName + "' ";
        List<Map<String, Object>> maps = bigDataUtil.starrocksQuery(sql);
        try {
            if (CollectionUtils.isNotEmpty(maps)) {
                Map<String, Object> map = maps.get(0);
                String partitionKey = map.getOrDefault("PARTITION_KEY", "").toString();
                String propertiesObj = map.getOrDefault("PROPERTIES", "").toString();
                if (StringUtils.isNoneBlank(propertiesObj)) {
                    Boolean dynamicPartition = JSONObject.parseObject(propertiesObj).getBoolean("dynamic_partition.enable");
                    if (dynamicPartition != null && dynamicPartition) {
                        if (StringUtils.isNoneBlank(partitionKey)) {
                            return partitionKey.replace("`", "");
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("getTableDDL error", e);
        }
        return null;
    }
}
