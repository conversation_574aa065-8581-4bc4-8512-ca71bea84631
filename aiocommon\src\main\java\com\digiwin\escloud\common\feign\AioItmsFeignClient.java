package com.digiwin.escloud.common.feign;

import com.digiwin.escloud.aiobasic.edr.model.edr.EdrDevice;
import com.digiwin.escloud.aiobasic.report.model.EdrReport;
import com.digiwin.escloud.aiobasic.report.model.VulnerabilityEsReport;
import com.digiwin.escloud.aiocdp.group.report.AioCdpGroupProfilesReport;
import com.digiwin.escloud.aioitms.model.authorize.AiopsAuthStatus;
import com.digiwin.escloud.aioitms.model.bigdata.UploadBigDataContext;
import com.digiwin.escloud.aioitms.model.collectapp.ProductAppCodeMapping;
import com.digiwin.escloud.aioitms.model.device.AiopsKitDevice;
import com.digiwin.escloud.aioitms.model.device.DeviceWarningNotifyMapping;
import com.digiwin.escloud.aioitms.model.device.TenantDeviceCnt;
import com.digiwin.escloud.aioitms.model.device.TenantDeviceCntReq;
import com.digiwin.escloud.aioitms.model.dot.AiopsDotDeviceMapping;
import com.digiwin.escloud.aioitms.model.instance.*;
import com.digiwin.escloud.aioitms.model.report.ReportDTO;
import com.digiwin.escloud.aioitms.report.model.db.DbReportRecord;
import com.digiwin.escloud.common.model.ResponseBase;
import com.digiwin.escloud.common.response.BaseResponse;
import com.digiwin.escloud.userv2.model.EsCustomers;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.util.Collection;
import java.util.List;
import java.util.Map;

@FeignClient(value = "aioitms", url = "${feign.aioitms.url:}")
public interface AioItmsFeignClient {

    @GetMapping("/collectapp/bind/productcodes")
    ResponseBase getBindProductCodes(@RequestParam(value = "sourceModelCode") String sourceModelCode,
                                     @RequestParam(value = "targetModelCode") String targetModelCode);

    @ApiOperation(value = "检查是否有设备预警通知存在")
    @GetMapping(value = "/v2/device/warning/notify/exist")
    BaseResponse checkDeviceWarningNotifyExist(
            @ApiParam(value = "来源Id(租户通知群组Id或租户群组通知人Id)", required = true)
            @RequestParam(value = "sourceId") Long sourceId,
            @ApiParam(value = "通知类别")
            @RequestParam(value = "notifyCategoryString", required = false, defaultValue = "") String notifyCategoryString);


    @ApiOperation(value = "查询warningNotifyMapping")
    @GetMapping(value = "/v2/device/warningNotifyMapping")
    List<DeviceWarningNotifyMapping> selectDeviceWarningNotifyMapping(
            @RequestParam(value = "adcwId") Long adcwId,
            @RequestParam(value = "notifyCategory") String notifyCategory);

    @ApiOperation(value = "授权到期异动运维实例授权状态")
    @PutMapping(value = "/aiops/instance/contract/expire")
    BaseResponse modifyAiopsInstanceAuthStatusByContractExpire(
            @ApiParam(value = "租户模组合约明细部分信息列表", required = true)
            @RequestBody() List<Map<String, Object>> partTmcdList);

    @ApiOperation("保存设备基础信息")
    @PostMapping(value = "/v2/device/info")
    BaseResponse saveDeviceInfo(
            @ApiParam(value = "设备基础信息对象", required = true)
            @RequestBody AiopsKitDevice device);

    @ApiOperation(value = "依据租户模组合约明细Id列表获取实例已授权数量字典")
    @PostMapping(value = "/aiops/instance/authed/count/map/by/tmcd/id/list")
    BaseResponse<Map<Long, Integer>> getAuthedCountMapByTmcdIdList(
            @ApiParam(value = "租户Id")
            @RequestParam(value = "eid", required = false) Long eid,
            @ApiParam(value = "是否包含占用授权标的")
            @RequestParam(value = "isContainHoldAuth", required = false) Boolean isContainHoldAuth,
            @ApiParam(value = "tmcdIdList")
            @RequestBody(required = false) Collection<Long> tmcdIdList);

    @ApiOperation(value = "保存设备网关运维设备映射")
    @PostMapping(value = "/aiops/dot/device/mapping")
    BaseResponse saveDotDeviceMapping(
            @ApiParam(value = "设备网关运维设备映射", required = true)
            @RequestBody() AiopsDotDeviceMapping aiopsDotDeviceMapping);

    @ApiOperation("获取设备基础信息")
    @GetMapping(value = "/v2/device/{deviceId}/basic/info")
    BaseResponse<AiopsKitDevice> getDeviceBasicInfo(
            @ApiParam(value = "设备Id", required = true)
            @PathVariable("deviceId") String deviceId);

    @ApiOperation("依据运维设备实例映射关系上下文添加设备运维实例映射")
    @PostMapping(value = "/v2/device/aiops/instance/mapping/by/arc")
    BaseResponse addDeviceAiopsInstanceMappingByArc(
            @ApiParam(value = "运维设备实例映射关系上下文", required = true)
            @RequestBody AdimRelateContext arc);

    @ApiOperation("依据运维设备实例映射关系上下文移除设备运维实例映射")
    @DeleteMapping(value = "/v2/device/aiops/instance/mapping/by/arc")
    BaseResponse removeDeviceAiopsInstanceMappingByArc(
            @ApiParam(value = "运维设备实例映射关系上下文", required = true)
            @RequestBody AdimRelateContext arc);

    @ApiOperation(value = "批量保存EDR实例")
    @PostMapping(value = "/aiops/instance/edr/batch/save")
    BaseResponse batchSaveEDRInstance(
            @ApiParam("是否自动开启授权")
            @RequestParam(value = "autoOpenAuth", required = false) Boolean autoOpenAuth,
            @ApiParam("来源设备Id")
            @RequestParam(value = "sourceDeviceId", required = false) String sourceDeviceId,
            @ApiParam(value = "EDR实例列表", required = true)
            @RequestBody() List<AiopsEDRInstance> aiopsEDRInstanceList);

    @ApiOperation(value = "批量保存智能电表实例")
    @PostMapping(value = "/aiops/instance/smart/meter/batch/save")
    BaseResponse batchSaveSmartMeterInstance(
            @ApiParam("是否自动开启授权")
            @RequestParam(value = "autoOpenAuth", required = false) Boolean autoOpenAuth,
            @ApiParam("来源设备Id")
            @RequestParam(value = "sourceDeviceId", required = false) String sourceDeviceId,
            @ApiParam(value = "智能电表实例列表", required = true)
            @RequestBody() List<AiopsSmartMeterInstance> aiopsSmartMeterInstanceList);

    @ApiOperation("上传数据到大数据平台")
    @PostMapping("/data/upload/to/big/data")
    BaseResponse dataUploadToBigData(
            @ApiParam("收集项Id")
            @RequestParam(value = "accId", required = false) Long accId,
            @ApiParam("收集项代号")
            @RequestParam(value = "collectCode", required = false) String collectCode,
            @ApiParam(value = "上传大数据数据上下文列表", required = true)
            @RequestBody() List<UploadBigDataContext> ubdcList);

    @ApiOperation(value = "依据条件字典列表修正实例表租户模组明细Id")
    @PostMapping(value = "/aiops/instance/fix/tmcd/id/by/map/list")
    BaseResponse fixAiopsInstanceTmcdIdByMapList(
            @ApiParam(value = "条件字典列表", required = true)
            @RequestBody() List<Map<String, Object>> mapList);

    @ApiOperation(value = "依租户模组合约明细Id列表获取产品应用代号字典")
    @PostMapping(value = "/collectapp/code/map/list/by/tmcd/id/list")
    BaseResponse<List<ProductAppCodeMapping>> getProductAppCodeMapListByTmcdIdList(
            @ApiParam(value = "租户模组合约明细Id列表")
            @RequestBody() List<Long> tmcdIdList);

    @ApiOperation(value = "漏洞报告生成")
    @PostMapping(value = "/vulnerability/es/report")
    void vulnerabilityReportGenerate(
            @RequestBody VulnerabilityEsReport report);

    @ApiOperation(value = "修正实例授权状态")
    @PostMapping(value = "/aiops/instance/fixAiopsInstanceStatusByTmcdIdList")
    BaseResponse fixAiopsInstanceStatusByTmcdIdList(
            @RequestBody List<AiopsInstanceFixParam> fixParamList);

    @PostMapping("/industry/getTagData")
    ResponseBase getTagData(
            @RequestBody List<Long> tagIdList
    );

    @PostMapping("/industry/getTagCount")
    ResponseBase getTagCount(
            @RequestBody List<Long> tagIdList,
            @RequestParam ("eid") String eid,
            @RequestParam ("appCode") String appCode
    );

    @PostMapping("/industry/getCXOQuickFilterDetail")
    BaseResponse getCXOQuickFilterDetail(
            @RequestBody com.digiwin.escloud.userv2.model.params.CxoQuickFilteringParams params
    );

    @PostMapping("/industry/getCXOQuickFilterDetail")
    BaseResponse getReportRecord(
            @RequestBody com.digiwin.escloud.userv2.model.params.CxoQuickFilteringParams params
    );

    @PostMapping("/industry/setTableData")
    ResponseBase<List<EsCustomers>> setTableData(
            @RequestBody List<EsCustomers> esCustomersList,
            @RequestParam(value = "tableName") String tableName
    );

    @PostMapping("/edr/v2/es/report/generate")
    void edrv2ReportGenerate(
            @RequestBody EdrReport report
    );

    @GetMapping("/edr/v2/es/report")
    EdrReport edrv2GetReport(
            @RequestParam("id") String id
    );

    @PostMapping("/db/report")
    BaseResponse oracleV2Reportgenerate(
            @RequestBody DbReportRecord  dbReportRecord
    );

    @GetMapping("/db/report/getDbReportList")
    ResponseBase<Map<String,List<ReportDTO>>> getDbReportList(@RequestParam("eid") Long eid);

    @PostMapping("/cdp/group/report")
    BaseResponse saveCdpGroupReport(@RequestBody AioCdpGroupProfilesReport report);

    @DeleteMapping("/cdp/group/report")
    BaseResponse deleteReport(
            @RequestBody List<String> idList
    ) ;

    @PostMapping("/data/api/{apiCode}")
    ResponseBase bigDataQuery(@PathVariable("apiCode") String apiCode,
                                     @RequestBody Map<String, String> queryParam);

    @ApiOperation(value = "根据deviceId查询Edr得serverId")
    @GetMapping(value = "/aiops/instance/getEdrServerId")
    BaseResponse<List<EdrDevice>> getEdrServerId(
            @ApiParam(value = "运维实例Id", required = true)
            @RequestParam("deviceId") String deviceId);

    @PostMapping("/v2/device/tenant/cnt")
    BaseResponse<List<TenantDeviceCnt>> getTenantDeviceCnt(@RequestBody() TenantDeviceCntReq tenantDeviceCntReq);

    @ApiOperation(value = "获取预警采集通知方式")
    @GetMapping(value = "/collectwarning/warning/notify/way")
    BaseResponse<List<String>> getWarningNotifyWayByEid(@RequestParam Long eid);

    @ApiOperation(value = "获取预警采集通知级别")
    @GetMapping(value = "/collectwarning/warning/notify/level")
    BaseResponse<List<String>> getWarningNotifyLevel(@RequestParam Long eid);

    @ApiOperation(value = "获取云备援收集项详细信息")
    @PutMapping(value = "/aiops/instance/eai/cloud/detail")
    BaseResponse getAiopsEaiCloudInstanceCollectDetail(
            @ApiParam(value = "客代")
            @RequestParam(value = "serviceCode", defaultValue = "") String serviceCode,
            @ApiParam(value = "上传模型code", required = true)
            @RequestParam(value = "uploadDataModelCode") String uploadDataModelCode,
            @ApiParam(value = "类型", required = true)
            @RequestParam(value = "eaiType") String eaiType,
            @ApiParam(value = "aiopsItemType")
            @RequestParam(value = "aiopsItemType", required = false) String aiopsItemType);

    @PostMapping("/data/api/flume")
    ResponseBase webLogAggQuery(@RequestBody() List<Map<String, Object>> mapList);

    @PostMapping(value = "/aiops/instance/getCommonAiopsInstanceAssetListNoPage")
    BaseResponse<List<com.digiwin.escloud.aioitms.model.instance.MergeStatisticsDetailInfo>> getCommonAiopsInstanceAssetListNoPage(
            @ApiParam(value = "合并统计明细请求条件", required = true)
            @RequestBody com.digiwin.escloud.aioitms.model.instance.MergeStatisticsDetailRequest request);

    @ApiOperation("异动运维实例授权")
    @PutMapping(value = "/v2/authorize//aiops/instance")
    @Transactional(rollbackFor = Exception.class)
    BaseResponse modifyAiopsInstanceAuth(
            @ApiParam(value = "租户Id", required = true)
            @RequestParam(value = "eid") Long eid,
            @ApiParam(value = "运维授权状态", required = true)
            @RequestParam(value = "aiopsAuthStatus") AiopsAuthStatus aiopsAuthStatus,
            @ApiParam(value = "是否修改资产状态")
            @RequestParam(value = "modifyAsset",required = false) Boolean modifyAsset,
            @ApiParam(value = "运维项目上下文列表", required = true)
            @RequestBody() List<AiopsItemContextDTO> aicList
    );
}
