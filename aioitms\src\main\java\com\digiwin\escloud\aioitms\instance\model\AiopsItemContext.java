package com.digiwin.escloud.aioitms.instance.model;

import com.digiwin.escloud.aioitms.device.model.DeviceCollectDetail;
import com.digiwin.escloud.aioitms.model.authorize.AiopsAuthStatus;
import com.digiwin.escloud.aioitms.upgrade.model.AiopsUpgradeDcdpDetail;
import com.digiwin.escloud.aiouser.model.tenant.TenantModuleContractDetail;
import com.digiwin.escloud.common.util.LongUtil;
import com.digiwin.escloud.common.util.OperationUtil;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang.StringUtils;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.function.BiConsumer;

@ApiModel("运维项目上下文对象")
@Data
public class AiopsItemContext {

    public AiopsItemContext() {
        //默认是要关联设备的
        this.isAddInstanceCollectToDevice = true;
        this.isMapInstancesToDevices = true;
        this.hasAddedAiopsInstance = true;
        this.changeToUnauth = false;
        this.upgradeDcdpDetailList = new ArrayList<>();
    }

    public AiopsItemContext(String aiopsItem, String aiopsItemId, String sourceDeviceId) {
        this();
        this.aiopsItem = aiopsItem;
        this.aiopsItemId = aiopsItemId;
        this.sourceDeviceId = sourceDeviceId;
        if (StringUtils.isBlank(sourceDeviceId)) {
            //若来源设备Id为空，就认为不需关联设备
            this.isAddInstanceCollectToDevice = false;
            this.isMapInstancesToDevices = false;
        }
    }

    @ApiModelProperty("运维项目")
    private String aiopsItem;
    @ApiModelProperty("运维项目Id")
    private String aiopsItemId;
    @ApiModelProperty("来源设备Id")
    private String sourceDeviceId;

    @ApiModelProperty("是否添加实例收集项到设备(若为true，则sourceDeviceId必填)")
    private Boolean isAddInstanceCollectToDevice;
    @ApiModelProperty("是否映射实例到设备(若为true，则sourceDeviceId必填)")
    private Boolean isMapInstancesToDevices;
    @ApiModelProperty("原始执行参数内容(明文)-即为大数据平台储存的结构")
    private String oriExecParamsContent;
    @ApiModelProperty("是否需修正执行参数内容，默认false")
    private Boolean needFixExecParamsContent;
    @ApiModelProperty("设备收集项明细只与实例映射，默认false")
    private Boolean adcdOnlyMappingByAiId;

    @ApiModelProperty("是否通过同步线程创建设备收集项")
    private boolean isCreateAdcdBySync;
    @ApiModelProperty("规则引擎用授权状态")
    private Boolean reAuthStatus;

    // 10766【後端API】【Athena稳态自动更新】稳态自动更新的授权起讫同步逻辑调整，支持稳态根据敏态的合约管控自动更新
    @ApiModelProperty("是否獨立授權")
    private Boolean independentAuth;

    public void setReAuthStatus(AiopsAuthStatus aiopsAuthStatus) {
        if (aiopsAuthStatus == null) {
            this.reAuthStatus = null;
        } else {
            this.reAuthStatus = AiopsAuthStatus.getAuthStatus(aiopsAuthStatus);
        }
    }

    @ApiModelProperty("租户Id-会在检查合约时统一填写(前端不须关注)")
    @JsonIgnore
    private Long eid;
    @ApiModelProperty(value = "运维商模组类别明细Id(前端不须关注)", hidden = true)
    @JsonIgnore
    private Long samcdId;
    @ApiModelProperty(value = "运维项目类型(前端不须关注)", hidden = true)
    @JsonIgnore
    private String aiopsItemType;
    @ApiModelProperty(value = "是否占用授权(前端不须关注)", hidden = true)
    @JsonIgnore
    private Boolean holdAuth;
    @ApiModelProperty(value = "是否包含占用授权标的(前端不须关注)", hidden = true)
    @JsonIgnore
    private Boolean isContainHoldAuth;
    @ApiModelProperty(value = "执行参数模型编号(前端不须关注)", hidden = true)
    @JsonIgnore
    private String execParamsModelCode;
    @ApiModelProperty(value = "是否允许重复映射到设备(前端不须关注)", hidden = true)
    @JsonIgnore
    private Boolean allowDuplicateMapping;
    @ApiModelProperty(value = "授权来源的租户模组合约明细(前端不须关注)", hidden = true)
    @JsonIgnore
    private TenantModuleContractDetail sourceTmcd;
    @ApiModelProperty(value = "是否更新设备收集项执行参数内容(前端不须关注)", hidden = true)
    @JsonIgnore
    private Boolean needUpdateDeviceCollectExecParamsContent;
    @ApiModelProperty(value = "是否已添加运维实例(前端不须关注)", hidden = true)
    @JsonIgnore
    private Boolean hasAddedAiopsInstance;
    @ApiModelProperty(value = "是否异动为未授权(注意：该设定并不会刷新授权数量)(前端不须关注)", hidden = true)
    @JsonIgnore
    private Boolean changeToUnauth;
    @ApiModelProperty(value = "是否分离运维实例(前端不须关注)", hidden = true)
    @JsonIgnore
    private Boolean needSeparateAiopsInstance;

    @ApiModelProperty(value = "运维实例Id(前端不须关注)", hidden = true)
    @JsonIgnore
    private Long aiId;
    @ApiModelProperty(value = "设备运维实例映射Id(前端不须关注)", hidden = true)
    @JsonIgnore
    private Long adimId;
    @ApiModelProperty(value = "设备收集项明细Id(前端不须关注)", hidden = true)
    @JsonIgnore
    private Long adcdId;
    @ApiModelProperty(value = "设备主键Id(前端不须关注)", hidden = true)
    @JsonIgnore
    private Long adId;
    @ApiModelProperty(value = "创建实例时指定的运维实例Id(前端不须关注)", hidden = true)
    @JsonIgnore
    private Long specifyCreateAiId;
    @ApiModelProperty(value = "创建实例时指定的设备运维实例映射Id(前端不须关注)", hidden = true)
    @JsonIgnore
    private Long specifyCreateAdimId;

    @ApiModelProperty(value = "原始运维实例Id-进行转换实例使用(前端不须关注)", hidden = true)
    @JsonIgnore
    private Long oriAiId;
    @ApiModelProperty(value = "原始设备运维实例映射Id(前端不须关注)", hidden = true)
    @JsonIgnore
    private Long oriAdimId;

    @ApiModelProperty(value = "设备收集项明细添加后消费端(前端不须关注)", hidden = true)
    @JsonIgnore
    private BiConsumer<AiopsItemContext, List<DeviceCollectDetail>> addedAdcdConsumer;

    @ApiModelProperty(value = "有效的运维模组明细Id结果(前端不须关注)", hidden = true)
    @JsonIgnore
    private String validSamcdIdResult;
    @ApiModelProperty(value = "添加的设备收集项明细Id结果(前端不须关注)", hidden = true)
    @JsonIgnore
    private String addedAdcdIdResult;

    @ApiModelProperty(value = "执行参数选择accId备选列表(前端不须关注)", hidden = true)
    private List<Long> execParamsSelectAccIdList;

    @ApiModelProperty(value = "企业运维升级数采明细列表(前端不须关注)", hidden = true)
    @JsonIgnore
    private List<AiopsUpgradeDcdpDetail> upgradeDcdpDetailList;

    @ApiModelProperty(value = "添加收集项Id列表，当指定后(如果规则符合)会添加列表中的收集项到到设备上(前端不关注)", hidden = true)
    @JsonIgnore
    private Collection<Long> addAccIdList;
    @ApiModelProperty(value = "是否刷新收集项缓存(前端不关注)", hidden = true)
    @JsonIgnore
    private Boolean needClearDeviceCollectCache;
    public Boolean getNeedClearDeviceCollectCache() {
        return needClearDeviceCollectCache == null || needClearDeviceCollectCache;
    }

    @ApiModelProperty(value = "操作信息字典", hidden = true)
    @JsonIgnore
    private Map<String, Object> operationInfoMap;
    /**
     * 如果这个状态是true，则表示修改资产状态
     */
    private Boolean modifyAsset;

    public void fillOperationInfoMap() {
        operationInfoMap = OperationUtil.getHeaderOperationInfo();
    }

    @ApiModelProperty(value = "内部用-获取唯一业务主键", hidden = true)
    @JsonIgnore
    public String getAiopsInstanceBusinessKey() {
        return getAiopsInstanceKey(this.samcdId, this.aiopsItemId);
    }

    @ApiModelProperty(value = "内部用-获取键", hidden = true)
    @JsonIgnore
    public static String getAiopsInstanceKey(Long samcdId, String aiopsItemId) {
        return String.join("_", LongUtil.safeToString(samcdId), aiopsItemId);
    }


}
