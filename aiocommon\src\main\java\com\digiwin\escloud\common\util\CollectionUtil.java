package com.digiwin.escloud.common.util;

import java.util.*;

public final class CollectionUtil {

    /**
     * 安全取得第一筆
     * @param collection 集合
     * @return 集合
     */
    public static <T> Optional<T> getFirstSafe(Collection<T> collection) {
        return isEmpty(collection) ? Optional.empty() : Optional.ofNullable(collection.iterator().next());
    }


    /**
     * 判斷集合是否為空
     * @param collection 集合
     * @return 是否為空
     */
    public static Boolean isEmpty(Collection<?> collection) {
        return collection == null || collection.isEmpty();
    }


    /**
     * 判斷字典是否為空
     * @param map 字典
     * @return 是否為空
     */
    public static Boolean isEmpty(Map<?, ?> map) {
        return map == null || map.isEmpty();
    }

    /**
     * 判斷集合是否不為空
     * @param collection 集合
     * @return 是否不為空
     */
    public static Boolean isNotEmpty(Collection<?> collection) {
        return !isEmpty(collection);
    }


    /**
     * 判斷字典是否不為空
     * @param map 字典
     * @return 是否不為空
     */
    public static Boolean isNotEmpty(Map<?, ?> map) {
        return !isEmpty(map);
    }

    /**
     * 将对象转换为集合
     * @param obj 对象
     * @return 集合
     * @param <T> 泛型
     */
    public static <T> Collection<T> objectToCollection(Object obj) {
        return objectToCollection(obj, new ArrayList<>(0));
    }

    /**
     * 将对象转换为集合
     * @param obj 对象
     * @param defaultValue 默认值
     * @return 集合
     * @param <T> 泛型
     */
    public static <T> Collection<T> objectToCollection(Object obj, Collection<T> defaultValue) {
        if (Objects.isNull(obj)) {
            return defaultValue;
        }

        if (obj instanceof List) {
            return (List<T>) obj;
        }
        if (obj instanceof Collection) {
            return (Collection<T>) obj;
        }

        return defaultValue;
    }

    /**
     * 安全添加所有项目
     * @param source 来源
     * @param target 目标
     * @return 是否成功添加
     * @param <T> 泛型对象
     */
    public static <T> boolean safeAddAll(Collection<T> source, Collection<T> target) {
        if (isEmpty(source) || Objects.isNull(target)) {
            return false;
        }
        return target.addAll(source);
    }

    /**
     * 将一个Collection<T>拆分为多个集合
     *
     * @param <T>          泛型参数
     * @param original     原始的Collection<T>
     * @param size         每个集合的最大容量
     * @return             包含多个子集合的列表
     */
    public static <T> List<List<T>> splitCollection(Collection<T> original, int size) {
        if (original == null || original.isEmpty() || size <= 0) {
            throw new IllegalArgumentException("原始集合不能为空且拆分大小必须大于0");
        }

        List<List<T>> result = new ArrayList<>();
        List<T> currentBatch = new ArrayList<>(size);
        for (T element : original) {
            currentBatch.add(element);
            if (currentBatch.size() == size) {
                result.add(currentBatch);
                currentBatch = new ArrayList<>(size);
            }
        }

        if (!currentBatch.isEmpty()) {
            result.add(currentBatch);
        }

        return result;
    }


    /**
     * 将一个 LinkedHashMap 拆分为多个固定大小的子 LinkedHashMap
     *
     * @param map  要拆分的原始 LinkedHashMap
     * @param size 每个子 LinkedHashMap 的大小
     * @return 包含子 LinkedHashMap 的列表
     */
    public static <K, V> List<LinkedHashMap<K, V>> splitLinkedHashMap(LinkedHashMap<K, V> map, int size) {
        List<LinkedHashMap<K, V>> listOfMaps = new ArrayList<>();
        if (map == null || size <= 0) {
            return listOfMaps;
        }

        LinkedHashMap<K, V> currentMap = new LinkedHashMap<>();
        int counter = 0;
        for (Map.Entry<K, V> entry : map.entrySet()) {
            currentMap.put(entry.getKey(), entry.getValue());
            counter++;
            if (counter == size) {
                listOfMaps.add(currentMap);
                currentMap = new LinkedHashMap<>();
                counter = 0;
            }
        }

        if (!currentMap.isEmpty()) {
            listOfMaps.add(currentMap);
        }

        return listOfMaps;
    }
}
