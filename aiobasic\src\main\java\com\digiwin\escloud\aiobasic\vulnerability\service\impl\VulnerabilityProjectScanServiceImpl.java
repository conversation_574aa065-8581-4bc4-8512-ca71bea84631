package com.digiwin.escloud.aiobasic.vulnerability.service.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.read.listener.PageReadListener;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.fastjson.JSONObject;
import com.digiwin.escloud.aiobasic.assetrisk.model.enums.VulnerabilityLanguage;
import com.digiwin.escloud.aiobasic.assetrisk.service.impl.UploadFileService;
import com.digiwin.escloud.aiobasic.report.model.VulnerabilityReportContent;
import com.digiwin.escloud.aiobasic.vulnerability.dao.*;
import com.digiwin.escloud.aiobasic.vulnerability.model.*;
import com.digiwin.escloud.aiobasic.vulnerability.model.enums.VulnerabilityRepairStatus;
import com.digiwin.escloud.aiobasic.vulnerability.model.enums.VulnerabilityScanAssetStatusEnum;
import com.digiwin.escloud.aiobasic.vulnerability.model.excel.VulnerabilityScanData;
import com.digiwin.escloud.aiobasic.vulnerability.model.exception.VulnerabilityException;
import com.digiwin.escloud.aiobasic.vulnerability.model.param.*;
import com.digiwin.escloud.aiobasic.vulnerability.service.*;
import com.digiwin.escloud.common.controller.ParamCheckHelp;
import com.digiwin.escloud.common.model.ResponseCode;
import com.digiwin.escloud.common.response.BaseResponse;
import com.digiwin.escloud.common.util.CollectionUtil;
import com.digiwin.escloud.common.util.SnowFlake;
import com.digiwin.escloud.common.util.StringUtil;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.IOException;
import java.io.InputStream;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import static com.digiwin.escloud.aiobasic.vulnerability.model.enums.VulnerabilityRepairStatus.NOT_REPAIR;
import static com.digiwin.escloud.aiobasic.vulnerability.model.enums.VulnerabilityScanAssetStatusEnum.ASSET_NO_PLAN;
import static com.digiwin.escloud.common.model.ResponseCode.*;

@Slf4j
@Service
public class VulnerabilityProjectScanServiceImpl implements VulnerabilityProjectScanService, ParamCheckHelp {
    @Resource
    private VulnerabilityProjectScanMapper vulnerabilityProjectScanMapper;
    @Resource
    private VulnerabilityProjectScanAssetMapper vulnerabilityProjectScanAssetMapper;
    @Resource
    private VulnerabilityProjectMapper vulnerabilityProjectMapper;
    @Resource
    private VulnerabilityProjectAssetMapper vulnerabilityProjectAssetMapper;
    @Resource
    VulnerabilityReportRecordMapper vulnerabilityReportRecordMapper;
    @Resource
    VulnerabilityProjectService vulnerabilityProjectService;
    @Resource
    private VulnerabilityRepairRecordService vulnerabilityRepairRecordService;
    @Resource
    private VulnerabilityMaintenanceAssetService vulnerabilityMaintenanceAssetService;
    @Resource
    private VulnerabilityStoreMapper vulnerabilityStoreMapper;
    @Resource
    private VulnerabilityRepairPlanMapper vulnerabilityRepairPlanMapper;
    @Resource
    private ServiceMaintenanceService serviceMaintenanceService;
    @Resource
    private VulnerabilityReportRecordServiceImpl vulnerabilityReportRecordService;

//    private final ExecutorService executorService = Executors.newFixedThreadPool(4);
    private final ThreadPoolExecutor executorService = new ThreadPoolExecutor(
            5,
            10,
            0,
            TimeUnit.SECONDS,
            new LinkedBlockingQueue<>()
    );

    private static final String RISK_NONE = "none";
    private static final String LEVEL_INFO = "INFO";

    @Resource
    private VulnerabilityStoreService vulnerabilityStoreService;
    @Autowired
    private UploadFileService uploadFileService;

    @Override
    public BaseResponse scanGet(VulnerabilityProjectScanParam param) {
        if (StringUtils.isBlank(param.getProjectId())) {
            return BaseResponse.error(VULNERABILITY_PROJECT_ID_IS_NEED);
        }

        if (Objects.isNull(param.getLanguage())) {
//            return BaseResponse.error(VULNERABILITY_LANGUAGE);
            param.setLanguage(VulnerabilityLanguage.TW.getCode());
        }
        PageHelper.startPage(param.getPageNum(), param.getPageSize());
        List<VulnerabilityProjectScanRecord> vulnerabilityProjectScanRecords =
                vulnerabilityProjectScanMapper.selectByParam(param);
        Map<String, VulnerabilityLevel> levelMap = vulnerabilityStoreMapper.levelGet().stream()
                .collect(Collectors.toMap(VulnerabilityLevel::getCode, Function.identity()));
        Set<String> numbers = vulnerabilityProjectScanRecords.stream()
                .map(VulnerabilityProjectScanRecord::getVulnerabilityNumber).collect(Collectors.toSet());

        if (!CollectionUtils.isEmpty(numbers)) {
            Map<String, List<VulnerabilityStorehouse>> storeMap = vulnerabilityStoreMapper.storeDetailByNumbers(numbers).stream()
                    .collect(Collectors.groupingBy(VulnerabilityStorehouse::getVulnerabilityNumber));

            for (VulnerabilityProjectScanRecord record : vulnerabilityProjectScanRecords) {
                List<VulnerabilityProjectAsset> assets = vulnerabilityProjectAssetMapper.projectAssetGetByProjectIdAndAssetIp(record.getProjectId(),
                        record.getAssetIp());
                record.setAssetName(assets.get(0).getAssetName());
                //有漏洞不会再漏洞知识库的情况
                if (!CollectionUtils.isEmpty(storeMap.get(record.getVulnerabilityNumber()))) {


                    record.setVulnerabilityLevel_en(levelMap.get(storeMap.get(record.getVulnerabilityNumber())
                            .stream()
                            .filter(i -> i.getVulnerabilityLanguage().equals(VulnerabilityLanguage.ENGLISH.getCode()))
                            .findFirst()
                            .orElse(generateDefaultVulnerabilityStorehouse(storeMap, record.getVulnerabilityNumber(),VulnerabilityLanguage.ENGLISH))
                            .getVulnerabilityLevel().toString()).getLevelEn());
                    record.setVulnerabilityLevel_zh(levelMap.get(storeMap.get(record.getVulnerabilityNumber())
                            .stream()
                            .filter(i -> i.getVulnerabilityLanguage().equals(VulnerabilityLanguage.ZH.getCode()))
                            .findFirst()
                            .orElse(generateDefaultVulnerabilityStorehouse(storeMap, record.getVulnerabilityNumber(),VulnerabilityLanguage.ZH))
                            .getVulnerabilityLevel().toString()).getLevel_zh());
                    record.setVulnerabilityLevel_tw(levelMap.get(storeMap.get(record.getVulnerabilityNumber())
                            .stream()
                            .filter(i -> i.getVulnerabilityLanguage().equals(VulnerabilityLanguage.TW.getCode()))
                            .findFirst()
                            .orElse(generateDefaultVulnerabilityStorehouse(storeMap, record.getVulnerabilityNumber(),VulnerabilityLanguage.TW))
                            .getVulnerabilityLevel().toString()).getLevel_tw());
                    //英文语言别一定存在 如果没有随机选一个

                    record.setVulnerabilityDesc_en(storeMap.get(record.getVulnerabilityNumber())
                            .stream()
                            .filter(i -> i.getVulnerabilityLanguage().equals(VulnerabilityLanguage.ENGLISH.getCode()))
                            .findFirst()
                            .orElse(generateDefaultVulnerabilityStorehouse(storeMap, record.getVulnerabilityNumber(),VulnerabilityLanguage.ENGLISH))
                            .getVulnerabilityDesc());

                    record.setVulnerabilityDesc_tw(storeMap.get(record.getVulnerabilityNumber())
                            .stream()
                            .filter(i -> i.getVulnerabilityLanguage().equals(VulnerabilityLanguage.TW.getCode()))
                            .findFirst()
                            .orElse(generateDefaultVulnerabilityStorehouse(storeMap, record.getVulnerabilityNumber(),VulnerabilityLanguage.TW))
                            .getVulnerabilityDesc());

                    record.setVulnerabilityDesc_zh(storeMap.get(record.getVulnerabilityNumber())
                            .stream()
                            .filter(i -> i.getVulnerabilityLanguage().equals(VulnerabilityLanguage.ZH.getCode()))
                            .findFirst()
                            .orElse(generateDefaultVulnerabilityStorehouse(storeMap, record.getVulnerabilityNumber(),VulnerabilityLanguage.ZH))
                            .getVulnerabilityDesc());

                    record.setVulnerabilityName_en(storeMap.get(record.getVulnerabilityNumber())
                            .stream()
                            .filter(i -> i.getVulnerabilityLanguage().equals(VulnerabilityLanguage.ENGLISH.getCode()))
                            .findFirst()
                            .orElse(generateDefaultVulnerabilityStorehouse(storeMap, record.getVulnerabilityNumber(),VulnerabilityLanguage.ENGLISH))
                            .getVulnerabilityName());

                    record.setVulnerabilityName_tw(storeMap.get(record.getVulnerabilityNumber())
                            .stream()
                            .filter(i -> i.getVulnerabilityLanguage().equals(VulnerabilityLanguage.TW.getCode()))
                            .findFirst()
                            .orElse(generateDefaultVulnerabilityStorehouse(storeMap, record.getVulnerabilityNumber(),VulnerabilityLanguage.TW))
                            .getVulnerabilityName());

                    record.setVulnerabilityName_zh(storeMap.get(record.getVulnerabilityNumber())
                            .stream()
                            .filter(i -> i.getVulnerabilityLanguage().equals(VulnerabilityLanguage.ZH.getCode()))
                            .findFirst()
                            .orElse(generateDefaultVulnerabilityStorehouse(storeMap, record.getVulnerabilityNumber(),VulnerabilityLanguage.ZH))
                            .getVulnerabilityName());

                    record.setRepairSuggestion_en(storeMap.get(record.getVulnerabilityNumber())
                            .stream()
                            .filter(i -> i.getVulnerabilityLanguage().equals(VulnerabilityLanguage.ENGLISH.getCode()))
                            .findFirst()
                            .orElse(generateDefaultVulnerabilityStorehouse(storeMap, record.getVulnerabilityNumber(),VulnerabilityLanguage.ENGLISH))
                            .getRepairSuggestion());

                    record.setRepairSuggestion_zh(storeMap.get(record.getVulnerabilityNumber())
                            .stream()
                            .filter(i -> i.getVulnerabilityLanguage().equals(VulnerabilityLanguage.ZH.getCode()))
                            .findFirst()
                            .orElse(generateDefaultVulnerabilityStorehouse(storeMap, record.getVulnerabilityNumber(),VulnerabilityLanguage.ZH))
                            .getRepairSuggestion());

                    record.setRepairSuggestion_tw(storeMap.get(record.getVulnerabilityNumber())
                            .stream()
                            .filter(i -> i.getVulnerabilityLanguage().equals(VulnerabilityLanguage.TW.getCode()))
                            .findFirst()
                            .orElse(generateDefaultVulnerabilityStorehouse(storeMap, record.getVulnerabilityNumber(),VulnerabilityLanguage.TW))
                            .getRepairSuggestion());
                }
            }
        }

        return BaseResponse.ok(new PageInfo<>(vulnerabilityProjectScanRecords));
    }


    @Override
    public BaseResponse scanSave(VulnerabilityProjectScanRecord param, Boolean duplicateCheck) {
        if (Objects.isNull(param.getEid())) {
            return BaseResponse.error(ResponseCode.RISK_EID_EMPTY);
        }
        param.setId(SnowFlake.getInstance().newIdStr());
        vulnerabilityProjectScanMapper.insertScan(param);
//        List<VulnerabilityProjectScanRecord> vulnerabilityProjectScanRecords =
//                scanGetByAssetIpAndVulnerabilityNumberAndEid(param.getAssetIp(), param.getVulnerabilityNumber(), param.getEid(), param.getProjectId());
//        if (CollectionUtils.isEmpty(vulnerabilityProjectScanRecords)) {
//
//        } else {
//            //todo 这边覆盖的逻辑没有使用到，后期删除，现在逻辑都是上传之前删除所有漏扫数据，然后重新上传
//            if (duplicateCheck) {
//                throw new VulnerabilityException(VULNERABILITY_SCAN_DUPLICATE.getCode(), VULNERABILITY_SCAN_DUPLICATE.getMsg());
//            }
//            param.setId(vulnerabilityProjectScanRecords.get(0).getId());
//            vulnerabilityProjectScanMapper.updateScan(param);
//        }

        return BaseResponse.ok();
    }

    @Override
    public BaseResponse scanSave(List<VulnerabilityProjectScanRecord> params, Boolean duplicateCheck) {

        for (VulnerabilityProjectScanRecord param : params) {
            if (Objects.isNull(param.getEid())) {
                return BaseResponse.error(ResponseCode.RISK_EID_EMPTY);
            }
            param.setId(SnowFlake.getInstance().newIdStr());
        }

        int batchSize = 1000;
        for (int i = 0; i < params.size(); i += batchSize) {

            int endIndex = Math.min(i + batchSize, params.size());
            List<VulnerabilityProjectScanRecord> batchList = params.subList(i, endIndex);
            log.info("[scanSave]  {},{}",i,batchList.size());
            vulnerabilityProjectScanMapper.insertScans(batchList);
        }

        return BaseResponse.ok();
    }

    //弱點掃描新增、編輯專案的上傳清單邏輯需同步修改
    @Override
    public BaseResponse scanUpload(VulnerabilityProjectScanParam param, List<MultipartFile> files, Boolean modify) throws IOException {
        if (files.isEmpty()) {
            return BaseResponse.error(ResponseCode.VULNERABILITY_SCAN_DATA);
        }
        if (StringUtils.isBlank(param.getProjectId())) {
            return BaseResponse.error(ResponseCode.VULNERABILITY_PROJECT_ID_IS_NEED);
        }
        VulnerabilityProject vp = vulnerabilityProjectMapper.selectVulnerabilityProjectById(param.getProjectId());
        List<Set<VulnerabilityScanData>> dataLists = processScanDataFiles(files);
        // 提交一个新任務到線程池執行
        VulnerabilityProjectParam vpp = new VulnerabilityProjectParam();
        BeanUtils.copyProperties(vp, vpp);
        vpp.setLanguage(param.getLanguage());
        vpp.setDuplicateCheck(param.getDuplicateCheck());
        executorService.submit(() -> vulnerabilityProjectService.vulnerabilityProjectFileProcess(vpp, dataLists, false));
        Constants.getThreadTaskList().add(vp.getId());

//        Boolean reportGenerate = vulnerabilityReportRecordService.isReportGenerate(param.getProjectId());
//        if (reportGenerate) {
//            return BaseResponse.error(VULNERABILITY_PROJECT_NOT_EDIT);
//        }
//
//        if (Objects.isNull(param.getLanguage())) {
//            return BaseResponse.error(ResponseCode.VULNERABILITY_LANGUAGE);
//        }
//        if (Objects.isNull(param.getEid())) {
//            return BaseResponse.error(ResponseCode.RISK_EID_EMPTY);
//        }
//
//        // 判斷是否project是否執行中
//        if (threadTaskList.contains(param.getProjectId())){
//            return BaseResponse.error(VULNERABILITY_SCAN_DATA_IS_BEING_UPLOADED);
//        }
//
//        threadTaskList.add(param.getProjectId());
//
//        List<Set<VulnerabilityScanData>> dataLists = processScanDataFiles(files);
//
//        //重新上传漏扫清单时 覆盖旧的漏扫清单
//        vulnerabilityProjectScanAssetMapper.deleteVulnerabilityProjectScanRecord(param.getProjectId());
//        //重新上傳弱掃清單時 覆蓋舊的掃描結果設備清單
//        vulnerabilityProjectScanAssetMapper.deleteVulnerabilityProjectScanAsset(param.getProjectId());

        //處理弱點清單
        return BaseResponse.ok();
    }

    @Override
    public BaseResponse scanDelete(String id) {
        //如果已经产生报告则不可以删除
        VulnerabilityProjectScanRecord record = vulnerabilityProjectScanMapper.selectById(id);
        List<VulnerabilityReportRecord> vulnerabilityReportRecord = vulnerabilityProjectScanMapper
                .selectReportByProjectId(record.getProjectId());
        if (!CollectionUtils.isEmpty(vulnerabilityReportRecord)) {
            // 已经生成报告不可以删除
            return BaseResponse.error(ResponseCode.VULNERABILITY_REPORT_EXIST);
        }
        vulnerabilityProjectScanMapper.deleteScanById(id);
        return BaseResponse.ok();
    }

    @Override
    public BaseResponse scanDataCount(VulnerabilityScanDataParams params) {

        Optional<BaseResponse> optResponse = checkParamIsEmpty(params.getTimes(), "times");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }
        List<Long> eidList = serviceMaintenanceService.getServiceEidList(vulnerabilityProjectMapper.selectVulnerabilityProjectEidList());
        List<VulnerabilityProjectParam> vulnerabilityProjects = vulnerabilityProjectMapper
                .selectVulnerabilityProjectByTime(params.getUseDateFunction(), params.getTimes(), params.getEid(),eidList);


        Map<String, String> reversedProjectTimesMap = reversedProjectTimesMap(vulnerabilityProjects);
        if (CollectionUtils.isEmpty(reversedProjectTimesMap.keySet())) {
            return BaseResponse.ok();
        }

        List<VulnerabilityCount> ret = vulnerabilityProjectScanMapper.selectVulnerabilityCount(reversedProjectTimesMap.keySet())
                .stream()
                .filter(vc->Objects.nonNull(vc.getProjectId()))
                .collect(Collectors.groupingBy(VulnerabilityCount::getProjectId)) // 对结果按projectId分组
                .entrySet()
                .stream()
                .flatMap(entry -> {
                    String projectId = entry.getKey();
                    Map<String, VulnerabilityCount> levelMap = entry.getValue()
                            .stream()
                            .collect(Collectors.toMap(VulnerabilityCount::getLevel, Function.identity()));

                    List<String> allLevels = Arrays.asList("INFO", "MEDIUM", "HIGH", "CRITICAL", "LOW");

                    return allLevels.stream().map(level -> levelMap.computeIfAbsent(level, l -> {
                        VulnerabilityCount vc = new VulnerabilityCount();
                        vc.setProjectId(projectId);
                        vc.setLevel(l);
                        vc.setCnt(0);
                        return vc;
                    }));
                })
                .peek(i -> i.setTimes(reversedProjectTimesMap.get(i.getProjectId())))
                .collect(Collectors.toList());

        Map<String, String> remainingProjects = new HashMap<>(reversedProjectTimesMap);

        ret.forEach(vulnerabilityCount -> remainingProjects.remove(vulnerabilityCount.getProjectId()));

        remainingProjects.forEach((projectId, times) -> ret.addAll(createVcList(projectId, times)));
        return BaseResponse.ok(ret);
    }

    private Map<String, String> reversedProjectTimesMap(List<VulnerabilityProjectParam> vulnerabilityProjects) {
        Map<String, String> projectTimesMap = vulnerabilityProjects
                .stream()
                .collect(Collectors.toMap(VulnerabilityProjectParam::getTimes, VulnerabilityProjectParam::getId,(existingValue, newValue) -> existingValue));

        return projectTimesMap
                .entrySet()
                .stream()
                .collect(Collectors.toMap(Map.Entry::getValue, Map.Entry::getKey, (existingValue, newValue) -> existingValue));
    }

    @Override
    public BaseResponse assetScanDataCount(VulnerabilityScanDataParams params) {
        Optional<BaseResponse> optResponse = checkParamIsEmpty(params.getEid(), "eid");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }
        optResponse = checkParamIsEmpty(params.getAssetIdList(), "assetIdList");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }
        optResponse = checkParamIsEmpty(params.getTimes(), "times");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        List<String> levelList = vulnerabilityStoreMapper.levelGet().stream().map(VulnerabilityLevel::getLevelEn).collect(Collectors.toList());

        List<VulnerabilityProjectParam> vulnerabilityProjects = vulnerabilityProjectMapper
                .selectVulnerabilityProjectByTime(true, params.getTimes(), params.getEid(),null);
        Map<String, String> reversedProjectTimesMap = reversedProjectTimesMap(vulnerabilityProjects);
        // 获取资产扫描数据
        List<VulnerabilityProjectScanAsset> vpsaList = getVulnerabilityProjectScanAssets(
                params, reversedProjectTimesMap, levelList);

        return BaseResponse.ok(vpsaList);
    }


    private List<VulnerabilityProjectScanAsset> getVulnerabilityProjectScanAssets(
            VulnerabilityScanDataParams params, Map<String, String> projectTimesMap, List<String> levelList) {
        return vulnerabilityProjectScanMapper.selectVulnerabilityCountByAssetId(projectTimesMap.keySet(), params.getAssetIdList())
                .stream()
                .peek(vpsa -> {
                    vpsa.getVcList().forEach(vc -> vc.setTimes(projectTimesMap.get(vc.getProjectId())));
                    params.getTimes().stream()
                            .filter(times -> !projectTimesMap.containsValue(times))
                            .flatMap(times -> createVcList(times, levelList).stream())
                            .forEach(vpsa.getVcList()::add);
                })
                .collect(Collectors.toList());
    }

    private List<VulnerabilityCount> createVcList(String times, List<String> levelList) {
        return levelList.stream()
                .map(level -> new VulnerabilityCount(times, null, level))
                .collect(Collectors.toList());
    }


    @Override
    public BaseResponse scanDataSelectGet(Long eid, Boolean duplicate) {

        Map<String, Object> queryCondition = new HashMap<>();
        queryCondition.put("eid", eid);
        queryCondition.put("size", 0);
        queryCondition.put("eidList", serviceMaintenanceService.getServiceEidList(vulnerabilityProjectMapper.selectVulnerabilityProjectEidList()));
        List<VulnerabilityProject> vpExecuteTimeList = vulnerabilityProjectMapper.selectVulnerabilityProject(queryCondition);
        if (duplicate) {
            if (vpExecuteTimeList.isEmpty()) {
                return BaseResponse.ok(Collections.emptyMap());
            }
            Map<String, VulnerabilityProject> timeMap = vpExecuteTimeList
                    .stream()
                    .collect(Collectors.toMap(VulnerabilityProject::getProjectExecuteTime, Function.identity(), (oldV, newV) -> oldV,LinkedHashMap::new));
            List<VulnerabilityProject> retVpList = new ArrayList<>();
            timeMap.forEach((key, value) -> {
                VulnerabilityProject  newVp = new VulnerabilityProject();
                newVp.setId(value.getId());
                newVp.setProjectExecuteTime(key);
                retVpList.add(newVp);
            });
            Map<String, Object> resultMap = new HashMap<>();
            resultMap.put("executeTimeList", retVpList);
            return BaseResponse.ok(resultMap);
        }

        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("executeTimeList", vpExecuteTimeList);


        return BaseResponse.ok(resultMap);
    }

    @Override
    public BaseResponse scanDataSelectGet(List<String> projectIdList) {
        List<VulnerabilityProjectScanAsset> vpsaList =
                vulnerabilityProjectScanAssetMapper.selectVulnerabilityProjectScanAssetByProjectIdList(projectIdList);
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("vpsaList", vpsaList);
        return BaseResponse.ok(resultMap);
    }


    @Override
    public BaseResponse scanDataVulnerabilityAssetCount(VulnerabilityProjectScanParam param) {
        Optional<BaseResponse> optResponse = checkParamIsEmpty(param.getProjectId(), "projectId");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        List<VulnerabilityProjectScanAsset> vpsaList = vulnerabilityProjectScanAssetMapper.selectVulnerabilityProjectScanAssetByProjectIdList(Lists.newArrayList(param.getProjectId()));
        Map<String, String> vpsaMap = vpsaList.stream().collect(Collectors.toMap(VulnerabilityProjectScanAsset::getAssetIp, VulnerabilityProjectScanAsset::getAssetRepairStatus));


        vulnerabilityProjectScanMapper.updateMaxGroup();
        PageHelper.startPage(param.getPageNum(), param.getPageSize());
        List<VulnerabilityAndAssetCount> vacList = vulnerabilityProjectScanMapper.selectVpsrAndVpsa(param);

        if (CollectionUtils.isEmpty(vacList)) {
            return BaseResponse.ok(new PageInfo<>(new ArrayList<>(1)));
        }
        populateCountMap(vacList,
                item -> item.getAssetIps().split(","),
                item -> item.getVulnerabilityRepairStatusString().split(","), param);

        Map<String, List<VulnerabilityStorehouse>> storeMap = getVulnerabilityStorehouseMap(vacList);


        vacList.forEach(vac -> processAssetCountAndLanguage(vac, param, vpsaMap, storeMap));
        return BaseResponse.ok(new PageInfo<>(vacList));
    }

    @Override
    public BaseResponse scanDataVulnerabilityDetail(VulnerabilityProjectScanParam param) {
        Optional<BaseResponse> optResponse = checkParamIsEmpty(param.getProjectId(), "projectId");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        PageHelper.startPage(param.getPageNum(), param.getPageSize());
        List<VulnerabilityProjectScanRecord> vpsrList = vulnerabilityProjectScanMapper.selectScanVulnerabilityData(param);
        if (CollectionUtils.isEmpty(vpsrList)) {
            return BaseResponse.ok(new PageInfo<>(vpsrList));
        }
        Map<String, List<VulnerabilityStorehouse>> storeMap = getVulnerabilityStorehouseMap(vpsrList);

        vpsrList.forEach(vpsr -> {
            String vulnerabilityName = getVulnerabilityNameByLanguage(param.getLanguage(), vpsr.getVulnerabilityNumber(), storeMap, vpsr);
            vpsr.setVulnerabilityName(vulnerabilityName);
        });

        return BaseResponse.ok(new PageInfo<>(vpsrList));
    }

    @Override
    public BaseResponse scanDataVulnerabilityAssetDetail(VulnerabilityProjectScanParam param) {
        Optional<BaseResponse> optResponse = checkParamIsEmpty(param.getProjectId(), "projectId");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        PageHelper.startPage(param.getPageNum(), param.getPageSize());
        if (!CollectionUtils.isEmpty(param.getAssetRepairStatusCodeList())) {
            param.setAssetRepairStatusList(param.getAssetRepairStatusCodeList()
                    .stream().map(status->VulnerabilityScanAssetStatusEnum.fromCode(status).name()).collect(Collectors.toList())
            );
        }
        List<VulnerabilityScanAssetCount> vpsrList = vulnerabilityProjectScanMapper.selectScanAssetData(param);
        populateCountMap(vpsrList,
                item -> StringUtils.isNotBlank(item.getVulnerabilityNumbers()) ? item.getVulnerabilityNumbers().split(",") : new String[0],
                item -> StringUtils.isNotBlank(item.getVulnerabilityRepairStatusString()) ? item.getVulnerabilityRepairStatusString().split(",") : new String[0], param
        );

        return BaseResponse.ok(new PageInfo<>(vpsrList));
    }

    @Override
    public BaseResponse vulnerabilityDetail(String vulnerabilityNumber, String projectId, Long eid, String assetId, Integer language) {
        VulnerabilityProjectScanRecord vpsr = vulnerabilityProjectScanMapper.selectVulnerabilityData(vulnerabilityNumber, projectId, eid, assetId);

        if (vpsr == null || vpsr.getAssetIps() == null || vpsr.getAssetIds() == null || vpsr.getAssetNames() == null
                || vpsr.getServerPorts() == null || vpsr.getVulnerabilityRepairStatusString() == null) {
            return BaseResponse.ok();
        }

        String[] assetIpArray = vpsr.getAssetIps().split(",");
        String[] assetIdArray = vpsr.getAssetIds().split(",");
        String[] assetNameArray = vpsr.getAssetNames().split(",");
        String[] serverArray = vpsr.getServerPorts().split(",");
        String[] vulnerabilityRepairStatusArray = vpsr.getVulnerabilityRepairStatusString().split(",");


        if (IntStream.of(assetIpArray.length, assetIdArray.length, assetNameArray.length, serverArray.length, vulnerabilityRepairStatusArray.length)
                .distinct().count() != 1) {
            log.error("[populateCountMap] Data length mismatch error! {} ", projectId);
        }
        int[] arrayLengths = {
                assetIpArray.length,
                assetIdArray.length,
                assetNameArray.length,
                serverArray.length,
                vulnerabilityRepairStatusArray.length
        };

        int minSize = arrayLengths[0];
        for (int i = 1; i < arrayLengths.length; i++) {
            minSize = Math.min(minSize, arrayLengths[i]);
        }
        List<VulnerabilityProjectScanRecord.VulnerabilityProjectScanAssetData> vpsadList = IntStream.range(0, minSize)
                .mapToObj(i -> new VulnerabilityProjectScanRecord.VulnerabilityProjectScanAssetData(
                        assetIdArray[i], assetIpArray[i], serverArray[i], assetNameArray[i],
                        vulnerabilityRepairStatusArray[i], VulnerabilityRepairStatus.valueOf(vulnerabilityRepairStatusArray[i]).ordinal(),
                        vulnerabilityProjectScanMapper.selectSuggest(assetIpArray[i], serverArray[i], projectId, vulnerabilityNumber)))
                .sorted(Comparator.comparing(VulnerabilityProjectScanRecord.VulnerabilityProjectScanAssetData::getAssetIp))
                .collect(Collectors.toList());

        Map<String, List<VulnerabilityStorehouse>> storeMap = vulnerabilityStoreMapper.storeDetailByNumbers(Sets.newHashSet(vulnerabilityNumber)).stream()
                .collect(Collectors.groupingBy(VulnerabilityStorehouse::getVulnerabilityNumber));

        VulnerabilityStorehouse vs = storeMap.get(vulnerabilityNumber)
                .stream()
                .filter(i -> i.getVulnerabilityLanguage().equals(language))
                .findFirst()
                .orElse(generateDefaultVulnerabilityStorehouse(storeMap, vulnerabilityNumber, VulnerabilityLanguage.fromCode(language)));

        vpsr.setVulnerabilityName(vs.getVulnerabilityName());
        vpsr.setRepairSuggestion(vs.getRepairSuggestion());
        vpsr.setVulnerabilityDesc(vs.getVulnerabilityDesc());

        vpsr.setVpsadList(vpsadList);
        return BaseResponse.ok(vpsr);
    }

    @Override
    public BaseResponse vulnerabilitySuggest(VulnerabilitySuggestParam param){
        if (param == null) {
            return BaseResponse.error(VULNERABILITY_SCAN_SUGGEST_PARAM_IS_NOT_EXIST);
        }

        Integer res = vulnerabilityProjectScanMapper.updateVulnerabilitySuggest(param);
        if (res == 0) {
            return BaseResponse.error(VULNERABILITY_SCAN_SUGGEST_UPDATE_ERROR);
        }

        return BaseResponse.ok(res);
    }

    private <T> void populateCountMap(List<T> itemList,
                                      Function<T, String[]> splitFunction1,
                                      Function<T, String[]> splitFunction2,
                                      VulnerabilityProjectScanParam param) {
        itemList.forEach(item -> {
            Map<String, Set<String>> countMap = new HashMap<>();
            for (VulnerabilityRepairStatus status : VulnerabilityRepairStatus.values()) {
                countMap.put(status.name(), new HashSet<>());
            }

            String[] array1 = splitFunction1.apply(item);
            String[] array2 = splitFunction2.apply(item);

            if (array1.length != array2.length) {
              log.error("[populateCountMap] Data length mismatch error! {} ", param.getProjectId());
            }

            IntStream.range(0, Math.min(array2.length,array1.length)).forEach(j -> {
                String vrs = array2[j];
                countMap.computeIfAbsent(vrs, k -> new HashSet<>()).add(array1[j]);
            });

            ((BaseVulnerabilityCount) item).setCountMap(countMap);
        });
    }


    private static VulnerabilityStorehouse generateDefaultVulnerabilityStorehouse(
            Map<String, List<VulnerabilityStorehouse>> vulnerMap,
            String vulnerabilityNumber,
            VulnerabilityLanguage preferredLanguage) {

        // 首先尝试获取首选语言版本的数据
        Optional<VulnerabilityStorehouse> preferredOptional = vulnerMap.get(vulnerabilityNumber).stream()
                .filter(i -> i.getVulnerabilityLanguage().equals(preferredLanguage.getCode()))
                .findFirst();

        // 如果首选语言数据不存在，尝试获取 TW 语言版本的数据
        if (!preferredOptional.isPresent()) {
            preferredOptional = vulnerMap.get(vulnerabilityNumber).stream()
                    .filter(i -> i.getVulnerabilityLanguage().equals(VulnerabilityLanguage.TW.getCode()))
                    .findFirst();
        }

        // 如果 TW 数据不存在，尝试获取 ZH 语言版本的数据
        if (!preferredOptional.isPresent()) {
            preferredOptional = vulnerMap.get(vulnerabilityNumber).stream()
                    .filter(i -> i.getVulnerabilityLanguage().equals(VulnerabilityLanguage.ZH.getCode()))
                    .findFirst();
        }

        // 如果 ZH 数据也不存在，尝试获取 ENGLISH 语言版本的数据
        if (!preferredOptional.isPresent()) {
            preferredOptional = vulnerMap.get(vulnerabilityNumber).stream()
                    .filter(i -> i.getVulnerabilityLanguage().equals(VulnerabilityLanguage.ENGLISH.getCode()))
                    .findFirst();
        }

        // 如果以上都不存在，返回列表中的第一个
        return preferredOptional.orElseGet(() -> vulnerMap.get(vulnerabilityNumber).stream().findFirst().get());
    }

    private List<VulnerabilityProjectScanRecord>
    scanGetByAssetIpAndVulnerabilityNumberAndEid(String assetIp, String vulnerabilityNumber, Long eid, String projectId) {
        return vulnerabilityProjectScanMapper.selectScanByParam(VulnerabilityProjectScanParam.builder().assetIp(assetIp)
                .vulnerabilityNumber(vulnerabilityNumber).eid(eid).projectId(projectId).build());
    }


    public VulnerabilityProjectScanAsset buildVpsa(String id, String assetId, String assetIp, String projectId, String assetRepairStatus) {
        VulnerabilityProjectScanAsset vpsa = new VulnerabilityProjectScanAsset();
        vpsa.setId(id);
        vpsa.setAssetId(assetId);
        vpsa.setAssetIp(assetIp);
        vpsa.setProjectId(projectId);
        vpsa.setAssetRepairStatus(assetRepairStatus);
        return vpsa;
    }


    public VulnerabilityProjectScanRecord createRecord(VulnerabilityScanData scanData,
                                                        Map<String, List<VulnerabilityStorehouse>> vulnerMap,
                                                        VulnerabilityProjectScanParam param,
                                                        Map<String, String> vpsrIdMap) {
        VulnerabilityProjectScanRecord record = new VulnerabilityProjectScanRecord();
        record.setId(SnowFlake.getInstance().newIdStr());
        record.setProjectId(param.getProjectId());

        String pluginId = scanData.getPluginId();
        List<VulnerabilityStorehouse> vulnerabilityStorehouses = vulnerMap.getOrDefault(pluginId, Collections.emptyList());
        VulnerabilityStorehouse vulnerabilityStorehouse = vulnerabilityStorehouses.stream()
                .filter(v -> v.getVulnerabilityLanguage().equals(param.getLanguage()))
                .findFirst()
                .orElseGet(() -> generateDefaultVulnerabilityStorehouse(vulnerMap, pluginId,VulnerabilityLanguage.ENGLISH));
        record.setVulnerabilityId(vulnerabilityStorehouse.getId());

        record.setAssetIp(scanData.getHost());
        record.setEid(param.getEid());
        record.setVulnerabilityNumber(pluginId);
        //弱點等級以知識庫為主
        List<VulnerabilityLevel> levels = vulnerabilityStoreMapper.levelGet();
        String levelEn = levels.stream()
                .filter(i -> i.getCode().equals(vulnerabilityStorehouse.getVulnerabilityLevel().toString()))
                .findFirst()
                .orElseGet(() -> levels.get(0)).getLevelEn();
        record.setVulnerabilityLevel(parseRiskLevel(levelEn));
//        record.setVulnerabilityLevel(parseRiskLevel(scanData.getRisk()));
        record.setVulnerabilityName(scanData.getName());
        record.setServerPort(parsePort(scanData.getPort()));
        record.setVulnerabilityDesc(scanData.getDescription());
        setCvssScores(record, scanData.getCvssV2(), scanData.getCvssV2Temporal(), scanData.getCvssV3(), scanData.getCvssV3Temporal());
        record.setVulnerabilityRepairStatus(NOT_REPAIR);
        record.setVpsaId(vpsrIdMap.get(scanData.getHost()));
        record.setDataJson(JSONObject.toJSONString(scanData));
        scanSave(record, param.getDuplicateCheck());

        return record;

    }

    //處理多筆紀錄
    public List<VulnerabilityProjectScanRecord> createRecord(Set<VulnerabilityScanData> scanDatas,
                                                             Map<String, List<VulnerabilityStorehouse>> vulnerMap,
                                                             VulnerabilityProjectScanParam param,
                                                             Map<String, String> vpsrIdMap) {
        List<VulnerabilityProjectScanRecord> records = new ArrayList<>();
        scanDatas.forEach( scanData -> {
            VulnerabilityProjectScanRecord record = new VulnerabilityProjectScanRecord();
            record.setId(SnowFlake.getInstance().newIdStr());
            record.setProjectId(param.getProjectId());

            String pluginId = scanData.getPluginId();
            List<VulnerabilityStorehouse> vulnerabilityStorehouses = vulnerMap.getOrDefault(pluginId, Collections.emptyList());
            VulnerabilityStorehouse vulnerabilityStorehouse = vulnerabilityStorehouses.stream()
                    .filter(v -> v.getVulnerabilityLanguage().equals(param.getLanguage()))
                    .findFirst()
                    .orElseGet(() -> generateDefaultVulnerabilityStorehouse(vulnerMap, pluginId,VulnerabilityLanguage.ENGLISH));
            record.setVulnerabilityId(vulnerabilityStorehouse.getId());

            record.setAssetIp(scanData.getHost());
            record.setEid(param.getEid());
            record.setVulnerabilityNumber(pluginId);
            //弱點等級以知識庫為主
            List<VulnerabilityLevel> levels = vulnerabilityStoreMapper.levelGet();
            String levelEn = levels.stream()
                    .filter(i -> i.getCode().equals(vulnerabilityStorehouse.getVulnerabilityLevel().toString()))
                    .findFirst()
                    .orElseGet(() -> levels.get(0)).getLevelEn();
            record.setVulnerabilityLevel(parseRiskLevel(levelEn));
//        record.setVulnerabilityLevel(parseRiskLevel(scanData.getRisk()));
            record.setVulnerabilityName(scanData.getName());
            record.setServerPort(parsePort(scanData.getPort()));
            record.setVulnerabilityDesc(scanData.getDescription());
            setCvssScores(record, scanData.getCvssV2(), scanData.getCvssV2Temporal(), scanData.getCvssV3(), scanData.getCvssV3Temporal());
            record.setVulnerabilityRepairStatus(NOT_REPAIR);
            record.setVpsaId(vpsrIdMap.get(scanData.getHost()));
            record.setDataJson(JSONObject.toJSONString(scanData));
            record.setRepairSuggestion(scanData.getSolution());

            records.add(record);
        });

        scanSave(records, param.getDuplicateCheck());

        return records;

    }

    public BaseResponse processVulnerabilityScanUpload(VulnerabilityProjectScanParam param, List<Set<VulnerabilityScanData>> dataLists, Boolean modify){
        Set<VulnerabilityScanData> dataList = new HashSet<>();
        dataLists.forEach(dataList::addAll);

        // 这里默认每次会读取100条数据 然后返回过来 直接调用使用数据就行

        if (CollectionUtil.isEmpty(dataList)) {
            return BaseResponse.error(ResponseCode.VULNERABILITY_SCAN_TEMPLATE_ERROR);
        }
        if (dataList.stream().anyMatch(i -> (StringUtil.isEmpty(i.getHost())) || StringUtil.isEmpty(i.getPluginId()))) {
            return BaseResponse.error(ResponseCode.VULNERABILITY_SCAN_TEMPLATE_ERROR);
        }
        Set<String> vulnerabilityNumbers = dataList.stream().map(VulnerabilityScanData::getPluginId).collect(Collectors.toSet());
        Set<String> ips = dataList.stream().map(VulnerabilityScanData::getHost).collect(Collectors.toSet());
        try {
            //校验ip
            Set<String> errorIp = ips.stream().filter(i -> !vulnerabilityMaintenanceAssetService.checkAssetIp(i)).collect(Collectors.toSet());
            if (CollectionUtil.isNotEmpty(errorIp)) {
                throw new VulnerabilityException(VULNERABILITY_SCAN_IP_ERROR.getCode(), String.join(",", errorIp));
            }

            List<VulnerabilityStorehouse> vulnerabilityStorehouses = vulnerabilityProjectScanMapper
                    .vulnerabilitySelectByNumber(vulnerabilityNumbers);
            Map<String, List<VulnerabilityStorehouse>> vulnerMap = vulnerabilityStorehouses
                    .stream().collect(Collectors.groupingBy(VulnerabilityStorehouse::getVulnerabilityNumber));
            List<VulnerabilityProjectAsset> projectAssets = vulnerabilityProjectScanMapper
                    .projectAssetSelectByIps(ips, param.getProjectId());

            //todo 设备不在 维护扫描设备清单 跳过
            Map<String, List<VulnerabilityProjectAsset>> ipMap = projectAssets.stream()
                    .collect(Collectors.groupingBy(VulnerabilityProjectAsset::getAssetIp));

            // 自動新增弱點至弱點知識庫;
            dataList.stream()
                    .filter(i -> ipMap.containsKey(i.getHost()))
                    .filter(i -> !vulnerMap.containsKey(i.getPluginId()))
                    .forEach(i -> {
                        //獲取弱點知識庫資料
                        List<VulnerabilityStorehouse> tenableContent = (List<VulnerabilityStorehouse>) vulnerabilityStoreService.getTenableContent(i.getPluginId()).getData();

                        VulnerabilityStoreParam storeparam = new VulnerabilityStoreParam();
                        storeparam.setVulnerabilityNumber(i.getPluginId());
                        storeparam.setVulnerabilityLevel(tenableContent.get(0).getVulnerabilityLevel());
                        storeparam.setVulnerabilityStorehouses(tenableContent);
                        vulnerabilityStoreService.storeSave(storeparam, true);

                        vulnerMap.put(i.getPluginId(), tenableContent);
                    });

            AtomicReference<Boolean> isComplete = new AtomicReference<>(false);
            Map<String, List<VulnerabilityScanData>> vsdMap = dataList.stream().collect(Collectors.groupingBy(VulnerabilityScanData::getHost));

            // 提交一个新的任务到线程池执行
            updateVulnerabilityScanData(vsdMap, dataList, ipMap, param, vulnerMap, isComplete);

            Constants.getThreadTaskList().remove(param.getProjectId());
            if (!isComplete.get()) {
                if (dataList.stream().anyMatch(i -> ipMap.containsKey(i.getHost()))) {
                    // 资产匹配到了 但是 漏洞编号没有匹配到 导致上传不成功
                    return BaseResponse.error(ResponseCode.VULNERABILITY_SCAN_LIST_VULNERABILITY);
                }
                //發生錯誤變更執行狀態
                Constants.getThreadTaskList().remove(param.getProjectId());
                return BaseResponse.error(ResponseCode.VULNERABILITY_SCAN_LIST);
            }

        } catch (VulnerabilityException e) {
            //發生錯誤變更執行狀態
            Constants.getThreadTaskList().remove(param.getProjectId());
            log.error("[scanUpload] VulnerabilityException error, {} ", e.getMessage(), e);
            return BaseResponse.error(e.getErrorCode(), e.getMessage());
        } catch (Exception e) {
            //發生錯誤變更執行狀態
            Constants.getThreadTaskList().remove(param.getProjectId());
            log.error("[scanUpload] error, {} ", e.getMessage(), e);
            return BaseResponse.error(VULNERABILITY_SCAN_TEMPLATE_ERROR);
        }

        return BaseResponse.ok();
    }

    public List<Set<VulnerabilityScanData>> processScanDataFiles(List<MultipartFile> files){
        List<Set<VulnerabilityScanData>> dataLists = new ArrayList<>();
        for (MultipartFile file : files) {
            //處理弱點清單
            Set<VulnerabilityScanData> dataList = processExcelFiles(file).stream()
                    .filter(data -> StringUtils.isNotBlank(data.getPluginId()))
                    .collect(Collectors.toSet());

            dataLists.add(dataList);
        }

        return dataLists;
    }

    private String parseRiskLevel(String risk) {
        return RISK_NONE.equalsIgnoreCase(risk) ? LEVEL_INFO : risk.toUpperCase();
    }

    private int parsePort(String port) {
        try {
            return Integer.parseInt(port);
        } catch (NumberFormatException e) {
            return -1;
        }
    }

    private void setCvssScores(VulnerabilityProjectScanRecord record, String cvssV2, String cvssV2Temporal, String cvssV3, String cvssV3Temporal) {
        record.setCvssV2BaseScore(parseDoubleOrNull(cvssV2));
        record.setCvssV2TemporalScore(parseDoubleOrNull(cvssV2Temporal));
        record.setCvssV3BaseScore(parseDoubleOrNull(cvssV3));
        record.setCvssV3TemporalScore(parseDoubleOrNull(cvssV3Temporal));
    }

    private Double parseDoubleOrNull(String value) {
        try {
            return StringUtils.isNotBlank(value) ? Double.parseDouble(value) : null;
        } catch (NumberFormatException e) {
            // Log and handle exception
            return null;
        }
    }

    private List<VulnerabilityCount> createVcList(String projectId, String times) {
        VulnerabilityCount info = createVc(projectId, times, "INFO");
        VulnerabilityCount critical = createVc(projectId, times, "CRITICAL");
        VulnerabilityCount high = createVc(projectId, times, "HIGH");

        VulnerabilityCount medium = createVc(projectId, times, "MEDIUM");
        VulnerabilityCount low = createVc(projectId, times, "LOW");
        return Lists.newArrayList(info, critical, high, medium,low);

    }

    private VulnerabilityCount createVc(String projectId, String times, String level) {
        VulnerabilityCount defaultVulnerabilityCount = new VulnerabilityCount();
        defaultVulnerabilityCount.setProjectId(projectId);
        defaultVulnerabilityCount.setTimes(times);
        defaultVulnerabilityCount.setLevel(level);
        return defaultVulnerabilityCount;
    }


    public void asyncScanDataFix(String projectId, Long eid) {
        VulnerabilityRepairPlanParams planParams = new VulnerabilityRepairPlanParams();
        planParams.setProjectId(projectId);
        planParams.setEid(eid);
        List<VulnerabilityRepairPlan> vrp = vulnerabilityRepairPlanMapper.selectVulnerabilityRepairPlan(planParams);
        if (!CollectionUtils.isEmpty(vrp)) {
            CompletableFuture
                    .runAsync(() -> vulnerabilityRepairRecordService.repairRecordStatusFix(projectId), executorService)
                    .exceptionally(ex -> {
                        ex.printStackTrace();
                        return null;
                    });
        }
    }

    private Map<String, String> vulnerabilityNameTransform(String vulnerabilityNumber, Map<String, List<VulnerabilityStorehouse>> storeMap) {
        Map<String, String> vulnerabilityNameMap = new HashMap<>();
        List<VulnerabilityStorehouse> storehouses = storeMap.get(vulnerabilityNumber);

        if (storehouses != null && !storehouses.isEmpty()) {

            for (VulnerabilityStorehouse storehouse : storehouses) {
                Integer languageCode = storehouse.getVulnerabilityLanguage();
                String name = storehouse.getVulnerabilityName();
                if (VulnerabilityLanguage.ZH.getCode()==(languageCode)) {
                    vulnerabilityNameMap.put("zh", name);
                } else if (VulnerabilityLanguage.ENGLISH.getCode()==(languageCode)) {
                    vulnerabilityNameMap.put("en", name);
                } else if (VulnerabilityLanguage.TW.getCode()==(languageCode)) {
                    vulnerabilityNameMap.put("tw", name);
                }
            }
        }

        return vulnerabilityNameMap;
    }


    private Map<String, List<VulnerabilityStorehouse>> getVulnerabilityStorehouseMap(List<?> list) {
        return vulnerabilityStoreMapper
                .storeDetailByNumbers(list.stream().map(l -> {
                    BaseVulnerabilityCount bvc = (BaseVulnerabilityCount) l;
                    return bvc.getVulnerabilityNumber();
                }).collect(Collectors.toSet())).stream()
                .collect(Collectors.groupingBy(VulnerabilityStorehouse::getVulnerabilityNumber));

    }


    private String getVulnerabilityNameByLanguage(Integer language, String vulnerabilityNumber, Map<String, List<VulnerabilityStorehouse>> storeMap,
    BaseVulnerabilityCount bvc) {
        if (Objects.nonNull(language)){
            Map<String, String> nameMap = vulnerabilityNameTransform(vulnerabilityNumber, storeMap);
            if (language.equals(VulnerabilityLanguage.TW.getCode())) {
                return nameMap.getOrDefault("tw", nameMap.getOrDefault("zh", nameMap.getOrDefault("en", bvc.getVulnerabilityName())));
            } else if (language.equals(VulnerabilityLanguage.ZH.getCode())) {
                return nameMap.getOrDefault("zh", nameMap.getOrDefault("tw", nameMap.getOrDefault("en", bvc.getVulnerabilityName())));
            }else {
                return nameMap.getOrDefault("en", bvc.getVulnerabilityName());
            }
        }
        return bvc.getVulnerabilityName();
    }


    private void processAssetCountAndLanguage(VulnerabilityAndAssetCount vac, VulnerabilityProjectScanParam param, Map<String, String> vpsaMap, Map<String, List<VulnerabilityStorehouse>> storeMap) {
        Set<String> notRepairIpList = vac.getCountMap().get(NOT_REPAIR.name());
        Set<String> noPlanList = new HashSet<>();
        Set<String> plannedList = new HashSet<>();

        for (String notRepairIp : notRepairIpList) {
            if (ASSET_NO_PLAN.name().equals(vpsaMap.get(notRepairIp))) {
                noPlanList.add(notRepairIp);
            } else {
                plannedList.add(notRepairIp);
            }
        }
        Map<String, String> nameMap = vulnerabilityNameTransform(vac.getVulnerabilityNumber(), storeMap);

        vac.setVulnerabilityName_cn(nameMap.getOrDefault("zh", ""));
        vac.setVulnerabilityName_en(nameMap.getOrDefault("en", ""));
        vac.setVulnerabilityName_tw(nameMap.getOrDefault("tw", ""));

        if (Objects.nonNull(param.getLanguage())){
            if (param.getLanguage().equals(VulnerabilityLanguage.TW.getCode())) {
                vac.setVulnerabilityName(nameMap.getOrDefault("tw", nameMap.getOrDefault("zh", nameMap.getOrDefault("en", vac.getVulnerabilityName()))));
            } else if (param.getLanguage().equals(VulnerabilityLanguage.ZH.getCode())) {
                vac.setVulnerabilityName(nameMap.getOrDefault("zh", nameMap.getOrDefault("tw", nameMap.getOrDefault("en", vac.getVulnerabilityName()))));
            }else {
                vac.setVulnerabilityName(nameMap.getOrDefault("en", vac.getVulnerabilityName()));
            }
        }

        vac.setVulnerabilityLevelCode(!CollectionUtils.isEmpty(storeMap.get(vac.getVulnerabilityNumber())) ?
                storeMap.get(vac.getVulnerabilityNumber()).get(0).getVulnerabilityLevel() : vac.getVulnerabilityLevelCode());
        vac.getCountMap().put("NO_PLAN", noPlanList);
        vac.getCountMap().put("PLANNED", plannedList);
        vac.getCountMap().remove(NOT_REPAIR.name());
    }

    private void riskCount(VulnerabilityProjectScanParam param, Boolean modify){
        VulnerabilityRiskCount riskCountParam = new VulnerabilityRiskCount();
        riskCountParam.setEid(param.getEid());
        riskCountParam.setProjectId(param.getProjectId());
        riskCountParam.setLanguage(param.getLanguage());
        List<VulnerabilityLevelMap> levelSizeMaps = (List<VulnerabilityLevelMap>) vulnerabilityProjectService.getVulnerabilityRisksCount(riskCountParam).getData();
        VulnerabilityReportContent content = vulnerabilityReportRecordMapper.selectReportContentByLanguage(param.getLanguage());
        VulnerabilityProjectSummary vulnerabilityProjectSummary = new VulnerabilityProjectSummary();
        String summary = String.format(content.getScanExecuteToolResult(),
                levelSizeMaps.get(0).getCRITICAL(),
                levelSizeMaps.get(0).getHIGH(),
                levelSizeMaps.get(0).getMEDIUM(),
                levelSizeMaps.get(0).getLOW());


        vulnerabilityProjectSummary.setProjectId(param.getProjectId());
        vulnerabilityProjectSummary.setScanExecuteToolResult(summary);

        // 若為更新則只替換總結第一段
        if(modify){
            VulnerabilityProject project = vulnerabilityProjectMapper.selectVulnerabilityProjectById(param.getProjectId());
            String riskCountSummary = String.format(content.getScanExecuteToolResult().split("。")[0],
                    levelSizeMaps.get(0).getCRITICAL(),
                    levelSizeMaps.get(0).getHIGH(),
                    levelSizeMaps.get(0).getMEDIUM(),
                    levelSizeMaps.get(0).getLOW());
            StringBuffer modifiedSummary = new StringBuffer();
            if (StringUtils.isNotBlank(project.getProjectSummary())) {
                List<String> summarySplit = Arrays.asList(project.getProjectSummary().split("。"));
                summarySplit.stream().skip(1).forEach(s -> {
                    modifiedSummary.append("。").append(s);
                });
            }

            summary = riskCountSummary + modifiedSummary;
            vulnerabilityProjectSummary.setScanExecuteToolResult(summary);
        }
        //等弱點清單處理完畢後更新總結
        vulnerabilityProjectMapper.updateVulnerabilityProjectSummary(vulnerabilityProjectSummary);
    }

    private Set<VulnerabilityScanData> processExcelFiles(MultipartFile file) {
        Set<VulnerabilityScanData> dataList = new HashSet<>();
        try (InputStream inputStream = file.getInputStream()){
            if(Objects.requireNonNull(file.getOriginalFilename()).endsWith(".xlsx") || Objects.requireNonNull(file.getOriginalFilename()).endsWith(".xls")) {
                EasyExcel.read(inputStream, VulnerabilityScanData.class,
                                new PageReadListener<VulnerabilityScanData>(dataList::addAll))
                        .sheet().doRead();
            }else if(Objects.requireNonNull(file.getOriginalFilename()).endsWith(".csv")) {
                EasyExcel.read(inputStream, VulnerabilityScanData.class,
                                new PageReadListener<VulnerabilityScanData>(dataList::addAll))
                        .excelType(ExcelTypeEnum.CSV)
                        .sheet().doRead();
            }
        } catch (Exception e) {
            log.error("processExcelFiles error", e);
        }
        return dataList;
    }

    // 更新设备统计表
//    @Transactional(rollbackFor = Exception.class)
    public void updateVulnerabilityScanData(Map<String, List<VulnerabilityScanData>> vsdMap,
                     Set<VulnerabilityScanData> dataList,
                     Map<String, List<VulnerabilityProjectAsset>> ipMap,
                     VulnerabilityProjectScanParam param,
                     Map<String, List<VulnerabilityStorehouse>> vulnerMap,
                     AtomicReference<Boolean> isComplete) {
        // 更新设备统计表
        Map<String, String> vpsrIdMap = new HashMap<>();
        vsdMap.forEach((ip, records) -> {
            ipMap.getOrDefault(ip, Collections.emptyList()).stream().findFirst().ifPresent(asset -> {
                String vpsrId = SnowFlake.getInstance().newIdStr();
                vpsrIdMap.put(ip, vpsrId);
                VulnerabilityProjectScanAsset vpsa = buildVpsa(vpsrId, asset.getAssetId(), ip, param.getProjectId(), ASSET_NO_PLAN.name());
                vulnerabilityProjectScanAssetMapper.insertVulnerabilityProjectScanAsset(vpsa);
            });
        });

        //批次新增弱點列表
        Set<VulnerabilityScanData> filterDataList = dataList.stream()
                .filter(i -> ipMap.containsKey(i.getHost()))
                .filter(i -> vulnerMap.containsKey(i.getPluginId()))
                .peek(i -> isComplete.set(true))
                .collect(Collectors.toSet());

        createRecord(filterDataList, vulnerMap, param, vpsrIdMap);
        vulnerabilityProjectService.updateVulnerabilityProjectStatus(param.getEid());
        asyncScanDataFix(param.getProjectId(), param.getEid());
    }
}
