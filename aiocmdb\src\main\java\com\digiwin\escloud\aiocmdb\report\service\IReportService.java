package com.digiwin.escloud.aiocmdb.report.service;

import com.digiwin.escloud.aiocmdb.report.model.*;
import com.digiwin.escloud.aiocmdb.report.model.dto.ReportMaintenanceUpdateDTO;
import com.digiwin.escloud.common.response.BaseResponse;
import com.google.gson.JsonArray;
import org.apache.ibatis.annotations.Param;

import javax.servlet.http.HttpServletResponse;
import java.util.Date;
import java.util.List;
import java.util.Map;

public interface IReportService {
    List<ReportReceiver> getReceivers(String serviceCode, String moduleCode, int page, int size);

    BaseResponse getReceiversByRmId(Long rmId);

    int getReceiversCount(String serviceCode, String moduleCode);

    List<ReportReceiverModuleBase> getModules();

    boolean saveReceiver(ReportReceiver reportReceiver);

    boolean deleteReceiver(long id);

    List<ReportMaintenance> getReports(String serviceCode,Integer status, Integer misStatus, String beginTime, String endTime, int page, int size);

    int getReportsCount(String serviceCode,Integer reportStatus, Integer misStatus, String beginTime, String endTime);

    BaseResponse sendReport(ReportMaintenance rm);

    BaseResponse downloadReport(ReportTemp rt);

    JsonArray GetMaintenancestateReportDataByAllDevice(String serviceCode, String modelCode, String yearMonth, String item);
    JsonArray GetMaintenancestateReportData(String serviceCode, String reportTempId, String modelCode, String type);
    JsonArray GetMaintenancestateReportDataByDeviceId(String serviceCode, String deviceId, String modelCode, String yearMonth, String type);

    JsonArray GetGetMaintenancestateReportDataByArrayItem(String serviceCode, String reportTempId, String modelCode, String type, String item);
    JsonArray GetGetMaintenancestateReportDataByDeviceIdByArrayItem(String serviceCode, String deviceId, String modelCode, String yearMonth, String type, String item);

    JsonArray getMaintenancestateReportDataByDeviceIdByRelationArrayItem(String serviceCode, String deviceId, String modelCode,String yearMonth,  String type, String item);
    JsonArray GetGetMaintenancestateReportDataByRelationArrayItem(String serviceCode, String reportTempId, String modelCode, String type, String item);

    List<Map<String, Object>> GetHostMaintenancestateReportByDeviceId(String modelCode, String deviceId, String yearMonth, String type, String item, String serviceCode, String eid);
    List<Map<String, Object>> GetHostMaintenancestateReport(String reportTempId, String modelCode, String type, String item, String serviceCode, String eid);

    List<Map<String, Object>> GetNasMaintenancestateReportByDeviceId(String modelCode, String deviceId, String yearMonth, String type, String item, String serviceCode, String eid);
    List<Map<String, Object>> GetNasMaintenancestateReport(String reportTempId, String modelCode, String type, String item, String serviceCode, String eid);

    List<Map<String, Object>> GetFirewallMaintenancestateReportByDeviceId(String modelCode, String deviceId, String yearMonth, String type, String item, String serviceCode, String eid);
    List<Map<String, Object>> GetFirewallMaintenancestateReport(String reportTempId, String modelCode, String type, String item, String serviceCode, String eid);

    List<Map<String, Object>> GetMailServerMaintenancestateReportByDeviceId(String modelCode, String deviceId, String yearMonth, String type, String item, String serviceCode, String eid);
    List<Map<String, Object>> GetMailServerMaintenancestateReport(String reportTempId, String modelCode, String type, String item, String serviceCode, String eid);

    List<Map<String, Object>> GetClientMaintenancestateReportByDeviceId(String modelCode, String deviceId, String yearMonth, String type, String item, String serviceCode, String eid);
    List<Map<String, Object>> GetClientMaintenancestateReport(String reportTempId, String modelCode, String type, String item, String serviceCode, String eid);

//    List<Map<String, Object>> GetBackupScheduleMaintenancestateReportByDeviceId(String modelCode, String deviceId, String yearMonth, String type, String item, String serviceCode, String eid);
    List<Map<String, Object>> GetBackupScheduleMaintenancestateReport(String yearMonth, String modelCode, String type, String item, String serviceCode, String eid);


    List<Map<String, Object>> GetSoftware_HardwareMaintenancestateReportByDeviceId(String modelCode, String deviceId, String yearMonth, String type, String item, String serviceCode, String eid);
    List<Map<String, Object>> GetSoftware_HardwareMaintenancestateReport(String reportTempId, String modelCode, String type, String item, String serviceCode, String eid);

    List<Map<String, Object>> GetEsxiMaintenancestateReportByDeviceId(String modelCode, String deviceId, String yearMonth, String type, String item, String serviceCode, String eid);
    List<Map<String, Object>> GetEsxiMaintenancestateReport(String reportTempId, String modelCode, String type, String item, String serviceCode, String eid);

    int updateReportMaintenance(ReportMaintenanceUpdateDTO dto);

    List<ReportMaintenanceLog> getReportLogs(Long reportMaintenanceId);

    BaseResponse saveAssetMaintenanceReport(ReportMaintenance reportMaintenance);
    BaseResponse getMaintenanceDeviceList(Long reportAssetMaintenanceId);
    BaseResponse createMaintenancereadLog(ReportMaintenanceReadLog reportMaintenanceReadLog);
    BaseResponse getMaintenancereadLog(Long reportAssetMaintenanceId, Integer pageNum, Integer pageSize);
    BaseResponse updateReportMISStatus(Long reportAssetMaintenanceId, Integer misStatus, String sender);
    void downloadMaintenanceReportFile(String serviceCode, String reportId, String fileId, HttpServletResponse response);
    BaseResponse selectAssetReportRecord(String eid, String startReportDate, String endReportDate, String assetCategory, String userName, String startReportGenerateTime, String endReportGenerateTime, String serviceCodeORCustomerName, int pageNum, int pageSize);
    BaseResponse deleteAssetReportRecordById(Long id, String reportType);
    BaseResponse insertAssetReportRecord(AssetReportRecord record);
    BaseResponse selectAssetReportRecordById(Long id);

}
