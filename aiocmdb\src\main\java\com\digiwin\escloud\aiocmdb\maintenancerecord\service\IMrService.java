package com.digiwin.escloud.aiocmdb.maintenancerecord.service;

import com.digiwin.escloud.common.response.BaseResponse;

import java.util.List;
import java.util.Map;

public interface IMrService {
    /**
     * 依据id查询实例数据
     * @param modelCode 模型编号
     * @param id 模型key
     * @param eid 租户Id
     * @return 模型实例
     */
    Object getMrDetail(String modelCode, String id, Long eid);

    /**
     * 依据字典列表查询维护记录
     * @param mapList 字典列表
     * @return 回覆对象
     */
    BaseResponse gerMrDetailByMapList(List<Map<String, Object>> mapList);

    /**
     * 储存模型实例数据
     * @param modelCode 模型编号
     * @param id 模型key
     * @param targetValue 实例数据
     * @return
     */
    boolean saveMrDetail(String modelCode, String id, Object targetValue);

    /**
     * 依据rowKey查询实例数据
     * @param modelCode 模型编号
     * @param containSplitSign 是否包含分隔符
     * @param keyList 模型key列表
     * @return 回覆对象
     */
    BaseResponse getMrDetailByKeyList(String modelCode, Boolean containSplitSign, List<String> keyList);

    /**
     * 根据id列表删除维护记录
     *
     * @param modelCode 模型代号
     * @param idList    id列表
     * @return 是否成功
     */
    boolean removeMrDetailByIdList(String modelCode, List<String> idList);

    boolean removeMrDetailByIdListAndEid(String modelCode, List<String> idList,Long eid);

//    /**
//     * 依据条件字典查询实例数据
//     * @param modelCode 模型编号
//     * @param conditionMap 条件字典
//     * @return 回覆对象
//     */
//    BaseResponse getMrDetailByMap(String modelCode, Map<String, Object> conditionMap);
//
//    /**
//     * 依据分组条件字典查询实例数据
//     * @param modelCode 模型编号
//     * @param groupConditionMap 分组条件字典
//     * @return 回覆对象
//     */
//    BaseResponse getMrDetailByGroupMap(String modelCode, Map<String, Object> groupConditionMap);
//
//    /**
//     * 依据分组条件字典删除实例数据
//     * @param modelCode 模型编号
//     * @param groupConditionMap 分组条件字典
//     * @return 回覆对象
//     */
//    BaseResponse removeMrDetailByGroupMap(String modelCode, Map<String, Object> groupConditionMap);
}
