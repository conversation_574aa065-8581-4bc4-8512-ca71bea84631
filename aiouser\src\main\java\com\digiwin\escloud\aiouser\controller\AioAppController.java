package com.digiwin.escloud.aiouser.controller;

import com.digiwin.escloud.aiouser.model.tenant.TenantInfo;
import com.digiwin.escloud.aiouser.service.IAioAppService;
import com.digiwin.escloud.common.feign.AioBasicFeignClient;
import com.digiwin.escloud.common.model.App;
import com.digiwin.escloud.common.model.ResponseBase;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @Date: 2022-04-26 19:07
 * @Description
 */
@Api(value = "应用接口", tags = {"应用接口"})
@RestController
@RequestMapping("/api/app")
public class AioAppController {

    @Resource
    private IAioAppService aioAppService;

    @Resource
    private AioBasicFeignClient aioBasicFeignClient;

//    @ApiOperation(value = "获取应用列表")
//    @GetMapping("/list")
//    public List<App> getApps(
//            @ApiParam(required = false, value = "租户sid")
//            @RequestParam(value = "tenantSid", required = false) Long tenantSid) {
//        return aioAppService.getAioApps(tenantSid);
//    }

    @ApiOperation(value = "获取应用列表")
    @GetMapping("/list")
    public List<App> getAioApps(
            @ApiParam(value = "租户sid")
            @RequestParam(value = "tenantSid", required = false) Long tenantSid,
            @ApiParam(value = "运维商Sid")
            @RequestParam(value = "sid", required = false) Long sid) {
        return aioAppService.getAioApps(tenantSid, sid);
    }

    @ApiOperation(value = "获取购买应用的租户")
    @GetMapping("/tenant/subscribed")
    public List<TenantInfo> getSubscribedTenantByAppCode(@RequestParam String appCode) {
        return aioAppService.getSubscribedTenantByAppCode(appCode);
    }

//    @GetMapping("/test")
//    public ResponseBase test() {
//        ResponseBase<List<Long>> projectEidList = aioBasicFeignClient.getProjectEidList();
//        return projectEidList;
//    }
}
