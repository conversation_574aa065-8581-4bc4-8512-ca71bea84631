package com.digiwin.escloud.aiobasic.clouddrive.controller;

import com.digiwin.escloud.aiobasic.clouddrive.controller.dto.DocServerConfigResponse;
import com.digiwin.escloud.aiobasic.clouddrive.service.DocServerService;
import com.digiwin.escloud.common.controller.ControllerBase;
import com.digiwin.escloud.common.util.RequestUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 文档服务器控制器
 */
@Api(value = "/doc/server", protocols = "HTTP", tags = {"文档服务器相关接口"}, description = "文档服务器相关接口")
@RestController
@RequestMapping("/doc/server")
public class DocServerController extends ControllerBase {

    @Autowired
    DocServerService docServerService;

    @RequestMapping(value = "/{userSid}/config", method = RequestMethod.GET)
    public DocServerConfigResponse getServerConf(@ApiParam(value = "用戶Sid", required = true)
                                                 @PathVariable(value = "userSid") Long userSid,
                                                 @ApiParam(value = "是否是企业内部地址")
                                                 @RequestParam(value = "isIntranet", required = false) Boolean isIntranet,
                                                 @ApiParam(value = "服务中心")
                                                 @RequestParam(value = "serviceRegion", required = false) String serviceRegion) {
        Long sid = RequestUtil.getHeaderSid();
        String token = RequestUtil.getHeaderToken();
        return this.getResponse(DocServerConfigResponse.class,
                () -> docServerService.getDocServerConfig(sid, userSid, token, isIntranet, serviceRegion),
                false, null, GENERAL_FAIL_CODE);
    }
}
