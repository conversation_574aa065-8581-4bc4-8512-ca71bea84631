<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.digiwin.escloud.aiocmdb.report.dao.ReportDao">
    <resultMap id="ReceiverMap" type="com.digiwin.escloud.aiocmdb.report.model.ReportReceiver">
        <result property="id" column="id"/>
        <result property="sid" column="sid"/>
        <result property="serviceCode" column="serviceCode"/>
        <result property="receiverName" column="receiverName"/>
        <result property="receiverMail" column="receiverMail"/>
        <result property="title" column="title"/>
        <result property="customerName" column="customerName"/>
        <result property="moduleCode" column="moduleCode"/>
        <collection property="modules" column="{id=id,moduleCode=moduleCode}" javaType="ArrayList"
                    ofType="com.digiwin.escloud.aiocmdb.report.model.ReportReceiverModuleBase" select="getReportReceiverModule"/>
    </resultMap>
    <resultMap id="ReportMap" type="com.digiwin.escloud.aiocmdb.report.model.ReportMaintenance">
        <result property="id" column="id"/>
        <result property="sid" column="sid"/>
        <result property="rid" column="rid"/>
        <result property="serviceCode" column="serviceCode"/>
        <result property="reportTime" column="reportTime"/>
        <result property="reportType" column="reportType"/>
        <result property="reportStatus" column="reportStatus"/>
        <result property="reporter" column="reporter"/>
        <result property="sender" column="sender"/>
        <result property="deviceNum" column="deviceNum"/>
        <result property="receivers" column="receivers"/>
        <result property="generateTime" column="generateTime"/>
        <result property="sendTime" column="sendTime"/>
        <result property="platform" column="platform"/>
        <result property="description" column="description"/>
        <collection property="files" columnPrefix="cr_"
                    resultMap="ReportFileMap" />
<!--        <result property="status" column="status"/>-->
<!--        <collection property="generalReports" columnPrefix="gr_"-->
<!--                     resultMap="ReportFileMap" />-->
<!--        <collection property="assetReports" columnPrefix="ar_"-->
<!--                     resultMap="ReportFileMap" />-->
<!--        <collection property="clientReports" columnPrefix="cr_"-->
<!--                    resultMap="ReportFileMap" />-->

    </resultMap>
    <resultMap id="ReportFileMap" type="com.digiwin.escloud.aiocmdb.report.model.ReportFile">
        <result property="id" column="id"/>
        <result property="fileId" column="fileId"/>
        <result property="name" column="name"/>
        <result property="fileType" column="fileType"/>
        <result property="url" column="url"/>
        <result property="rmId" column="rmId"/>
    </resultMap>

    <resultMap id="ReportTempMap" type="com.digiwin.escloud.aiocmdb.report.model.ReportTemp">
        <result property="id" column="id"/>
        <result property="sid" column="sid"/>
        <result property="serviceCode" column="serviceCode"/>
        <result property="reportTime" column="reportTime"/>
        <association property="reportTempDevices" columnPrefix="rtd_"
                     resultMap="ReportTempDeviceMap" />
    </resultMap>
    <resultMap id="ReportTempDeviceMap" type="com.digiwin.escloud.aiocmdb.report.model.ReportTempDevice">
        <result property="id" column="id"/>
        <result property="reportTempId" column="reportTempId"/>
        <result property="deviceId" column="deviceId"/>
        <result property="modelCode" column="modelCode"/>
    </resultMap>

    <select id="getReceiversEidList" resultType="Long">
        SELECT DISTINCT stm.eid FROM report_receiver rr
        LEFT JOIN supplier_tenant_map stm ON stm.sid = rr.sid AND stm.serviceCode = rr.serviceCode
    </select>

    <select id="getReceivers" resultMap="ReceiverMap">
        SELECT distinct a.id,a.sid,a.serviceCode,receiverName,receiverMail,title,b.name as customerName,#{moduleCode} moduleCode
        FROM report_receiver a
        LEFT JOIN supplier_tenant_map stm ON stm.sid = a.sid AND stm.serviceCode = a.serviceCode
        LEFT JOIN tenant b ON stm.eid=b.sid
        LEFT JOIN report_receiver_module c ON a.id = c.rrId
        WHERE 1=1 and a.sid = #{sid}
        <if test="serviceCode !=''">
            and a.serviceCode=#{serviceCode}
        </if>
        <if test="moduleCode !='' and moduleCode != null">
            and c.moduleCode=#{moduleCode}
        </if>
        <if test="eidList !=null and eidList.size()>0">
           <foreach collection="eidList" item="item" open=" AND eid IN(" separator="," close=")">
               #{item}
           </foreach>
        </if>
        order by a.serviceCode
        <if test="!(start == 0 and size == 0)">
            LIMIT #{start} , #{size}
        </if>

    </select>

    <select id="getReportReceiverModule" resultType="com.digiwin.escloud.aiocmdb.report.model.ReportReceiverModuleBase">
        SELECT a.moduleCode, b.moduleName
        FROM report_receiver_module a
        LEFT JOIN report_receiver_module_base b ON a.moduleCode = b.moduleCode
        WHERE a.rrId = #{id}
        <if test="moduleCode !='' and moduleCode != null">
            and a.moduleCode=#{moduleCode}
        </if>
    </select>
    <select id="getReceiversCount" resultType="java.lang.Integer">
        SELECT count(DISTINCT a.id) count
        FROM report_receiver a
        LEFT JOIN supplier_tenant_map stm ON stm.sid = a.sid AND stm.serviceCode = a.serviceCode
        LEFT JOIN report_receiver_module b ON a.id = b.rrId
        WHERE 1=1 and a.sid = #{sid}
        <if test="serviceCode !=''">
            and a.serviceCode=#{serviceCode}
        </if>
        <if test="moduleCode !='' and moduleCode != null">
            and b.moduleCode=#{moduleCode}
        </if>
        <if test="eidList !=null and eidList.size()>0">
            <foreach collection="eidList" item="item" open=" AND eid IN(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="getModules" resultType="com.digiwin.escloud.aiocmdb.report.model.ReportReceiverModuleBase">
        SELECT *
        FROM report_receiver_module_base a
        order by a.moduleCode asc
    </select>

    <insert id="saveReceiver" parameterType="com.digiwin.escloud.aiocmdb.report.model.ReportReceiver">
        insert into report_receiver (id,sid,serviceCode,receiverName,receiverMail,title)
        values (#{id},#{sid},#{serviceCode},#{receiverName},#{receiverMail},#{title})
        ON DUPLICATE KEY UPDATE sid=#{sid},serviceCode=#{serviceCode},receiverName=#{receiverName},receiverMail=#{receiverMail},
        title=#{title}
    </insert>

    <delete id="deleteReceiverModules">
        delete from report_receiver_module
        where rrid = #{id}
    </delete>

    <insert id="saveReceiverModules" parameterType="com.digiwin.escloud.aiocmdb.report.model.ReportReceiver">
        insert into report_receiver_module (rrid, moduleCode) values
        <foreach collection="list" item="item" separator=",">
            (#{rrId},#{item.moduleCode})
        </foreach>
    </insert>

    <delete id="deleteReceiver">
        delete from report_receiver
        where id=#{id}
    </delete>

    <select id="getFiles" resultMap="ReportFileMap">
        SELECT id, fileId, name, fileType, url, rmId
        FROM report_maintenance_file
        WHERE rmId = #{reportId}
    </select>

    <select id="getReports" resultMap="ReportMap">
        SELECT *
        FROM report_asset_maintenance
        WHERE 1=1 and sid = #{sid} and serviceCode=#{serviceCode}
        <if test="reportStatus != null">
            and reportStatus=#{reportStatus}
        </if>
        <if test="misStatus != null">
            AND misStatus = #{misStatus}
        </if>
        <if test="beginTime != null and beginTime != ''">
            AND reportTime >= #{beginTime}
        </if>
        <if test="endTime != null and endTime != ''">
            AND reportTime <![CDATA[<= #{endTime}]]>
        </if>
        ORDER BY
            CASE
                WHEN reportStatus = 3 THEN 1
                ELSE 0
            END ASC,
            reportTime desc,
            CASE
                WHEN generateTime IS NULL THEN '1970-01-01 00:00:00'
                ELSE generateTime
            END desc
        LIMIT #{start} , #{size}
<!--        SELECT a.*,-->
<!--        b.id as gr_id,b.fileId as gr_fileId,b.name as gr_name,b.fileType as gr_fileType,b.url as gr_url,b.rmId as gr_rmId,-->
<!--        c.id as ar_id,c.fileId as ar_fileId,c.name as ar_name,c.fileType as ar_fileType,c.url as ar_url,c.rmId as ar_rmId,-->
<!--        d.id as cr_id,d.fileId as cr_fileId,d.name as cr_name,d.fileType as cr_fileType,d.url as cr_url,d.rmId as cr_rmId-->
<!--        FROM(-->
<!--        SELECT id,sid,serviceCode,reportTime,receivers,sendTime,status,description-->
<!--        FROM report_maintenance-->
<!--        WHERE 1=1 and sid = #{sid} and serviceCode=#{serviceCode}-->
<!--        <if test="status != null">-->
<!--            and status=#{status}-->
<!--        </if>-->
<!--        <if test="beginTime != null and beginTime != ''">-->
<!--            AND reportTime >= #{beginTime}-->
<!--        </if>-->
<!--        <if test="endTime != null and endTime != ''">-->
<!--            AND reportTime <![CDATA[<= #{endTime}]]>-->
<!--        </if>-->
<!--        order by status asc,sendTime desc-->
<!--        LIMIT #{start} , #{size}-->
<!--        ) a-->
<!--        LEFT JOIN report_maintenance_file b ON a.id=b.rmId and b.fileType=0-->
<!--        LEFT JOIN report_maintenance_file c ON a.id=c.rmId and c.fileType=1-->
<!--        LEFT JOIN report_maintenance_file d ON a.id=d.rmId and d.fileType=2-->
<!--        order by status asc,sendTime desc-->
    </select>

    <select id="getReportsCount" resultType="java.lang.Integer">
        SELECT count(*)
        FROM report_asset_maintenance
        WHERE 1=1 and sid=#{sid} and serviceCode=#{serviceCode}
        <if test="status != null">
            and reportStatus=#{reportStatus}
        </if>
        <if test="misStatus != null">
            AND misStatus = #{misStatus}
        </if>
        <if test="beginTime != null and beginTime != ''">
            AND reportTime >= #{beginTime}
        </if>
        <if test="endTime != null and endTime != ''">
            AND reportTime <![CDATA[<= #{endTime}]]>
        </if>
    </select>

    <select id="selectReportMaintenanceById" resultMap="ReportMap">
        SELECT id, sid, serviceCode, receivers
        FROM report_asset_maintenance rm
        WHERE rm.id = #{rmId}
        LIMIT 1
    </select>

    <select id="selectReportReceiversByPartMap" resultType="com.digiwin.escloud.aiocmdb.report.model.ReportReceiver">
        SELECT rr.id, rr.sid, rr.receiverName, rr.receiverMail, rr.title
        FROM report_receiver rr
        <where>
            <if test="sid != null and sid > 0">
                AND sid = ${sid}
            </if>
            <if test="serviceCode != null and serviceCode != ''">
                AND rr.serviceCode = #{serviceCode}
            </if>
            <if test="partMail != null and partMail != ''">
                AND (LOWER(rr.receiverMail) = #{partMail}
                OR rr.receiverMail LIKE CONCAT(#{partMail}, ';%')
                OR rr.receiverMail LIKE CONCAT('%;', #{partMail}, ';%')
                OR rr.receiverMail LIKE CONCAT('%;', #{partMail}))
            </if>
        </where>
    </select>

    <update id="saveReportMaintenance" parameterType="com.digiwin.escloud.aiocmdb.report.model.ReportMaintenance">
        update report_asset_maintenance
        SET
            reportTime=#{reportTime},
            sender=#{sender},
            receivers=#{receivers},
            sendTime=#{sendTime},
            description=#{description}
        WHERE
            id=#{id}
    </update>

    <insert id="saveReportFile" parameterType="com.digiwin.escloud.aiocmdb.report.model.ReportFile">
        insert into report_maintenance_file (id,sid,fileId,name,fileType,url,rmId)
        values (#{id},#{sid},#{fileId},#{name},#{fileType},#{url},#{rmId})
    </insert>

    <insert id="saveReportTemp" parameterType="com.digiwin.escloud.aiocmdb.report.model.ReportTemp">
        insert into report_temp (id,sid,serviceCode,reportTime)
        values (#{id},#{sid},#{serviceCode},#{reportTime})
    </insert>

    <insert id="saveReportTempDevice" parameterType="com.digiwin.escloud.aiocmdb.report.model.ReportTempDevice">
        insert into report_temp_device (id,reportTempId,deviceId,modelCode) values
        <foreach collection="list" item="item" separator=",">
            (#{item.id},#{item.reportTempId},#{item.deviceId},#{item.modelCode})
        </foreach>
    </insert>

    <select id="getReportTemp" resultMap="ReportTempMap">
        select a.id,a.sid,a.serviceCode, a.reportTime,
               rtd.id as rtd_id, rtd.reportTempId as rtd_reportTempId, rtd.deviceId as rtd_deviceId,rtd.modelCode as rtd_modelCode
        from report_temp a
        left join report_temp_device rtd on rtd.reportTempId = a.id
        where a.id = #{id} and rtd.modelCode = #{modelCode}
    </select>

   <!-- <select id="getEnumName" resultType="java.lang.String">
        select ifnull(enumName, '') as enumName
        from fieldtype_enum
        where fieldCode = #{fieldCode} and enumCode = #{enumCode}
        limit 1release/immediately
    </select>-->
    <select id="getBackSoftList" resultType="com.digiwin.escloud.aiocmdb.backupschedule.model.BackupScheduleSoftware">
        select b.value,b.labelname_CN AS namecn,b.labelname_TW as nametw
        from  backup_schedule_software_detail b order by value
    </select>

    <update id="updateReportMaintenance">
        update report_asset_maintenance set reportStatus = #{reportStatus} where id = #{id}
    </update>

    <insert id="insertReportMaintenanceLog">
        insert into report_maintenance_log (reportMaintenanceId,operatorId,operator,operationType,operationTime,operatorDetail)
        values (#{id},#{operatorId},#{operator},#{status},#{operationTime},#{operatorDetail})
    </insert>

    <select id="getReportLogs" resultType="com.digiwin.escloud.aiocmdb.report.model.ReportMaintenanceLog">
        select * from report_maintenance_log where reportMaintenanceId = #{reportMaintenanceId} order by operationTime desc
    </select>

    <insert id="saveAssetMaintenanceReport" parameterType="com.digiwin.escloud.aiocmdb.report.model.ReportMaintenance">
        insert into report_asset_maintenance (id,sid,rid,serviceCode,reportTime,reportType,reportStatus,reporter,deviceNum,generateTime,sendTime,platform)
        values (#{id},#{sid},#{rid},#{serviceCode},#{reportTime},#{reportType},#{reportStatus},#{reporter},#{deviceNum},#{generateTime},#{sendTime},#{platform})
    </insert>

    <insert id="saveMaintenanceAsset">
        insert into report_maintenance_device_mapping (id, ramId, deviceId, deviceName, computerPlacement, modelCode)
        VALUES
        <foreach collection="reportDeviceMappingList" item="reportDevice" separator=",">
            (#{reportDevice.id}, #{ramId}, #{reportDevice.deviceId}, #{reportDevice.deviceName},
            #{reportDevice.computerPlacement}, #{reportDevice.modelCode})
        </foreach>
    </insert>

    <select id="getMaintenanceAsset" resultType="com.digiwin.escloud.aiocmdb.report.model.ReportDeviceMapping">
        select *
        from report_maintenance_device_mapping
        where ramId = #{ramId}
    </select>

    <insert id="createMaintenanceReadLog">
        insert into report_asset_maintenance_read_log(id, sid, eid, assetMaintenanceId, serviceCode, userId, userName, readTime)
        values(#{id}, #{sid}, #{eid}, #{assetMaintenanceId}, #{serviceCode}, #{userId}, #{userName}, #{readTime})
    </insert>

    <select id="getMaintenanceReadLog" resultType="com.digiwin.escloud.aiocmdb.report.model.ReportMaintenanceReadLog">
        select *
        from report_asset_maintenance_read_log
        where assetMaintenanceId=#{assetMaintenanceId}
        order by readTime desc
        limit #{pageNum}, #{pageSize}
    </select>

    <select id="getMaintenanceReadLogCount" resultType="java.lang.Integer">
        select COUNT(*)
        from report_asset_maintenance_read_log
        where assetMaintenanceId=#{assetMaintenanceId}
    </select>
    
    <select id="getMaintenanceStatus" resultType="java.lang.Integer">
        select operationType
        from report_maintenance_log
        where reportMaintenanceId=#{reportMaintenanceId}
        order by operationTime desc
        limit 2
    </select>

    <update id="updateMISStatus">
        update report_asset_maintenance
        set misStatus = #{misStatus}, reportStatus = 2
        <if test="sender != null">
            , sender = #{sender}
        </if>
        <if test="sendTime != null">
            , sendTime = #{sendTime}
        </if>
        where id = #{assetMaintenanceId}
    </update>

    <select id="getFileByReportIdAndFileId" resultType="com.digiwin.escloud.aiocmdb.report.model.ReportFile">
        SELECT rmf.id, rmf.sid, rmf.fileId, rmf.name, rmf.fileType, rmf.url, rmf.rmId
        FROM report_maintenance_file rmf
        INNER JOIN report_asset_maintenance ram ON ram.id = rmf.rmId
        WHERE ram.serviceCode = #{serviceCode}
          AND ram.reportStatus != 3
          AND ram.id = #{reportId}
          AND rmf.id = #{fileId}
    </select>

    <select id="selectAssetReportRecord" resultType="com.digiwin.escloud.aiocmdb.report.model.AssetReportRecord">
        SELECT *
        FROM asset_report_record
        WHERE 1 = 1
        <if test="eid != null and eid > 0">
            AND eid = #{eid}
        </if>
        <if test="startReportDate != null  and startReportDate != '' and  endReportDate != '' and endReportDate != ''  ">
            AND reportDate BETWEEN #{startReportDate} AND #{endReportDate}
        </if>
        <if test="assetCategory != null and assetCategory != ''">
            AND assetCategory = #{assetCategory}
        </if>
        <if test="userName != null and userName != ''">
            AND userName LIKE CONCAT('%', #{userName}, '%')
        </if>
        <if test="startReportGenerateTime != null  and startReportGenerateTime != '' and  endReportGenerateTime != '' and endReportGenerateTime != ''  ">
            AND reportGenerateTime BETWEEN #{startReportGenerateTime} AND #{endReportGenerateTime}
        </if>
        <if test="serviceCodeORCustomerName != null and serviceCodeORCustomerName != ''">
            AND (serviceCode LIKE CONCAT('%', #{serviceCodeORCustomerName}, '%')
            OR customerName LIKE CONCAT('%', #{serviceCodeORCustomerName}, '%'))
        </if>
    </select>

    <delete id="deleteAssetReportRecordById">
        DELETE FROM asset_report_record
        WHERE id = #{id} and reportType = #{reportType}
    </delete>

    <insert id="insertAssetReportRecord" parameterType="com.digiwin.escloud.aiocmdb.report.model.AssetReportRecord">
        INSERT INTO asset_report_record (id, sid, eid, serviceCode, customerName, customerFullName, reportDate,
                                         reportType, reportStatus, assetCategory, confidentialityLevel, assetCount,
                                         assetObject, reportGenerateTime, reportVersion, documentNumber, recordNumber,
                                         retentionThreshold, retentionUnit, userId, userName, reportName)
        VALUES (#{id}, #{sid}, #{eid}, #{serviceCode}, #{customerName}, #{customerFullName}, #{reportDate},
                #{reportType}, #{reportStatus}, #{assetCategory}, #{confidentialityLevel}, #{assetCount},
                #{assetObject}, #{reportGenerateTime}, #{reportVersion}, #{documentNumber}, #{recordNumber},
                #{retentionThreshold}, #{retentionUnit}, #{userId}, #{userName}, #{reportName})
    </insert>

    <update id="updateReportStatus" parameterType="com.digiwin.escloud.aiocmdb.report.model.AssetReportRecord">
        UPDATE asset_report_record
        SET reportStatus = #{reportStatus}
        WHERE id = #{id}
    </update>

    <select id="queryMaxRecordNumberByEid" resultType="string">
        SELECT MAX(recordNumber)
        FROM asset_report_record
        WHERE eid = #{eid}
          AND recordNumber LIKE #{likePattern}
    </select>

    <select id="selectAssetReportRecordById" resultType="com.digiwin.escloud.aiocmdb.report.model.AssetReportRecord">
        SELECT * FROM asset_report_record
        WHERE id = #{id}
    </select>

</mapper>