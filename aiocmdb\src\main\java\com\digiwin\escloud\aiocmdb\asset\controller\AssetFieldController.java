package com.digiwin.escloud.aiocmdb.asset.controller;

import com.digiwin.escloud.aiocmdb.asset.model.AssetFieldInfo;
import com.digiwin.escloud.aiocmdb.asset.model.CmdbModelShowFieldUser;
import com.digiwin.escloud.aiocmdb.asset.service.IAssetFieldService;
import com.digiwin.escloud.aiocmdb.field.model.Field;
import com.digiwin.escloud.common.response.BaseResponse;
import com.digiwin.escloud.common.util.RequestUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 资产字段管理控制器
 */
@Api(tags = "资产字段管理")
@RestController
@RequestMapping("/asset/field")
public class AssetFieldController {

    @Autowired
    private IAssetFieldService assetFieldService;

    @ApiOperation("获取模型显示字段列表")
    @GetMapping("/showFieldList")
    public BaseResponse<List<CmdbModelShowFieldUser>> getShowFieldList(
            @ApiParam(value = "模型编码", required = true) @RequestParam String modelCode,
            @ApiParam(value = "用户ID", required = true) @RequestParam String userId) {
        
        BaseResponse<List<CmdbModelShowFieldUser>> response = new BaseResponse<>();
        try {
            long sid = RequestUtil.getHeaderSid();
            List<CmdbModelShowFieldUser> fieldList = assetFieldService.getShowFieldList(modelCode, userId, sid);
            response.setData(fieldList);
        } catch (Exception e) {
            response.setErrMsg("获取显示字段列表失败: " + e.getMessage());
        }
        return response;
    }

    @ApiOperation("基于ModelDetail解析字段列表")
    @GetMapping("/fieldList")
    public BaseResponse<List<AssetFieldInfo>> getFieldList(
            @ApiParam(value = "模型编码", required = true) @RequestParam String modelCode) {

        BaseResponse<List<AssetFieldInfo>> response = new BaseResponse<>();
        try {
            long sid = RequestUtil.getHeaderSid();
            List<AssetFieldInfo> fieldList = assetFieldService.getFieldList(modelCode, sid);
            response.setData(fieldList);
        } catch (Exception e) {
            response.setErrMsg("获取字段列表失败: " + e.getMessage());
        }
        return response;
    }

    @ApiOperation("保存用户字段配置")
    @PostMapping("/saveUserFieldConfig")
    public BaseResponse<Void> saveUserFieldConfig(
            @ApiParam(value = "模型编码", required = true) @RequestParam String modelCode,
            @ApiParam(value = "用户ID", required = true) @RequestParam String userId,
            @ApiParam(value = "字段信息列表", required = true) @RequestBody List<AssetFieldInfo> fieldInfoList) {
        try {
            long sid = RequestUtil.getHeaderSid();
            assetFieldService.saveUserFieldConfig(modelCode, userId, sid, fieldInfoList);
            return BaseResponse.ok();
        } catch (Exception e) {
           return BaseResponse.error("1","保存用户字段配置失败: " + e.getMessage());
        }
    }

    @ApiOperation("批量获取模型显示字段列表")
    @PostMapping("/batchShowFieldList")
    public BaseResponse<Map<String, List<CmdbModelShowFieldUser>>> getBatchShowFieldList(
            @ApiParam(value = "模型编码列表", required = true) @RequestBody List<String> modelCodes,
            @ApiParam(value = "用户ID", required = true) @RequestParam String userId) {

        BaseResponse<Map<String, List<CmdbModelShowFieldUser>>> response = new BaseResponse<>();
        try {
            long sid = RequestUtil.getHeaderSid();
            Map<String, List<CmdbModelShowFieldUser>> fieldListMap = assetFieldService.getBatchShowFieldList(modelCodes, userId, sid);
            response.setData(fieldListMap);
        } catch (Exception e) {
            response.setErrMsg("批量获取显示字段列表失败: " + e.getMessage());
        }
        return response;
    }


    @ApiOperation("批量保存用户字段配置")
    @PostMapping("/batchSaveUserFieldConfig")
    public BaseResponse<Void> batchSaveUserFieldConfig(
            @ApiParam(value = "字段配置映射", required = true) @RequestBody Map<String, List<AssetFieldInfo>> fieldConfigMap,
            @ApiParam(value = "用户ID", required = true) @RequestParam String userId) {
        try {
            long sid = RequestUtil.getHeaderSid();
            assetFieldService.batchSaveUserFieldConfig(fieldConfigMap, userId, sid);
            return BaseResponse.ok();
        } catch (Exception e) {
           return BaseResponse.error("1","批量保存用户字段配置失败: " + e.getMessage());
        }
    }
}
